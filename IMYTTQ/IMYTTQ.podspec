# MARK: converted automatically by spec.py. @hgy

Pod::Spec.new do |s|
	s.name = 'IMYTTQ'
	s.version = '********'
	s.description = 'IMYTTQ, 美柚她她圈'
	s.license = 'MIT'
	s.summary = 'IMYTTQ'
	s.homepage = 'http://www.meiyou.com'
	s.authors = { 'haha' => '<EMAIL>' }
	s.source = { :git => '*********************:iOS/IMYTTQ.git', :branch => 'release-8.95.0' }
	s.requires_arc = true
	s.ios.deployment_target = '11.0'

	s.source_files = 'IMYTTQ/source/**/*.{h,m}'
	s.resources = 'Resource/**/*.{xib,json,png,jpg,gif,js,pag}','IMYTTQ/source/**/*.{xib,json,png,jpg,gif,js,pag}','IMYTTQ/Bundles/*.{bundle,xcassets}'
	s.frameworks = 'MediaPlayer'
	
	s.dependency 'IMYAdvertisement'
	s.dependency 'IMYBaseKit'
	s.dependency 'IMYVideoPlayer'
	s.dependency 'IMYAccount'
	
	s.dependency 'StandardPaths','1.6.6'
	s.dependency 'IMYViewCache','0.4'
	s.dependency 'Myhpple','0.0.3'
	s.dependency 'MXParallaxHeader'
end
