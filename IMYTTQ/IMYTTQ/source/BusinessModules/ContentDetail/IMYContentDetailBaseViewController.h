//
//  IMYContentDetailBaseViewController.h
//  IMYTTQ
//
//  Created by 林云峰 on 2025/5/26.
//

#import "IMYPublicBaseViewController.h"
#import "TTQTopicBaseViewModel.h"
#import "IMYTableViewAdapterModule.h"
#import <IMYUGC/IMYCKBottomReplyBar.h>
#import "TTQNavigationBarView.h"
#import <IMYUGC/IMYCKInputWithStickerView.h>
#import "TTQCommentHeaderAdapter.h"

NS_ASSUME_NONNULL_BEGIN

@class IMYCaptionViewV2;
@class IMYCoolShareItem;
@class IMYVideoView;

FOUNDATION_EXTERN NSString *gJindouReviewTaskKey;

@interface IMYContentDetailBaseViewController : IMYPublicBaseViewController

@property (nonatomic, strong, readonly) UITableView *tableView;
@property (nonatomic, strong, readonly) IMYTableViewAdapter *tableViewAdapter;
@property (nonatomic, strong) id <IMYTableViewAdapterModuleDelegate>commentAdapter;
@property (nonatomic, strong, readonly) UIView *mainContentView;
@property (nonatomic, strong, readonly) TTQNavigationBarView *customNavigationBar;
@property (nonatomic, strong, readonly) IMYCKBottomReplyBar *bottomBar;
@property (nonatomic, strong, readonly) IMYCaptionViewV2 *captionView;
@property (nonatomic, strong, readonly) IMYCKInputWithStickerView *inputContentsView;
/// 是否检查过分享可用
@property (nonatomic, assign) BOOL checkShareSetting;
///分享是否可用
@property (nonatomic, assign) NSInteger shareEnable;
@property (nonatomic, copy) NSString *algorithm;
@property (nonatomic, copy) NSString *al_source;

@property (nonatomic, assign) BOOL isLocateEnd; // 进页面后是否定位结束，结束后可恢复播放视频
@property (nonatomic, strong) IMYVideoView *curPlayingVideoView;

/// 传入各自的viewModel
- (instancetype)initWithViewModel:(TTQTopicBaseViewModel *)viewModel;
- (TTQTopicBaseViewModel *)viewModel;
- (void)bindViewModel;
/// 她她圈详情页会缓存上次的排序，因此要优先读取缓存，基类默认不读取
- (void)loadCache:(void(^ _Nullable)(void))completeBlock;
- (void)addEventNotifications;

#pragma mark - 内容的关键数据
/// 主内容作者，
- (TTQPublisherModel *)contentPublisher;
/// 内容id，可以是帖子，也可以是评论
- (NSInteger)contentId;
/// 返回对应的业务类型
- (IMYNewsItemType)contentType;
/// 原帖或者原问题、资讯内容id
- (NSInteger)articleId;
/// 是否是柚+文章
- (BOOL)isYouPlus;
#pragma mark - UI 流程
/// didLoad时创建
- (void)initViewWhenDidLoad;
/// 创建tableView
- (void)initTableView;
- (void)addHeaderRefreshCompont;
- (void)addFooterRefreshCompont;
/// 是否是短图文内容，决定顶部的UI样式
- (BOOL)isShortContent;
/// 主内容区域，如帖子详情页、评论详情内容
- (UIView *)createContentHeaderView;
- (void)updateContentHeaderView;

#pragma mark - 导航栏更新
- (void)setupNavigationBar;
/// 滚动多少距离时展示顶部作者信息
- (CGFloat)offsetYWhenBarUserInfoShow;
/// 滚动多少距离时，展示导航栏底部线条
- (CGFloat)offsetYWhenShowBarBottomLine;
#pragma mark - bottomBar

- (void)setupBottomBar;
- (void)updateBottomBar;
- (void)bottomBarTextBgTapAction;
/// 返回 publicType, 埋点使用
- (NSInteger)bottomBarCommentAction;
- (void)bottomBarCollectAction;
- (void)bottomBarHugAction;
- (void)bottomBarPraiseAction;

/// 是否是空评论， 子类需要根据实际情况重载
- (BOOL)isEmptyComment;
#pragma mark - inputView
- (void)initInputContentView;
#pragma mark - 请求相关
/// 请求数据
- (void)requestData:(BOOL)isRefresh;
/// 请求数据
- (void)requestData:(BOOL)isRefresh
            success:(void(^ _Nullable)(id _Nullable response))successBlk
            failure:(void(^ _Nullable)(NSError * _Nullable error))failureBlk;
/// 请求结束，这里error的情况比较复杂，子类自己处理error，主要内容主体的Model不一致，无法在基类统一判断是否加载完成
- (void)finishedRequest:(NSError *)error isRefresh:(BOOL)isRefresh;
/// 自定义请求参数
- (NSDictionary *)requestParams;
/// 注册adapter
- (void)registerAdaters;
- (id<IMYTableViewAdapterModuleDelegate>)createCommentAdapter;
/// 好友关系视图更新
- (void)updateFollowViews;

#pragma mark - 楼层回复

- (BOOL)commentEnable;
- (void)replyActionAtIndexPath:(NSIndexPath *)indexPath;

/// 回复逻辑
/// - Parameters:
///   - indexPath: 评论所在的indexPath， 如果是nil，则回复主评论
///   - isFromCommentIcon: 是否从评论icon点击进入回复
- (void)replyActionAtIndexPath:(NSIndexPath *)indexPath isFromCommentIcon:(BOOL)isFromCommentIcon;
- (NSString *)cacheCommentContentForIndexPath:(NSIndexPath *)indexPath;

/// 复制评论内容
/// @param indexPath 评论所在的indexPath
/// 如果indexPath为nil，则不进行任何操作
- (void)copyActionAtIndexPath:(nonnull NSIndexPath *)indexPath;

/// 删除评论操作
/// @param indexPath IndexPath of the comment to delete
- (void)deleteActionAtIndexPath:(NSIndexPath *)indexPath ;

/// 举报评论
/// - Parameter indexPath: 评论的indexPath
- (void)reportActionAtIndexPath:(NSIndexPath *)indexPath;

/// 置顶评论
/// - Parameters:
///   - indexPath: 评论的indexPath
///   - isTop: 是否置顶, YES置顶，NO取消置顶
- (void)pinTopAtIndexPath:(NSIndexPath *)indexPath setTop:(BOOL)isTop;

#pragma mark - 回复请求相关
- (BOOL)loginAction;
/// 是否可以发送评论请求，社区有的圈子需要判断加圈
- (BOOL)canSendReplyRequest;

#pragma markr - scrollView

- (void)scrollViewDidScroll:(UIScrollView *)scrollView;

//MARK: - keyboard相关
- (void)hideKeyboard;
- (void)hideKeyboardForceDispatchToMain:(BOOL)forceDispatchToMain;
- (void)clearInputView ;
/// 进入页面时是否需要弹窗回复框
- (void)showReplyWhenAppearIfNeeded;
//检查键盘内容是否可以发送
// 如果不可以发送，返回NO
- (BOOL)checkInputViewCanSend;

- (NSString *)inputContentFromTextView;

//MARK: - 请求相关
- (void)appendImagesV2Params:(NSMutableDictionary *)params ;
/// 回复评论
/// - Parameter imageNames: 图片名称数组，如果没有图片则传nil
- (void)postReplyWithImages:(nullable NSArray<NSString *> *)imageNames;
- (void)judgeBlockedWithUserInfo:(TTQTopicCurrentUserInfo *)userInfo;

//MARK: - 视频相关
/// 刷新播放器
- (void)checkScrollWithDelay:(double)delay;

//MARK: - 图片浏览器相关
- (void)showWithPhotos:(NSMutableArray *)photos atIndex:(NSUInteger)index;

///子类重载，查看大图，退出图片浏览业务处理逻辑
- (void)postHandlePhotoBroswerShow;
- (void)postHandlePhotoBroswerHide;

//MARK: - IMYRM80AttributedLabelDelegate
- (void)m80AttributedLabel:(IMYRM80AttributedLabel *)label longedOnLink:(IMYRM80AttributedLabelURL *)linkURL;
- (void)m80AttributedLabel:(IMYRM80AttributedLabel *)label clickedOnLink:(IMYRM80AttributedLabelURL *)linkURL;

#pragma markr - 评论相关

- (NSInteger)contentTotalReview;

- (void)setProcessHUD:(CGFloat)progress;
- (void)hidenProgressHUD;

@end

NS_ASSUME_NONNULL_END
