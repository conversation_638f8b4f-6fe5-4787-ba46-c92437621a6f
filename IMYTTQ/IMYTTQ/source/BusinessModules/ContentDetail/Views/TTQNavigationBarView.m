//
//  TTQNavigationBarView.m
//  IMYTTQ
//
//  Created by 林云峰 on 2025/5/26.
//

#import "TTQNavigationBarView.h"
#import <IMYBaseKit/IMYViewKit.h>
#import "IMYCKFollowButton.h"

@interface TTQNavigationBarView ()

@property (nonatomic, assign) BOOL largeStyle;
@property (nonatomic, strong) UIView *container;
@property (nonatomic, strong) IMYTouchEXButton *backButton;
@property (nonatomic, strong) IMYTouchEXButton *moreButton;
@property (nonatomic, strong) UIView *bottomLineView;
@property (nonatomic, strong) TTQTopicDetailPublisherTitleView *publisherInfoView;
@property (nonatomic, strong) TTQPublisherModel *publisherInfo;
@end

@implementation TTQNavigationBarView

+ (instancetype)defaultView {
    return [[TTQNavigationBarView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT) largeStyle:NO];
}

+ (instancetype)largeView {
    return [[TTQNavigationBarView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 56+SCREEN_STATUSBAR_HEIGHT) largeStyle:YES];
}

- (instancetype)initWithFrame:(CGRect)frame {
    return [self initWithFrame:frame largeStyle:NO];
}

- (instancetype)initWithFrame:(CGRect)frame largeStyle:(BOOL)largeStyle {
    self = [super initWithFrame:frame];
    if (self) {
        self.largeStyle = largeStyle;
        [self initViews];
    }
    return self;
}

- (void)initViews {
    [self imy_setBackgroundColorForKey:kIMY_BG];
    self.container = [[UIView alloc] initWithFrame:CGRectMake(0, SCREEN_STATUSBAR_HEIGHT, self.imy_width, self.imy_height - SCREEN_STATUSBAR_HEIGHT)];
    [self addSubview:self.container];
    [self.container addSubview:self.backButton];
    [self.container addSubview:self.moreButton];
    [self.container addSubview:self.publisherInfoView];
    [self.container addSubview:self.bottomLineView];
    self.backButton.imy_centerY = self.moreButton.imy_centerY = self.publisherInfoView.imy_centerY = self.container.imy_height/2;
    self.bottomLineView.imy_bottom = self.container.imy_height;
}

- (void)updatePublisher:(TTQPublisherModel *)publisher {
    self.publisherInfo = publisher;
    NSString *babyInfoStr = publisher.baby_info;
    if (imy_isNotEmptyString(publisher.mp_user_icon)){
        babyInfoStr = publisher.mp_expert_user;
    } else if(imy_isNotEmptyString(publisher.mp_expert_user_icon)){
        babyInfoStr = publisher.mp_expert_user_icon;
    } else if (imy_isNotEmptyString(publisher.qa_user_icon)){
        babyInfoStr = publisher.qa_user_icon;
    } else if (imy_isNotEmptyString(publisher.expertIconNew)){
        babyInfoStr = publisher.expertIconNew;
    }
    [self.publisherInfoView setUserAvastar:publisher.user_avatar.large isVip:publisher.isvip name:publisher.screen_name title:babyInfoStr];
    //  增加是否匿名 &自己发帖子的判断，是的话不显示关注按钮
    BOOL isSHow = (publisher.error == 0 || publisher.error == 2 || publisher.error == 3) && publisher.userID != [[IMYPublicAppHelper shareAppHelper].userid integerValue];
    if (publisher.doctor || publisher.hospital) {
        isSHow = NO; // 医生和医院不显示关注按钮
    }
    [self.publisherInfoView updatefollowButtonShow:isSHow status:publisher.is_followed rightShowNotice:NO];
}

- (void)hideMoreButton {
    self.moreButton.hidden = YES;
    self.publisherInfoView.imy_width = SCREEN_WIDTH - 48;
}

- (IMYTouchEXButton *)backButton {
    if (!_backButton) {
        _backButton = [[IMYTouchEXButton alloc] initWithFrame:CGRectMake(0, 0, 38, 44)];
        [_backButton imy_setImage:@"nav_btn_back_black"];
        _backButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentRight;
        @weakify(self);
        [[_backButton rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
            @strongify(self);
            if (self.backButtonActionBlock) {
                self.backButtonActionBlock();
            }
        }];
    }
    return _backButton;
}

- (IMYTouchEXButton *)moreButton {
    if (!_moreButton) {
        _moreButton = [[IMYTouchEXButton alloc] initWithFrame:CGRectMake(self.imy_width - 40 - 6, 0, 40, 44)];
        BOOL isNight = [IMYPublicAppHelper shareAppHelper].isNight;
        [_moreButton imy_setTitleColor:kCK_Black_A];
        _moreButton.titleEdgeInsets = UIEdgeInsetsMake(0, 0, 0, 2.5);
        _moreButton.imageEdgeInsets = UIEdgeInsetsMake(0, 0, 0, 2.5);
        _moreButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentCenter;
        [_moreButton imy_setTitle:@"\U0000e6f6"];
        _moreButton.titleLabel.font = [UIFont imy_IconFontWith:22];
        _moreButton.accessibilityIdentifier = @"ttq_detail_topRight_more";
        @weakify(self);
        [[_moreButton rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
            @strongify(self);
            if (self.moreButtonActionBlock) {
                self.moreButtonActionBlock();
            }
        }];
    }
    return _moreButton;
}

- (UIView *)bottomLineView {
    if (!_bottomLineView) {
        _bottomLineView = [[UIView alloc] initWithFrame:CGRectMake(0, 44, SCREEN_WIDTH, 1 / [UIScreen mainScreen].scale)];
        [_bottomLineView imy_setBackgroundColorForKey:kCK_Black_J];
        _bottomLineView.alpha = 0;
    }
    return _bottomLineView;
}

- (TTQTopicDetailPublisherTitleView *)publisherInfoView {
    if (!_publisherInfoView) {
        if (self.largeStyle) {
            _publisherInfoView = [[TTQTopicDetailPublisherTitleView alloc] initWithType:TTQTopicDetailPublisherTitleStyleBigIcon];
            _publisherInfoView.frame = CGRectMake(48, 0, SCREEN_WIDTH - 60 - 48, 56);// 修改布局
        } else {
            _publisherInfoView = [[TTQTopicDetailPublisherTitleView alloc] initWithType:TTQTopicDetailPublisherTitleStyleDefault];
            _publisherInfoView.frame = CGRectMake(48, 0, SCREEN_WIDTH - 60 - 48 + 10, 44);
        }
        _publisherInfoView.containView.alpha = self.largeStyle ? 1 : 0;
        @weakify(self);
        [_publisherInfoView bk_whenTapped:^{
            @strongify(self);
            if (self.publisherInfoView.containView.alpha == 0) {
                return;
            }
            
            // 894互医病例帖子判断，跳转到互医页面
            if ([self.publisherInfo.doctor isKindOfClass:NSDictionary.class] && self.publisherInfo.doctor.count > 0) {
                if (imy_isNotEmptyString(self.publisherInfo.redirect_url)) {
                    [[IMYURIManager sharedInstance] runActionWithString:self.publisherInfo.redirect_url];
                }
            } else if (self.publisherInfo.error == 1) {
                [UIView imy_showTextHUD:kStatusText_UserAnonymous];
            } else if (self.publisherInfo.error > 1) {
                [UIView imy_showTextHUD:kStatusText_homePageNotOpen];
            } else {
                NSInteger userId = self.publisherInfo.userID;
                // 女人通广告位的柚+文章携带page参数
                IMYURI *uri = [IMYURI uriWithPath:@"user/dynamic"
                                           params:@{ @"userID": @(userId)}
                                                         info:nil];
                [[IMYURIManager shareURIManager] runActionWithURI:uri];
            }
            if (self.userInfoTapBlock) {
                self.userInfoTapBlock();
            }
        }];
        [_publisherInfoView setTapFollowAction:^(UIButton *btn) {
            @strongify(self);
            IMYCKFollowButton *followBtn = (IMYCKFollowButton *)btn;
            TTQPublisherModel *publisherModel = self.publisherInfo;
            [followBtn followAction:publisherModel.is_followed userId:publisherModel.userID targetUserError:publisherModel.error myError:0 completeBlock:^(BOOL success, BOOL hasRequest, IMYRelationType finalRelation, id  _Nonnull responseObj) {
                @strongify(self);
                if(success){
                    // 1. 关注或者取消关注接口请求成功埋点上报处理
//                    if(self.publisherInfo.is_followed == 1 || self.publisherInfo.is_followed == 4){
//                        // 取消关注成功
//                        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_gz",@"public_type":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topic.publisher.userID),@"action":@2} headers:nil completed:nil];
//                    } else {
//                        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_gz",@"public_type":@1,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topic.publisher.userID),@"action":@2} headers:nil completed:nil];
//                    }
                    // 2.改变model状态, 按钮title跟数据联动
                    self.publisherInfo.is_followed = finalRelation;
                    [self updatePublisher:self.publisherInfo];
                }
                if (self.followResultActionBlock) {
                    self.followResultActionBlock(self.publisherInfo, success);
                }
            }];
        }];
    }
    return _publisherInfoView;
}

- (void)changePublisherViewShowStatus:(BOOL)show animated:(BOOL)animated {
    if (show) {
        [self.publisherInfoView.containView pop_removeAnimationForKey:@"hiden_publish"];
        if (![self.publisherInfoView.containView pop_animationForKey:@"show_publish"] && self.publisherInfoView.containView.alpha < 1.0) {
            POPBasicAnimation *alphaAnim = [POPBasicAnimation animationWithPropertyNamed:kPOPViewAlpha];
            alphaAnim.toValue = @(1.0);
            alphaAnim.duration = 0.25 * (1.0 - self.publisherInfoView.containView.alpha);
            [self.publisherInfoView.containView pop_addAnimation:alphaAnim forKey:@"show_publish"];
        }
    } else {
        [self.publisherInfoView.containView pop_removeAnimationForKey:@"show_publish"];
        if (![self.publisherInfoView.containView pop_animationForKey:@"hiden_publish"] && self.publisherInfoView.containView.alpha > 0.0) {
            POPBasicAnimation *alphaAnim = [POPBasicAnimation animationWithPropertyNamed:kPOPViewAlpha];
            alphaAnim.toValue = @(0.0);
            alphaAnim.duration = 0.25 * self.publisherInfoView.containView.alpha;
            [self.publisherInfoView.containView pop_addAnimation:alphaAnim forKey:@"hiden_publish"];
        }
    }

}
@end
