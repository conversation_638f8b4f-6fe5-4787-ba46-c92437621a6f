//
//  TTQTopicDetailCommentAdapter.m
//  IMYTTQ
//
//  Created by 连晓彬 on 2025/5/29.
//

#import "TTQTopicDetailCommentAdapter.h"

#import <IMYBaseKit/IMYBaseKit.h>
#import <IMYBaseKit/NSString+IMYR.h>

#import "TTQCommentModel.h"
#import "TTQEmptyCommentModel.h"
#import "TTQJumpType.h"
#import "TTQDetailHelper.h"
#import "TTQCheckService.h"
#import "TTQABTestConfig.h"

#import "TTQPrimaryCommentCell.h"
#import "TTQSecondaryCommentCell.h"
#import "TTQTopicCommentEmptyCell.h"
#import "TTQDetailCommentLoadingCell.h"
#import "TTQTopicScreenPlaceholderCell.h"
#import "TTQTopicCommentSectionHeader.h"

@interface TTQTopicDetailCommentAdapter ()
@property (nonatomic, weak) IMYTableViewAdapterModule *module;///< adapter module
@property (nonatomic, strong) TTQTopicViewModel *viewModel;///< 话题视图模型, 外部传递进来
@property (nonatomic, strong) TTQTopicCommentSectionHeader *sectionHeaderView;///< section header view

@property (nonatomic, strong) TTQPrimaryCommentCell *primaryHeightCell;///< 用于计算高度
@property (nonatomic, strong) TTQSecondaryCommentCell *secondaryHeightCell;///< 用于计算高度
@end

@implementation TTQTopicDetailCommentAdapter
- (instancetype)initWithViewModel:(TTQTopicBaseViewModel *)viewModel{
    self = [super initWithViewModel:viewModel];
    if (self) {
        if (![viewModel isKindOfClass:TTQTopicViewModel.class]) {
            NSAssert(NO, @"viewModel must be TTQTopicViewModel");
        }
        self.viewModel = viewModel;
    }
    return self;
}

- (void)setupWithAdapterModule:(IMYTableViewAdapterModule *)module {
    [super setupWithAdapterModule:module];
    self.module = module;
    self.module.key = @"comment";
    NSArray<Class> *cellClasses = @[TTQPrimaryCommentCell.class,
                                    TTQSecondaryCommentCell.class,
                                    TTQTopicScreenPlaceholderCell.class,
                                    TTQDetailCommentLoadingCell.class,
                                    TTQTopicCommentEmptyCell.class];
    for (Class cellClass in cellClasses) {
        [module registerClass:cellClass forCellReuseIdentifier:NSStringFromClass(cellClass)];
    }
    // 注册高亮cell
    [module registerClass:TTQPrimaryCommentCell.class forCellReuseIdentifier:@"TTQPrimaryCommentCell_highlight"];
    [module registerClass:TTQSecondaryCommentCell.class forCellReuseIdentifier:@"TTQSecondaryCommentCell_highlight"];
}


//MARK: - public methods

/// 更新评论条数
/// - Parameter count: 评论条数
- (void)updateCommentCount:(NSUInteger)count {
    [self.sectionHeaderView updateCommentCount:count];
}

- (void)updateSort:(BOOL)isHot {
    [self.sectionHeaderView updateSort:isHot];
}

- (void)finishedRequest:(NSError *)error isRefresh:(BOOL)isRefresh {
    [super finishedRequest:error isRefresh:isRefresh];

    [self.sectionHeaderView updateHeaderView];
}

//MARK: - private methods (reply)
/// 处理点击回复
/// - Parameters:
///   - indexPath: IndexPath
///   - isCommentBtn:是否是评论按钮点击
- (void)handleDidSelectRowAtIndexPath:(NSIndexPath *)indexPath isCommentBtn:(BOOL)isCommentBtn{
    if (indexPath == nil) {
        return;
    }
    TTQCommentModel *model = [self.viewModel tableCellModelAtIndexPath:indexPath];
    if ([model isKindOfClass:TTQCommentModel.class]) {
        if (self.viewModel.topic.is_close_comment) {/// 关闭评论了
            return;
        }
        if (indexPath.row == 0) {
            // 主评论点击
            [self replyActionAtIndexPath:indexPath];
            [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqypl_plqpl",@"action":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topicUserID),@"mediaType":@(self.viewModel.isUGCUIStlye?3:0)} headers:nil completed:nil];
            NSInteger clickpos = isCommentBtn ? 6 : 32;
            [self postCommentBiFeedsView:3 commentId:model.commentID floor:indexPath.section clickpos:clickpos];
        } else {
            [self replyActionAtIndexPath:indexPath];
            NSInteger clickpos = isCommentBtn ? 6 : 33;
            [self postCommentBiFeedsView:3 commentId:model.commentID floor:indexPath.row clickpos:clickpos];
        }
        
    } else if ([model isKindOfClass:TTQEmptyCommentModel.class]) {
        if (self.viewModel.topic.is_close_comment) {
            return;
        }
        // 空白视图点击事件
        [self replyActionAtIndexPath:nil];
        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_wpldj",@"action":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topicUserID)} headers:nil completed:nil];
    }
}


//MARK: - private methods (Long Press) override
- (NSArray<NSString *> *)longPressMenuTitlesAtIndexPath:(NSIndexPath *)indexPath {
    NSArray *titles = [super longPressMenuTitlesAtIndexPath:indexPath];
    
    UITableViewCell *cell = [self.module cellForRowAtIndexPath:indexPath];
    TTQCommentModel *comment = [self.viewModel tableCellModelAtIndexPath:indexPath];
    NSAssert(cell, @"cell must not be nil");
    NSAssert(comment, @"comment must not be nil");
    
    if ([IMYPublicAppHelper shareAppHelper].hasLogin
        && self.viewModel.topic.publisher.userID == [IMYPublicAppHelper shareAppHelper].userid.integerValue) {
        NSMutableArray *ownerActions = [NSMutableArray arrayWithArray:titles];
        if ([cell isKindOfClass:TTQPrimaryCommentCell.class]) {
            BOOL isAuthorTop = NO;
            if (comment.pin_type == TTQCommonPinTypeAuthor) {
                isAuthorTop = YES;
            }
            [ownerActions insertObject:isAuthorTop?IMYString(@"取消置顶"):IMYString(@"置顶") atIndex:0];
        }
        if (![ownerActions.lastObject isEqualToString:IMYString(@"删除")]) {
            [ownerActions addObject:IMYString(@"删除")];
        }
        titles = ownerActions;
    }
    return [titles copy];
}

//MARK: - private methods (Cells)
- (void)setupPrimaryCommentCell:(TTQPrimaryCommentCell *)cell atIndexPath:(NSIndexPath *)indexPath{
    TTQCommentModel *model = [self.viewModel tableCellModelAtIndexPath:indexPath];
    @weakify(self,model, indexPath);
    cell.contentClickedOnLinkBlk = ^(IMYRM80AttributedLabel * _Nonnull label, IMYRM80AttributedLabelURL * _Nonnull linkData) {
        @strongify(self);
        !self.contentClickedOnLinkBlk?:self.contentClickedOnLinkBlk(label, linkData);
    };
    cell.contentLongedOnLinkBlk = ^(IMYRM80AttributedLabel * _Nonnull label, IMYRM80AttributedLabelURL * _Nonnull linkData) {
        @strongify(self);
        !self.contentLongedOnLinkBlk?:self.contentLongedOnLinkBlk(label, linkData);
    };
    cell.reloadCellBlk = ^(NSIndexPath * _Nonnull indexPath) {
        @strongify(self);
        TTQCommentModel *model = [self.viewModel tableCellModelAtIndexPath:indexPath];
        if (model.isOpen) {
            [self postCommentBiFeedsView:3 commentId:model.commentID floor:indexPath.section clickpos:54];
        }
        [self.module reloadData];
    };
    cell.removeObjctBlk = ^(TTQCommentModel * _Nonnull commentModel, NSIndexPath * _Nonnull indexPath) {
      @strongify(self);
        [self.viewModel removeObject:commentModel atIndexPath:indexPath];
    };
    cell.praiseButtonBlk = ^(NSIndexPath * _Nonnull indexPath, BOOL isPraise) {
        @strongify(self,model);
        [self.viewModel handleCommentPraiseWithModel:model isPraise:isPraise completionBlk:^(id <IMYHTTPResponse>resData, NSError *error) {
            if (isPraise && !error) {
                [TTQCommonHelp GAEventForEventWith:@{@"action": @(2), @"topic_id": @(self.viewModel.topic.topic_id), @"event": @"tzxqy_pldz"}];
            }
        }];
    };
    cell.commentButtonBlk = ^(NSIndexPath * _Nonnull indexPath) {
        @strongify(self);
        [self handleDidSelectRowAtIndexPath:indexPath isCommentBtn:YES];
    };
    cell.imageDidClickBlk = ^(TTQCommentModel * _Nonnull commentData, NSMutableArray * _Nonnull photos, NSUInteger index) {
        @strongify(self);
        !self.showImageBrowserBlk?:self.showImageBrowserBlk(photos, index);
        [self postCommentBiFeedsView:2 commentId:commentData.commentID floor:indexPath.section clickpos:18];
        // 埋点:帖子详情页_评论图片
        [TTQCommonHelp GAEventForEventWith:@{@"action": @(2),
                                             @"topic_id": @(self.viewModel.topic_id),
                                             @"comment_id":@(commentData.commentID),
                                             @"event": @"tzxqy_pltp"}];
    };
    cell.longPressActionBlk = ^(UIGestureRecognizer * _Nonnull sender) {
        @strongify(self);
        [self handleLongPressAction:sender];
    };
    
    cell.contentExposuredBlk = ^(id  _Nonnull uPlusData) {
        @strongify(self);
        [self youjiaShowExportWithYouPlusModel:uPlusData dataDic:@{@"public_info": @2}];
    };
    NSInteger commentId = ((TTQCommentModel *)model).commentID;
    cell.biFeedsView.imyut_eventInfo.eventName = [NSString stringWithFormat:@"ttq_comment_feeds_%ld",commentId];
    cell.biFeedsView.imyut_eventInfo.edgeOffset = UIEdgeInsetsMake(0, 0, 50+SCREEN_TABBAR_SAFEBOTTOM_MARGIN, 0);
    [cell.biFeedsView.imyut_eventInfo setExposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
        @strongify(self,model, indexPath);
        IMYMixHomeUserBadge *badge = [model.publisher.badges bk_match:^BOOL(IMYMixHomeUserBadge * _Nonnull element) {
            return [element.type isEqualToString:@"badge"];
        }];
        [self postCommentBiFeedsView:1 commentId:model.commentID floor:indexPath.section clickpos:32 publicType:badge.badgeID ];
    }];
    
    cell.biBadgeOnClickedBlk = ^(NSString *badgeID, BOOL result) {
        @strongify(self, indexPath);
        TTQCommentModel *model = [self.viewModel tableCellModelAtIndexPath:indexPath];
        [self postCommentBiFeedsView:2 commentId:model.commentID floor:indexPath.section clickpos:61 publicType:badgeID];
    };
    
    //首条曝光事件
    if (indexPath.section == 1 && indexPath.row == 0) {
        cell.imyut_eventInfo.eventName = [NSString stringWithFormat:@"ttq_detail_comment_%@", @(model.commentID)];
        cell.imyut_eventInfo.eventValue = @{@"topicId": @(model.topic_id), @"commentId": @(model.commentID)};
        [cell.imyut_eventInfo setExposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
            // 评论区第一条曝光
            @strongify(self);
            NSMutableDictionary *tmpDic = [[NSMutableDictionary alloc] init];
            tmpDic[@"info_type"] = @(12);
            tmpDic[@"action"] = @(1);
            tmpDic[@"info_id"] = @(self.viewModel.topic.topic_id);
            tmpDic[@"event"] = @"ttq_nrxqy_plq";
            [IMYGAEventHelper postWithPath:@"event" params:[tmpDic copy] headers:nil completed:nil];
        }];
    }
}

- (void)setupSecondaryCommentCell:(TTQSecondaryCommentCell *)cell atIndexPath:(NSIndexPath *)indexPath{
    TTQCommentModel *model = [self.viewModel tableCellModelAtIndexPath:indexPath];
    NSIndexPath *mainCommentIndexPath = [NSIndexPath indexPathForRow:0 inSection:indexPath.section];/// 查找主评论
    TTQCommentModel *mainComment = [self.viewModel tableCellModelAtIndexPath:mainCommentIndexPath];
    TTQCommentModel *subComment = (TTQCommentModel *)model;
    cell.biFeedsView.imyut_eventInfo.eventName = [NSString stringWithFormat:@"ttq_comment_feeds_sub_%ld",subComment.commentID];
    cell.biFeedsView.imyut_eventInfo.showRadius = 1;
    cell.biFeedsView.imyut_eventInfo.edgeOffset = UIEdgeInsetsMake(0, 0, 50+SCREEN_TABBAR_SAFEBOTTOM_MARGIN, 0);
    @weakify(self, indexPath, subComment);
    [cell.biFeedsView.imyut_eventInfo setExposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
        @strongify(self, subComment, indexPath);
        IMYMixHomeUserBadge *badge = [subComment.publisher.badges bk_match:^BOOL(IMYMixHomeUserBadge * _Nonnull element) {
            return [element.type isEqualToString:@"badge"];
        }];
        [self postCommentBiFeedsView:1 commentId:subComment.commentID floor:indexPath.row clickpos:33 publicType:badge.badgeID];
    }];
    cell.contentClickedOnLinkBlk = ^(IMYRM80AttributedLabel * _Nonnull label, IMYRM80AttributedLabelURL * _Nonnull linkData) {
        @strongify(self);
        [self m80AttributedLabel:label clickedOnLink:linkData];
    };
    
    cell.reloadCellBlk = ^(NSIndexPath * _Nonnull indexPath) {
        @strongify(self);
        TTQCommentModel *model = [self.viewModel tableCellModelAtIndexPath:indexPath];
        if (model.isOpen) {
            [self postCommentBiFeedsView:3 commentId:model.commentID floor:indexPath.row clickpos:54];
        }
        [self.module reloadData];
    };
    cell.commentButtonBlk = ^(NSIndexPath * _Nonnull indexPath) {
        @strongify(self);
        [self handleDidSelectRowAtIndexPath:indexPath isCommentBtn:YES];
    };
    cell.praiseButtonBlk = ^(NSIndexPath * _Nonnull indexPath, BOOL isPraise) {
        @strongify(self, subComment);
        [self.viewModel handleCommentPraiseWithModel:subComment isPraise:isPraise completionBlk:nil];
    };
    @weakify(mainComment);
    cell.loadingMoreBlock = ^{
        //加载更多子评论
        @strongify(self, indexPath,mainComment,subComment);
        if (mainComment.isMoreCommentsLoading) {
            return ;
        }
        
        [self postCommentBiFeedsView:3 commentId:subComment.commentID floor:indexPath.row clickpos:58];
        mainComment.isMoreCommentsLoading = YES;
        [[[self.viewModel requestSecondaryCommentsWithIndexPath:indexPath count:5] deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
            @strongify(self,mainComment);
            mainComment.isMoreCommentsLoading = NO;
            subComment.cellHeight = 0; // 重新计算高度
            [self.module reloadData];
        } error:^(NSError * _Nullable error) {
            @strongify(self,subComment, mainComment);
            mainComment.isMoreCommentsLoading = NO;
            subComment.cellHeight = 0; // 重新计算高度
            [self.module reloadData];
        }];
        
    };
    cell.longPressActionBlk = ^(UIGestureRecognizer * _Nonnull sender) {
        @strongify(self);
        [self handleLongPressAction:sender];
    };
    cell.imageDidClickBlk = ^(TTQCommentModel * _Nonnull model, NSMutableArray * _Nonnull photos, NSUInteger index) {
        !self.showImageBrowserBlk?:self.showImageBrowserBlk(photos, index);
        [self postCommentBiFeedsView:2 commentId:model.commentID floor:indexPath.section clickpos:18];
        // 埋点:帖子详情页_评论图片
        [TTQCommonHelp GAEventForEventWith:@{@"action": @(2),
                                             @"topic_id": @(self.viewModel.topic_id),
                                             @"comment_id":@(model.commentID),
                                             @"event": @"tzxqy_pltp"}];
    };
    cell.biBadgeOnClickedBlk = ^(NSString *badgeID, BOOL result) {
        @strongify(self, indexPath);
        TTQCommentModel *model = [self.viewModel tableCellModelAtIndexPath:indexPath];
        [self postCommentBiFeedsView:2 commentId:model.commentID floor:indexPath.section clickpos:61 publicType:badgeID];
    };
}


- (UITableViewCell *)fetchTableViewCellAtIndexPath:(NSIndexPath *)indexPath{
    NSString *identifier = [self.viewModel identifierRowAtIndexPath:indexPath];
    NSAssert(imy_isNotEmptyString(identifier), @"Cell identifier must be string");
    if (imy_isEmptyString(identifier)) {
        identifier = NSStringFromClass([TTQTopicScreenPlaceholderCell class]);
    }
    UITableViewCell *cell = [self.module dequeueReusableCellWithIdentifier:identifier];
    if (!cell) {
        NSAssert(NO, @"cell identifier must be registered");
        cell = (UITableViewCell<ReactiveTableViewCell> *)[[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:identifier];
    }
    return cell;
}

- (void)m80AttributedLabel:(IMYRM80AttributedLabel *)label clickedOnLink:(IMYRM80AttributedLabelURL *)linkURL {
    NSString *copyString = linkURL.linkData;
    if (copyString && ![copyString isKindOfClass:NSString.class]) {
        !self.contentClickedOnLinkBlk?:self.contentClickedOnLinkBlk(label, linkURL);
        return ;
    }
    if ([copyString isEqualToString:@"to"] || [copyString isEqualToString:@"to-reply"]) {
        TTQCommentBaseCell *cell = [label imy_findParentViewWithClass:[TTQCommentBaseCell class]];
        NSIndexPath *indexPath = [self.tableView indexPathForCell:cell];
        TTQCommentModel *model = [self.viewModel tableCellModelAtIndexPath:indexPath];
        
        if (model.replygoal.userID > 0 && model.replygoal.error == 0) {
            IMYURI *uri = [IMYURI uriWithPath:@"user/dynamic"
                                       params:@{ @"userID": @(model.replygoal.userID)
                                                 ,@"source": @"话题详情"}
                                         info:nil];
            [[IMYURIManager shareURIManager] runActionWithURI:uri];
            NSIndexPath *indexPath = [self.tableView indexPathForCell:cell];
            [self postCommentBiFeedsView:2 commentId:model.commentID floor:indexPath.row + 1 clickpos:1];
        } else if (model.replygoal.error == 1) {
            [UIView imy_showTextHUD:kStatusText_UserAnonymous];
        } else if (model.replygoal.error == 2) {
            [UIView imy_showTextHUD:kStatusText_homePageNotOpen];
        }
    } else {
        !self.contentClickedOnLinkBlk?:self.contentClickedOnLinkBlk(label, linkURL);
    }
}

//MARK: - IMYAdapterModuleTableViewDelegate
- (CGFloat)tableView:(IMYTableViewAdapterModule *)module heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    id model = [self.viewModel tableCellModelAtIndexPath:indexPath];
    if (!(self.tableView.tableFooterView && !self.tableView.tableFooterView.hidden) && [model isKindOfClass:[TTQEmptyCommentModel class]]) {
        if (self.viewModel.topic.is_close_comment) {/// 禁止评论
            return [TTQTopicCommentEmptyCell disableCommentCellHeight];
        } else {
            return [TTQTopicCommentEmptyCell cellHeight];
        }
    }else if([model isKindOfClass:TTQCommentModel.class]){
        TTQCommentModel  *model = [self.viewModel tableCellModelAtIndexPath:indexPath];
        if (self.viewModel.keepRowsWhenDeleteComment && model.isDeletedLocal) {
            return CGFLOAT_MIN; // 删除的评论不显示
        }else if (indexPath.row == 0) {//主评论
            if ([self.primaryHeightCell respondsToSelector:@selector(bindModel:heightForRowAtIndexPath:viewModel:)]) {
                [self.primaryHeightCell bindModel:model heightForRowAtIndexPath:indexPath viewModel:self.viewModel];
            }
            return model.cellHeight;
        }else {//子评论
            if ([self.secondaryHeightCell respondsToSelector:@selector(bindModel:heightForRowAtIndexPath:viewModel:)]) {
                [self.secondaryHeightCell bindModel:model heightForRowAtIndexPath:indexPath viewModel:self.viewModel];
            }
            return model.cellHeight;
        }
    } else if ([model isKindOfClass:NSString.class] && [model isEqualToString:@"loading"]) {
        /// loading cell 撑满导航栏和底部之外的区域，保证切换的时候，内容不会位移
        return SCREEN_HEIGHT - SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT - 44 - 52 - SCREEN_TABBAR_SAFEBOTTOM_MARGIN;
    } else if ([model isKindOfClass:NSString.class] && [model isEqualToString:@"request_failed_cell"]){
        return 223;
    } else {
        return CGFLOAT_MIN;
    }
}

- (UITableViewCell *)tableView:(IMYTableViewAdapterModule *)module cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    UITableViewCell<ReactiveTableViewCell> *cell = [self fetchTableViewCellAtIndexPath:indexPath];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    if ([cell respondsToSelector:@selector(bindModel:cellForRowAtIndexPath:viewModel:)]) {
        id model = [self.viewModel tableCellModelAtIndexPath:indexPath];
        [cell bindModel:model cellForRowAtIndexPath:indexPath viewModel:self.viewModel];
    }
    @weakify(self);
    if ([cell isKindOfClass:TTQPrimaryCommentCell.class]) {//主评论
        [self setupPrimaryCommentCell:cell atIndexPath:indexPath];
    }else if ([cell isKindOfClass:TTQSecondaryCommentCell.class]) {//子评论
        [self setupSecondaryCommentCell:cell atIndexPath:indexPath];
    }else if ([cell isKindOfClass:TTQTopicCommentEmptyCell.class]){
        ((TTQTopicCommentEmptyCell *)cell).tapActionBlock = ^{
            @strongify(self);
            [self replyActionAtIndexPath:nil];
            NSDictionary *params = @{@"event":@"dsq_nrxqy_wpldj",
                                     @"action":@2,
                                     @"info_type":@12,
                                     @"info_id":@(self.viewModel.topic_id),
                                     @"fuid":@(self.viewModel.topicUserID)};
            [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
        };
    }else if ([cell isKindOfClass:TTQDetailCommentLoadingCell.class]) {
        id model = [self.viewModel tableCellModelAtIndexPath:indexPath];
        if ([model isKindOfClass:NSString.class]
            && [((NSString *)model) isEqualToString:@"request_failed_cell"]) {// 请求失败，且无缓存数据
            ((TTQDetailCommentLoadingCell *)cell).state = TTQDetailCommentLoadingCellStateRetry;
            @weakify(self);
            [((TTQDetailCommentLoadingCell *)cell) setRetryBlockHandler:^(void (^changeCaptionViewStateBlock)(IMYCaptionViewState state)) {
                @strongify(self);
                if ([IMYNetState networkEnable]) {
                    !self.changeOrderFilterBlk?:self.changeOrderFilterBlk(self.viewModel.orderByFilter, NO);
                } else {
                    changeCaptionViewStateBlock(IMYCaptionViewStateRetry);
                    [UIWindow imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
                }
            }];
        } else {// 请求加载中
            ((TTQDetailCommentLoadingCell *)cell).state = TTQDetailCommentLoadingCellStateLoading;
            [((TTQDetailCommentLoadingCell *)cell) setSingleLineLoading:self.tableView.tableFooterView && !self.tableView.tableFooterView.hidden];
        }
    }
    return cell;
}

- (void)tableView:(IMYTableViewAdapterModule *)module didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [module deselectRowAtIndexPath:indexPath animated:NO];
    [self handleDidSelectRowAtIndexPath:indexPath isCommentBtn:NO];
}

- (CGFloat)tableView:(IMYTableViewAdapterModule *)module heightForHeaderInSection:(NSInteger)section{
    if (section == 0) {
        [self.sectionHeaderView updateHeaderView];
        return [self.sectionHeaderView viewHeight:!!self.tableView.tableHeaderView];
    }
    return 0;
}

/// custom view for header. will be adjusted to default or specified header height
- (nullable UIView *)tableView:(IMYTableViewAdapterModule *)module viewForHeaderInSection:(NSInteger)section{
    if (section == 0) {
        return self.sectionHeaderView;
    }
    return nil;
}

- (CGFloat)tableView:(IMYTableViewAdapterModule *)module heightForFooterInSection:(NSInteger)section{
    id model = [self.viewModel tableCellModelAtIndexPath:[NSIndexPath indexPathForRow:0 inSection:section]];
    if (self.viewModel.keepRowsWhenDeleteComment
        && [model isKindOfClass:TTQCommentModel.class]
        && ((TTQCommentModel *)model).isDeletedLocal) {
        return CGFLOAT_MIN; // 删除的评论不显示
    }
    return section > 0 ? 8 : CGFLOAT_MIN;
}

- (nullable UIView *)tableView:(IMYTableViewAdapterModule *)module viewForFooterInSection:(NSInteger)section{
    UIView *view = [UIView new];
    view.backgroundColor = [UIColor clearColor];
    if (section > 0) {//历史原因，section0 的 cell是空占位
        view.frame = CGRectMake(0, 0, SCREEN_WIDTH, 12);
    }else{
        view.frame = CGRectMake(0, 0, SCREEN_WIDTH, CGFLOAT_MIN);
    }
    return view;
}


//MARK: - Bi Event
/// 后期BI的entrance, 首页过来是14，她她圈是2
- (NSInteger)biFeedsEntrance {
    NSInteger entrance = [self.fromURI.params[@"entrance"] integerValue];
    return entrance;
}

- (void)postCommentBiFeedsView:(NSInteger)action commentId:(NSInteger)commentId floor:(NSInteger)floor clickpos:(NSInteger)clickpos {
    [self postCommentBiFeedsView:action commentId:commentId floor:floor clickpos:clickpos publicType:nil];
}
- (void)postCommentBiFeedsView:(NSInteger)action commentId:(NSInteger)commentId floor:(NSInteger)floor clickpos:(NSInteger)clickpos publicType:(nullable NSString *)publicType {
    NSInteger entrance = [self biFeedsEntrance];
    
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"action"] = @(action);
    params[@"entrance"] = @(entrance);
    params[@"position"] = @141;
    params[@"channel_id"] = self.viewModel.orderByFilter == TTQOrderByFilterLastest ?@2:@1;
    params[@"clickpos_feedscard"] = @(clickpos);
    params[@"info_type"] = @63;
    params[@"info_id"] = @(commentId);
    params[@"floor"] = @(floor);
    params[@"data_id"] = @(self.viewModel.topic_id);
    if (imy_isNotEmptyString(publicType)) {
        params[@"public_type"] = publicType;
    }
    if (floor == 0) {
        // 没匹配到，异常了
        return;
    }
    [IMYGAEventHelper postWithPath:@"bi_feeds_view" params:params headers:nil completed:nil];
    
}

//MARK: - BI Event(YouPlus)
- (void)youjiaShowExportWithYouPlusModel:(TTQTopicRichParserYouPlusModel *)youPlusModel dataDic:(NSDictionary *)dataDic{
    if (youPlusModel == nil || ![youPlusModel isKindOfClass:[TTQTopicRichParserYouPlusModel class]]) {
        return;
    }
    //  public_info  1文章， 2评论， 3底部插件， 4底部插件ICON
    NSNumber *publicInfo = @2;
    if (dataDic[@"public_info"]) {
        publicInfo = dataDic[@"public_info"];
    }
    // 柚+曝光事件
    TTQTopicRichParserYouPlusModel *model = youPlusModel;
    NSString *youplus_id = model.youplus_id ?: @"";
    NSDictionary *dic = @{
        @"addin_id":model.youplus_id?:@"",
        @"public_type":@(model.type),
        @"e_user_id":model.e_user_id?:@"",
        @"action":@(1),
        @"public_info":publicInfo,
        @"isAdPlugin":@(model.isAdPlugin)
    };
    if (model.type == 1) {
        NSMutableDictionary *mutDic = [[NSMutableDictionary alloc] initWithDictionary:dic];
        mutDic[@"wx_account_type"] = @(model.wx_account_type);
        mutDic[@"wx_account_wechat"] = model.wx_account_wechat;
        dic = [mutDic copy];
    }
    [self postYouPlusBIData:dic];
}
- (void)postYouPlusBIData:(NSDictionary *)paramsDic {
    BOOL isAdPlugin = [paramsDic[@"isAdPlugin"] boolValue];
    if (isAdPlugin) {
        NSMutableDictionary *tmpDic = [[NSMutableDictionary alloc] init];
        tmpDic[@"event"] = @"sq_tzxqy_ggcj";
        tmpDic[@"action"] = [paramsDic objectForKey:@"action"];
        tmpDic[@"info_type"] = @(12);
        tmpDic[@"info_id"] = @(self.viewModel.topic.topic_id);
        [IMYGAEventHelper postWithPath:@"event" params:[tmpDic copy] headers:nil completed:nil];
    } else {
        NSMutableDictionary *tmpDic = [[NSMutableDictionary alloc] initWithDictionary:paramsDic];
        tmpDic[@"info_type"] = @(12);
        tmpDic[@"info_id"] = @(self.viewModel.topic.topic_id);
        tmpDic[@"event"] = @"dsq_nrxqy_ggcj";
        [tmpDic removeObjectForKey:@"isAdPlugin"];
        tmpDic[@"al_source"] = self.fromURI.params[@"al_source"] ? self.fromURI.params[@"al_source"] : @"";
        [tmpDic imy_setNonNilObject:self.viewModel.global_track_id ? : self.fromURI.params[@"global_track_id"] forKey:@"global_track_id"];
        [tmpDic imy_setNonNilObject:self.viewModel.ad_id ? : self.fromURI.params[@"ad_id"] forKey:@"ad_id"];
        [IMYGAEventHelper postWithPath:@"event" params:[tmpDic copy] headers:nil completed:nil];
    }
}

//MARK: - Setter & Getter

- (TTQTopicCommentSectionHeader *)sectionHeaderView{
    if (!_sectionHeaderView) {
        TTQTopicCommentSectionHeader *view = [[TTQTopicCommentSectionHeader alloc] initWithFrame:CGRectMake(0, 10, SCREEN_WIDTH, 44) viewModel:self.viewModel biFeedsEntrance:[self biFeedsEntrance]];
        @weakify(self);
        view.hideKeyboardBlk = ^{
            @strongify(self);
            !self.hideKeyboardBlk?:self.hideKeyboardBlk();
        };
        view.changeOrderFilterBlk = ^(TTQTopicFilter topicFilter, BOOL shouldLocateToComment) {
            @strongify(self);
            !self.changeOrderFilterBlk?:self.changeOrderFilterBlk(topicFilter, shouldLocateToComment);
        };
        _sectionHeaderView = view;
    }
    return _sectionHeaderView;
}

- (TTQPrimaryCommentCell *)primaryHeightCell{
    if (!_primaryHeightCell) {
        _primaryHeightCell = [[TTQPrimaryCommentCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"primaryHeightCell"];
    }
    return _primaryHeightCell;
}

- (TTQSecondaryCommentCell *)secondaryHeightCell{
    if (!_secondaryHeightCell) {
        _secondaryHeightCell = [[TTQSecondaryCommentCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"secondaryHeightCell"];
    }
    return _secondaryHeightCell;
}

@end
