//
//  TTQTopicDetailViewControllerV2.m
//  IMYTTQ
//
//  Created by 林云峰 on 2025/5/28.
//

#import "TTQTopicDetailViewControllerV2.h"
#import "IMYContentDetailBaseViewController+Share.h"
#import "IMYContentDetailBaseViewController+AD.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import "TTQTopicDetailForumView.h"
#import "TTQTopicDetailHeadView.h"
#import <IMYUGC/IMYUGCTopicDetailStatusBar.h>
#import "TTQTopicDetailHeaderViewAdapter.h"
#import <IMYUGC/IMYUGCEventHelper.h>
#import "TTQTopics848ViewController.h"
#import "TTQJumpType.h"
#import "TTQSegmentModel.h"
#import "TTQTopicDetailCommentAdapter.h"
#import "TTQABTestConfig.h"
#import <IMYUGC/IMYRewardAlertView.h>
#import "TTQCommentContentCacheManager.h"
#import "IMYCKFollowButton.h"
#import "TTQDetailHelper.h"
#import "IMYHugTipsView.h"
#import <IMYUGC/IMYUGC.h>
#import "TTQCheckService.h"
#import "TTQHome5TopicModel.h"
#import "TTQNewbeeTaskManager.h"
#import <IMYUGC/IMYUGCImageObject.h>
#import <IMYBaseKit/IMYVKStickerManager.h>
#if __has_include(<IMYEBPublic/IMYEBYoubiTaskManager.h>)
#import "IMYEBYoubiTaskManager.h"
#endif
#import <IMYAccount/IMYAccountServerURL.h>
#import "TTQMessageDetailViewModel.h"
#import <IMYBaseKit/IMYCoolShareSheet.h>
#import "TTQPublishTopicViewModel.h"
#import "IMYUGCFeedBackView.h"
#import "TTQRefreshBackNormalFooter.h"
#import "IMYContentDetailBaseViewController+VideoPlay.h"
#import "IMYUGCTaskManager.h"
#import <IMYRM80AttributedLabelURL.h>
#import <IMYUGC/IMYAdYoujMarketTipsView.h>
#import <IMYAdvertisement/IMYSideAdManager.h>
#import <IMYAdvertisement/IMYAdABManager.h>
#import <IMYAdvertisementSDK.h>
#import "IMYVideoView.h"
#import "IMYVideoView+TTQVideoPlayUtil.h"
#import "TTQVideoPlayerManager.h"
#import <IMYVideoPlayer.h>
#import <IMYVideoPlayer/IMYVPlayerView+HL.h>
#import "TTQDetailVideoWraperView.h"
#import <Poseidon/UIViewController+Poseidon.h>
#import <Poseidon/_PSDUIPageElement.h>

@interface TTQTopicDetailViewControllerV2 () <UIScrollViewDelegate>

@property (nonatomic, strong) TTQTopicDetailForumView *forumView;
// 切换Tab/排序处理定位评论的标记位置
@property (nonatomic, assign) BOOL shouldHandleLocateToComment;
/// 帖子状态异常status bar
@property (nonatomic, strong) IMYUGCTopicDetailStatusBar *topicStatusTopView;
/// 抱抱引导
@property (nonatomic, strong) IMYHugTipsView *hugTipsView;
/// 是否可以展示抱抱引导
@property (nonatomic, assign) BOOL shouldShowHugTips;

@property (nonatomic, assign) NSInteger lastTimeNetState; //上一次的移动网络状态 0.wifi 1.移动网络

/// 柚加引导
@property (nonatomic, strong) IMYAdYoujMarketTipsView *youjiaTipsView;

@property (nonatomic, assign) BOOL isYjPlugShow;

// MARK: - bi埋点参数
@property (nonatomic, assign) NSTimeInterval bi_startDuration; ///< 开始时间，记录每次播放开始时所处的时间
@property (nonatomic, assign) NSTimeInterval bi_endDuration;   ///< 终止时间，记录每次播放终止时所处的时间
/**
开始类型：1 手动点击播放  2：手动重新播放  3 恢复播放（包含：返回视频位置自动恢复播放，4g切换到wifi自动恢复播放）4 播放完成后自动循环播放 5 流里自动播放  6 进入详情页自动播放
 */
@property (nonatomic, assign) NSInteger bi_startType;
/**
 终止类型（1是播放完成，2是暂停（含退出/切换））
 */
@property (nonatomic, assign) NSInteger bi_endType;
@property (nonatomic, assign) BOOL firstEnterDetail; //首次进去详情页
@property (nonatomic, assign) BOOL firstChangeVideoState; //首次改变播放状态
@property (nonatomic, assign) NSTimeInterval enterPageTime;
@property (nonatomic, strong) NSPointerArray *weakExtraVideoViews;
@property (nonatomic, assign) BOOL canReportInputViewChange;
@end

@implementation TTQTopicDetailViewControllerV2

- (void)bindViewModel {
    @weakify(self);
    [[[[NSNotificationCenter defaultCenter] rac_addObserverForName:@"TTQDetailControllerRefresh" object:nil] deliverOnMainThread] subscribeNext:^(NSNotification *note) {
        @strongify(self);
        [self.tableView imy_headerBeginRefreshing];
    }];
    
    void (^replyImageBlock)(id x) = ^(id x) {
        @strongify(self);
        [self setupCameraButtonVisiable];
    };
    [[RACObserve(self.viewModel, forum) deliverOnMainThread] subscribeNext:^(id x) {
        replyImageBlock(x);
    }];
    RACSignal *viewtopicSignal = RACObserve(self.viewModel, topic);
    [[viewtopicSignal deliverOnMainThread] subscribeNext:^(id x) {
        replyImageBlock(x);
        @strongify(self);
        
    }];
    [[[viewtopicSignal distinctUntilChanged] deliverOnMainThread] subscribeNext:^(TTQTopicModel *topic) {
        @strongify(self);
        if (topic) {
            NSArray *viewControllers = nil;
            if (self.forumView == nil
                && !self.viewModel.isUGCUIStlye// 短图文样式不展示圈子入口
                && topic.category != TTQTopicCategoryCaseHistory// 病例帖不展示圈子入口
                && imy_isNotEmptyString(topic.forum_name)) {
                BOOL isFromCircleSearch = NO;
                BOOL isFromCircleDetail = NO; // 从新圈详情页进入不展示圈头部推荐
                UIViewController *viewVC = self.imy_currentShowViewController;
                if (viewVC) {
                    if ([viewVC isKindOfClass:UITabBarController.class]) {
                        UITabBarController *tabController = ((UITabBarController*)viewVC);
                        if ([tabController.selectedViewController isKindOfClass:UINavigationController.class]) {
                            viewControllers = ((UINavigationController*)tabController.selectedViewController).viewControllers;
                        }
                        
                    } else if([viewVC isKindOfClass:UINavigationController.class]) {
                        UINavigationController *navController = ((UINavigationController*)viewVC);
                        viewControllers = navController.viewControllers;
                    } else {
                        viewControllers = viewVC.navigationController.viewControllers;
                    }
                } else {
                    viewControllers = self.navigationController.viewControllers;
                }
                TTQTopics848ViewController *beforVC = [viewControllers imy_objectAtIndex:viewControllers.count - 2];
                if (beforVC && [beforVC isKindOfClass:TTQTopics848ViewController.class] && beforVC.viewModel.forum_id == self.viewModel.forum_id) {
                    isFromCircleDetail = YES;
                }
                isFromCircleSearch = [viewControllers.firstObject isKindOfClass:NSClassFromString(@"SYSearchResultWithH5VC")];
                // 处理顶部圈子入口，满足以下的所有条件:
                // - 不是贴士精华圈(id=160)
                // - 不是技术测试圈(id=94)
                // - 不是从同一个圈子进入的
                if (topic.forum_id != 160
                    && topic.forum_id != 94
                    && !isFromCircleSearch
                    && !isFromCircleDetail) {
                    self.forumView = [TTQTopicDetailForumView imy_loadFromXIB];
                    [self updateForumView];
                    UITapGestureRecognizer *tapGestureRecognizer = [UITapGestureRecognizer new];
                    [self.forumView addGestureRecognizer:tapGestureRecognizer];
                    [[tapGestureRecognizer.rac_gestureSignal deliverOnMainThread] subscribeNext:^(id x) {
                        @strongify(self);
                        [self.view endEditing:YES];
                        [[IMYURIManager shareURIManager] runActionWithURI:[IMYURI uriWithPath:@"circles/group"
                                                                                       params:@{@"groupID": @(self.viewModel.forum_id)}
                                                                                         info:nil]];
                    }];
                };
            }
            
            if(imy_isEmptyString(topic.forum_name) && self.forumView != nil){
                [self.forumView removeFromSuperview];
                self.forumView = nil;
                TTQTopicDetailHeadView *headView = (TTQTopicDetailHeadView *)self.tableView.tableHeaderView;
                headView.forumHeightConstraint.constant = 0;
                headView.forumView.hidden = YES;
                [headView setNeedsUpdateConstraints];
            }
            
            if (self.forumView) {
                BOOL singleLine = topic.forum_name.length <= 8 || imy_isEmptyString(topic.guide_info);
                if (singleLine) {
                    self.forumView.imy_height = 44.0;
                    [self.forumView remakeConstraints:YES];
                } else {
                    self.forumView.imy_height = 62.0;
                    [self.forumView remakeConstraints:NO];
                }
                [self updateHeaderForumHeight];
                self.forumView.imy_width = SCREEN_WIDTH;
                self.forumView.topicLabel.text = topic.forum_name;
                self.forumView.guideLabel.text = topic.guide_info;
            }
            self.customNavigationBar.moreButton.enabled = YES;

            if (IOS9) { //spotlight
                IMYSpotlightItem *tipItem = [IMYSpotlightItem new];
                tipItem.title = topic.title?:@"";
                tipItem.content = topic.content?:@"";
                tipItem.domainIdentifier = @"kIMYTTQSpotlightDomainTopic";
                tipItem.uri = @"circles/group/topic";
                tipItem.icon = topic.publisher.user_avatar.large?:@"";
                tipItem.uriParams = @{@"topicID": @(topic.topic_id),
                                      @"source": @"Spotlight搜索"};
                if (topic.forum_name) {
                    tipItem.keywords = @[topic.forum_name?:@""];
                }
                tipItem.itemIdentifier = [NSString stringWithFormat:@"%lu_%@", topic.topic_id, topic.title];
                [[IMYSpotlightManager sharedManager] addSpotlightItems:@[tipItem] completionHandler:nil];
            }
        } else {
            self.customNavigationBar.moreButton.enabled = NO;

        }
    }];
    
    // 处理评论删除通知
    [[[[[NSNotificationCenter defaultCenter] rac_addObserverForName:TTQTopicReviewDidDeleteNotifition object:nil] takeUntil:self.rac_willDeallocSignal] deliverOnMainThread] subscribeNext:^(NSNotification *note) {
        @strongify(self);
        NSInteger commentID = [[note.object valueForKey:@"review_id"] integerValue];
        __block NSInteger deleteReviewCount = 0;
        self.viewModel.dataSource = [self.viewModel.dataSource bk_select:^BOOL(TTQCommentModel *model) {
            if ([model isKindOfClass:TTQCommentModel.class]) {
                if (self.viewModel.keepRowsWhenDeleteComment && model.commentID == commentID) {
                    /// 保留删除的评论
                    model.isDeletedLocal = YES;
                    return YES;
                }
                BOOL isDelelteComnent = model.commentID == commentID;
                if (isDelelteComnent) {
                    deleteReviewCount = 1 + model.referenced_num;
                }
                return !isDelelteComnent;
            } else {
                return YES;
            }
        }];
        /// 更新下评论数显示
        self.viewModel.topic.total_review = MAX(0, self.viewModel.topic.total_review - deleteReviewCount);
        [self.viewModel forceHandleAttachDatas];
        [self.tableViewAdapter reloadModules];
    }];
        
    [[[NSNotificationCenter defaultCenter] rac_addObserverForName:kTTQForumChangeNotification object:nil] subscribeNext:^(NSNotification *x) {
        @strongify(self);
        if ([x.object isKindOfClass:[TTQForumModel class]] && [(TTQForumModel *)x.object forum_id] == self.viewModel.forum_id) {
            self.viewModel.forum = x.object;
        }
    }];
    [[[[NSNotificationCenter defaultCenter] rac_addObserverForName:TTQTopicDetailChangeNotifition object:nil] deliverOnMainThread] subscribeNext:^(NSNotification *x) {
        @strongify(self);
        NSUInteger topicId = [[x.object valueForKey:@"topic_id"] unsignedIntegerValue];
        NSUInteger topic_status = [[x.object valueForKey:@"topic_status"] unsignedIntegerValue];
        if (topicId == self.viewModel.topic_id && topic_status == 2) {
            // 刷新数据源
            @strongify(self);
            [self.tableView setContentOffset:CGPointZero animated:NO];
            [self requestData:YES];
        }
    }];
    
    // 处理负反馈数据删除更新UI
    [[[RACObserve(self.viewModel, feedsSourceChange) skip:1] deliverOnMainThread]
     subscribeNext:^(id x) {
        @strongify(self);
        UIView<TTQTopicDetailHeaderViewProtocol> *headView = (UIView<TTQTopicDetailHeaderViewProtocol> *)self.tableView.tableHeaderView;
        [headView bindModel:self.viewModel.topic cellForRowAtIndexPath:nil viewModel:self.viewModel];
        [self.tableViewAdapter reloadModules];
    }];
    
    [[[RACObserve(self.viewModel, topic.total_review) deliverOnMainThread] distinctUntilChanged]
     subscribeNext:^(id x) {
        @strongify(self);
        [self.bottomBar.commentButton updateComment:[x integerValue]];
        if ([self.commentAdapter isKindOfClass:TTQTopicDetailCommentAdapter.class]) {
            [(TTQTopicDetailCommentAdapter *)self.commentAdapter updateCommentCount:[x integerValue]];
        }
    }];
}

- (void)loadCache:(void (^)(void))completeBlock {
    @weakify(self);
    [[[self.viewModel requestTopicCache] deliverOnMainThread] subscribeNext:^(TTQTopicCache *look) {
        @strongify(self);
        if (self.viewModel.gotoID > 0) {
            self.viewModel.orderByFilter = TTQOrderByFilterHot;
            self.viewModel.datasourceOrderFilter = TTQOrderByFilterHot;
        }
        if ([self.commentAdapter isKindOfClass:[TTQTopicDetailCommentAdapter class]]) {
            [(TTQTopicDetailCommentAdapter *)self.commentAdapter updateSort:!(self.viewModel.orderByFilter == TTQOrderByFilterLastest)];
        }
        
        // 处理缓存数据
        // 1、有缓存内容优先展示缓存，有网络请求网络。
        // 2、无缓存内容，有网络显示加载中状态，并且请求网络；无网络显示无网络页面。
        if (![self.viewModel isCurFilterDataSourceNil] || self.viewModel.topic) {
            self.viewModel.isUsingCacheData = YES;//
            [self finishedRequest:nil isRefresh:NO];
            self.viewModel.automaticallyRefresh = NO;// 正在使用缓存数据的时候，要禁用底部的加载更多
        }
        if (completeBlock) {
            completeBlock();
        }
    }];
}

- (void)viewDidLoad {
    
    self.viewModel.global_track_id = self.global_track_id ? : self.fromURI.params[@"global_track_id"];
    self.viewModel.ad_id = self.ad_id ? : self.fromURI.params[@"ad_id"];
    
    self.firstEnterDetail = YES;
    self.firstChangeVideoState = YES;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        self.firstEnterDetail = NO;
    });
    self.enterPageTime = [[NSDate date] timeIntervalSince1970];

    [super viewDidLoad];
    self.captionView.allTapToRetry = true;
    self.captionView.hideButtonUnlessRetry = YES;
//    [self.commentHeaderAdapter updateSortToHot:!(self.viewModel.orderByFilter == TTQOrderByFilterLastest)];
}

- (TTQPublisherModel *)contentPublisher {
    return self.viewModel.topic.publisher;
}

- (NSInteger)contentId {
    return self.viewModel.topic_id;
}

- (NSInteger)articleId {
    return self.viewModel.topic_id;
}

- (BOOL)isShortContent {
    return self.viewModel.isUGCUIStlye;
}

- (BOOL)isYouPlus {
    return self.viewModel.topic.is_you_plus;
}
#pragma mark - events

- (void)addEventNotifications {
    [super addEventNotifications];
    @weakify(self);
    [[[[[NSNotificationCenter defaultCenter] rac_addObserverForName:UIApplicationDidEnterBackgroundNotification object:nil] takeUntil:self.rac_willDeallocSignal] deliverOnMainThread] subscribeNext:^(id x) {
        // 停止视频播放
        [NSObject imy_asyncBlock:^{
            @strongify(self);
            [[self viewModel] saveOffsetCache:nil];
        } level:IMYQueueLevelBackground];
    }];
}

#pragma mark - life circle

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    NSNumber *lastTimeNetState = [[IMYUserDefaults standardUserDefaults]
                                  objectForKey:@"lastTimeNetState"];
    self.lastTimeNetState = lastTimeNetState.integerValue;
    [self.tableViewAdapter reloadModules];
    // 视频：开始播放
    imy_asyncMainBlock(0.5, ^{
        NSLog(@"]======");
        if (self.view.window && self.view.imy_inWindowVisible) {
            [self startPlayVideo];
        }
    });
    if (self.shouldShowHugTips) {
        [self showHugTipViewIfNeeded];
    }
    [IMYUGCTaskManager getUGCTaskRequest];
}

- (void)viewWillDisappear:(BOOL)animated {
    [[IMYUserDefaults standardUserDefaults] setObject:
     [NSNumber numberWithInteger:self.lastTimeNetState]
                                               forKey:@"lastTimeNetState"];
    [super viewWillDisappear:animated];
    if ([self imy_isPop]) {
        @weakify(self);
        if (self.viewModel.topic != nil && !self.viewModel.topic.is_favorite) {
            [[NSNotificationCenter defaultCenter] postNotificationName:TTQCollectTopicCancle object:@(self.viewModel.topic_id)];
        }
        [NSObject imy_asyncBlock:^{
            @strongify(self);
            [[self viewModel] saveOffsetCache:nil];
        } level:IMYQueueLevelBackground];
    }
    // 停止视频播放
    [self stopPlayVideo];
    //页面消失时把数据通知出去
    TTQTopicModel *topicModel = self.viewModel.topic;
    [[NSNotificationCenter defaultCenter] postNotificationName:kNewsDataRefreshNotification object:nil userInfo:@{@"type":@(IMYNewsItemTypeTTQ),@"itemId":@(topicModel.topic_id),@"comments":@(topicModel.total_review),@"isPraise":@(topicModel.has_praise),@"praise_num":@(topicModel.praise_num),@"userId":@(topicModel.publisher.userID),@"isFollow":topicModel.publisher.is_followed == 0?@NO:@YES,@"isFavority":@(self.viewModel.topic.is_favorite),@"attitude_id":@(topicModel.attitude_id),@"gotoId":@(self.viewModel.gotoID),@"gotoIdCommenetPraiseCount":@(self.viewModel.gotoIDCommenPraiseCount),@"gotoIdCommentIsPraised":@([self.viewModel isGotoIDCommentPraised]),@"isHug":@(self.viewModel.topic.hugged),@"hug_num":@(self.viewModel.topic.hug_num)}];
}

- (UIStatusBarStyle)preferredStatusBarStyle {
    return UIStatusBarStyleDefault;
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
}

- (void)dealloc {
    // 恢复 允许删除SD缓存
    [SDImageCache sharedImageCache].pauseClean = NO;
    [[IMYURIManager shareURIManager] runActionWithPath:@"push/checkPushSettings" params:@{@"position":@(IMYNABoxShowPosition_browseFeeds)} info:nil];
}


#pragma mark -

- (UIView<TTQTopicDetailHeaderViewProtocol> *)createContentHeaderView {
    UIView<TTQTopicDetailHeaderViewProtocol> *headerView = nil;

    if (self.viewModel.isUGCUIStlye) {
        headerView = [[TTQTopicDetailHeaderViewUGCAdapter alloc] init];
    } else {
        headerView = [[[NSBundle mainBundle] loadNibNamed:@"TTQTopicDetailHeadView" owner:nil options:nil] lastObject];
        headerView.entrance = self.viewModel.entrance;
        ((TTQTopicDetailHeadView *)headerView).forumHeightConstraint.constant = 0;
        ((TTQTopicDetailHeadView *)headerView).forumView.hidden = YES;
        @weakify(headerView, self);
        [headerView setTapFollowAction:^(IMYCKFollowButton * _Nonnull btn) {
            [btn followAction:self.contentPublisher.is_followed userId:self.contentPublisher.userID targetUserError:self.contentPublisher.error myError:0 completeBlock:^(BOOL success, BOOL hasRequest, IMYRelationType finalRelation, id  _Nonnull responseObj) {
                @strongify(self);
                if(success) {
                    // 2.改变model状态, 按钮title跟数据联动
                    self.contentPublisher.is_followed = finalRelation;
                    [self updateFollowViews];
                    if (self.contentPublisher.is_followed) {
                        // 展开推荐列表
                        if([headerView canShowSheet]){
                            imy_asyncMainBlock(0.1, ^{
                                [headerView showRecomendList];
                            });
                        }
                        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_gz",@"public_type":@1,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topic.publisher.userID),@"action":@2} headers:nil completed:nil];
                    } else {
                        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_gz",@"public_type":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topic.publisher.userID),@"action":@2} headers:nil completed:nil];
                    }
                }
            }];
        }];

        [(TTQTopicDetailHeadView *)headerView setHighlightExpourseBlock:^(TTQHighLightWordModel *word) {
            @strongify(self);
            NSMutableDictionary *biParams = [NSMutableDictionary dictionaryWithDictionary:[self pageBiCommonData]];
            biParams[@"event"] = @"dsq_nrxqy_glc";
            biParams[@"action"] = @1;
            biParams[@"public_type"] = word.word;
            [IMYGAEventHelper postWithPath:@"event" params:biParams headers:nil completed:nil];
        }];
        
    }
    headerView.imyut_eventInfo.eventName = [NSString stringWithFormat:@"ttq_detail_content_%@", @(self.viewModel.topic_id)];
    headerView.imyut_eventInfo.eventValue = @{@"topicId": @(self.viewModel.topic_id)};
    
    headerView.richParserView.delegate = self;
    return headerView;
}

- (void)updateContentHeaderView {
    UIView<TTQTopicDetailHeaderViewProtocol> *headView = self.tableView.tableHeaderView;
    [(TTQTopicDetailHeadView *)headView reset];
    [headView bindModel:self.viewModel.topic cellForRowAtIndexPath:nil viewModel:self.viewModel];
}

- (void)updateheaderFollow {
    BOOL isSHow = self.viewModel.topic.publisher.error == 0 && self.viewModel.topic.publisher.userID != [[IMYPublicAppHelper shareAppHelper].userid integerValue];
    if ([self.tableView.tableHeaderView isKindOfClass:TTQTopicDetailHeadView.class]) {
        [(TTQTopicDetailHeadView *)self.tableView.tableHeaderView updatefollowButtonShow:isSHow status:self.viewModel.topic.publisher.is_followed];
        if(isSHow && self.viewModel.topic.publisher.is_followed == 0){
            imy_asyncMainBlock(0.1, ^{
                [(TTQTopicDetailHeadView *)self.tableView.tableHeaderView hideRecomendList];
            });
        }
    }
}

- (void)updateForumView {
    if (self.forumView) {
        if (self.tableView.tableHeaderView == nil) {
            [self.forumView removeFromSuperview];
        } else {
            if (self.forumView.superview == nil) {
                [self.tableView addSubview:self.forumView];
            }
            // forumView 处于TableView上需要处理跟随滚动
            if (self.forumView.superview == self.tableView) {
                if (self.tableView.contentOffset.y > 0) {
                    /// screenTipToolView是加在self.headView上，通过self.headView来判断forumView是否到达临界值
                    CGRect rect = [self.tableView rectForHeaderInSection:0];
                    /// 8 评论导航栏的顶部间距
                    if (self.tableView.contentOffset.y > rect.origin.y - self.forumView.imy_height + 8) {
                        self.forumView.imy_bottom = rect.origin.y + 8;
                    } else {
                        self.forumView.imy_top = self.tableView.contentOffset.y;
                    }
                } else {
                    self.forumView.imy_top = 0;
                }
                [self.tableView bringSubviewToFront:self.forumView];
                [self updateHeaderForumHeight];
            }
        }
    }
}

-(void)updateHeaderForumHeight {
    UIView<TTQTopicDetailHeaderViewProtocol> *headView = self.tableView.tableHeaderView;
    if ([headView isKindOfClass:TTQTopicDetailHeadView.class]) {
        if (self.forumView) {
            if (self.forumView.isSingleLine) {
                ((TTQTopicDetailHeadView *)headView).forumHeightConstraint.constant = 44;
                self.forumView.imy_height = 44;
            } else {
                ((TTQTopicDetailHeadView *)headView).forumHeightConstraint.constant = 62;
                self.forumView.imy_height = 62;
            }
        } else {
            ((TTQTopicDetailHeadView *)headView).forumHeightConstraint.constant = 0;
            ((TTQTopicDetailHeadView *)headView).forumView.hidden = YES;
        }
    }
    
    [headView setNeedsUpdateConstraints];
}

#pragma mark - 导航栏

- (CGFloat)offsetYWhenBarUserInfoShow {
    if (self.viewModel.isUGCUIStlye) {
        return -SCREEN_HEIGHT;
    } else {
        UIView<TTQTopicDetailHeaderViewProtocol> *headView = self.tableView.tableHeaderView;
        return headView.avatarButton.imy_bottom;
    }
}

- (CGFloat)offsetYWhenShowBarBottomLine {
    CGFloat startChangeOffset = 0;
    if (!self.viewModel.isUGCUIStlye) {
        if (self.forumView) {
            startChangeOffset = self.forumView.imy_bottom;
        }
    }
    return startChangeOffset;
}

#pragma mark - 回复请求相关
/// 回复评论
/// - Parameter imageNames: 图片名称数组，如果没有图片则传nil
- (void)postReplyWithImages:(nullable NSArray<NSString *> *)imageNames{
    
    @weakify(self);
    ProgressCallback progressBlock = nil;
    if (imageNames.count == 0) {
        [self hidenProgressHUD];
        [UIWindow imy_showLoadingHUDWithText:IMYString(@"正在发送...")];
    } else {
        [self setProcessHUD:0.9];
        progressBlock = ^(int64_t completedUints, int64_t totalUnits) {
            imy_asyncMainBlock(^{
                @strongify(self);
                [self setProcessHUD:0.1 * completedUints / (double)totalUnits + 0.9f];
            });
        };
    }
    __block NSMutableDictionary *params = [[NSMutableDictionary alloc] init];
    params[@"content"] = [[self inputContentFromTextView] imy_trimString];
    if ([self.inputContentsView respondsToSelector:NSSelectorFromString(@"machTopics")]) {
        params[@"subject_struct"] = self.inputContentsView.machTopics;
    }
    if (imageNames.count > 0) {
        params[@"images"] = imageNames;
    }
    NSInteger imageCounts = 0;
    if ([params[@"images"] isKindOfClass:NSArray.class]) {
        NSArray *images = params[@"images"];
        imageCounts = images.count;
    }
    [self appendImagesV2Params:params];
    if (self.inputContentsView.postArticleButton.selected) {
        params[@"is_forward"] = @1;
    }

    NSDate *requestStartTime = [NSDate date];
    [[[self.viewModel.replyCommand execute:(progressBlock == nil ? @[params] : @[params, progressBlock])] deliverOnMainThread] subscribeNext:^(id<IMYHTTPResponse> response) {
        @strongify(self);
        [self handleReplyCommandSuessWithResponse:response params:params.copy];
        
        NSUInteger topicID = [params[@"topic_id"] integerValue];
        NSInteger statusCode = response.response.statusCode;
        [TTQCommonHelp GATopicPostSuccessRate:(statusCode == 200)
                                      topicID:topicID
                                  imageCounts:imageCounts
                                    errorCode:statusCode errorMessage:nil
                              requestCostTime:[[NSDate date] timeIntervalSinceDate:requestStartTime]
                                    eventType:2];

    } error:^(NSError *error) {
        @strongify(self);
        [self handleReplyCommadWithError:error params:params.copy];

        NSUInteger topicID = [params[@"topic_id"] integerValue];
        [TTQCommonHelp GATopicPostSuccessRate:0
                                      topicID:topicID
                                  imageCounts:imageCounts errorCode:error.code
                                 errorMessage:error.description
                              requestCostTime:[[NSDate date] timeIntervalSinceDate:requestStartTime]
                                    eventType:2];
    }];
}


/// 处理回复请求接口返回成功逻辑
/// - Parameters:
///   - response: 接口返回数据
///   - params: 请求参数
- (void)handleReplyCommandSuessWithResponse:(id<IMYHTTPResponse>)response params:(NSDictionary *)params {
    NSString *content = self.inputContentsView.textView.text;
    [self hidenProgressHUD];
    IMYUGCImageObject *imageObj = [self.inputContentsView imageModel];
    self.viewModel.inputDefaultText = nil;
    self.inputContentsView.textView.text = nil;
    [self.inputContentsView clearPhoto];
    [self.inputContentsView resetPostArticle];
    [self.inputContentsView restPose];
    // 需要在重置inputContentsView内容之后调用隐藏键盘，否则隐藏键盘的步骤会记录数据导致错误
    [self hideKeyboardForceDispatchToMain:NO];
    
    // 清除缓存记录
    // referenced_id 需要使用 NSInteger 接收，详情页帖子评论该值为-1
    NSInteger commentID = [params[@"referenced_id"] integerValue];
    NSUInteger topicID = [params[@"topic_id"] integerValue];
    if (commentID > 0) {
        [[TTQCommentContentCacheManager sharedManager] setContent:nil withCommentID:commentID];
        [[TTQCommentContentCacheManager sharedManager] setIsForward:NO withCommentID:commentID];
    } else if (topicID) {
        [[TTQCommentContentCacheManager sharedManager] setContent:nil withTopicID:topicID];
        [[TTQCommentContentCacheManager sharedManager] setIsForward:NO withTopicID:topicID];
    }
    
    self.viewModel.selectedReplyIndex = nil;
    NSInteger timestamp = [response.responseObject[@"timestamp"] integerValue];
    NSInteger score = [response.responseObject[@"score"] integerValue];
    [[IMYURIManager shareURIManager] runActionWithPath:@"push/checkPushSettings" params:@{@"position":@(IMYNABoxShowPosition_ttqPublic)} info:nil];
    if (score) {
        [UIWindow imy_hideHUD];
        [[TTQNewbeeTaskManager sharedManager] showTipWithMessage:[NSString stringWithFormat:@"恭喜完成评论帖子任务 贡献值+%li",(long)score]];
    } else {
        NSDictionary *result_popup = response.responseObject[@"result_popup"];
        if (!result_popup || result_popup.allKeys.count == 0) {
            /// 没有物品奖励弹窗时出现
            [UIWindow imy_showTextHUD:IMYString(@"评论成功")];
        } else {
            [UIView imy_hideHUD];
        }
    }
    
    if (commentID < 1) {
        /// finishedRequest 给详情页的数据太少，分不清是不是评论会来的请求，所以在这里做了，回复主楼后定位到第一条
        [self.tableView scrollToRowAtIndexPath:[NSIndexPath indexPathForRow:0 inSection:1] atScrollPosition:UITableViewScrollPositionTop animated:YES];
    }
    
    if (imageObj && [imageObj.userInfo isKindOfClass:IMYVKStickerItemModel.class]) {
        IMYVKStickerItemModel *sticker = imageObj.userInfo;
        NSString *typeString = [NSString stringWithFormat:@"%@_%ld",sticker.groupName, sticker.stickerId];
        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqypl_fsemoji",@"action":@2,@"public_type":typeString} headers:nil completed:nil];
    }
    [self.tableViewAdapter reloadModules];
    
#if __has_include(<IMYEBPublic/IMYEBYoubiTaskManager.h>)
    [[IMYEBYoubiTaskManager shareManager] oprationTaskFinishedWithKey:gJindouReviewTaskKey uploadParams:nil];
#endif
}


/// 处理回复请求接口返回错误逻辑
/// - Parameters:
///   - error: 错误对象
///   - params: 请求参数
- (void)handleReplyCommadWithError:(NSError *)error params:(NSDictionary *)params {
    [self hidenProgressHUD];
    [UIWindow imy_hideHUD];
    /// 先报埋点
    NSString *postFaildMsg = [[NSString alloc] initWithData:error.af_responseData encoding:NSUTF8StringEncoding];
    NSString *stringError = [NSString stringWithFormat:@"接口报错:%@",error.localizedDescription];
    stringError = imy_isNotEmptyString(postFaildMsg)?postFaildMsg:stringError;
    [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_fssb",@"action":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topicUserID),@"publi_info":stringError} headers:nil completed:nil];
    NSMutableDictionary *errorData = [NSMutableDictionary dictionary];
    if (error.userInfo) {
        [errorData addEntriesFromDictionary:error.userInfo];
    }
    if (params) {
        errorData[@"requestParams"] = params;
    }
    if (error.af_responseData) {
        errorData[@"responseData"] = [error.af_responseData responseString];
    }
    
    [IMYErrorTraces postWithType:IMYErrorTraceTypeOthers pageName:[self ga_pageName] category:IMYErrorTraceCategoryCommunity message:@"帖子评论失败" detail:errorData];
    
    if (error.code % 1000 == 400) {
        if (![IMYNetState networkEnable]) {
            [UIView imy_showTextHUD:MT_Request_NoNetToast];
        } else {
            IMYWebMessageModel *failModel = [error.af_responseData toModel:[IMYWebMessageModel class]];
            if (failModel.message.length > 0) {
                [UIView imy_showTextHUD:failModel.message];
            } else {
                [UIView imy_showTextHUD:IMYString(@"网络缓慢，请稍后再试")];
            }
        }
        [self clearInputView];//清除输入框内容
    } else if (error.code == kPhoneDubiousErrorCode || error.code == kPhoneStolenErrorCode) {
        [self hideKeyboard];
        imy_asyncMainBlock(0.15, ^{
            NSInteger type = (error.code == kPhoneStolenErrorCode) ? 1 : 2;
            [[IMYURIManager shareURIManager] runActionWithPath:@"account/phone/verify" params:@{ @"type": @(type) } info:nil];
        });
    } else if (error.code % 1000 == 422) {
        if (![IMYNetState networkEnable]) {
            [UIView imy_showTextHUD:MT_Request_NoNetToast];
        } else {
            IMYWebMessageModel *failModel = [error.af_responseData toModel:[IMYWebMessageModel class]];
            if (failModel.message.length > 0) {
                [UIView imy_showTextHUD:failModel.message];
            } else {
                [UIView imy_showTextHUD:IMYString(@"网络缓慢，请稍后再试")];
            }
        }
        NSUInteger commentID = [params[@"referenced_id"] integerValue];
        if (commentID) {
            [TTQMessageDetailViewModel deleteMessageByReviewId:commentID topic_id:self.viewModel.topic_id postNotification:NO];
        }
        
    } else {
        if (error.code != ********) {
            [self hideKeyboard];
        }
        
        ///******** 代表用户安全问题, 不需要额外弹窗 775
        NSData *responseData = error.af_responseData;
        NSDictionary *errorMap = [NSData imy_dictionaryWithJSONData:responseData];
        NSInteger errorCode = [[errorMap objectForKey:@"code"] integerValue];
        if (errorCode == ********) {
            return;
        }
        TTQTopicCurrentUserInfo *currentUserInfo = [errorMap[@"user_info"] toModel:[TTQTopicCurrentUserInfo class]];
        if ([currentUserInfo isKindOfClass:TTQTopicCurrentUserInfo.class] && currentUserInfo.feedback_id) {
            [self judgeBlockedWithUserInfo:currentUserInfo];
            return;
        }
        IMYWebMessageModel *messageModel = nil;
        if (error.code != -999) {
            messageModel = [error.af_responseData toModel:[IMYWebMessageModel class]];
        }
        if (![IMYNetState networkEnable]) {
            [UIView imy_showTextHUD:MT_Request_NoNetToast];
        } else {
            if (messageModel.message.length > 0) {
                [UIView imy_showTextHUD:messageModel.message];
            } else {
                [UIView imy_showTextHUD:IMYString(@"网络缓慢，请稍后再试")];
            }
        }
        // code = 16/11的情况在底层未做处理，业务端处理
        if (messageModel.code == 16 || messageModel.code == 11) {
            NSString *title = messageModel.code == 11? IMYString(@"违反平台规范被封号") : IMYString(@"违反平台规范被禁言");
            [UIAlertController imy_showAlertViewWithTitle:title message:IMYString(@"可以到”帮助与反馈“里申诉反馈") cancelButtonTitle:IMYString(@"取消") otherButtonTitles:@[IMYString(@"去反馈")] handler:^(UIAlertController *alertController, NSInteger buttonIndex) {
                if (buttonIndex == 1) {
                    [[IMYURIManager shareURIManager] runActionWithString:@"meiyou:///qiyu/chat?params=eyJncm91cElkIjogNDgwODE1ODc4fQ=="];
                }
            }];
            if (messageModel.code == 11) {
                IMY_POST_NOTIFY(@"UserAcessBanned");
            }
        }
    }
}

#pragma mark - bottomBar

- (void)setupBottomBar {
    [super setupBottomBar];
    @weakify(self);
    [self.bottomBar setYjActionBlock:^{ //  点击底部icon中，柚+
        @strongify(self);
        TTQShoppingPlugin *spPlugin = self.viewModel.shopPlugins.count > 0 ? self.viewModel.shopPlugins.firstObject : nil;
        if (spPlugin) {
            [TTQTopicRichParserYouPlusModel youplusAction:spPlugin.info];
            [self youjiaClickExportWithYouPlusModel:spPlugin.info dataDic:@{@"public_info": @4}];
        }
    }];
    /// 抱抱提示处理
    [RACObserve(self.bottomBar, frame) subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        if (self.bottomBar.frame.origin.y > 10 && self.bottomBar.imy_top > (self.view.frame.size.height + self.view.bounds.origin.y - 1)) {
            self.hugTipsView.hidden = YES;
        } else if (self.shouldShowHugTips) {
            [self showHugTipViewIfNeeded];
        } else {
            self.hugTipsView.hidden = NO;
        }
    }];
}

- (void)setupCameraButtonVisiable {
    if (self.viewModel.topic.reply_image && self.viewModel.forum.limit_reply_images > 0) {
        [self.inputContentsView showCameraButton];
    } else {
        [self.inputContentsView hidenCameraButton];
    }
}

- (void)updateBottomBar {
    [self.bottomBar.commentButton updateComment:self.viewModel.topic.total_review];
    [self.bottomBar.praiseButton updatePraise:self.viewModel.topic.has_praise count:self.viewModel.topic.praise_num];
    [self.bottomBar.collectButton updateCollect:self.viewModel.topic.is_favorite count:self.viewModel.topic.favorite_num];
    [self.bottomBar.hugButton updateHug:self.viewModel.topic.hugged count:self.viewModel.topic.hug_num];
}

- (void)bottomBarTextBgTapAction {
    if (self.viewModel.topic.is_close_comment) {
        return;
    }
    [super bottomBarTextBgTapAction];
    [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_plkhq",@"action":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topicUserID),@"mediaType":@(self.viewModel.isUGCUIStlye?3:0)} headers:nil completed:nil];
}

- (void)bottomBarCollectAction {
    NSInteger beforeNum = self.viewModel.topic.favorite_num;
    [[IMYURIManager shareURIManager] runActionWithPath:@"push/checkPushSettings" params:@{@"position":@(IMYNABoxShowPositionCommunity_shoucang)} info:nil];
    @weakify(self);
    if ([IMYPublicAppHelper shareAppHelper].hasLogin) {
        [[self.viewModel.favoriteCommand execute:nil] subscribeNext:^(id  _Nullable x) {
            @strongify(self);
            NSLog(@"success");
            [self.bottomBar.collectButton updateCollect:self.viewModel.topic.is_favorite count:self.viewModel.topic.favorite_num];
            [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqypl_dbsc",@"action":@2,@"public_type":self.viewModel.topic.is_favorite?@1:@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topicUserID),@"interact_num":@(beforeNum),@"is_on_period":@([IMYUGCEventHelper userIsInPhysiological]),@"redirect_url":[IMYUGCEventHelper currentPageRedirectUrl],@"mediaType":@(self.viewModel.isUGCUIStlye?3:0)} headers:nil completed:nil];
        } error:^(NSError * _Nullable error) {
            NSLog(@"error");
        }];
    } else {
        [UIWindow imy_showTextHUD:kStatusText_unLogin];
        [[IMYURIManager shareURIManager] runActionWithString:@"login"];
    }
}

- (void)bottomBarHugAction {
    if (self.hugTipsView && self.hugTipsView.superview) {
        [self.hugTipsView removeFromSuperview];
        self.hugTipsView = nil;
    }
    if (self.viewModel.currentUserInfo.error == 2 && !self.viewModel.topic.hugged) {
        [UIWindow imy_showTextHUD:IMYString(@"您已被封号，无法抱抱哦")];
        return;
    }
    
    if (![IMYNetState networkEnable] && !self.viewModel.topic.hugged) {
        [self.bottomBar.hugButton startHugAnimatingInView:self.view];
    }
    BOOL shouldHug = [IMYCKFeedsHelper canDoHugAction:self.viewModel.topic.hugged];
    if (!shouldHug) {
        return;
    }
    NSInteger hugNume = self.viewModel.topic.hug_num;
    self.viewModel.topic.hugged = !self.viewModel.topic.hugged;
    if (self.viewModel.topic.hugged) {
        self.viewModel.topic.hug_num ++;
        [self.bottomBar.hugButton startHugAnimatingInView:self.view];
    } else {
        self.viewModel.topic.hug_num --;
    }
    [self.bottomBar.hugButton updateHug:self.viewModel.topic.hugged count:self.viewModel.topic.hug_num];
    if (self.viewModel.topic.hugged) {
        [IMYUGCRequest requestPostPath:@"v5/hugs" host:circle_seeyouyima_com params:@{@"item_type":@1,@"item_id":@(self.viewModel.topic.topic_id)} completion:^(id<IMYHTTPResponse>  _Nonnull resData, NSError * _Nonnull error) {
            if (!error) {
                [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_bb",@"action":@2,@"public_type":@1,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topic.publisher.userID),@"interact_num":@(hugNume),@"is_on_period":@([IMYUGCEventHelper userIsInPhysiological]),@"redirect_url":[IMYUGCEventHelper currentPageRedirectUrl],@"mediaType":@(self.viewModel.isUGCUIStlye?3:0)} headers:nil completed:nil];
            }
        }];
    } else {
        [IMYUGCRequest requestDeletePath:@"v5/hugs" host:circle_seeyouyima_com params:@{@"item_type":@1,@"item_id":@(self.viewModel.topic.topic_id)} completion:^(id<IMYHTTPResponse>  _Nonnull resData, NSError * _Nonnull error) {
            if (!error) {
                [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_bb",@"action":@2,@"public_type":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topic.publisher.userID),@"interact_num":@(hugNume),@"is_on_period":@([IMYUGCEventHelper userIsInPhysiological]),@"redirect_url":[IMYUGCEventHelper currentPageRedirectUrl],@"mediaType":@(self.viewModel.isUGCUIStlye?3:0)} headers:nil completed:nil];
            }
        }];
    }
}

- (void)bottomBarPraiseAction {
    [[IMYURIManager shareURIManager] runActionWithPath:@"push/checkPushSettings" params:@{@"position":@(IMYNABoxShowPositionCommunity_dianzan)} info:nil];
    BOOL shouldPraise = [TTQCheckService checkShouldPraiseWithUserStatus:self.viewModel.currentUserInfo.error hasPraised:self.viewModel.topic.has_praise];
    if (shouldPraise) {
        BOOL isPraise = !self.viewModel.topic.has_praise;
        if (isPraise) {
            [self.bottomBar.praiseButton startAnimating];
        }
        self.viewModel.topic.has_praise = !self.viewModel.topic.has_praise;
        NSInteger beforePraise = self.viewModel.topic.praise_num;
        if (isPraise) {
            self.viewModel.topic.praise_num ++;
        } else {
            self.viewModel.topic.praise_num --;
        }
        [self.bottomBar.praiseButton updatePraise:isPraise count:self.viewModel.topic.praise_num];
        @weakify(self);
        [self doPriaseWithModel:self.viewModel.topic hasPraise:self.viewModel.topic.has_praise shouldRequest:shouldPraise completion:^(BOOL isSuccess) {
            @strongify(self);
            if (isSuccess) {
                NSMutableDictionary *gaDic = [[NSMutableDictionary alloc] initWithDictionary:@{
                    @"action":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topicUserID),@"interact_num":@(beforePraise),@"public_info":@"内容详情页",@"is_on_period":@([IMYUGCEventHelper userIsInPhysiological]),@"redirect_url":[IMYUGCEventHelper currentPageRedirectUrl],@"mediaType":@(self.viewModel.isUGCUIStlye?3:0)}];
                gaDic[@"public_type"] = (isPraise?@11:@12);
                gaDic[@"event"] = @"dsq_nrxqy_dz";
                [IMYGAEventHelper postWithPath:@"event" params:[gaDic copy] headers:nil completed:nil];
            }
        }];
    }
}

- (void)doPriaseWithModel:(TTQTopicModel *)model
                hasPraise:(BOOL)hasPraise
            shouldRequest:(BOOL)shouldRequest
               completion:(void (^_Nullable)(BOOL isSuccess))completion {
    
    NSDictionary *param = @{@"topic_id": @(model.topic_id),
                            @"forum_id": @(model.forum_id),
                            @"is_praise": @(hasPraise),
                            @"is_ask": @NO};
    if (!shouldRequest) {
        return;
    }
    NSMutableDictionary *mutaDic = [NSMutableDictionary dictionaryWithDictionary:param];
    NSString *biURI = @"";
    if (model.redirect_url.length && [model.redirect_url containsString:@"params="]) {
        NSArray *urlArray = [model.redirect_url componentsSeparatedByString:@"params="];
        if (urlArray.count > 1) {
            biURI = urlArray[1];
        }
    }
    if (hasPraise && biURI.length) {
        mutaDic[@"bi_uri"] = biURI;
    }
    [self.viewModel doPriaseWithParam:mutaDic
                           completion:^(id _Nullable resData, NSError *_Nullable error) {
        BOOL isSuccess = [resData boolValue];
        !completion ?: completion(isSuccess);
    }];
}

/// 返回 publicType, 埋点使用
- (NSInteger)bottomBarCommentAction {
    if (self.viewModel.topic.is_close_comment) {
        [UIView imy_showTextHUD:IMYString(@"评论区已关闭")];
        return 0;
    }
    NSInteger publicType = [super bottomBarCommentAction];
    
    NSMutableDictionary *params = @{@"event":@"dsq_nrxqypl_dbpl",
                                    @"action":@2,
                                    @"mediaType":@(self.viewModel.isUGCUIStlye?3:0),
                                    @"info_type":@12,
                                    @"info_id":@(self.viewModel.topic_id)
    }.mutableCopy;
    if (publicType > 0) {
        params[@"public_type"] = @(publicType);
    }
    [IMYGAEventHelper postWithPath:@"event" params:params.copy headers:nil completed:nil];
    return publicType;
}

#pragma mark -

- (void)initInputContentView {
    [super initInputContentView];
    @weakify(self);
    [self.inputContentsView setForwardButtonClickBlock:^{
        @strongify(self);
        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_plzftxzk",@"action":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topicUserID)} headers:nil completed:nil];
    }];
}

#pragma mark - 好友关系

- (void)updateFollowViews {
    [super updateFollowViews];
    [self updateheaderFollow];
}

#pragma mark - request
- (void)requestAdList:(BOOL)isRefresh {
    if (self.viewModel.topic.category == TTQTopicCategoryCaseHistory) {
        //病例帖子不展示广告
        return ;
    }
    [super requestAdList:isRefresh];
}

- (void)finishedRequest:(NSError *)error isRefresh:(BOOL)isRefresh {
    if (self.captionView.state == IMYCaptionViewStateLoading && !error) {
        /// 首次加载显示后
        [IMYGAEventHelper postWithPath:@"imyugc_detailLoad" params:@{@"page":@"ttq_detail",@"isPreload":@(self.viewModel.isUsingCacheData),@"time":@(self._psd_ui_element.loading),@"source":[IMYMeetyouHTTPHooks currentPageSource]} headers:nil completed:nil];
        @weakify(self);
        imy_asyncMainBlock(5, ^{
            @strongify(self);
            if ([self.inputContentsView.textView isFirstResponder]) {
                return;
            }
            if (![IMYHugTipsView hasShownHugGuide]) {
                self.shouldShowHugTips = YES;
                [self showHugTipViewIfNeeded];
            }
        });
    }
    
    if (self.viewModel.topic.category == TTQTopicCategoryCaseHistory) {
        NSString *strCount = [NSString stringShowWithCount:self.viewModel.quickAccessModel.wait_patients];
        NSString *desc = [NSString stringWithFormat:@"%@人已问诊",strCount];
        @weakify(self);
        [self.bottomBar updateQuickAccessDescription:desc tapBlock:^{
            @strongify(self);
            NSString *url = self.viewModel.quickAccessModel.redirect_url;
            if (imy_isEmptyString(url)) {
                return ;
            }
            NSDictionary *params = @{@"event":@"dsq_nrxqy_wzrk",
                                     @"action":@2,
                                     @"info_type":@12,
                                     @"info_id":@(self.viewModel.topic_id)};
            [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
            [[IMYURIManager sharedInstance] runActionWithString:url];
        }];
    }

    self.viewModel.datasourceOrderFilter = self.viewModel.orderByFilter;
    [super finishedRequest:error isRefresh:isRefresh];
    if (!error) {
        [self setupTopicSatusTopView];
        if (self.viewModel.topic.is_close_comment) {
            [self.bottomBar updatePlaceHolder:IMYString(@"评论区已关闭")];
        } else {
            [self.bottomBar updatePlaceHolder:nil];
        }
        if (isRefresh) {
            [self addYoujiaView:NO];
        }
    }

    //修正数据源
    [self.viewModel fixDataSourceWhenRequestOccurError:error];
    if ([self.commentAdapter isKindOfClass:TTQTopicDetailCommentAdapter.class]) {
        [(TTQTopicDetailCommentAdapter *)self.commentAdapter finishedRequest:error isRefresh:isRefresh];
    }
    
    imy_asyncMainBlock(0.1, ^{
        self.isLocateEnd = YES;
        [self startPlayVideo];
    });
}

/// 是否是空评论， 子类需要根据实际情况重载
- (BOOL)isEmptyComment{
    return [self.viewModel isDataSourceNil];
}
#pragma mark - adapter

#pragma mark - 排序

- (void)changeOrderFilter:(TTQOrderByFilter)orderByFilter {
    [self.view endEditing:YES];
    self.viewModel.gotoID = -1;
    //有可能有插楼。所以不能直接倒序
    [self.tableView imy_endRefreshAndLoadMore];
    self.viewModel.orderByFilter = orderByFilter;
    self.viewModel.datasourceOrderFilter = orderByFilter;
    self.viewModel.changeOrderRequest = YES;
    [self.viewModel showCommentLoadingUI:YES withError:nil];
    CGPoint curOffset = self.tableView.contentOffset;
    [self.tableViewAdapter reloadModules];
    [self.tableView setContentOffset:curOffset animated:NO];
    [self requestData:YES success:^(id  _Nullable response) {
            //
    } failure:^(NSError * _Nullable error) {
        
    }];
}


#pragma mark - scrollView delegate

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    [self updateForumView];
    [super scrollViewDidScroll:scrollView];
    if (scrollView .contentOffset.y > self.view.imy_height / 2.0 && self.youjiaTipsView && !self.youjiaTipsView.isViewHide) {
        [self.youjiaTipsView hideAnimal];
    }
}

#pragma mark - Private Methods (override)
- (BOOL)commentEnable {
    if (self.viewModel.topic.is_close_comment) {
        /// 关闭评论了
        return NO;
    }
    return [super commentEnable];
}


/// 举报评论
/// - Parameter indexPath: 评论的indexPath
- (void)reportActionAtIndexPath:(NSIndexPath *)indexPath {
    if (!indexPath) {
        return ;
    }
    TTQCommentModel *model = [self.viewModel tableCellModelAtIndexPath:indexPath];
    if (!model || ![model isKindOfClass:TTQCommentModel.class]) {
        NSAssert(NO, @"reportActionAtIndexPath: model is not TTQCommentModel");
        return ;
    }
    @weakify(self);
    [TTQDetailHelper reportTopicAction:model.commentID postType:2 topicId:self.viewModel.topic_id callback:^(BOOL bOK, NSUInteger isPraise) {
        @strongify(self);
        NSDictionary *params = @{@"event":@"dsq_nrxqy_gdan",
                                 @"public_type":@"举报",
                                 @"info_type":@12,
                                 @"info_id":@(self.viewModel.topic_id),
                                 @"fuid":@(self.viewModel.topic.publisher.userID),
                                 @"action":@2};
        [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
    }];
}

/// 删除评论
/// - Parameter indexPath: 评论的indexPath
- (void)deleteActionAtIndexPath:(NSIndexPath *)indexPath {
    if (!indexPath) {
        return ;
    }
    TTQCommentModel *model = [self.viewModel tableCellModelAtIndexPath:indexPath];
    if (!model || ![model isKindOfClass:TTQCommentModel.class]) {
        NSAssert(NO, @"deleteActionAtIndexPath: model is not TTQCommentModel");
        return ;
    }
    if (model.is_you_plus) {
        [UIAlertController imy_showAlertViewWithTitle:IMYString(@"评论暂不支持删除，\n可以到“帮助与反馈”里申诉反馈")  message:nil cancelButtonTitle:IMYString(@"取消") otherButtonTitles:@[IMYString(@"去反馈")] handler:^(UIAlertController *alertController, NSInteger buttonIndex) {
            if (buttonIndex == 1) {
                [[IMYURIManager shareURIManager] runActionWithString:@"qiyu/feedback"];
            }
        }];
        return;
    }
    if (![IMYNetState networkEnable]) {
        [UIView imy_showTextHUD:kStatusText_networkDisconnectNoCache];
        return;
    }
    @weakify(self, indexPath, model);
    BOOL isMainComment = indexPath.row == 0;
    NSString *title = isMainComment?@"要删除该回复吗":nil;
    NSString *message = isMainComment?IMYString(@"删除后，该回复下的互动都将被删除") : IMYString(@"要删除该回复吗？");
    [UIAlertController imy_showAlertViewWithTitle:title message:message cancelButtonTitle:IMYString(@"取消") otherButtonTitles:@[IMYString(@"删除")] handler:^(UIAlertController *alertController, NSInteger buttonIndex) {
        @strongify(self, indexPath, model);
        if (buttonIndex == 0) {
            return ;
        }
        if (![IMYNetState networkEnable]) {
            [UIWindow imy_showTextHUD:IMYString(kStatusText_networkDisconnectCache)];
            return;
        }
        [[[[self.viewModel deleteCommentAtIndexPath:indexPath] doNext:^(id  _Nullable x) {
            @strongify(self);
            NSString *biInfo = @"子评";
            NSInteger deleteReviewCount = 1;//子评论
            if (indexPath.row == 0) {
                [[NSNotificationCenter defaultCenter] postNotificationName:TTQTopicReviewDidDeleteNotifition object:@{@"review_id": @(model.commentID)}];
                biInfo = model.referenced_num > 0?@"主评多条":@"主评单条";
                deleteReviewCount = 1 + model.referenced_num;
                // 强制处理附加的数据：空数据和相关推荐
                [self.viewModel forceHandleAttachDatas];
            } else {
                NSIndexPath *mainIndexPath = [NSIndexPath indexPathForRow:0 inSection:indexPath.section];
                TTQCommentModel *mainCommentModel = [self.viewModel tableCellModelAtIndexPath:mainIndexPath];
                if (mainCommentModel.referenceds.count == 0 && mainCommentModel.referenced_num > 0) {
                    mainCommentModel.isMoreCommentsLoading = YES;
                    NSIndexPath *indexPath = [NSIndexPath indexPathForRow:0 inSection:indexPath.section];
                    @weakify(mainCommentModel);
                    [[[self.viewModel requestSecondaryCommentsWithIndexPath:indexPath count:1] deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
                        @strongify(self,mainCommentModel);
                        mainCommentModel.isMoreCommentsLoading = NO;
                        [self.tableViewAdapter reloadModules];
                    } error:^(NSError * _Nullable error) {
                        @strongify(self,mainCommentModel);
                        mainCommentModel.isMoreCommentsLoading = NO;
                        [self.tableViewAdapter reloadModules];
                    }];
                }
                deleteReviewCount = 1;
                [self.tableViewAdapter reloadModules];
            }
            self.viewModel.topic.total_review = MAX(0, self.viewModel.topic.total_review - deleteReviewCount);
            
            [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_plsc",@"action":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topicUserID),@"public_info":biInfo} headers:nil completed:nil];
            
        }] deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
            @strongify(self);
            [self.tableViewAdapter reloadModules];
        }];
    }];
}


/// 置顶评论
/// - Parameters:
///   - indexPath: 评论的indexPath
///   - isTop: 是否置顶, YES置顶，NO取消置顶
- (void)pinTopAtIndexPath:(NSIndexPath *)indexPath setTop:(BOOL)isTop{
    
    if (!indexPath) {
        return ;
    }
    TTQCommentModel *model = [self.viewModel tableCellModelAtIndexPath:indexPath];
    if (!model || ![model isKindOfClass:TTQCommentModel.class]) {
        NSAssert(NO, @"pinTopAtIndexPath: model is not TTQCommentModel");
        return ;
    }
    
    if (![IMYNetState networkEnable]) {
        [UIView imy_showTextHUD:kStatusText_networkDisconnectNoCache];
        return;
    }
    TTQCommentModel *firstComment = [self.viewModel.dataSource match:^BOOL(id  _Nonnull element) {
        return [element isKindOfClass:TTQCommentModel.class];
    }];
    if (firstComment.pin_type == TTQCommonPinTypeAuthor && isTop) {
        @weakify(self);
        [UIAlertController imy_showAlertViewWithTitle:IMYString(@"要置顶该回复吗？") message:IMYString(@"当前已有置顶回复，继续置顶将替换原有回复哦") cancelButtonTitle:IMYString(@"取消") otherButtonTitles:@[IMYString(@"确认")] handler:^(UIAlertController *alertController, NSInteger buttonIndex) {
            if (buttonIndex == 1) {
                @strongify(self);
                [self setPinTopWithCommentID:model.commentID setTop:isTop];
                NSDictionary *params = @{@"event":@"dsq_nrxqy_plzd",
                                         @"info_type":@12,
                                         @"info_id":@(self.viewModel.topic_id),
                                         @"fuid":@(self.viewModel.topic.publisher.userID),
                                         @"action":@2,
                                         @"public_info":@"置顶替换"};
                [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
            }
        }];
        return;
    }
    
    [self setPinTopWithCommentID:model.commentID setTop:isTop];
    if (isTop) {
        NSDictionary *params = @{@"event":@"dsq_nrxqy_plzd",
                                 @"info_type":@12,
                                 @"info_id":@(self.viewModel.topic_id),
                                 @"fuid":@(self.viewModel.topic.publisher.userID),
                                 @"action":@2,
                                 @"public_info":@"直接置顶"};
        [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
    } else {
        NSDictionary *params = @{@"event":@"dsq_nrxqy_plqxzd",
                                 @"info_type":@12,
                                 @"info_id":@(self.viewModel.topic_id),
                                 @"fuid":@(self.viewModel.topic.publisher.userID),
                                 @"action":@2};
        [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
    }
}

- (void)setPinTopWithCommentID:(NSInteger)commentId setTop:(BOOL)isTop{
    if (!commentId) {
        return ;
    }
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"review_id"] = @(commentId);
    params[@"role_type"] = @1;
    params[@"forum_id"] = @(self.viewModel.forum_id);
    //   7=作者置顶评论 8=作者取消置顶
    params[@"item_type"] = isTop?@7:@8;
    params[@"topic_id"] = @(self.viewModel.topic_id);
    
    @weakify(self);
    [[[IMYServerRequest getPath:@"v2/user_operate" host:circle_seeyouyima_com params:params headers:nil] deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        [[NSNotificationCenter defaultCenter]postNotificationName:@"TTQDetailControllerRefresh" object:nil];
        [UIView imy_showTextHUD:isTop?IMYString(@"置顶成功"):IMYString(@"取消置顶成功")];
    } error:^(NSError * _Nullable error) {
        IMYWebMessageModel *failModel =
        [error.af_responseData toModel:[IMYWebMessageModel class]];
        NSString *toast = failModel.message;
        if (imy_isEmptyString(toast)) {
            toast = isTop?IMYString(@"置顶失败，请重试"):IMYString(@"取消置顶失败，请重试");
        }
        [UIView imy_showTextHUD:toast];
    }];
}

/// 回复逻辑
/// - Parameters:
///   - indexPath: 评论所在的indexPath， 如果是nil，则回复主评论
///   - isFromCommentIcon: 是否从评论icon点击进入回复
- (void)replyActionAtIndexPath:(NSIndexPath *)indexPath isFromCommentIcon:(BOOL)isFromCommentIcon{
    self.viewModel.bi_isRelyFromCommentIcon = isFromCommentIcon;
    [super replyActionAtIndexPath:indexPath isFromCommentIcon:isFromCommentIcon];
}
#pragma mark - 键盘

- (void)showReplyWhenAppearIfNeeded {
    BOOL disableComment = NO;
    IMYABTestVariables *vars = [[IMYCommonConfig sharedInstance] configForKey:@"disable_operation"];
    if (vars) {
        disableComment = ([vars integerForKey:@"forum_comments"] == 1);
    }
    if (self.viewModel.topic.is_close_comment) {
        /// 关闭评论了
        return;
    }
    if (self.viewModel.becomeFirstResponder && (self.viewModel.topic || self.viewModel.dataSource.count > 1) && self.isViewDidAppeared && !disableComment) {
        [self replyActionAtIndexPath:nil];
        self.viewModel.becomeFirstResponder = NO;
    }
}

- (void)inputViewWillBeginToEdit:(IMYCKInputWithStickerView *)inputView {
    self.canReportInputViewChange = YES;
}

- (void)inputViewWillChangeText:(IMYCKInputWithStickerView *)inputView {
    if (self.canReportInputViewChange) {
        self.canReportInputViewChange = NO;
        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_wzsr",@"action":@1,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topicUserID)} headers:nil completed:nil];
    }
}

#pragma mark - 状态提示

-(IMYUGCTopicDetailStatusBar *)topicStatusTopView{
    if(!_topicStatusTopView){
        _topicStatusTopView = [[IMYUGCTopicDetailStatusBar alloc] initWithFrame:CGRectMake(0, [self offsetTopForHidenNav], SCREEN_WIDTH, 36)];
        [self.view addSubview:_topicStatusTopView];
    }
    return _topicStatusTopView;
}

-(void)setupTopicSatusTopView{
    if(_topicStatusTopView != nil){
        [_topicStatusTopView removeFromSuperview];
        _topicStatusTopView = nil;
    }
    if (!self.viewModel.isUGCUIStlye) {
        // 社区长图文样式，个人主页进入的帖子状态异常情况
        if (self.viewModel.topic.publisher.userID == [[IMYPublicAppHelper shareAppHelper].userid intValue]){
            // 1 4 9 12这四种状态需要展示已违规状态条，详见， 改为服务端下发tip字段控制
            //https://alidocs.dingtalk.com/i/nodes/20eMKjyp81R0pdzmigpd40MNWxAZB1Gv?iframeQuery=sheet_range%3Dkgqie6hm_9_12_1_1
            if(![self.viewModel checkTopicValidate]){
                // 帖子状态不正常
                [self.topicStatusTopView setStatusBarTitle:self.viewModel.topic.tip[@"title"]];
                @weakify(self);
                [self.topicStatusTopView bk_whenTapped:^{
                    @strongify(self);
                    IMYURI *uri = [IMYURI uriWithURIString:self.viewModel.topic.tip[@"redirect_url"]];
                    [[IMYURIManager shareURIManager] runActionWithURI:uri];
                }];
                self.tableView.imy_top = [self offsetTopForHidenNav] + 36;
                self.tableView.imy_height = self.bottomBar.imy_top - self.tableView.imy_top;
            } else {
                self.tableView.imy_top = [self offsetTopForHidenNav];
                self.tableView.imy_height = self.bottomBar.imy_top - self.tableView.imy_top;
            }
        }
    }
}

- (CGFloat)offsetTopForHidenNav {
    return self.viewModel.isUGCUIStlye ? 56 : 44;
}

#pragma mark - 分享

- (BOOL)enableShareToPlatform {
    if (self.viewModel.topic.category == TTQTopicCategoryUGCVideo
        && self.viewModel.topic.topic_status != TTQTopicStatusNormal) {
        [UIView imy_showTextHUD:@"视频未审核通过，暂时不能分享~"];
        return NO;
    }
    return YES;
}

- (NSArray<IMYCoolShareItem *> *)shareItemsForLineSecond {
    NSString *userId = [NSString stringWithFormat:@"%lu",(unsigned long)[self contentPublisher].userID];
    if (![userId isEqualToString:[IMYPublicAppHelper shareAppHelper].userid]) {
        return @[[self shareCopyItem],[self shareDislikeItem],[self shareReportItem]];
    } else {
        NSMutableArray *items = [NSMutableArray arrayWithCapacity:2];
        [items addObject:[self shareCopyItem]];
        if (self.viewModel.topic.is_deletable) {
            [items addObject:[self shareDeleteItem]];
        }
        if (self.viewModel.topic.is_modified && !self.viewModel.isUGCUIStlye && self.viewModel.topic.category != TTQTopicCategoryCaseHistory) {
            [items addObject:[self shareEditItem]];
        }
        return items;
    }
}

- (IMYCoolShareItem *)shareCopyItem {
    IMYCoolShareItem *shareItem = [IMYCoolShareItem itemWithName:IMYString(@"复制链接") icon:@"all_share_btn_copylink" tag:kContentShareCopyTag];
    @weakify(self);
    shareItem.shareBlock = ^{
        @strongify(self);
        //有标题复制带标题
        NSString *linkUrl = [self viewModel].shareBody.share_url;
        if (imy_isNotEmptyString([self viewModel].topic.title)) {
            linkUrl = [NSString stringWithFormat:@"【%@-美柚】%@",[self viewModel].topic.title,linkUrl];
        }
        UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
        pasteboard.string = linkUrl;
        [UIWindow imy_showTextHUD:@"复制成功"];
        [self postBIShareWithType:kContentShareCopyTag];
    };
    return shareItem;
}


- (IMYCoolShareItem *)shareEditItem {
    IMYCoolShareItem *shareItem = [IMYCoolShareItem itemWithName:@"编辑"
                              icon:@"all_share_btn_modify"
                               tag:kContentShareEditTag];
    @weakify(self);
    shareItem.shareBlock = ^{
        @strongify(self);
        [self editTopic];
    };
    return shareItem;
}

- (void)editTopic {
    [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_gdan",@"public_type":@"编辑",@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topic.publisher.userID),@"action":@2} headers:nil completed:nil];
    // 请求是否可编辑接口
    if (![IMYNetState networkEnable]) {
        [UIView imy_showTextHUD:IMYString(kStatusText_networkDisconnectCache)];
        return;
    }
    NSDictionary *param = [NSDictionary dictionaryWithObject:@(self.viewModel.topic.topic_id) forKey:@"topic_id"];
    @weakify(self);
    [self.viewModel checkEditableWithParam:param completion:^(id isModified, id resData, NSError *error) {
        @strongify(self);
        if (isModified != nil) {
            BOOL modi = [isModified boolValue];
            if (modi) {
                // 可编辑跳转至帖子修改页
                // 不允许删除SD缓存
                [SDImageCache sharedImageCache].pauseClean = YES;
                [TTQPublishModel draftWithTopicModel:self.viewModel.topic completedBlock:^(TTQPublishModel *publishModel) {
                    /// 就不考虑 weak 了，页面会强制停留
                    if (!publishModel) {
                        return;
                    }
                    @strongify(self);
                    if (publishModel.draftId == 0) {
                        publishModel.draftId = [TTQPublishModel availableDraftId];
                    }
                    [publishModel saveToDB];
                    NSMutableDictionary *mutableDictionary = [NSMutableDictionary new];
                    mutableDictionary[@"TTQPublishFromType"] = @(TTQPublishFromEdit);
                    mutableDictionary[@"topic_id"] = @(self.viewModel.topic.topic_id);
                    mutableDictionary[@"template_id"] = @(self.viewModel.topic.templateID);
                    // 从帖子详情页进入编辑
                    [[IMYURIManager shareURIManager] runActionWithPath:@"circles/publish" params:mutableDictionary info:nil];
                }];
            } else {
                // 不可编辑弹窗
                UIAlertController *alert;
                NSDictionary *hint = (NSDictionary *)resData;
                if (hint != nil) {
                    NSString *mainTitle = [hint objectForKey:@"main_title"];
                    NSString *subTitle = [hint objectForKey:@"subtitle"];
                    NSArray *detailTitle = [hint objectForKey:@"child"];
                    for (NSString *str in detailTitle) {
                        subTitle = [subTitle stringByAppendingFormat:@"\n%@",str];
                    }
                    alert = [UIAlertController imy_showAlertViewWithTitle:mainTitle message:subTitle cancelButtonTitle:@"我知道了" otherButtonTitles:nil handler:nil];
                } else {
                    // 默认文案
                    alert = [UIAlertController imy_showAlertViewWithTitle:IMYString(@"帖子不可编辑") message:IMYString(@"满足以下任意一项，不可编辑帖子:\n1.发布时间超过7天\n2.帖子已编辑过\n3.帖子被加精\n4.帖子已推荐给更多人") cancelButtonTitle:@"我知道了" otherButtonTitles:nil handler:nil];
                }
            }
        }
    }];
}

/// 负反馈
- (void)disLikeAction {
    if (!self.viewModel.topic.fb_labels || self.viewModel.topic.fb_labels.count <= 0) {
        return;
    }
    [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_gdan",@"public_type":@"不感兴趣",@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topic.publisher.userID),@"action":@2} headers:nil completed:nil];
    //8.2.4负反馈改版
    UIView *view = [UIApplication sharedApplication].keyWindow;
    IMYUGCFeedBackView *feedbackView = [[IMYUGCFeedBackView alloc] initWithFrame:view.bounds];
    feedbackView.position = 22;
    feedbackView.showCurrentPage = YES;
    [view addSubview:feedbackView];
    [feedbackView showDisLikeAry:[self.viewModel.topic.fb_labels valueForKey:@"dislike"] contentAry:[self.viewModel.topic.fb_labels valueForKey:@"content"]];
    
    IMYMixHomeFeedsModel *model = [[IMYMixHomeFeedsModel alloc] init];
    model.itemId = self.viewModel.topic_id;
    model.recomm_type = self.viewModel.topic.recomm_type;
    
    @weakify(self);
    feedbackView.completeBlock = ^(NSMutableArray * _Nonnull feedbackAry) {
        @strongify(self);
        [[NSNotificationCenter defaultCenter] postNotificationName:@"kFeedbackDataRefreshNotification" object:@{@"postAry":feedbackAry,@"itemId":@(self.viewModel.topic_id),@"dissAnimation":@(YES),@"mixHomeFeedModel":model}];
    
//        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_gdan",@"public_type":@"举报",@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topic.publisher.userID),@"action":@2} headers:nil completed:nil];
    };
}

- (void)deleteContent {
    @weakify(self);
    [UIAlertController imy_showAlertViewWithTitle:nil message:@"要删除该内容吗？" cancelButtonTitle:IMYString(@"取消") otherButtonTitles:@[IMYString(@"删除")] handler:^(UIAlertController *alertController, NSInteger buttonIndex) {
        @strongify(self);
        if (buttonIndex == 1) {
            if (![IMYNetState networkEnable]) {
                [UIView imy_showTextHUD:MT_Request_NoNetToast];
                return;
            }
            
            [[[IMYServerRequest postPath:@"v5/user_article_delete" host:circle_seeyouyima_com params:@{@"item_id":@(self.viewModel.topic_id),@"item_type":@1,@"operate_type":@1} headers:nil] deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
                @strongify(self);
                [UIView imy_showTextHUD:IMYString(@"已删除")];
                [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_gdan",@"public_type":@"删除",@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topic.publisher.userID),@"action":@2} headers:nil completed:nil];
                //个人主页刷新
                [[NSNotificationCenter defaultCenter] postNotificationName:@"kArticleDeleteRefreshNotification" object:@{@"itemId":@(self.viewModel.topic_id),@"is_video":@([self.viewModel is_videoTopic]?YES:NO),@"ttq":@YES}];
                [self imy_pop:YES];
            } error:^(NSError * _Nullable error) {
                id idTemp = [error af_responseData];
                NSDictionary *dicValue = [idTemp imy_jsonObject];
                NSInteger code = [dicValue[@"code"] integerValue];
                if (code == 13000800) {
                    [UIWindow imy_hideHUD];
                    [UIAlertController imy_showAlertViewWithTitle:IMYString(@"作品暂不支持删除，\n可以到“帮助与反馈”里申诉反馈")  message:nil cancelButtonTitle:IMYString(@"取消") otherButtonTitles:@[IMYString(@"去反馈")] handler:^(UIAlertController *alertController, NSInteger buttonIndex) {
                        if (buttonIndex == 1) {
                            [[IMYURIManager shareURIManager] runActionWithString:@"qiyu/feedback"];
                        }
                    }];
                } else {
                    NSString *message = [dicValue valueForKey:@"message"];
                    [UIView imy_showTextHUD:message.length?message:IMYString(@"网络缓慢，请稍后再试")];
                }
            }];
        }
    }];
}


#pragma mark - BI
/// 返回页面通用的BI数据
- (NSDictionary *)pageBiCommonData {
    NSMutableDictionary *params = [NSMutableDictionary dictionaryWithCapacity:1];
    params[@"info_type"] = @12;
    params[@"info_id"] = @(self.viewModel.topic.topic_id);
    params[@"fuid"] = @(self.viewModel.topicUserID);
    params[@"is_on_period"] = @([IMYUGCEventHelper userIsInPhysiological]);
    return params;
}

- (void)shareResult:(BOOL)isSuccess type:(IMYCoolShareSheetType)type {
    if (isSuccess) {
//        1-微博分享 2-微信好友分享 3-微信朋友圈分享 4-QQ空间分享 5-QQ分享
        NSInteger toType = 0;
        if (type == IMYCoolShareSheetTypeWeixiSession) {
            toType = 2;
        } else if (type == IMYCoolShareSheetTypeWeixiTimeline) {
            toType = 3;
        } else if (type == IMYCoolShareSheetTypeQQSpace) {
            toType = 4;
        } else if (type == IMYCoolShareSheetTypeQQSession) {
            toType = 5;
        } else if (type == IMYCoolShareSheetTypeSinaWeibo) {
            toType = 1;
        }
        NSDictionary *params = [@{
            @"to_type": @(toType),
            @"type": @(0),
            @"target_id": @(self.viewModel.topic_id),
        } mutableCopy];
        NSString *biURI = @"";
        if (self.viewModel.redirect_url.length && [self.viewModel.redirect_url containsString:@"params="]) {
            NSArray *urlArray = [self.viewModel.redirect_url componentsSeparatedByString:@"params="];
            if (urlArray.count > 1) {
                biURI = urlArray[1];
            }
        }
        if (biURI.length) {
            params[@"bi_uri"] = biURI;
        }

        [[IMYPublicServerRequest getPath:@"v2/user_share" host:circle_seeyouyima_com params:params headers:nil] subscribeNext:^(id  _Nullable x) {
            
        }];
    }
}


#pragma  mark - Setter & getter


- (id<IMYTableViewAdapterModuleDelegate>)createCommentAdapter{
    TTQTopicDetailCommentAdapter *adapter = [[TTQTopicDetailCommentAdapter alloc] initWithViewModel:self.viewModel];
    @weakify(self);
    adapter.hideKeyboardBlk = ^{
        @strongify(self);
        [self hideKeyboard];
    };

    adapter.changeOrderFilterBlk = ^(TTQOrderByFilter topicFilter, BOOL shouldLocateToComment) {
      @strongify(self);
        self.shouldHandleLocateToComment = shouldLocateToComment;
        [self changeOrderFilter:topicFilter];
    };

    adapter.showImageBrowserBlk = ^(NSMutableArray * _Nonnull photos, NSUInteger index) {
      @strongify(self);
        [self showWithPhotos:photos atIndex:index];
    };
    adapter.contentLongedOnLinkBlk = ^(IMYRM80AttributedLabel * _Nonnull label, IMYRM80AttributedLabelURL * _Nonnull linkData) {
        @strongify(self);
        [self m80AttributedLabel:label longedOnLink:linkData];
    };
    adapter.contentClickedOnLinkBlk = ^(IMYRM80AttributedLabel * _Nonnull label, IMYRM80AttributedLabelURL * _Nonnull linkData) {
        @strongify(self);
        [self m80AttributedLabel:label clickedOnLink:linkData];
    };
    
    adapter.replyActionBlk = ^(NSIndexPath *indexPath) {
        @strongify(self);
        [self replyActionAtIndexPath:indexPath];
    };
    adapter.copyActionBlk = ^(NSIndexPath *indexPath) {
        @strongify(self);
        [self copyActionAtIndexPath:indexPath];
    };
    adapter.reportActionBlk = ^(NSIndexPath *indexPath) {
        @strongify(self);
        [self reportActionAtIndexPath:indexPath];
    };
    adapter.deleteActionBlk = ^(NSIndexPath *indexPath) {
        @strongify(self);
        [self deleteActionAtIndexPath:indexPath];
    };
    adapter.pinTopActionBlk = ^(NSIndexPath *indexPath, BOOL isTop) {
        @strongify(self);
        [self pinTopAtIndexPath:indexPath setTop:isTop];
    };
    
    adapter.fromURI = self.fromURI;
    adapter.tableView = self.tableView;
    
    return adapter;
}

#pragma mark - 柚+弹窗
- (void)addYoujiaView:(BOOL)isCache {
    TTQShoppingPlugin *spPlugin = self.viewModel.shopPlugins.count > 0 ? self.viewModel.shopPlugins.firstObject : nil;
    if (self.viewModel.topic.is_you_plus && spPlugin) {
        [self.bottomBar addYouJiaIconWithIconUrl:spPlugin.button_icon dec:spPlugin.info.btn_txt];
        [self showYJTipViewIfNeededWithSpPlugin:spPlugin];
        if (!isCache && !self.isYjPlugShow) {
            self.isYjPlugShow = YES;
            [self youjiaShowExportWithYouPlusModel:spPlugin.info dataDic:@{@"public_info": @3}]; // 曝光
            [self youjiaShowExportWithYouPlusModel:spPlugin.info dataDic:@{@"public_info": @4}]; // 曝光
        }
    }
}

- (void)showYJTipViewIfNeededWithSpPlugin:(TTQShoppingPlugin *)spPlugin {
    if (spPlugin == nil) {
        return;
    }
    
    if (self.youjiaTipsView == nil) {
        self.youjiaTipsView = [IMYAdYoujMarketTipsView tipsView];
    }
    
    self.youjiaTipsView.isViewHide = NO;
    
    @weakify(self);
    [self.youjiaTipsView setHideViewBlock:^{
        @strongify(self);
        if (self.bottomBar.adYjButton) {
            [self.bottomBar.adYjButton startYJIconAnimate];
        }
    }];
    [self.youjiaTipsView setClickViewBlock:^{
        @strongify(self);
        [TTQTopicRichParserYouPlusModel youplusAction:spPlugin.info];
        [self youjiaClickExportWithYouPlusModel:spPlugin.info dataDic:@{@"public_info": @3}];
    }];
    if (!self.youjiaTipsView.isViewHide) {
        [self.youjiaTipsView showInView:self.view targetView:self.bottomBar.adYjButton shopPlugin:spPlugin];
        [self.youjiaTipsView showAnimal];
    }
}

- (void)youjiaShowExportWithYouPlusModel:(TTQTopicRichParserYouPlusModel *)youPlusModel dataDic:(NSDictionary *)dataDic{
    if (youPlusModel == nil || ![youPlusModel isKindOfClass:[TTQTopicRichParserYouPlusModel class]]) {
        return;
    }
    //  public_info  1文章， 2评论， 3底部插件， 4底部插件ICON
    NSNumber *publicInfo = @2;
    if (dataDic[@"public_info"]) {
        publicInfo = dataDic[@"public_info"];
    }
    // 柚+曝光事件
    TTQTopicRichParserYouPlusModel *model = youPlusModel;
    NSString *youplus_id = model.youplus_id ?: @"";
    NSDictionary *dic = @{
        @"addin_id":model.youplus_id?:@"",
        @"public_type":@(model.type),
        @"e_user_id":model.e_user_id?:@"",
        @"action":@(1),
        @"public_info":publicInfo,
        @"isAdPlugin":@(model.isAdPlugin)
    };
    if (model.type == 1) {
        NSMutableDictionary *mutDic = [[NSMutableDictionary alloc] initWithDictionary:dic];
        mutDic[@"wx_account_type"] = @(model.wx_account_type);
        mutDic[@"wx_account_wechat"] = model.wx_account_wechat;
        dic = [mutDic copy];
    }
    [self postYouPlusBIData:dic];
}

- (void)youjiaClickExportWithYouPlusModel:(TTQTopicRichParserYouPlusModel *)youPlusModel dataDic:(NSDictionary *)dataDic{
    if (youPlusModel == nil || ![youPlusModel isKindOfClass:[TTQTopicRichParserYouPlusModel class]]) {
        return;
    }
    NSNumber *publicInfo = @2;
    if (dataDic[@"public_info"]) {
        publicInfo = dataDic[@"public_info"];
    }
    TTQTopicRichParserYouPlusModel *model = youPlusModel;
    /// 柚加的数据
    NSDictionary *dic = @{
        @"addin_id":model.youplus_id?:@"",
        @"public_type":@(model.type),
        @"e_user_id":model.e_user_id?:@"",
        @"action":@(2),
        @"public_info":publicInfo,
        @"isAdPlugin":@(model.isAdPlugin),
    };
    if (model.type == 1) {
        NSMutableDictionary *mutDic = [[NSMutableDictionary alloc] initWithDictionary:dic];
        mutDic[@"wx_account_type"] = @(model.wx_account_type);
        mutDic[@"wx_account_wechat"] = model.wx_account_wechat;
        dic = [mutDic copy];
    }
    [self postYouPlusBIData:dic];
}


#pragma mark - m80

// MARK: - IMYRM80AttributedLabelDelegate

- (void)m80AttributedLabel:(IMYRM80AttributedLabel *)label longedOnLink:(IMYRM80AttributedLabelURL *)linkURL {
    if ([linkURL.linkData isKindOfClass:[TTQTopicRichParserYouPlusModel class]]) {
        /// 柚加的数据
        return;
    }
    NSString *copyString = linkURL.linkData;
    IMYRM80AttributedLabelURL *tmpLinkURL = linkURL;
    //这里为了统计，要不然都可以直接在解析时的linkData直接为url就好了。。。都是为了统计啊呀
    if ([copyString isEqualToString:TTQTopicContentLinkTypeHotTopic]) {
        // 热议话题点击
        tmpLinkURL = [IMYRM80AttributedLabelURL urlWithLinkData:self.viewModel.topic.subject.redirect_url range:linkURL.range color:linkURL.color];
        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_ht",@"public_type":@(self.viewModel.topic.subject.subjectID),@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topic.publisher.userID),@"action":@2} headers:nil completed:nil];
    } else if ([copyString isEqualToString:TTQTopicContentLinkTypeTheme]) {
        // 主题点击
        if (imy_isNotEmptyString(self.viewModel.topic.theme_redirect_url)) {
            tmpLinkURL = [IMYRM80AttributedLabelURL urlWithLinkData:self.viewModel.topic.theme_redirect_url range:linkURL.range color:linkURL.color];
        } else {
            IMYURI *uri = [IMYURI uriWithPath:@"circles/video/theme/gather" params:@{@"theme_id": @(self.viewModel.topic.theme_id)} info:nil];
            tmpLinkURL = [IMYRM80AttributedLabelURL urlWithLinkData:uri.uri range:linkURL.range color:linkURL.color];
        }
        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_ht",@"public_type":@(self.viewModel.topic.theme_id),@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topic.publisher.userID),@"action":@2} headers:nil completed:nil];
    }
    [super m80AttributedLabel:label longedOnLink:tmpLinkURL];
}

- (void)m80AttributedLabel:(IMYRM80AttributedLabel *)label clickedOnLink:(IMYRM80AttributedLabelURL *)linkURL {
    if ([linkURL.linkData isKindOfClass:[TTQTopicRichParserYouPlusModel class]]) {
        [TTQTopicRichParserYouPlusModel youplusAction:linkURL.linkData];
        [self youjiaClickExportWithYouPlusModel:linkURL.linkData dataDic:@{@"public_info": @2}];
        return;
    }
    if ([linkURL.linkData isKindOfClass:TTQTopicSubjectModel.class]) {
        /// 正文里话题点击
        TTQTopicSubjectModel *subject = (TTQTopicSubjectModel *)linkURL.linkData;
        NSDictionary *matchTopic = [self.viewModel.topic.subjects match:^BOOL(NSDictionary *element) {
            return [element[@"name"] isEqualToString:subject.name];
        }];
        if (matchTopic) {
            NSString *uri = matchTopic[@"redirect_url"];
            if (imy_isNotEmptyString(uri)) {
                [[IMYURIManager sharedInstance] runActionWithString:uri];
            } else {
                NSDictionary *uriParams = @{
                    @"subjectID" : @(subject.subjectID)
                };
                [[IMYURIManager shareURIManager] runActionWithPath:@"circles/topic/subject" params:uriParams info:nil];
            }
        }
        return;
    }
    if ([self checkHighLightWord:linkURL.linkData]) {
        return;
    }
    NSString *copyString = linkURL.linkData;
    IMYRM80AttributedLabelURL *tmpLinkURL = linkURL;
    // 这里为了统计，要不然都可以直接在解析时的linkData直接为url就好了。。。都是为了统计啊呀
    if ([copyString isEqualToString:TTQTopicContentLinkTypeHotTopic]) {
        // 热议话题点击
        tmpLinkURL = [IMYRM80AttributedLabelURL urlWithLinkData:self.viewModel.topic.subject.redirect_url range:linkURL.range color:linkURL.color];
        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_ht",@"public_type":@(self.viewModel.topic.subject.subjectID),@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topic.publisher.userID),@"action":@2} headers:nil completed:nil];
    } else if ([copyString isEqualToString:TTQTopicContentLinkTypeTheme]) {
        // 主题点击
        if (imy_isNotEmptyString(self.viewModel.topic.theme_redirect_url)) {
            tmpLinkURL = [IMYRM80AttributedLabelURL urlWithLinkData:self.viewModel.topic.theme_redirect_url range:linkURL.range color:linkURL.color];
        } else {
            IMYURI *uri = [IMYURI uriWithPath:@"circles/video/theme/gather" params:@{@"theme_id": @(self.viewModel.topic.theme_id)} info:nil];
            tmpLinkURL = [IMYRM80AttributedLabelURL urlWithLinkData:uri.uri range:linkURL.range color:linkURL.color];
        }
        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_ht",@"public_type":@(self.viewModel.topic.theme_id),@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topic.publisher.userID),@"action":@2} headers:nil completed:nil];
    }
    [super m80AttributedLabel:label clickedOnLink:tmpLinkURL];
}

- (BOOL)checkHighLightWord:(TTQHighLightWordModel *)word {
    if ([word isKindOfClass:TTQHighLightWordModel.class]) {
//        https://www.tapd.meiyou.com/tapd_fe/21039721/story/detail/1121039721001292440?from_iteration_id=1121039721001004824
        IMYABTestExperiment *exp = [[IMYABTestManager sharedInstance] experimentForKey:@"words202507"];
        if (exp && [exp.vars integerForKey:@"words"] == 1) {
            /// 走搜索
            [[IMYURIManager sharedInstance] runActionWithPath:@"circles/searchresult" params:@{@"keyword":word.word,@"search_home_type":@1,@"pos_id":@70} info:nil];
        } else if (word.link_url) {
            [[IMYURIManager sharedInstance] runActionWithString:word.link_url];
        }
        NSMutableDictionary *biParams = [NSMutableDictionary dictionaryWithDictionary:[self pageBiCommonData]];
        biParams[@"event"] = @"dsq_nrxqy_glc";
        biParams[@"action"] = @2;
        biParams[@"public_type"] = word.word;
        [IMYGAEventHelper postWithPath:@"event" params:biParams headers:nil completed:nil];

        return YES;
    }
    return NO;
}

#pragma mark - 抱抱引导

- (void)showHugTipViewIfNeeded {
    if ([IMYHugTipsView hasShownHugGuide]) {
        return;
    }
    if (!self.isViewActived) {
        return;
    }
    if (self.bottomBar.adYjButton) {
        /// 有柚加组件的不弹。
        return;
    }
    
    // 如果hugButton不可见，不弹
    if (self.bottomBar.hugButton.superview == nil || self.bottomBar.hugButton.hidden || self.bottomBar.hugButton.alpha < 0.01) {
        return ;
    }
    
    if (self.shouldShowHugTips && !self.hugTipsView && self.bottomBar.imy_bottom < (self.view.frame.size.height + self.view.bounds.origin.y + 1)) {
        self.hugTipsView = [IMYHugTipsView tipsView];
        [self.hugTipsView showInView:self.view targetView:self.bottomBar.hugButton];
        self.shouldShowHugTips = NO;
        [IMYHugTipsView setShown];
    }
}

#pragma mark - 视频播放

- (NSPointerArray *)weakExtraVideoViews {
    if (!_weakExtraVideoViews) {
        _weakExtraVideoViews = [NSPointerArray weakObjectsPointerArray];
    }
    return _weakExtraVideoViews;
}

- (void)didAddVideoView:(TTQDetailVideoWraperView *)videoWraperView {
    self.viewModel.is_videoTopic = YES;
    videoWraperView.innerVideoView.delegate = self;
    videoWraperView.innerVideoView.videoType = TTQVideoViewTypeDetail;
    if (self.viewModel.entrance == 14 || self.viewModel.entrance == 2) {
        videoWraperView.innerVideoView.options.seekTimeCacheType = IMYVideoSeekTimeCacheTypeById;
        videoWraperView.innerVideoView.model.videoId = self.viewModel.topic.topic_id;
        videoWraperView.innerVideoView.model.bussinesskey = @"ttq";
    }
    if (![self.weakExtraVideoViews.allObjects containsObject:videoWraperView]) {
        [self.weakExtraVideoViews addPointer:(__bridge void *_Nullable)(videoWraperView)];
    }
}


- (void)startPlayVideo {
    if (!self.isLocateEnd) {
        return;
    }
    if (self.isViewActived && self.isViewDidAppeared) {
        CGFloat topInset = [self topViewHeight];
        [IMYVideoAutoPlayControl currentPlayingView];
        [IMYVideoAutoPlayControl autoPlayVideoInTableView:self.tableView extraVideoViews:self.weakExtraVideoViews.allObjects edgeInsets:UIEdgeInsetsMake(topInset, 0, 0, 0)];
        
        // bi埋点
        if ([IMYVideoAutoPlayControl currentPlayingView] != nil && [[IMYVideoAutoPlayControl currentPlayingView] isKindOfClass:TTQDetailVideoWraperView.class]) {
            NSTimeInterval delta = [[NSDate date] timeIntervalSince1970] - self.enterPageTime;
            // 2秒内自动播放的判定为【6 进入详情页自动播放】
            if (delta < 2) {
                self.bi_startType = 6;
            } else {
                self.bi_startType = 3;
            }
        }
    }
}

- (void)stopPlayVideo {
    // 视频：暂停播放
    if (self.curPlayingVideoView.videoType == TTQVideoViewTypeDetail) {
        if (!self.curPlayingVideoView.isFullScreen) {
            [IMYVideoAutoPlayControl stopCurrentVideoPlay];
        }
    } else {
        [IMYVideoAutoPlayControl stopCurrentVideoPlay];
    }
    [self resumeBackgroundSoundWithError:nil];
}

- (CGFloat)topViewHeight {
    CGFloat topInset = 0;
    CGRect forumViewRect = [self.forumView convertRect:self.forumView.bounds toView:self.view];
    if (!CGRectIsEmpty(forumViewRect) && CGRectIntersectsRect(forumViewRect, self.view.bounds)) {
        topInset = self.forumView.imy_height;
    } else {
        CGRect bottomBarRect = [self.bottomBar convertRect:self.bottomBar.bounds toView:self.view];
        CGRect bottomBarIntersection = CGRectIntersection(bottomBarRect, self.view.bounds);
        if (!CGRectIsEmpty(bottomBarRect) && bottomBarIntersection.origin.y < 2 && bottomBarIntersection.size.height > 0) {
            topInset = self.bottomBar.imy_height;
        }
    }
    return topInset;
}

- (void)postBiVideoEventWithVideoView:(IMYVideoView *)videoView {
    NSInteger startTime = self.bi_startDuration;
    // 1 暂停；2 播放完成(播放完成的endTime会为0,这时候要给他改成总时长)
    NSInteger endTime =  [self getPlayViewCurrentTime:videoView];
    if (endTime == 0) {
        endTime = (NSInteger)(videoView.track.totalDuration.integerValue * 1000);
    }
    NSMutableDictionary *params = [NSMutableDictionary dictionaryWithCapacity:0];
    // 业务参数
    params[@"topic_id"] = @(self.viewModel.topic.topic_id);
    params[@"subject_id"] = @(self.viewModel.topic.theme_id);
    params[@"community_type"] = @1;
    if (self.viewModel.bi_video_play_entrance) {
        params[@"entrance"] = @(self.viewModel.bi_video_play_entrance);
    } else if (self.viewModel.entrance) {
        if (self.viewModel.entrance == 9) { //发现过来的要改成11
            params[@"entrance"] = @(11);
        } else {
            params[@"entrance"] = @(self.viewModel.entrance);
        }
    } else {
        params[@"entrance"] = @(4);
    }
    if (self.al_source)
        params[@"al_source"] = self.al_source;
    if (self.algorithm)
        params[@"algorithm"] = self.algorithm;
    params[@"duration"] = videoView.track.totalDuration;
    // 视频参数
    params[@"star_type"] = @(self.bi_startType ? self.bi_startType : 6);
    params[@"end_type"] = @(self.bi_endType);
    params[@"star_duration"] = @((NSInteger)self.bi_startDuration * 1000);
    params[@"end_duration"] = @(endTime*1000);
    params[@"duration"] = @((NSInteger)(videoView.track.totalDuration.integerValue * 1000));
    // 7.6.4版本的入口来源参数（73关注，74推荐）
    if (self.viewModel.bi_catid) {
        [params setObject:@(self.viewModel.bi_catid) forKey:@"catid"];
    }
    if (endTime == startTime) {
        return;//如果播放时长为0则不上报
    }
    [IMYGAEventHelper postWithPath:@"bi_ttqvideoplay" params:params headers:nil completed:nil];
}

- (void)postPlayCountStatistaics {
    // 视频播放次数上报 @see https://gitlab.meiyou.com/meetyou_interface_group/community_interface_project/blob/master/v2/%E8%A7%86%E9%A2%91-%E6%92%AD%E6%94%BE%E7%BB%9F%E8%AE%A1.md
    NSDictionary *params = @{@"video_id": @(self.viewModel.topic.topic_id),
                             @"type": @(1)};
    [[IMYServerRequest postPath:@"v2/video_statistics" host:circle_seeyouyima_com params:params headers:nil] subscribeNext:^(id  _Nullable x) {
        
    }];
}

#pragma mark - video view delegate

- (void)playerView:(IMYVideoView *)playerView willChangeStateTo:(LLVideoPlayerState)state {
    if (self.firstChangeVideoState) {
        [self postPlayCountStatistaics];
        self.firstChangeVideoState = NO;
    }
    if (state == LLVideoPlayerStateContentPlaying) {
        [self pauseBackgroundSoundWithError:nil];
        self.curPlayingVideoView = playerView;
    }
}

- (void)playerView:(IMYVideoView *)playerView didChangeStateFrom:(LLVideoPlayerState)state {
    if (playerView.videoType == TTQVideoViewTypeRecommend) {
        // 相关推荐的视频播放不处理
        return;
    }
    if ((state == LLVideoPlayerStateContentPaused || state == LLVideoPlayerStateDismissed || state == LLVideoPlayerStateUnknown) && [IMYNetState isWWAN] && self.lastTimeNetState == 0) {
        [UIWindow imy_showTextHUD:@"正在使用移动网络"];
        self.lastTimeNetState = 1;
    }
    if ((state == LLVideoPlayerStateContentPaused || state == LLVideoPlayerStateDismissed || state == LLVideoPlayerStateUnknown) && [IMYNetState isWiFi] && self.lastTimeNetState == 1) {
        [UIWindow imy_showTextHUD:@"已切换至WiFi网络"];
        self.lastTimeNetState = 0;
    }
    
    //记录开始播放时间
    if ([playerView isPlaying] && [playerView.track isKindOfClass:LLVideoTrack.class]) {
        LLVideoTrack *track = playerView.track;
        NSUInteger second = [track.lastWatchedDuration floatValue];
        self.bi_startDuration = second;
    }
    // bi埋点
    // 暂停、退出播放停止的埋点
    if ([playerView isPaused] && [playerView.track isKindOfClass:LLVideoTrack.class]) {
        LLVideoTrack *track = playerView.track;
        NSUInteger second = [track.lastWatchedDuration floatValue];
        self.bi_endDuration = second * 1000;
        self.bi_endType = 2;
        
        [self postBiVideoEventWithVideoView:playerView];
    }
}

- (void)playerViewDidFinishPlaying:(IMYVideoView *)playerView {
    // 相关推荐的视频自动播放
    if (playerView.videoType == TTQVideoViewTypeRecommend) {
        if ([IMYNetState isWiFi] && playerView.superview) {
            [playerView replayByOutterVideoView];
        }
    } else {
        // 详情页视频处理
        // 不需要自动重播，显示重播按钮
        // bi埋点
        // 播放完成的埋点
        if ([playerView.track isKindOfClass:LLVideoTrack.class]) {
            LLVideoTrack *track = playerView.track;
            NSUInteger second = [track.totalDuration floatValue];
            self.bi_endDuration = second * 1000;
            self.bi_endType = 1;
            if (self.firstEnterDetail && [IMYNetState isWiFi]) {
                self.firstEnterDetail = NO;
                self.bi_startType = 6;
            }
        }
        [self postBiVideoEventWithVideoView:playerView];
        
        if ([self shouldAutoReplay]) {
            [playerView replay];
            playerView.playerView.HL.progressView.hidden = NO;
            [self postPlayCountStatistaics];
        }
    }
    self.bi_startDuration = 0;//重播以后,startDuration要重置
}

- (NSInteger)getPlayViewCurrentTime:(IMYVideoView *)playView {
    //只有这样获取的当前播放时刻才是精确的,别的例如playView.currentPlaybackTime 是不可靠的
    return (NSInteger)[[[[playView.playerView valueForKey:@"bottomControl"] valueForKey:@"scrubber"] valueForKey:@"value"] floatValue];
}

- (BOOL)shouldAutoReplay {
    return NO;
}

- (void)playerView:(IMYVideoView *)playerView willChangeToOrientation:(UIInterfaceOrientation)orientation {
    if (orientation == UIInterfaceOrientationLandscapeLeft || orientation == UIInterfaceOrientationLandscapeRight) {

    }
}

- (void)playerView:(IMYVideoView *)playerView didControlByEvent:(NSInteger)event userInfo:(id)userInfo {
    if (playerView.videoType == TTQVideoViewTypeRecommend) {
        // 相关推荐的视频播放不处理
        return;
    }
    if (event == kVControlEventReplay) {
        self.bi_startType = 2;
        // 重播
        self.bi_startDuration = 0;
        [self postPlayCountStatistaics];
    } else if (event == kVControlEventScrubbingEnd) {
        // 拖动结束开始播放
        self.bi_startDuration = [self getPlayViewCurrentTime:playerView];
    } else if (event == kVControlEventCollect) {
        if ([IMYPublicAppHelper shareAppHelper].hasLogin) {
            [self.viewModel.favoriteCommand execute:nil];
        } else {
            [UIWindow imy_showTextHUD:kStatusText_unLogin];
            [[IMYURIManager shareURIManager] runActionWithString:@"login"];
        }
    } else if (event == kVControlEventPlay) {
        self.bi_startDuration = [self getPlayViewCurrentTime:playerView];
        [self showPlayInCelluarTipsWithVideoView:playerView];
        //BI 埋点暂停上报
        if (playerView.isPausedByUser) {
            self.bi_startType = 1;
            [self postPlayCountStatistaics];
        }
    }
}

- (void)playerViewDidTapToPlay:(IMYVideoView *)playerView {
    [self showPlayInCelluarTipsWithVideoView:playerView];
}

- (void)playerViewWillStartPlaying:(IMYVideoView *)playerView {
    self.curPlayingVideoView = playerView;
}


#pragma mark - 投票相关

- (void)voteChange:(NSNotification *)notification {
    TTQVoteModel *voteModel = notification.object;
    NSInteger currentUserId = [[IMYPublicAppHelper shareAppHelper].userid integerValue];
    BOOL needUpdate = NO;
    BOOL needUpdateComment = NO;
    if (self.viewModel.topic.vote.item_type_new != 1) {
        /// 不是pk类型的就不管，评论区更新显示红、蓝方
        needUpdateComment = YES;
    }
    
    if ([voteModel isKindOfClass:TTQVoteModel.class]) {
        if (self.viewModel.topic.vote.voteID == voteModel.voteID) {
            needUpdate = YES;
        }
    } else if ([voteModel isKindOfClass:IMYMixHomeVoteModel.class]) {
        NSString *json = [voteModel imy_jsonString];
        TTQVoteModel *voteData = [json toModel:TTQVoteModel.class];
        if (self.viewModel.topic.vote.voteID == voteData.voteID) {
            self.viewModel.topic.vote = voteData;
            needUpdate = YES;
        }
    }
    if (needUpdate) {
        self.viewModel.feedsSourceChange = YES;
        if (needUpdateComment) {
            for (TTQCommentModel *comment in self.viewModel.dataSource) {
                if ([comment isKindOfClass:TTQCommentModel.class]) {
                    if (comment.publisher.userID == currentUserId) {
                        comment.voteType = 1;
                        comment.votedIndexData = @[@([self.viewModel voteIndex])];;
                    }
                    for (TTQCommentModel *subComment in comment.referenceds) {
                        if (subComment.publisher.userID == currentUserId) {
                            subComment.voteType = 1;
                            subComment.votedIndexData = @[@([self.viewModel voteIndex])];
                        }
                    }
                }
            }
            [self.tableViewAdapter reloadModules];
        }
    }
}

#pragma mark - IMYFullPopGestureRecognizerDelegate
/**
 视屏区域左滑手势屏蔽掉
 */
- (BOOL)fullPopGestureRecognizerShouldBegin:(UIPanGestureRecognizer *)gestureRecognizer {
    CGPoint location = [gestureRecognizer locationInView:self.view];
    if ([self isVideoViewInPosition:location]) {
        return NO;
    }
    TTQTopicDetailHeadView *header = self.tableView.tableHeaderView;
    if ([header isKindOfClass:TTQTopicDetailHeadView.class]) {
        if (!header.activityBannerView.hidden) {
            CGRect scrollBannerFrame = [header.activityBannerView convertRect:header.activityBannerView.bounds toView:self.view];
            if (location.y > CGRectGetMinY(scrollBannerFrame) && location.y < CGRectGetMaxY(scrollBannerFrame)) {
                return NO;
            }
        }
    }
    return TRUE;
}


/**
 判断触摸点位置是否是视频的View，全屏返回手势和视频播放的进度条拖动手势有冲突，需要特殊处理
 */
- (BOOL)isVideoViewInPosition:(CGPoint)point {
    if (![self.tableView.tableHeaderView isKindOfClass:TTQTopicDetailHeadView.class]) {
        return NO;
    }
    TTQTopicDetailHeadView *headView = (TTQTopicDetailHeadView *)self.tableView.tableHeaderView;
    for (IMYVideoView *videoView in self.weakExtraVideoViews.allObjects) {
        CGRect videoViewRectInView = [headView.richParserView convertRect:videoView.frame toView:self.view];
        if (CGRectContainsPoint(videoViewRectInView, point)) {
            return YES;
        }
    }
    return NO;
}


#pragma mark - 埋点

- (void)postLongImgBIData:(NSDictionary *)params{
    // action参数由params携带
    NSMutableDictionary *tmpDic = [[NSMutableDictionary alloc] initWithDictionary:params];
    tmpDic[@"info_type"] = @(12);
    tmpDic[@"info_id"] = @(self.viewModel.topic.topic_id);
    tmpDic[@"event"] = @"ttq_nrxqy_ct";
    [IMYGAEventHelper postWithPath:@"event" params:[tmpDic copy] headers:nil completed:nil];
}

- (void)postUnfoldImgBIData:(NSDictionary *)params{
    // action参数由params携带
    NSMutableDictionary *tmpDic = [[NSMutableDictionary alloc] initWithDictionary:params];
    tmpDic[@"info_type"] = @(12);
    tmpDic[@"info_id"] = @(self.viewModel.topic.topic_id);
    tmpDic[@"event"] = @"ttq_nrxqy_zksytp";
    [IMYGAEventHelper postWithPath:@"event" params:[tmpDic copy] headers:nil completed:nil];
}

- (void)postYouPlusBIData:(NSDictionary *)paramsDic {
    BOOL isAdPlugin = [paramsDic[@"isAdPlugin"] boolValue];
    if (isAdPlugin) {
        NSMutableDictionary *tmpDic = [[NSMutableDictionary alloc] init];
        tmpDic[@"event"] = @"sq_tzxqy_ggcj";
        tmpDic[@"action"] = [paramsDic objectForKey:@"action"];
        tmpDic[@"info_type"] = @(12);
        tmpDic[@"info_id"] = @(self.viewModel.topic.topic_id);
        [IMYGAEventHelper postWithPath:@"event" params:[tmpDic copy] headers:nil completed:nil];
    } else {
        NSMutableDictionary *tmpDic = [[NSMutableDictionary alloc] initWithDictionary:paramsDic];
        tmpDic[@"info_type"] = @(12);
        tmpDic[@"info_id"] = @(self.viewModel.topic.topic_id);
        tmpDic[@"event"] = @"dsq_nrxqy_ggcj";
        tmpDic[@"al_source"] = self.al_source;
        [tmpDic removeObjectForKey:@"isAdPlugin"];
        [tmpDic imy_setNonNilObject:self.global_track_id ? : self.fromURI.params[@"global_track_id"] forKey:@"global_track_id"];
        [tmpDic imy_setNonNilObject:self.ad_id ? : self.fromURI.params[@"ad_id"] forKey:@"ad_id"];
        [IMYGAEventHelper postWithPath:@"event" params:[tmpDic copy] headers:nil completed:nil];
    }
}

-(void)hideYouPlusBottomBar{
    // 重刷一下head
    UIView<TTQTopicDetailHeaderViewProtocol> *headView = self.tableView.tableHeaderView;
    [headView bindModel:self.viewModel.topic cellForRowAtIndexPath:nil viewModel:self.viewModel];
    [self.tableViewAdapter reloadModules];
}

- (void)postYouPinBIData:(NSDictionary *)paramsDic {
    NSMutableDictionary *tmpDic = [[NSMutableDictionary alloc] initWithDictionary:paramsDic];
    tmpDic[@"entrance_id"] = @"12";
    tmpDic[@"event"] = @"ypgoods";
    tmpDic[@"label"] = @{
        @"goods_id" : paramsDic[@"goods_id"],
        @"topic_id" : @(self.viewModel.topic.topic_id)
    };
    [tmpDic removeObjectForKey:@"goods_id"];
    [IMYGAEventHelper postWithPath:@"event" params:[tmpDic copy] headers:nil completed:nil];
}

#pragma mark - page 埋点
- (NSString *)ga_pageName {
    return @"TTQTopicDetailViewController";
}

- (NSDictionary *)ga_appendParams
{
    NSMutableDictionary *appendParams = [NSMutableDictionary dictionary];
    [self addRangeParams:appendParams];
    appendParams[@"dataId"] = @(self.viewModel.topic_id);
    appendParams[@"dataType"] = @1;
    appendParams[@"forum_id"] = @(self.viewModel.forum_id);
    appendParams[@"search_key"] = self.viewModel.search_key;
    appendParams[@"ad_position"] = @(self.viewModel.ad_position);
    [appendParams addEntriesFromDictionary:super.ga_appendParams];
    [appendParams setObject:@(12) forKey:@"info_type"];
    [appendParams setObject:@(self.viewModel.topic_id) forKey:@"info_id"];
    self.viewModel.hasViewMaxRange = 0;
    self.viewModel.hasReachMaxRange = 0;
    appendParams[@"is_on_period"] = @([IMYUGCEventHelper userIsInPhysiological]);
    appendParams[@"redirect_url"] = self.fromURI.uri;
    return appendParams;
}

- (void)addRangeParams:(NSMutableDictionary*)appendParams {
    if (self.viewModel.is_videoTopic) {
        return;
    }
    CGFloat contentHeight = self.tableView.tableHeaderView.imy_height - self.forumView.imy_height;
    CGFloat contentOffset = self.tableView.contentOffset.y;
    if (self.viewModel.hasViewMaxRange == 0) {
        CGFloat screenVisibleHeight = SCREEN_HEIGHT - SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT - 52;
        //当用户没有滚动屏幕,进入以后,直接退出的情况
        if (contentOffset > contentHeight) {
            //主楼超出可视区
            self.viewModel.hasViewMaxRange = 0;
        } else {
            //主楼在可视区
            self.viewModel.hasViewMaxRange = MIN(contentHeight, contentOffset + screenVisibleHeight);
        }
    }
    if (contentHeight > 0) {
        //增加浏览位置的上报
        appendParams[@"info_height"] = @(ceilf(contentHeight));
    } else {
        appendParams[@"info_height"] = @(0);
    }
    appendParams[@"browse_height"] = @(ceilf(self.viewModel.hasViewMaxRange));
    appendParams[@"roll_distance"] = @(ceilf(self.viewModel.hasReachMaxRange));
}

@end
