//
//  IMYContentDetailBaseViewController.m
//  IMYTTQ
//
//  Created by 林云峰 on 2025/5/26.
//

#import "IMYContentDetailBaseViewController.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import <IMYUGC/IMYCKBottomReplyBar.h>
#import "TTQTopicDetailPublisherTitleView.h"
#import <IMYCommonKit/IMYCRefreshHeader.h>
#import <IMYCommonKit/IMYCKFollowButton.h>
#import "TTQCheckService.h"
#import <IMYUGC/IMYCKInputWithStickerView.h>
#import <IMYAccount/IMYAccountCheckService.h>
#import <IMYAccount/IMYAccountServerURL.h>
#import "TTQABTestConfig.h"
#import "TTQCommentContentCacheManager.h"
#import "TTQDetailHelper.h"
#import <IMYBaseKit/IMYCoolShareSheet.h>
#import <IMYUGC/IMYUGCRequest.h>
#import <IMYUGC/IMYUGCEventHelper.h>
#import "IMYUGCImageObject.h"
#import "MBProgressHUD+TTQ.h"
#import <IMYAdvertisementSDK.h>
#import "IMYVideoView+TTQVideoPlayUtil.h"
#import <IMYVideoPlayer.h>
#import <IMYVideoPlayer/IMYVPlayerView+HL.h>
#import "TTQNavigationBarView.h"
#import "TTQSharedObject.h"
#import <GCDObjC/GCDQueue.h>
#import <IMYBaseKit/IMYRM80AttributedLabelURL.h>
#import <IMYBaseKit/NSString+IMYR.h>

#import "TTQTopicViewModel.h"
#import "TTQTopicDetailViewController.h"
#import "IMYRewardAlertView.h"
#import <IMYBaseKit/IMYVKStickerManager.h>
#import "IMYCKInputPlaceholder.h"
#import "TTQCommentManager.h"
#import "IMYContentDetailBaseViewController+Share.h"
#import "TTQRefreshBackNormalFooter.h"
#import "IMYContentDetailBaseViewController+VideoPlay.h"
#import "IMYContentDetailBaseViewController+AD.h"
#import <Poseidon/UIViewController+Poseidon.h>
#import <Poseidon/_PSDUIPageElement.h>
#import "TTQDoorConfig.h"

NSString *gJindouReviewTaskKey  = @"community_review";

@interface IMYContentDetailBaseViewController () <UITableViewDelegate, UITableViewDataSource,IMYCKInputWithStickerViewDelegate>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) TTQTopicBaseViewModel *viewModel;
@property (nonatomic, strong) IMYTableViewAdapter *tableViewAdapter;
/// 主内容区域
@property (nonatomic, strong) UIView *mainContentView;
@property (nonatomic, strong) IMYCaptionViewV2 *captionView;
/// 自定义导航栏
@property (nonatomic, strong) TTQNavigationBarView *customNavigationBar;
//导航栏右边按钮
@property (nonatomic, strong) IMYCKBottomReplyBar *bottomBar;
@property(nonatomic, strong) MBProgressHUD *progressHUD; //图片上传进度展示
@property (nonatomic, strong) IMYCKInputWithStickerView *inputContentsView;
@property (nonatomic, assign) CGPoint preContentOffset;// 当前内容偏移量
@property (nonatomic, assign) CGPoint preCommentOffset;// 当前评论偏移量
/// 最热评论曝光
@property (nonatomic, strong) UIView *commentBiViewHot;
/// 最新评论曝光
@property (nonatomic, strong) UIView *commentBiViewNew;

@property (nonatomic, strong) IMYPhotoBrowser *photoBrowser;
/// 登录后重新弹起键盘的判断
@property (nonatomic, assign) BOOL shouldBecomeFirstResponderWhenAppear;
@property (nonatomic, strong) NSIndexPath *lastSelectedReplyIndex;

@end

@implementation IMYContentDetailBaseViewController

- (BOOL)isNavigationBarHidden {
    return YES;
}

- (instancetype)initWithViewModel:(TTQTopicBaseViewModel *)viewModel {
    self = [super init];
    if (self) {
        self.viewModel = viewModel;
        [self bindViewModel];
    }
    return self;
}

- (void)bindViewModel {
    
}

- (TTQPublisherModel *)contentPublisher {
    return nil;
}
- (NSInteger)contentId {
    return 0;
}

- (IMYNewsItemType)contentType {
    return IMYNewsItemTypeTTQ;
}

/// 原帖或者原问题、资讯内容id
- (NSInteger)articleId {
    return self.viewModel.topic_id;
}

- (BOOL)isYouPlus {
    return NO;
}

- (void)addEventNotifications {
    @weakify(self);
    //    6.6.0 监听其他页面关注状态是否改变，是的话就更新model
    [[[[[NSNotificationCenter defaultCenter] rac_addObserverForName:@"mpnFollowNotification" object:nil] deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNotification *x) {
        @strongify(self);
        NSUInteger userId = [[x.object valueForKey:@"userId"] integerValue];
        if ([self contentPublisher].userID != userId) {
            return;
        }
        
        NSUInteger followStatus = [[x.object valueForKey:@"isFollow"] integerValue];
        if (followStatus > 1) {
            return;
        }
        [self contentPublisher].is_followed = followStatus;
        [self updateFollowViews];
    }];
    [[[[[NSNotificationCenter defaultCenter] rac_addObserverForName:UIApplicationDidEnterBackgroundNotification object:nil] takeUntil:self.rac_willDeallocSignal] deliverOnMainThread] subscribeNext:^(id x) {
        @strongify(self);
        // 停止视频播放
        [self stopPlayVideo];
    }];
    [[[[NSNotificationCenter defaultCenter] rac_addObserverForName:UIApplicationDidBecomeActiveNotification object:nil] deliverOnMainThread] subscribeNext:^(id x) {
        @strongify(self)
        imy_asyncMainBlock(0.5, ^{
            if (self.isViewActived) {
                [self startPlayVideo];
            }
        });
    }];
   
    [[[IMYNetState networkChangedSignal] skipUntilBlock:^BOOL(id x) {
        @strongify(self);
        return self.isViewDidAppeared;
    }] subscribeNext:^(id x) {
        @strongify(self);
        // 视频：自动播放流程
        if (self.isViewActived) {
            [self startPlayVideo];
        }
    }];
    // 用户ID变化
    [[[[[IMYPublicAppHelper shareAppHelper].useridChangedSignal skip:1] takeUntil:self.rac_willDeallocSignal] deliverOnMainThread] subscribeNext:^(id x) {
        @strongify(self);
        [self.tableView imy_headerBeginRefreshing];
    }];

    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(onKeyboardHideNotification:) name:UIKeyboardWillHideNotification object:nil];

}


- (void)viewDidLoad {
    [super viewDidLoad];
    [self addEventNotifications];
    [self initViewWhenDidLoad];
    @weakify(self);
    [self loadCache:^{
        @strongify(self);
        [self requestData:YES];
    }];
    [self initADManager];
    /// 检查内容是否可以被分享
    [TTQCommentManager shareInstance].checkedShareContent = NO;
    [TTQCommentManager shareInstance].canShareContent = NO;

    [self addCommentBiView];
    [RACObserve(self.tableView, contentSize) subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        imy_asyncMainBlock(0.1, ^{
            [self updateBICommentView];
        });
    }];

    // Do any additional setup after loading the view.
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    if (self.shouldBecomeFirstResponderWhenAppear) {
        self.viewModel.selectedReplyIndex = self.lastSelectedReplyIndex;
        if (![self.inputContentsView.textView isFirstResponder]) {
            //bugfix 修复第一次进入详情页选择照片后, 键盘主动弹起会遮住图片预览缩略的bug
            [self.inputContentsView.textView becomeFirstResponder];
        }
        self.shouldBecomeFirstResponderWhenAppear = NO;
    }
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    [self showReplyWhenAppearIfNeeded];
}

- (void)viewWillLayoutSubviews {
    [super viewWillLayoutSubviews];
    self.captionView.frame = self.view.bounds;
}

- (void)initViewWhenDidLoad {
    [self setupNavigationBar];
    [self initTableView];
    [self addRefreshCompont];
    [self setupBottomBar];
    self.captionView = [IMYCaptionViewV2 addToView:self.view show:YES];
    IMYCKLoadingView *loading = [[IMYCKLoadingView alloc] initWithtype:IMYCKLoadingDetail];
    loading.frame = self.captionView.bounds;
    [self.captionView setStateView:loading forState:IMYCaptionViewStateLoading];
    @weakify(self);
    [self.captionView setRetryBlock:^{
        @strongify(self);
        [self requestData:YES];
    }];
    [self.captionView setNoNetworkRetryBlock:^{
        [UIView imy_showTextHUD:kStatusText_networkDisconnectNoCache];
    }];
    [self initInputContentView];
}

- (void)initTableView {
    self.tableView = [[UITableView alloc] initWithFrame:CGRectMake(0, self.customNavigationBar.imy_bottom, SCREEN_WIDTH, SCREEN_HEIGHT - self.customNavigationBar.imy_height - 52 - SCREEN_TABBAR_SAFEBOTTOM_MARGIN) style:UITableViewStylePlain];
    [self.tableView imy_setBackgroundColorForKey:kCK_White_AN];
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.tableView.estimatedRowHeight = 0;
    self.tableView.estimatedSectionHeaderHeight = 0;
    self.tableView.estimatedSectionFooterHeight = 0;
    [self.view addSubview:self.tableView];
    
    self.mainContentView = [self createContentHeaderView];
    self.tableView.tableHeaderView = self.mainContentView;
    
    self.tableViewAdapter = [IMYTableViewAdapter adpaterWithTableView:self.tableView];
    self.tableViewAdapter.UIDelegate = self;
    [self registerAdaters];
}

- (UIView *)createContentHeaderView {
    return nil;
}

- (void)addRefreshCompont {
    [self addHeaderRefreshCompont];
    [self addFooterRefreshCompont];
}

- (void)addHeaderRefreshCompont {
    @weakify(self);
    IMYCRefreshHeader *refreshHeader = [IMYCRefreshHeader headerWithRefreshingBlock:^{
        @strongify(self);
        [self.viewModel.requestRemoteDataCommand cancel];
        if (![IMYNetState networkEnable]) {
            [self.tableView imy_headerEndRefreshing];
            [UIWindow imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
            return;
        }
        // 发起请求
        self.hasRequestSideAd = NO;
        [self requestData:YES];
    }];
    self.tableView.mj_header = refreshHeader;
}

- (void)addFooterRefreshCompont {
    if (!self.tableView.mj_footer) {
        @weakify(self);
        TTQRefreshBackNormalFooter *footer = [TTQRefreshBackNormalFooter footerWithRefreshingBlock:^{
            @strongify(self);
            if (![IMYNetState networkEnable]) {
                [UIWindow imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
                [self.tableView imy_footerEndRefreshing];
            } else {
                [self requestData:NO];
            }
        }];
        [footer setTitle:IMYString(@"上拉加载更多") forState:MJRefreshStateIdle];
        [footer setTitle:IMYString(@"上拉加载更多") forState:MJRefreshStatePulling];
        [footer setTitle:IMYString(@"正在加载更多...") forState:MJRefreshStateRefreshing];
        [footer setTitle:IMYString(@"已显示全部") forState:MJRefreshStateNoMoreData];
        
        @weakify(footer);
        [RACObserve(self.viewModel, automaticallyRefresh) subscribeNext:^(id x) {
            @strongify(self, footer);
            footer.automaticallyRefresh = [x boolValue];
        }];
        self.tableView.mj_footer = footer;
    }
}

- (void)setupBottomBar {
    self.bottomBar = [IMYCKBottomReplyBar bottomBarView];
    self.bottomBar.imy_bottom = SCREEN_HEIGHT - SCREEN_STATUSBAR_HEIGHT;
    [self.view addSubview:self.bottomBar];
    @weakify(self);
    [self.bottomBar setPlaceholderActionBlock:^{
        @strongify(self);
        [self bottomBarTextBgTapAction];
    }];
    [self.bottomBar setCommentActionBlock:^{
        @strongify(self);
        [self bottomBarCommentAction];
    }];
    [self.bottomBar setPraiseActionBlock:^{
        @strongify(self);
        if (![IMYNetState networkEnable]) {
            [UIWindow imy_showTextHUD:kStatusText_networkDisconnectCache];
            return;
        }
        //收藏操作
        if ([TTQABTestConfig disableInteractByType:TTQ_Interact_type_collect]) {
            return;//互动禁止793
        }
        [self bottomBarPraiseAction];
    }];
    [self.bottomBar setColletionActionBlock:^{
        @strongify(self);
        if (![IMYNetState networkEnable]) {
            [UIWindow imy_showTextHUD:kStatusText_networkDisconnectCache];
            return;
        }
        //收藏操作
        if ([TTQABTestConfig disableInteractByType:TTQ_Interact_type_collect]) {
            return;//互动禁止793
        }
        [self bottomBarCollectAction];
    }];
    [self.bottomBar setHugActionBlock:^{
        @strongify(self);
        if (![IMYNetState networkEnable]) {
            [UIWindow imy_showTextHUD:kStatusText_networkDisconnectCache];
            return;
        }
        //收藏操作
        if ([TTQABTestConfig disableInteractByType:TTQ_Interact_type_collect]) {
            return;//互动禁止793
        }
        [self bottomBarHugAction];
    }];
}

- (BOOL)isShortContent {
    return NO;
}

- (void)loadCache {
    
}

#pragma mark - 导航栏

- (void)setupNavigationBar {
    // 创建自定义导航栏
    self.customNavigationBar = [self isShortContent]? [TTQNavigationBarView largeView]: [TTQNavigationBarView defaultView];
    self.customNavigationBar.imy_top = -SCREEN_STATUSBAR_HEIGHT;
    [self.view addSubview:self.customNavigationBar];
    @weakify(self);
    [self.customNavigationBar setBackButtonActionBlock:^{
        @strongify(self);
        [self imy_topLeftButtonTouchupInside];
    }];
    [self.customNavigationBar setMoreButtonActionBlock:^{
        @strongify(self);
        [self.inputContentsView.textView resignFirstResponder];
        [self shareForDetail];
    }];
    [self.customNavigationBar setUserInfoTapBlock:^{
        @strongify(self);
        NSMutableDictionary *params = [@{@"event":@"dsq_nrxqy_tx",@"action":@2,@"info_type":@12,@"info_id":@([self articleId]),@"fuid":@([self contentPublisher].userID),@"public_type":@1} mutableCopy];
        if ([self contentType] == IMYNewsItemTypeCommnent) {
            params[@"comment_id"] = @([self contentId]);
            params[@"public_type"] = @2;
        }
        [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
    }];
    [self.customNavigationBar setFollowResultActionBlock:^(TTQPublisherModel * _Nonnull publisher, BOOL success) {
        @strongify(self);
        if (success) {
            NSMutableDictionary *params = [@{@"event":@"dsq_nrxqy_gz",@"public_type":@1,@"info_type":@12,@"info_id":@([self articleId]),@"fuid":@([self contentPublisher].userID),@"action":@2} mutableCopy];
            if ([self contentType] == IMYNewsItemTypeCommnent) {
                params[@"comment_id"] = @([self contentId]);
            }
            if (self.contentPublisher.is_followed) {
                params[@"public_type"] = @1;
            } else {
                params[@"public_type"] = @2;
            }
            [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
        }
    }];
    
    [self.customNavigationBar updatePublisher:[self contentPublisher]];
    if ([TTQDoorConfig isDisableShare]) {
        [self.customNavigationBar hideMoreButton];
    }
}

- (CGFloat)offsetYWhenBarUserInfoShow {
    return 52;
}

- (void)showBarUserInfoIfNeeded {
    if (self.tableView.contentOffset.y >= [self offsetYWhenBarUserInfoShow]) {
        [self.customNavigationBar changePublisherViewShowStatus:YES animated:YES];
    } else {
        [self.customNavigationBar changePublisherViewShowStatus:NO animated:YES];
    }
}

- (CGFloat)offsetYWhenShowBarBottomLine {
    return 0;
}

- (void)changeNavigaitonBarUIWithTableViewOffset:(CGFloat)offsetY {
    if (offsetY >  [self offsetYWhenShowBarBottomLine]) {
        if (offsetY < 8 + [self offsetYWhenShowBarBottomLine]) {
            CGFloat alpha = offsetY / (8.0 + [self offsetYWhenShowBarBottomLine]);
            self.customNavigationBar.bottomLineView.alpha = alpha;
        } else {
            self.customNavigationBar.bottomLineView.alpha = 1;
        }
    } else {
        self.customNavigationBar.bottomLineView.alpha = 0;
    }
}

#pragma mark - headerView

- (UIView *)contentHeaderView {
    return nil;
}

- (void)updateContentHeaderView {
    
}

#pragma mark - 好友关系刷新

- (void)updateFollowViews {
    [self.customNavigationBar updatePublisher:[self contentPublisher]];
}

#pragma mark - 键盘相关

- (void)initInputContentView {
    self.inputContentsView = [[IMYCKInputWithStickerView alloc] initPhotoMultitudeInnerWithFrame:CGRectMake(0, 0, self.view.imy_width, 87)];
    self.inputContentsView.enablePostArticle = YES;
    self.inputContentsView.enablePostOnlyWithImage = YES;
    self.inputContentsView.limmitWordToPost = 30;
    [self.inputContentsView showCameraButton];
    self.inputContentsView.delegate = self;
    [self.inputContentsView resetKeyboardWithShowSticker:YES];
    self.inputContentsView.attachedView = self.view;
    [self.inputContentsView.sendButton addTarget:self action:@selector(inputViewSend) forControlEvents:UIControlEventTouchUpInside];
    self.inputContentsView.inputBottomForNone = self.view.imy_height + self.inputContentsView.imy_height + 100;
    self.inputContentsView.imy_bottom = self.inputContentsView.inputBottomForNone;
    @weakify(self);
    self.inputContentsView.biCameraButtonClickBlock = ^{
        @strongify(self);
        NSMutableDictionary *dict = [@{@"event":@"dsq_nrxqy_xztp",
                               @"action":@2,
                               @"public_type":@1} mutableCopy];
        if ([self contentType] == IMYNewsItemTypeCommnent) {
            dict[@"comment_id"] = @([self contentId]);
            dict[@"public_type"] = @2;
        } else if (self.viewModel.selectedReplyIndex) {
            TTQCommentModel *model = [self.viewModel tableCellModelAtIndexPath:self.viewModel.selectedReplyIndex];
            dict[@"public_type"] = @2;
            dict[@"comment_id"] = @(model.commentID);
        }
        [IMYGAEventHelper postWithPath:@"event" params:dict headers:nil completed:nil];

    };
}

- (void)clearInputView {
    if (self.inputContentsView.textView.internalTextView.inputView != nil) {
        self.inputContentsView.textView.internalTextView.inputView = nil;
        [self.inputContentsView.textView.internalTextView reloadInputViews];
        [self.inputContentsView restPoseForInputText];
    }
}

#pragma mark - request
/// 缓存的逻辑子类自己实现
- (void)loadCache:(void(^ _Nullable)(void))completeBlock {
    if (completeBlock) {
        completeBlock();
    }
}
/// 请求数据
- (void)requestData:(BOOL)isRefresh{
    [self requestData:isRefresh success:nil failure:nil];
}

- (void)requestData:(BOOL)isRefresh success:(void(^ _Nullable)(id _Nullable response))successBlk failure:(void(^ _Nullable)(NSError * _Nullable error))failureBlk {
    /// 取消之前的请求
    [self.viewModel.requestRemoteDataCommand cancel];
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params addEntriesFromDictionary:[self requestParams]];
    TTQTopicDetailRequestType requestType = isRefresh?TTQTopicDetailRequestTypeRefresh:TTQTopicDetailRequestTypeNextPage;
    if (isRefresh && self.viewModel.gotoID > 0) {
        requestType = TTQTopicDetailRequestTypeGoto;
    }
    [[self.viewModel requestRemoteDataForType:requestType params:params] subscribeNext:^(id  _Nullable x) {
        [self requestAdList:isRefresh];
        [self finishedRequest:nil isRefresh:isRefresh];
        !successBlk ?: successBlk(x);
    } error:^(NSError * _Nullable error) {
        !failureBlk ?: failureBlk(error);
        [self finishedRequest:error isRefresh:isRefresh];
    }];
}

- (void)finishedRequest:(NSError *)error isRefresh:(BOOL)isRefresh {
    /// 无痕埋点统计，用于统计页面加载时长,请求结束后页面加载出来后上报
    [self imyut_pageDidShowed];
    [self.tableView imy_endRefreshAndLoadMore];
    if (!error) {
        [self.captionView setState:IMYCaptionViewStateHidden];
        [self updateContentHeaderView];
        [self updateBottomBar];
        [self updateFollowViews];
        if (isRefresh) {
            if (self.viewModel.locate_to_comment) {
                self.viewModel.locate_to_comment = NO;
                imy_asyncMainBlock(0.3, ^{
                    [self locateToCommentAreaWithAnimation:YES];
                });
            }
            if (self.viewModel.gotoID > 0) {
                [self scrollToGotoID:self.viewModel.gotoID];
            }
        }
    } else {
        /// 请求出错了都不自动弹键盘
        self.viewModel.becomeFirstResponder = NO;
        NSInteger requestErrorCode = error.code %1000;
        if (![IMYNetState networkEnable]) {
            [UIWindow imy_showTextHUD:MT_Request_NoNetToast];
        } else if (requestErrorCode == 404 || requestErrorCode == 400 || requestErrorCode == 410 || requestErrorCode == 430) {
            /// 帖子/评论被删除
            [UIWindow imy_showHUDwithNetworkError:error andResponseObject:error.af_responseData];
            self.captionView.state = IMYCaptionViewStateNoResult;
            [self imy_pop:YES];
            return;
        } else if (requestErrorCode == 422) {
            /// 评论的
            [self.captionView setTitle:IMYString(@"该楼层已被删除") forState:IMYCaptionViewStateNoResult];
            self.captionView.state = IMYCaptionViewStateNoResult;
            [UIWindow imy_showTextHUD:IMYString(@"该楼层已被删除")];
            self.viewModel.becomeFirstResponder = NO;
            [self imy_pop:YES];
            return;
        } else {
            [UIWindow imy_showTextHUD:IMYString(@"网络缓慢，请稍后再试")];
        }
        if (![self.viewModel contentReady]) {
            if ([IMYNetState networkEnable]) {
                [self.captionView setTitle:MT_Request_Retry andState:IMYCaptionViewStateRetry];
            } else {
                [self.captionView setTitle:MT_Request_NoNet andState:IMYCaptionViewStateRetry];
            }
        }
    }
    [self.tableViewAdapter reloadModules];
    [self updateAutoFooterStatus];
}

- (NSDictionary *)requestParams {
    return nil;
}

- (void)updateAutoFooterStatus{
    // 处理Footer
    if ([self.tableView.mj_footer isKindOfClass:TTQRefreshBackNormalFooter.class]) {
        TTQRefreshBackNormalFooter *footer = (TTQRefreshBackNormalFooter *)self.tableView.mj_footer;
        footer.automaticallyRefresh = self.viewModel.automaticallyRefresh && [IMYNetState networkEnable];
        if (self.viewModel.automaticallyRefresh || ![IMYNetState networkEnable]) {
            [footer endRefreshing];
        }else{
            [footer endRefreshingWithNoMoreData];
        }

        if (self.captionView.state != IMYCaptionViewStateHidden || [self.viewModel commentCount] == 0) {
            footer.hidden = YES;
        }else{
            footer.hidden = NO;
        }
    }
}



#pragma mark - 右上角按钮

- (void)shareForDetail {
    @weakify(self);
    if (!self.checkShareSetting) {
        [self checkUserCanShare:^(NSInteger canShare) {
            @strongify(self);
            self.checkShareSetting = YES;
            self.shareEnable = canShare;
        }];
    }
    [self showShareSheet];
}

#pragma mark - 键盘
- (void)hideKeyboard {
    //在主线程收键盘。不然可能会奔溃
    [self hideKeyboardForceDispatchToMain:YES];
}

- (void)hideKeyboardForceDispatchToMain:(BOOL)forceDispatchToMain {
    if (forceDispatchToMain) {
        @weakify(self);
        [[GCDQueue mainQueue] queueBlock:^{
            @strongify(self);
            [self.inputContentsView.textView resignFirstResponder];
        }];
    } else {
        [self.inputContentsView.textView resignFirstResponder];
    }
}

- (void)postHandleInputViewResign {
    // 键盘隐藏，处理保存评论数据的逻辑
    if (self.viewModel.selectedReplyIndex) {
        TTQCommentModel *comment = [self.viewModel tableCellModelAtIndexPath:self.viewModel.selectedReplyIndex];
        if ([comment isKindOfClass:TTQCommentModel.class]) {
            [[TTQCommentContentCacheManager sharedManager] setContent:[self inputContentFromTextView] withCommentID:comment.commentID];
            [[TTQCommentContentCacheManager sharedManager] setIsForward:self.inputContentsView.postArticleButton.selected withCommentID:comment.commentID];
        }
    } else {
        if ([self contentType] == IMYNewsItemTypeTTQ) {
            [[TTQCommentContentCacheManager sharedManager] setContent:[self inputContentFromTextView] withTopicID:[self contentId]];
            [[TTQCommentContentCacheManager sharedManager] setIsForward:self.inputContentsView.postArticleButton.selected withTopicID:[self contentId]];
        } else {
            [[TTQCommentContentCacheManager sharedManager] setContent:[self inputContentFromTextView] withCommentID:[self contentId]];
            [[TTQCommentContentCacheManager sharedManager] setIsForward:self.inputContentsView.postArticleButton.selected withCommentID:[self contentId]];
        }
    }
}

- (NSString *)inputContentFromTextView {
    NSAttributedString *attributedText = self.inputContentsView.textView.internalTextView.attributedText;
    if (attributedText) {
        return [IMYREmoticonManager encodeEmojiText:attributedText];
    }
    return nil;
}

- (void)onKeyboardHideNotification:(NSNotification *)notification {
    // 处理发送评论未登录的情况，登录完成返回来显示评论内容
    if (self.inputContentsView.isFirstResponder) {
        @weakify(self);
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            @strongify(self);
            if (![[UIViewController imy_currentVisibleViewController] isEqual:self]) {
                self.shouldBecomeFirstResponderWhenAppear = YES;
                self.lastSelectedReplyIndex = self.viewModel.selectedReplyIndex;
            }
        });
    }
}

- (void)showReplyWhenAppearIfNeeded {
    if (self.viewModel.becomeFirstResponder && self.isViewDidAppeared && [self.viewModel contentReady]) {
        [self replyActionAtIndexPath:nil];
        self.viewModel.becomeFirstResponder = NO;
    }
}

#pragma mark - 键盘delegate

- (void)inputViewOfTextViewDidChange:(HPGrowingTextView *)growingTextView {
    NSInteger textLength = growingTextView.text.ttq_textLength;
    if (growingTextView.internalTextView.markedTextRange == nil &&
        textLength > 1000) {
        [UIWindow imy_showTextHUD:IMYString(@"最多回复1000字哦~")];
        growingTextView.text = [growingTextView.text ttq_subChapterToIndex:1000];
    }
    if (textLength == 0) {
        self.inputContentsView.sendButton.enabled = NO;
    } else {
        self.inputContentsView.sendButton.enabled = YES;
    }
}

- (void)inputViewWillBecomeFirstResponder:(IMYREasyInputView *)inputView
                           keyboardHeight:(CGFloat)height
                        animationDuration:(CGFloat)duration
                                   option:(UIViewAnimationOptions)option {
    [self.view bringSubviewToFront:self.inputContentsView];
    [UITextView imy_addLineBreakActionMenu];
}

- (void)inputViewWillResignFirstResponder:(IMYREasyInputView *)inputView
                           keyboardHeight:(CGFloat)height
                        animationDuration:(CGFloat)duration
                                   option:(UIViewAnimationOptions)option {
    [self postHandleInputViewResign];
}


- (void)inputViewSend{
    [self inputViewSendWithFromKeyboard:NO];
}
/// 键盘里的发送按钮
- (void)inputViewWillSend:(IMYREasyInputView *)inputView {
    [self inputViewSendWithFromKeyboard:YES];
}

- (void)inputViewSendWithFromKeyboard:(BOOL)fromKeyboard {
    NSMutableDictionary *plParams = [@{@"event":@"dsq_nrxqy_djfs",@"action":@2,@"info_type":@12,@"info_id":@([self articleId]),@"fuid":@([self contentPublisher].userID)} mutableCopy];
    if ([self contentType] == IMYNewsItemTypeCommnent) {
        plParams[@"comment_id"] = @([self contentId]);
    }
    if (fromKeyboard) {
        plParams[@"public_info"] = @"输入法发送";
    }
    
    [IMYGAEventHelper postWithPath:@"event" params:plParams headers:nil completed:nil];
    if (![self loginAction]) {
        self.shouldBecomeFirstResponderWhenAppear = YES;
        plParams = [plParams mutableCopy];
        plParams[@"event"] = @"dsq_nrxqy_fssb";
        plParams[@"publi_info"] = @"未登录";
        [IMYGAEventHelper postWithPath:@"event" params:plParams headers:nil completed:nil];
        return;
    }

    if (![self canSendReplyRequest]) {
        return ;
    }
    
    IMYCKInputWithStickerView *inputView = self.inputContentsView;
    NSString *text = [self.inputContentsView.textView.text imy_trimString];
    if ([NSString imy_isEmptyString:text]) {
        BOOL shouldStop = YES;
        /// 没内容也没图片的
        if (inputView.imageModel) {
            shouldStop = NO;
        }
        if (shouldStop) {
            /// 详情页允许空回复，
            self.inputContentsView.textView.text = text;
            [UIWindow imy_showTextHUD:IMYString(@"您的回复为空，多写一点吧")];
            return;
        }
    }
    if (inputView.imageModel) {
        /// 8.78版本评论改为1张图
        if ([inputView isPostSticker]) {
            /// 表情就直接传图
            [UIWindow imy_showLoadingHUDWithText:IMYString(@"正在发送...")];
            [self postReplyWithImages:@[inputView.imageModel.url]];
        } else {
            IMYUGCImageObject *uploadImageObject = inputView.imageModel;
            if (uploadImageObject.url) {
                [self postReplyWithImages:@[uploadImageObject.url]];
                return;
            }
            [self.progressHUD showAnimated:YES];
            [self setProcessHUD:0.2];
            id<IMYOSSFileObject> fileObject = [uploadImageObject getImageOSSFileObject];
            fileObject.querys = @{@"scene":@14};
            @weakify(self,uploadImageObject);
            __block NSError *_error = nil;
            __block NSDate *uploadStartTime = [NSDate date];
            [[IMYOSS defaultUploader] uploadAllObjects:@[fileObject] progressBlock:^(id<IMYOSSFileObject> _Nonnull object, double progress) {
                @strongify(self);
                self.progressHUD.progress = 0.2 + progress * 0.7;
            } complatedBlock:^(id<IMYOSSFileObject> _Nonnull object, NSError *_Nullable error) {
                @strongify(uploadImageObject)
                NSInteger success = 1;
                if (error) {
                    _error = error;
                    success = 0;
                }
                [TTQCommonHelp GAuploadImageSuccessRate:success code:error.code errorMessage:error.description time:[[NSDate date] timeIntervalSinceDate:uploadStartTime]  imageURL:object.url.absoluteString type:1];
                uploadStartTime = [NSDate date];
                if (error == nil && object.state == IMYOSSFileStateCompleted &&
                    object.name) {
                    uploadImageObject.url = object.url.absoluteString;
                }
            } allComplatedBlock:^( NSArray<id<IMYOSSFileObject>> *_Nonnull allObjects) {
                 @strongify(self);
                 if (_error) {
                     @strongify(self);
                     [self hidenProgressHUD];
                     [UIWindow imy_hideHUD];
                     
                     if (_error.code == - 121) {
                         [UIWindow imy_showTextHUD:@"图片大小须不超过20M"];
                     } else {
                         if ([IMYNetState networkEnable] &&
                             _error.code != NSURLErrorNetworkConnectionLost &&
                             _error.code != NSURLErrorNotConnectedToInternet) {
                             [UIWindow imy_showHUDwithNetworkError:_error];
                         } else {
                             [UIWindow imy_showTextHUD:MT_Request_NoNetToast];
                         }
                     }
                     
                     //发送到bugly
                     NSDictionary *errorMap =
                     [NSObject imy_validJSONObject:_error.userInfo];
                     NSMutableDictionary *extraInfo = [NSMutableDictionary dictionary];
                     extraInfo[@"error"] = [errorMap imy_jsonString];
                     [[IMYAppReport shareInstance] reportErrorWithCategory:3 name:@"OSS Upload Fail In TTQ!" reason:_error.domain ?: @"com.imyttq.uploadImage" callStack:nil extraInfo:extraInfo];
                     [self postImagesFailed];
                 } else {
                     @strongify(self,uploadImageObject);
                     [self postReplyWithImages:@[uploadImageObject.url]];
                 }
             }];

        }
    } else {
        /// 目前只有帖子详情页主评论支持发图
        [self postReplyWithImages:nil];
    }
}

#pragma mark - adapter
- (void)registerAdaters {
    
    id<IMYTableViewAdapterModuleDelegate> commentAdapter = [self createCommentAdapter];
    self.commentAdapter = commentAdapter;
    
    [self.tableViewAdapter registerModuleDelegate:commentAdapter];
}

- (id<IMYTableViewAdapterModuleDelegate>)createCommentAdapter {
    return [TTQTestCommentAdapter new];
}

#pragma mark - bottomBar

- (void)updateBottomBar {
    
}

- (void)bottomBarTextBgTapAction {
    [self replyActionAtIndexPath:nil];
}

- (void)bottomBarCollectAction {

}
- (void)bottomBarHugAction {
    
}
- (void)bottomBarPraiseAction {
    
}


/// 返回 publicType, 埋点使用
- (NSInteger)bottomBarCommentAction {
    NSInteger publicType = 0;
    if ([TTQABTestConfig disableInteractByType:TTQ_Interact_type_comment]) {
        return publicType;//互动禁止793
    }
    publicType = 2;
    if ([self isEmptyComment]) {
        [self replyActionAtIndexPath:nil isFromCommentIcon:YES];
    } else {
        //评论区的位置小于屏幕高度/2，直接唤起键盘，否则吸顶显示
        CGFloat statusNavBarHeight = SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT;
        CGFloat contentOffsetY = self.tableView.contentOffset.y;
        CGFloat commentSectionY = [self firstSectionHeaderY] + SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT - contentOffsetY;
        CGFloat halfVCHeight = SCREEN_HEIGHT / 2.0;
        
        CGFloat currentMaxY = floor(CGRectGetHeight(self.tableView.bounds) +  contentOffsetY);
        CGFloat contentHeight = floor(self.tableView.contentSize.height);
        
        //当前偏移的高度距离最大高度的距离
        CGFloat y = self.tableView.contentSize.height - self.tableView.contentOffset.y - CGRectGetHeight(self.tableView.bounds);
        if (currentMaxY >= contentHeight || commentSectionY < halfVCHeight || (y < 40)) {
            [self replyActionAtIndexPath:nil isFromCommentIcon:YES];
        }else{
            [self locateToCommentAreaWithAnimation:YES];
            publicType = 1;
        }
    }
    return publicType;
}

#pragma mark - ShowLargePhotosDelegate methods

- (void)showWithPhotos:(NSMutableArray *)photos atIndex:(NSUInteger)index {
    if (self.inputContentsView.isFirstResponder) {
        [self hideKeyboard];
        return;
    }
    self.photoBrowser = [IMYPhotoBrowser showWithPhotos:photos atIndex:index];
    self.photoBrowser.pageControlStyle = IMYBrowserPageControlStyleText;
    self.photoBrowser.delegate = self;
    
    [self postHandlePhotoBroswerShow];
}

// MARK: - IMYPhotoBrowserDelegate

- (void)photoBrowserWillHide:(IMYPhotoBrowser *)photoBrowser {
    [self postHandlePhotoBroswerHide];
}

// MARK: - public methods
- (void)postHandlePhotoBroswerShow {
    // 子类可重写
    [self stopPlayVideo];
}
- (void)postHandlePhotoBroswerHide {
    // 子类可重写
    // 视频：开始播放
    imy_asyncMainBlock(0.5, ^{
        NSLog(@"]======");
        if (self.view.window && self.view.imy_inWindowVisible) {
            [self startPlayVideo];
        }
    });
}

//MARK: - IMYRM80AttributedLabelDelegate
- (void)m80AttributedLabel:(IMYRM80AttributedLabel *)label longedOnLink:(IMYRM80AttributedLabelURL *)linkURL {
    [label resetLongPressedStatus];
    [self longPressWithLink:(NSString *)linkURL.linkData];
}

- (void)longPressWithLink:(NSString *)linkUrl {
    NSURL *url = [NSURL URLWithString:linkUrl];
    if (url == nil) {
        return;
    }
    
    [self.inputContentsView.textView resignFirstResponder];
    [self.inputContentsView resignFirstResponder];
    @weakify(self);
    [IMYActionSheet sheetWithCancelTitle:IMYString(@"取消") otherTitles:@[IMYString(@"打开")] summary:nil showInView:self.navigationController.view action:^(NSInteger index) {
        @strongify(self);
        if (index == 1) {
            NSString *topic_id = [url imy_queryDictionary][@"topic_id"];
            if (topic_id) {
                TTQTopicViewModel *model = [[TTQTopicViewModel alloc] initWithTopicID:[topic_id intValue]];
                [self imy_push:[[TTQTopicDetailViewController alloc] initWithViewModel:model]];
            } else {
                if ([url.absoluteString hasPrefix:@"http"]) {
                    [self imy_push:[IMYVKWebViewController webWithURLString:url.absoluteString]];
                } else {
                    IMYURI *uri = [IMYURI uriWithURIString:linkUrl];
                    if ([[IMYURIManager shareURIManager] containScheme:uri.scheme] && [[IMYURIManager shareURIManager] runActionWithURI:uri]) {
                        return;
                    }
                    [[UIApplication sharedApplication] openURL:url options:nil completionHandler:nil];
                }
            }
        }
    }];
}
/**
 1  '外链',
 2  '内链',
 3  '主题商城',
 4  '皮肤详情',
 5  '话题',          xixiaoyou.com?__type=5&topic_id=234325
 6  '话题专题',        xixiaoyou.com?__type=6&catid=0&specialid=0
 7  '消息',        xixiaoyou.com?__type=7
 8  '意见反馈',      xixiaoyou.com?__type=8
 9  '柚子街',        xixiaoyou.com?__type=9
 10  '我的柚币',     xixiaoyou.com?__type=10
 11  '签到',         xixiaoyou.com?__type=11
 12  '求助区入口',   xixiaoyou.com?__type=12&forum_id=0 //已经删除了，没有求助区了哟
 13  '达人堂入口'    xixiaoyou.com?__type=13&forum_id=0
 */
- (void)m80AttributedLabel:(IMYRM80AttributedLabel *)label clickedOnLink:(IMYRM80AttributedLabelURL *)linkURL {
    NSString *copyString = linkURL.linkData;
    if ([copyString isKindOfClass:NSString.class]) {
        [self clickWithLink:copyString];
    }
}

- (void)clickWithLink:(NSString *)linkUrl {
    NSURL *url = [NSURL URLWithString:linkUrl];
    if ([url.absoluteString containsString:@"itunes.apple.com"]) {
        [[UIApplication sharedApplication] openURL:url options:nil completionHandler:nil];
    } else {
        IMYURI *uri = nil;
        if (linkUrl) {
            if ([self isKindOfClass:[TTQTopicDetailViewController class]]) {
                if (((TTQTopicDetailViewController *)self).viewModel.topic.is_experience && [linkUrl containsString:@"circles/publish?"]) {
                    NSMutableString *tmp = [[NSMutableString alloc] initWithString:linkUrl];
                    [tmp replaceOccurrencesOfString:@"circles/publish" withString:@"circles/publish/experience" options:NSCaseInsensitiveSearch range:NSMakeRange(0, linkUrl.length)];
                    linkUrl = [tmp copy];
                }
            }
            uri = [IMYURI uriWithURIString:linkUrl];
            /// 发布入口埋点
            [uri appendingParams:@{@"publish_entrance":@7}];
            if ([[IMYURIManager shareURIManager] containScheme:uri.scheme] && [self injectSaleUri:uri]) {
                return;
            }
        }
        
        NSDictionary *dic = [linkUrl imy_queryDictionary];
        
        NSString *uriString = dic[@"uri"];
        if (uriString) {
            uri = [IMYURI uriWithURIString:[uriString imy_base64DecodedSafeURLString]];
            if ([[IMYURIManager shareURIManager] containScheme:uri.scheme] && [self injectSaleUri:uri]) {
                return;
            }
        }
        NSString *topic_id = dic[@"topic_id"];
        //旧的跳转方式.只提供topic_id.没有提供__type
        if (topic_id) {
            [IMYEventHelper event:@"ttq-2"];
            [[IMYURIManager shareURIManager]
             runActionWithURI:[IMYURI uriWithPath:@"circles/group/topic"
                                           params:@{@"topicID": topic_id}
                                             info:nil]];
            return;
        }
        NSString *type = dic[@"__type"];
        if (type) {
            [IMYEventHelper event:[NSString stringWithFormat:@"ttq-%@", type]];
            if ([type isEqualToString:@"1"]) {
                [[UIApplication sharedApplication] openURL:url options:nil completionHandler:nil];
            } else if ([type isEqualToString:@"2"]) {
                NSDictionary *params = @{ @"url": url.absoluteString,
                                          @"usingWK": @(YES),
                };
                [[IMYURIManager shareURIManager] runActionWithPath:@"web" params:params info:nil];
            } else if ([type isEqualToString:@"3"]) {
                [IMYEventHelper event:@"gxzt" addType:0 attributes:@{@"来源": @"话题-主题商城"}];
                //跳到主题商城
                [[IMYURIManager shareURIManager] runActionWithPath:@"theme" params:nil info:nil];
                return;
            } else if ([type isEqualToString:@"4"]) {
                [IMYEventHelper event:@"gxzt" addType:0 attributes:@{@"来源": @"话题-皮肤详情"}];
                //跳到主题详情
                [[IMYURIManager shareURIManager] runActionWithPath:@"theme/detail" params:@{@"themeID": dic[@"skinid"]} info:nil];
                return;
            } else if ([type isEqualToString:@"5"]) {
                // 5  '话题',           xixiaoyou.com?__type=5&topic_id=234325
                [IMYEventHelper event:@"ttq-2"];
                [[IMYURIManager shareURIManager]
                 runActionWithURI:[IMYURI uriWithPath:@"circles/group/topic"
                                               params:@{@"topicID": dic[@"topic_id"]}
                                                 info:nil]];
            } else if ([type isEqualToString:@"6"]) {
                // 6  '话题专题',         xixiaoyou.com?__type=6&catid=0&specialid=0
                IMYURI *uri = [IMYURI uriWithPath:@"news/special" params:dic info:nil];
                [[IMYURIManager shareURIManager] runActionWithURI:uri];
            } else if ([type isEqualToString:@"7"]) {
                // 7  '消息',            xixiaoyou.com?__type=7
                [[IMYURIManager shareURIManager] runActionWithURI:[IMYURI uriWithPath:@"msg/entrance" params:nil info:nil]];
            } else if ([type isEqualToString:@"8"]) {
                // 8  '意见反馈',      xixiaoyou.com?__type=8
                [[IMYURIManager shareURIManager] runActionWithString:@"feedback"];
                
            } else if ([type isEqualToString:@"9"]) {
                // 9  '柚子街',        xixiaoyou.com?__type=9
                [[IMYURIManager shareURIManager] runActionWithString:@"sale/home"];
            } else if ([type isEqualToString:@"10"]) {
                // 10  '我的柚币',     xixiaoyou.com?__type=10
                [[IMYURIManager shareURIManager] runActionWithString:@"youbi"];
            } else if ([type isEqualToString:@"11"]) {
                // 11  '签到',         xixiaoyou.com?__type=11
                [[IMYURIManager shareURIManager] runActionWithString:@"sale/sign"];
            } else if ([type isEqualToString:@"13"]) {
                // 13  '达人堂入口'    xixiaoyou.com?__type=13&forum_id=0
                if ([IMYPublicAppHelper shareAppHelper].hasLogin) {
                    [[IMYURIManager shareURIManager]
                     runActionWithURI:[IMYURI uriWithPath:@"circles/honorhall"
                                                   params:@{ @"forum_id": @([dic[@"forum_id"] integerValue]) }
                                                     info:nil]];
                } else {
                    [IMYEventHelper event:@"dl" addType:0 attributes:@{@"来源": @"话题详情"}];
                    [UIWindow imy_showTextHUD:kStatusText_unLogin];
                    [[IMYURIManager shareURIManager] runActionWithString:@"login"];
                }
            } else {
                NSLog(@"传入的type有问题");
                return;
            }
        } else {
            [self imy_push:[IMYVKWebViewController webWithURLString:url.absoluteString]];
        }
    }
}
/// 835电商有个埋点需求，前后版本电商需要的协议不一样，但服务端只会下发旧协议，会添加一个特定的协议参数下来，这里需要调用下对方的协议
- (BOOL)injectSaleUri:(IMYURI *)uri {
    if ([[IMYURIManager shareURIManager] containActionBlockForPath:uri.path]) {
        /// 有相应电商的协议才生效，防止电商版本不一致，过滤下热议话题业务
        if (![uri.params objectForKey:@"topic_id"] && ![uri.path hasSuffix:@"circles/topic/subject"]) {
            [uri appendingParams:@{@"topic_id":@(self.viewModel.topic_id)}];
        }
        IMYURIActionBlockObject *objc = [IMYURIActionBlockObject actionBlockWithURI:[IMYURI uriWithPath:@"sale/interceptor" params:@{@"redirect_url":[uri uri]} info:@{}]];
        __block BOOL uriComplete = NO;
        /// 这里是电商替换完redirect_url后最终执行的地方
        objc.implCallbackBlock = ^(id result, NSError *error, NSString *eventName) {
            NSString *redirect_url = result[@"redirect_url"];
            uriComplete = [[IMYURIManager shareURIManager] runActionWithString:redirect_url];
        };
        [[IMYURIManager shareURIManager] runActionWithActionObject:objc completed:nil];
        return uriComplete;
    } else {
        return [[IMYURIManager shareURIManager] runActionWithURI:uri];
    }
}

#pragma mark - scrollView

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    if (scrollView == self.tableView) {
        [self showBarUserInfoIfNeeded];
        [self changeNavigaitonBarUIWithTableViewOffset:scrollView.contentOffset.y];
        [self updateBICommentView];
    }
}

-(void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView{
    if (scrollView.isDragging || scrollView.isDecelerating) {
        return;
    }
    self.isLocateEnd = YES;
    [self checkScrollWithDelay:0.2];
    
    if (self.tableView.contentOffset.y < [self firstSectionHeaderY]) { // 处于正文区域
        self.preContentOffset = self.tableView.contentOffset;
    } else {
        self.preCommentOffset = self.tableView.contentOffset;
    }
}

- (void)scrollViewDidEndDragging:(UIScrollView *)scrollView willDecelerate:(BOOL)decelerate {
    if (scrollView.isDragging || scrollView.isDecelerating) {
        return;
    }
    self.isLocateEnd = YES;
    [self checkScrollWithDelay:0.2];
    if (!decelerate) {
        if (self.tableView.contentOffset.y < [self firstSectionHeaderY]) { // 处于正文区域
            self.preContentOffset = self.tableView.contentOffset;
        } else {
            self.preCommentOffset = self.tableView.contentOffset;
        }
    }
}

#pragma mark - 定位

- (void)locateToCommentAreaWithAnimation:(BOOL)animation {
    CGFloat headY = [self firstSectionHeaderY];
    CGFloat maxOffset = MIN(MAX(0, self.tableView.contentSize.height - self.tableView.imy_height - self.tableView.contentInset.top),headY);
    [self.tableView setContentOffset:CGPointMake(0, maxOffset) animated:animation];
    if (!self.viewModel.locate_to_comment) {
        self.isLocateEnd = YES;
    }
}

- (CGFloat)firstSectionHeaderY {
    CGFloat sectionHeaderY = 0;
    UIView *headView = (UIView *)self.tableView.tableHeaderView;
    if ([self.tableView numberOfSections] > 0) {
        sectionHeaderY = [self.tableView rectForHeaderInSection:0].origin.y + 8;
    } else {
        sectionHeaderY = headView.imy_height;
    }
    return sectionHeaderY;
}

- (void)scrollToGotoID:(NSInteger)gotoId {
    if (self.viewModel.dataSource.count > 1 && self.viewModel.gotoID > 0) {
        __block BOOL canJump = NO;
        __block BOOL findID = NO;
        @weakify(self);
        [self.viewModel.dataSource enumerateObjectsUsingBlock:^(TTQCommentModel *model, NSUInteger index, BOOL *stop) {
            @strongify(self);
            if (![model isKindOfClass:[TTQCommentModel class]]) {
                return;
            }
            if (self.viewModel.gotoID > 0) {
                // 定位评论模式处理（eg. 1、从消息进来的；2、保存上次的ID第二次进入跳转到ID对应的位置）
                if (model.commentID == self.viewModel.gotoID) {
                    findID = true;
                    canJump = true;
                }
            }
            if (canJump) {
                *stop = YES;
                if (index < self.viewModel.dataSource.count) {
                    imy_asyncMainBlock(0.4, ^{
                        [self.tableView scrollToRowAtIndexPath:[NSIndexPath indexPathForRow:0 inSection:index] atScrollPosition:UITableViewScrollPositionTop animated:YES];
                        [self hideWarmReviewHighlightAtSection:index data:model];
                    });
                }
            }
        }];
        self.viewModel.gotoID = -1;
    }

}

#pragma mark - 暖评高

- (void)hideWarmReviewHighlightAtSection:(NSInteger)section data:(TTQCommentModel *)model {
    ///持有一下
    @weakify(model);
    imy_asyncMainBlock(0.5, ^{
        @strongify(model);
        /// 设置数据已展示高亮
        model.hasShowHighlight = YES;
        for (TTQCommentModel *review in model.referenceds) {
            review.hasShowHighlight = YES;
        }
        NSArray *cells = [self.tableView visibleCells];
        [cells bk_each:^(UITableViewCell *obj) {
            if ([obj respondsToSelector:@selector(hideWarmreviewHighlighted)]) {
                [obj performSelector:@selector(hideWarmreviewHighlighted)];
            }
        }];
    });
}

#pragma mark - 回复


/// 是否是空评论， 子类需要根据实际情况重载
- (BOOL)isEmptyComment{
    return self.viewModel.dataSource.count == 0;
}

- (BOOL)commentEnable {
    if ([TTQABTestConfig disableInteractByType:TTQ_Interact_type_comment]) {
        return NO;//互动禁止793
    }
    if ([self judgeBlocked]) {
        return NO;
    }
    if ([IMYRewardAlertView showInWindow]) {
        /// 有弹窗就不响应
        return NO;
    }

    if (self.viewModel.hasBlocked) {//判断禁言778
        TTQTopicCurrentUserInfo *userInfo = self.viewModel.currentUserInfo;
        NSString *message = userInfo.error == 3 ? @"违反圈规被禁言" : (userInfo.error == 2 ? @"违反圈规被封号" : @"违反圈规被禁言");
        [UIAlertController imy_showAlertViewWithTitle:message
                                              message:@"可以到\"帮助与反馈\"里申诉反馈"
                                    cancelButtonTitle:IMYString(@"取消")
                                    otherButtonTitles:@[IMYString(@"去反馈")]
                                              handler:^(UIAlertController *alertController, NSInteger buttonIndex) {
            if (buttonIndex == 1) {
                [[IMYURIManager shareURIManager] runActionWithString:@"meiyou:///qiyu/chat?params=eyJncm91cElkIjogNDgwODE1ODc4fQ=="];
            }
        }];
        return NO;
    }
    return YES;
}

- (void)replyActionAtIndexPath:(NSIndexPath *)indexPath {
    [self replyActionAtIndexPath:indexPath isFromCommentIcon:NO];
}
- (void)replyActionAtIndexPath:(NSIndexPath *)indexPath isFromCommentIcon:(BOOL)isFromCommentIcon{
    if (![self commentEnable]) {
        return;
    }
    if (indexPath) {
        TTQCommentModel *model = [self.viewModel tableCellModelAtIndexPath:indexPath];
        if (!model) {
            /// 没找到评论的
            return;
        }
    }
    [self.inputContentsView restPose];
    self.inputContentsView.scrollIndexPath = indexPath;
    self.inputContentsView.associatedTableView = self.tableView;
    self.viewModel.selectedReplyIndex = indexPath;
    /// 读取未发送的评论缓存
    NSString *cacheCommentContent = [self cacheCommentContentForIndexPath:indexPath];
    BOOL isForwardPost = [self cacheForwardPostForIndexPath:indexPath];
    /// 展示
    [self showInputViewWithContent:cacheCommentContent selectForward:isForwardPost indexPath:indexPath];
}

- (NSString *)cacheCommentContentForIndexPath:(NSIndexPath *)indexPath {
    NSString *cachedContent = nil;
    if (indexPath == nil) {
        self.viewModel.inputDefaultText = [IMYCKInputPlaceholder subCommentPlaceholder];
        if (self.contentType == IMYNewsItemTypeTTQ) {
            cachedContent = [[TTQCommentContentCacheManager sharedManager] contentWithTopicID:[self contentId]];
        } else {
            cachedContent = [[TTQCommentContentCacheManager sharedManager] contentWithCommentID:[self contentId]];
        }
        
    } else {
        TTQCommentModel *comment = [self.viewModel tableCellModelAtIndexPath:indexPath];
        if (comment) {
            self.viewModel.inputDefaultText = [NSString stringWithFormat:@"@%@：", comment.publisher.screen_name];
            cachedContent = [[TTQCommentContentCacheManager sharedManager] contentWithCommentID:comment.commentID];
        }
    }
    self.inputContentsView.textView.placeholder = self.viewModel.inputDefaultText;
    return cachedContent;
}

- (BOOL)cacheForwardPostForIndexPath:(NSIndexPath *)indexPath {
    BOOL forwardPost = NO;
    if (indexPath == nil) {
        if (self.contentType == IMYNewsItemTypeTTQ) {
            forwardPost = [[TTQCommentContentCacheManager sharedManager] isForwardWithTopicID:[self contentId]];
        } else {
            forwardPost = [[TTQCommentContentCacheManager sharedManager] isForwardWithCommentID:[self contentId]];
        }
    } else {
        TTQCommentModel *comment = [self.viewModel.dataSource imy_objectAtIndex:indexPath.section];
        if (comment) {
            forwardPost = [[TTQCommentContentCacheManager sharedManager] isForwardWithCommentID:[self contentId]];
        }
    }
    return forwardPost;
}

- (void)showInputViewWithContent:(NSString *)content selectForward:(BOOL)isForwardPost indexPath:(NSIndexPath *)indexPath {
    if (indexPath) {
        self.inputContentsView.associatedTableView = self.tableView;
        /// 取评论的adapter
        IMYTableViewAdapterModule *module = [[self.tableViewAdapter modules] match:^BOOL(IMYTableViewAdapterModule *element) {
            return [element.key isEqualToString:@"comment"];
        }];
        if (!module) {
            /// 没有就走兜底，正常是最后一个
            module = [self.tableViewAdapter.modules lastObject];
        }
        self.inputContentsView.scrollIndexPath = [module feedsIndexPathByModulePath:indexPath];
    }
    if (content.length) {
        NSAttributedString *attributeStrToShow = [IMYREmoticonManager decodeEmojiText:content attributes:self.inputContentsView.textView.internalTextView.typingAttributes];
        self.inputContentsView.textView.selectedRange = NSMakeRange(attributeStrToShow.length,0);
        self.inputContentsView.textView.internalTextView.attributedText = attributeStrToShow;
    } else {
        self.inputContentsView.textView.internalTextView.attributedText = nil;
    }
    self.inputContentsView.postArticleButton.selected = isForwardPost;
    if (![self.inputContentsView.textView isFirstResponder]) {
        [self.inputContentsView.textView becomeFirstResponder];
    }
    [self.inputContentsView.textView refreshHeight];
}


/// 复制评论内容
/// @param indexPath 评论所在的indexPath
/// 如果indexPath为nil，则不进行任何操作
- (void)copyActionAtIndexPath:(nonnull NSIndexPath *)indexPath {
    if (!indexPath) {
        return ;
    }
    TTQCommentModel *model = [self.viewModel tableCellModelAtIndexPath:indexPath];
    if (!model || ![model isKindOfClass:TTQCommentModel.class]) {
        NSAssert(NO, @"copyActionAtIndexPath: model is not TTQCommentModel");
        return ;
    }
    [IMYEventHelper event:@"qzxq-fz"];
    NSMutableString *string = [NSMutableString new];
    [string appendString:[model.content imyr_replaceEmotionStringByString:@"" withType:IMYRReplaceEmotionStringTypeDynamic]];
    
    string = [NSString ttq_filterYouPlusTag:string];
    IMYSwitchModel *door = [[IMYDoorManager sharedManager] switchForType:@"copy_copyright"];
    NSString *words = door.dataDictionary[@"words"];
    if (words != nil) {
        [string appendString:words];
    }
    [UIPasteboard generalPasteboard].string = string;
    [UIView imy_showTextHUD:IMYString(@"复制成功")];
}
/// 举报评论
/// - Parameter indexPath: 评论的indexPath
- (void)reportActionAtIndexPath:(NSIndexPath *)indexPath {
    //子类重载
    NSAssert(NO, @"reportActionAtIndexPath: should be overridden by subclass");
}

/// 删除评论操作
/// @param indexPath IndexPath of the comment to delete
- (void)deleteActionAtIndexPath:(NSIndexPath *)indexPath {
    //子类重载
    NSAssert(NO, @"deleteActionAtIndexPath: should be overridden by subclass");
}

/// 置顶评论
/// - Parameters:
///   - indexPath: 评论的indexPath
///   - isTop: 是否置顶, YES置顶，NO取消置顶
- (void)pinTopAtIndexPath:(NSIndexPath *)indexPath setTop:(BOOL)isTop{
    //子类重载
    NSAssert(NO, @"pinTopAtIndexPath:setTop: should be overridden by subclass");
}

#pragma mark - 回复请求

- (BOOL)canSendReplyRequest {
    // 点击发送，进行判断是否登录、设置昵称、绑定手机号、加入圈子、封号
    if (![self loginAction]) {
        return NO;
    }
    
    if ([NSString imy_isEmptyString:[IMYPublicAppHelper shareAppHelper].nickName]) {
        [UIWindow imy_showTextHUD:IMYString(@"请先设置你的昵称哦~")];
        [[IMYURIManager shareURIManager] runActionWithString:@"user/nickname"];
        return NO;
    }
    
    if ([IMYAccountCheckService checkPhoneShouldBeBindedWithCompletion:nil]) {
        return NO;
    }

    if (!self.viewModel.canReply) {
        @weakify(self);
        [UIAlertView imy_showAlertViewWithTitle:nil
                                        message:IMYString(@"加入话题所在的圈子后才能回复哦")
                              cancelButtonTitle:IMYString(@"取消")
                              otherButtonTitles:@[IMYString(@"加入圈子")]
                                        handler:^(UIAlertView *alertView, NSInteger buttonIndex) {
            @strongify(self);
            if (buttonIndex == 1) {
                [self.viewModel joinForum];
            }
        }];
        return NO;
    }
    if ([IMYPublicAppHelper shareAppHelper].cannotAccess) {
        @weakify(self);
        NSString *title = IMYString(@"违反平台规范被封号");
        [UIAlertController imy_showAlertViewWithTitle:title message:IMYString(@"可以到”帮助与反馈“里申诉反馈") cancelButtonTitle:IMYString(@"取消") otherButtonTitles:@[IMYString(@"去反馈")] handler:^(UIAlertController *alertController, NSInteger buttonIndex) {
            if (buttonIndex == 1) {
                [[IMYURIManager shareURIManager] runActionWithString:@"meiyou:///qiyu/chat?params=eyJncm91cElkIjogNDgwODE1ODc4fQ=="];
            }
        }];
        
        [self hideKeyboard];
        return NO;
    }
    
    if (![IMYNetState networkEnable]) {
        [UIWindow imy_showTextHUD:MT_Request_NoNetToast];
        return NO;
    }

    return YES;
}

- (void)postReplyWithImages:(nullable NSArray *)imageNames {
    /// 发送评论
    NSAssert(NO, @"子类需要实现postWithImageNames:");
}

- (void)postImagesFailed {
    [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_fssb",@"action":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topicUserID),@"publi_info":@"图片上传失败"} headers:nil completed:nil];
    [IMYErrorTraces postWithType:IMYErrorTraceTypeOthers pageName:NSStringFromClass([UIViewController imy_currentViewControlloer].class) category:IMYErrorTraceCategoryCommunity message:@"社区评论图片上传失败" detail:@{@"message":@"上传失败"}];
    
}

- (void)appendImagesV2Params:(NSMutableDictionary *)params {
    if (params[@"images"]) {
        /// 有图片的才处理
        NSMutableDictionary *imageDic = [NSMutableDictionary dictionaryWithCapacity:1];
        IMYUGCImageObject *image = [self.inputContentsView imageModel];
        IMYVKStickerItemModel *obj = image.userInfo;
        if ([image.userInfo isKindOfClass:NSClassFromString(@"IMYVKStickerItemModel")]) {
            /// 贴纸
            imageDic[@"url"] = obj.url;
            imageDic[@"referer"] = @1;
            imageDic[@"referer_id"] = @(obj.stickerId);
        } else {
            imageDic[@"url"] = [params[@"images"] firstObject];
            imageDic[@"referer"] = @0;
        }
        params[@"images_v2"] = @[imageDic];
    }
}

#pragma mark - 权限判断、操作

- (BOOL)loginAction {
    if (![IMYPublicAppHelper shareAppHelper].hasLogin) {
        [UIWindow imy_showTextHUD:kStatusText_unLogin];
        [[IMYURIManager shareURIManager] runActionWithString:@"login"];
        return NO;
    } else {
        return YES;
    }
}

- (BOOL)judgeBlocked {
  //禁言处理
    if (self.viewModel.currentUserInfo.error == 2 || self.viewModel.currentUserInfo.error == 3) {
        [self judgeBlockedWithUserInfo:self.viewModel.currentUserInfo];
        return YES;
    }
    return NO;
}

- (void)judgeBlockedWithUserInfo:(TTQTopicCurrentUserInfo *)userInfo {
    //禁言处理
    NSString *message = userInfo.error == 3 ? @"违反圈规被禁言": (userInfo.error == 2 ? @"违反圈规被封号" : @"违反圈规被禁言");
    [UIAlertController imy_showAlertViewWithTitle:message message:@"可以到\"帮助与反馈\"里申诉反馈" cancelButtonTitle:IMYString(@"取消") otherButtonTitles:@[IMYString(@"去反馈")] handler:^(UIAlertController *alertController, NSInteger buttonIndex) {
        if (buttonIndex == 1) {
            [[IMYURIManager shareURIManager] runActionWithString:@"meiyou:///qiyu/chat?params=eyJncm91cElkIjogNDgwODE1ODc4fQ=="];
        } else {
            [self resignFirstResponder];
        }
    }];
}



#pragma mark - 分享

- (void)postBIShareWithType:(IMYCoolShareSheetType)type {
    NSString *public_type = @"";
    if (type == IMYCoolShareSheetTypeWeixiTimeline) {
        public_type = @"微信朋友圈";
    } else if (type == IMYCoolShareSheetTypeWeixiSession) {
        public_type = @"微信好友";
    } else if (type == IMYCoolShareSheetTypeQQSpace) {
        public_type = @"QQ空间";
    } else if (type == IMYCoolShareSheetTypeQQSession) {
        public_type = @"QQ好友";
    } else if (type == IMYCoolShareSheetTypeCopyLink) {
        public_type = @"复制链接";
    } else if (type == IMYCoolShareSheetTypeSinaWeibo) {
        public_type = @"微博";
    }
    if (public_type.length == 0) return;
//    NSMutableDictionary *params = [self detailBiDataWithEvent:@"dsq_nrxqy_fxtz" action:2];
//    params[@"public_type"] = public_type;
//    [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
}

- (void)checkScrollWithDelay:(double)delay {
    @weakify(self);
    [NSObject
     imy_asyncBlock:^{
        @strongify(self);
        if (self.tableView.isDragging || self.tableView.isTracking) {
            [self checkScrollWithDelay:delay];
        } else {
            [self startPlayVideo];
        }
    }
     onLevel:IMYQueueLevelMain
     afterSecond:delay
     forKey:@"AD_Video_Scroll"];
}

//进度展示
- (MBProgressHUD *)progressHUD {
    if (_progressHUD == nil) {
        UIWindow *topWindow = [UIWindow imy_getShowTopWindow];
        //        UIView *topWindow = self.view;
        _progressHUD = [MBProgressHUD progressHUDForRound:topWindow];
    }
    return _progressHUD;
}

- (void)setProcessHUD:(CGFloat)progress {
    self.progressHUD.progress = progress;
}

- (void)hidenProgressHUD {
    [UIWindow imy_hideHUD];
    if (_progressHUD) {
        [self.progressHUD removeFromSuperview];
        [self.progressHUD hideAnimated:YES];
        self.progressHUD = nil;
    }
}

#pragma mark - BI

- (void)addCommentBiView {
    /// 为了解决切换排序时，要上报旧排序的退出，和新排序的进入，所以这里对两个排序单独做了曝光view
    self.commentBiViewHot = [[UIView alloc] init];
    self.commentBiViewHot.imy_height = 10;
    self.commentBiViewHot.imy_width = 20;
    [self.tableView addSubview:self.commentBiViewHot];
    self.commentBiViewHot.hidden = YES;
    
    self.commentBiViewHot.imyut_eventInfo.eventName = [NSString stringWithFormat:@"topicDetail_comment_%p",self];
    self.commentBiViewHot.imyut_eventInfo.type = IMYUTExposureTypeReal;
    self.commentBiViewHot.imyut_eventInfo.edgeOffset = UIEdgeInsetsMake(SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT, 0, SCREEN_TABBAR_SAFEBOTTOM_MARGIN + 52, 0);
    @weakify(self);
    void (^commentViewExposureBlock)(BOOL isHot,__kindof UIView *view, NSDictionary *params) = ^(BOOL isHot,__kindof UIView *view, NSDictionary *params) {
        @strongify(self);
        NSInteger entrance = [self.fromURI.params[@"entrance"] integerValue];
        NSMutableDictionary *biParams = [@{@"event":@"dsq_nrxqy_plqll", @"entrance":@(entrance),@"position":@141,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"index":@1} mutableCopy];
        biParams[@"channel_id"] = isHot?@1:@2;
        if ([self isShortContent]) {
            biParams[@"mediaType"] = @3;
        } else if ([self contentType] == IMYNewsItemTypeCommnent) {
            params[@"comment_id"] = @([self contentId]);
            biParams[@"channel_id"] = nil;
        }
        if (view.imyut_eventInfo.isVisible) {
            biParams[@"action"] = @3;
        } else {
            biParams[@"action"] = @4;
        }
        [IMYGAEventHelper postWithPath:@"event" params:biParams headers:nil completed:nil];
    };
    
    [self.commentBiViewHot.imyut_eventInfo setExposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
        commentViewExposureBlock(YES, view, params);
    }];
    
    self.commentBiViewNew = [[UIView alloc] init];
    self.commentBiViewNew.imy_height = 10;
    self.commentBiViewNew.imy_width = 20;
    [self.tableView addSubview:self.commentBiViewNew];
    self.commentBiViewNew.hidden = YES;
    
    self.commentBiViewNew.imyut_eventInfo.eventName = [NSString stringWithFormat:@"topicDetail_comment_new_%p",self];
    self.commentBiViewNew.imyut_eventInfo.type = IMYUTExposureTypeReal;
    self.commentBiViewNew.imyut_eventInfo.edgeOffset = UIEdgeInsetsMake(SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT, 0, SCREEN_TABBAR_SAFEBOTTOM_MARGIN + 52, 0);
    [self.commentBiViewNew.imyut_eventInfo setExposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
        commentViewExposureBlock(NO, view, params);
    }];
    
}

- (void)updateBICommentView {
    /// 已经可以曝光过self.headView
    if (self.viewModel.commentCount && self.captionView.state == IMYCaptionViewStateHidden) {
        self.commentBiViewHot.hidden = YES;
        self.commentBiViewNew.hidden = YES;
        UIView *currentCommentView = self.viewModel.orderByFilter == TTQOrderByFilterLastest? self.commentBiViewNew : self.commentBiViewHot;
        currentCommentView.hidden = NO;
            /// 去除评论的section
        CGRect rect = [self.tableView rectForSection:0];
        currentCommentView.imy_top = MAX(self.tableView.contentOffset.y, rect.origin.y + rect.size.height + 36 + 12);
    } else {
        self.commentBiViewHot.hidden = YES;
        self.commentBiViewNew.hidden = YES;
    }
}



@end
