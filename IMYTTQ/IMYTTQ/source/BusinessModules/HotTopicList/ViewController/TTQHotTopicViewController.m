//
//  TTQHotTopicViewController.m
//  IMYTTQ
//
//  Created by <PERSON><PERSON> on 2017/11/27.
//  Copyright © 2017年 MeiYou. All rights reserved.
//

#import "TTQHotTopicViewController.h"
#import "IMYAccountCheckService.h"
#import "TTQHotTopicListViewController.h"

#import "MXParallaxHeader.h"
#import "MXScrollView.h"
#import "TTQHotTopicInfoViewProtocol.h"
#import "TTQHotTopicInfoViewV2.h"
#import "TTQHotTopicPilotPublishButton.h"
#import "TTQHotTopicPilotPublishView.h"

#import "TTQShareView+TTQCommonShare.h"
#import "UIColor+TTQ.h"
#import "UIFont+TTQ.h"

#import "TTQABTestConfig.h"
#import "TTQDoorConfig.h"
#import <IMYCommonKit/IMYCRefreshHeader.h>
#import "NSString+TTQ.h"
#import "IMYSwipePopAnimatedTransition.h"
#import <IMYUGC/IMYSwipePopAnimatedTransition.h>
#import <IMYUGC/IMYDismissInteractive.h>
#import "IMYUGCFloatPublishView.h"
#import "TTQRelateTopicListViewController.h"
#import "TTQHotTopicGuideView.h"

@interface TTQHotTopicViewController () <IMYPageViewControllerDataSource, IMYPageViewControllerDelegate, MXScrollViewDelegate>
@property (nonatomic, strong) IMYGGesturePassableScrollView *scrollView;
@property (nonatomic, strong) IMYPageViewController *pageViewController;
@property (nonatomic, strong) UIScrollView *pageScrollView; //pageViewController的scroll
@property (nonatomic, strong) TTQHotTopicInfoViewV2 *headerView;
@property (nonatomic, strong) TTQHotTopicPilotPublishView *bottomPublishView;
@property (nonatomic, strong) TTQHotTopicPilotPublishButton *rightPublishButton;
@property (nonatomic, strong) UIButton *joinButton;// 参与按钮
@property (nonatomic, strong) IMYCaptionView *captionView;

@property (nonatomic, strong) UIView *specialNavBar; //假的导航栏
@property (nonatomic, strong) UIView *navBackgroundView;
@property (nonatomic, strong) IMYTouchEXButton *navShareButton;
@property (nonatomic, strong) UILabel *navTitleLabel;
@property (nonatomic, strong) IMYTouchEXButton *navBackButton;
//@property (nonatomic, strong) UIActivityIndicatorView *activityView;
@property (nonatomic, strong) NSMutableArray *controllersArray;
@property (nonatomic, strong) TTQHotTopicInfoModel *hotTopicInfo;

@property (nonatomic, strong) TTQHotTopicListViewModel *startedListViewModel;
@property (nonatomic, assign) NSInteger hotTopicID;
@property (nonatomic, assign) BOOL is_ad_topic;

/// 是否吸顶了
@property (nonatomic, assign) BOOL isSuckToTop;
/// 控制子view滚动
@property (nonatomic, assign) BOOL subScrollViewCanScroll;
/// 控制外部tableView滚动
@property (nonatomic, assign) BOOL backgroundScrollViewCanScroll;

@property (nonatomic, strong) IMYSwipePopAnimatedTransition *dismissAnimator;
@property (nonatomic, strong) IMYDismissInteractive *dismissInteractive;

@end

@implementation TTQHotTopicViewController

- (instancetype)initWithHotTopicID:(NSInteger)hotTopicID {
    if (self = [super init]) {
        self.hotTopicID = hotTopicID;
    }
    return self;
}

- (BOOL)isWhiteNavigationBar {
    return NO;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.backgroundScrollViewCanScroll = YES;
    self.subScrollViewCanScroll = NO;
    self.navigationBarHidden = YES;
    self.view.frame = [[UIScreen mainScreen] bounds];
    // Do any additional setup after loading the view.
    if (self.postionToLatest) {
        self.startedListViewModel = [[TTQHotTopicListViewModel alloc] initWithHotTopicID:self.hotTopicID sortType:TTQHotTopicSortNew];
    } else {
        self.startedListViewModel = [[TTQHotTopicListViewModel alloc] initWithHotTopicID:self.hotTopicID sortType:TTQHotTopicSortHot];
    }
    self.startedListViewModel.entrance = self.entrance;
    self.startedListViewModel.is_ad_topic = self.is_ad_topic;
    self.startedListViewModel.redirect_type = self.redirect_type;
    if (self.captionView.retryBlock) {
        self.captionView.retryBlock();
    }
    [self specialNavBar];
    [self updateStatusBarStyle];
}

- (void)initTransitionIfNeeded {
    if (self.fromPublish && !_dismissAnimator) {
        [self dismissAnimator];
        self.dismissInteractive = [IMYDismissInteractive new];
        [self.dismissInteractive wireToViewController:self];
        self.navigationController.transitioningDelegate = self;
        if (self.navigationController.viewControllers.firstObject != self) {
            NSMutableArray *viewControllers = [NSMutableArray array];
            [viewControllers addObject:self];
            self.navigationController.viewControllers = viewControllers;
        }
    }
}


- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

- (void)viewWillLayoutSubviews {
    //    self.view.frame = [[UIScreen mainScreen] bounds];
}

- (void)updateStatusBarStyle {
    if ([IMYPublicAppHelper shareAppHelper].isNight) {
        self.statusBarStyle = UIStatusBarStyleLightContent;
    } else {
        self.statusBarStyle = UIStatusBarStyleDefault;
    }
    [[UIApplication sharedApplication] setStatusBarStyle:self.statusBarStyle];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    if ([self imy_isPop] && !self.hotTopicInfo.is_favorite) {
        [[NSNotificationCenter defaultCenter] postNotificationName:@"TTQSubjectCollectCancle" object:@(self.hotTopicInfo.hotTopicId)];
    }
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    TTQHotTopicListViewController *vc = [self controllerAtIndex:self.headerView.currentIndex];
    if ([vc isKindOfClass:TTQHotTopicListViewController.class]) {
        [vc checkVideoPlayWhenAppear];
    }
    [self initTransitionIfNeeded];
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    TTQHotTopicListViewController *vc = [self controllerAtIndex:self.headerView.currentIndex];
    if ([vc isKindOfClass:TTQHotTopicListViewController.class]) {
        [vc pauseVideoPlayWhenDisappear];
    }
    if (self.imy_isPop && !self.hotTopicInfo.is_favorite) {
    }
}

#pragma mark - setup view
- (IMYCaptionView *)captionView {
    if (_captionView == nil) {
        _captionView = [[IMYCaptionView alloc] initWithFrame:CGRectMake(0, SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT, self.view.imy_width, self.view.imy_height - SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT)];
        [self.view addSubview:_captionView];
        [_captionView setStateView:[[IMYCKLoadingView alloc] initWithtype:IMYCKLoadingDetail] forState:IMYCaptionViewStateLoading];
        _captionView.state = IMYCaptionViewStateLoading;
        @weakify(self);
        [_captionView setRetryBlock:^{
            @strongify(self);
            self.captionView.state = IMYCaptionViewStateLoading;
            [self requestRemoteDataForType:0 params:nil];
        }];
    }
    return _captionView;
}

- (UIView *)specialNavBar {
    if (_specialNavBar == nil) {
        _specialNavBar = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT)];
        [_specialNavBar imy_lineViewWithDirection:IMYDirectionDown show:YES margin:0];

        UIView *bgView = [[UIView alloc] initWithFrame:_specialNavBar.bounds];
        [bgView imy_setBackgroundColorForKey:kCK_Black_F];
        [_specialNavBar addSubview:bgView];
        self.navBackgroundView = bgView;

        CGRect boxFrame = CGRectMake(15, SCREEN_STATUSBAR_HEIGHT, 65, SCREEN_NAVIGATIONBAR_HEIGHT);

        // 返回按钮
        IMYTouchEXButton *button = [[IMYTouchEXButton alloc] initWithFrame:boxFrame];
        [button imy_setTitleColor:kCK_Black_A highl:kIMY_TopbarButtonTitleHighlightedColor];
        button.titleEdgeInsets = UIEdgeInsetsMake(0, -3.5, 0, 0);
        [button setTitle:@"\U0000e6f3" forState:UIControlStateNormal];
        [button.titleLabel setFont:[UIFont ttqIconFontWith:22.0]];
        button.contentHorizontalAlignment = UIControlContentHorizontalAlignmentLeft;
        [button addTarget:self action:@selector(imy_topLeftButtonTouchupInside) forControlEvents:UIControlEventTouchUpInside];
        self.navBackButton = button;
        [_specialNavBar addSubview:self.navBackButton];
        

        // 标题
        UILabel *label = [[UILabel alloc] initWithFrame:CGRectMake(50, button.imy_top, SCREEN_WIDTH - 2 * 50, button.imy_height)];
        label.lineBreakMode = NSLineBreakByTruncatingMiddle;
        [label imy_setTextColorForKey:kCK_Black_A];
        [label setFont:[UIFont ttqFontWith:20]];
        label.textAlignment = NSTextAlignmentCenter;
        [label imy_setText:self.hotTopicInfo.title];
        label.alpha = 0;
        [_specialNavBar addSubview:label];
        self.navTitleLabel = label;
        [_specialNavBar bringSubviewToFront:button];

        CGFloat rightValue = 0;

        if (![TTQDoorConfig isDisableShare]) {
            // 分享按钮
            IMYTouchEXButton *navShareButton = [[IMYTouchEXButton alloc] initWithFrame:boxFrame];
            [navShareButton addTarget:self action:@selector(shareButtonAction:) forControlEvents:UIControlEventTouchUpInside];
            [navShareButton imy_setImageForKey:@"nav_share_white" andState:UIControlStateNormal];
            [navShareButton imy_setImageForKey:@"nav_share_black" andState:UIControlStateSelected];
            navShareButton.imy_right = _specialNavBar.imy_width;
            navShareButton.imy_centerY = boxFrame.origin.y + boxFrame.size.height / 2;
            navShareButton.hidden = YES;
            [_specialNavBar addSubview:navShareButton];
            self.navShareButton = navShareButton;

            rightValue = navShareButton.imy_left;
        } else {
            rightValue = _specialNavBar.imy_width - 15;
        }
        [self.view addSubview:_specialNavBar];
    }
    return _specialNavBar;
}

- (void)imy_topLeftButtonTouchupInside {
    if (self.fromPublish) {
        [self dismissModalViewControllerAnimated:YES];
    } else {
        [self imy_pop:YES];
    }
}

- (void)publishTopic {
    if ([TTQABTestConfig disableInteractByType:TTQ_Interact_type_post]) {
        return;
    }
    NSDictionary *uparams = @{@"event" : @"ryht_cytl",
                             @"action" : @(2),
                             @"info_type" : @(31),
                             @"info_id" : @(self.hotTopicInfo.hotTopicId), // 内容id
    };
    [IMYGAEventHelper postWithPath:@"event" params:uparams headers:nil completed:nil];
    
    [IMYEventHelper event:@"ryht-fb" attributes:@{@"来源": (self.hotTopicInfo.post.style == 1 ? @"发布按钮" : @"底部加入讨论")}];
    [[IMYURIManager shareURIManager] runActionWithPath:@"circles/publish"
                                                params:@{@"inletSource": @"她她圈热门话题详情页",
                                                         @"subject_id": @(self.hotTopicInfo.hotTopicId),
                                                         @"subject_name": self.hotTopicInfo.title ?: @"",
                                                         @"taskID" : @(self.taskID),
                                                         @"taskType" : @(self.taskType),
                                                         @"publish_entrance":@([self publishEntranceId]),
                                                         @"experience_id": self.hotTopicInfo.isExperience?[NSString stringWithFormat:@"%@", @(self.hotTopicInfo.hotTopicId)]:@""
                                                         }
                                                  info:nil];
    [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"TopicPublish_entrance",@"publish_entrance":@([self publishEntranceId]),@"action":@(2)} headers:nil completed:nil];
}

- (void)touchCardAction {
    [[IMYURIManager shareURIManager] runActionWithString:self.hotTopicInfo.card.redirect_url];
    if (self.startedListViewModel.redirectSource) {
        [TTQCommonHelp GAEventForInformationWithURL:self.hotTopicInfo.card.redirect_url
                                              floor:0
                                             action:2
                                             params:@{@"entrance": self.startedListViewModel.redirectSource == TTQHotTopicListSourceHome ? @(1) : @(2)}];
    } else {
    }
}

- (void)shareButtonAction:(UIButton *)button {
    if ([IMYNetState networkEnable] == NO) {
        [UIView imy_showTextHUD:kStatusText_networkDisconnectCache];
        return;
    }

    if (!self.startedListViewModel.hotTopicInfo.share_body) {
        return;
    }

    TTQCommonShareModel *shareItem = [TTQCommonShareModel new];
    NSMutableString *shareUrl = nil;
    if (self.startedListViewModel.hotTopicInfo.share_body.share_url) {
        shareUrl = self.startedListViewModel.hotTopicInfo.share_body.share_url.mutableCopy;
        if ([shareUrl containsString:@"?"]) {
            [shareUrl appendString:[NSString stringWithFormat:@"&redirect_type=%@", @(self.startedListViewModel.redirect_type)]];
        } else {
            [shareUrl appendString:[NSString stringWithFormat:@"?redirect_type=%@", @(self.startedListViewModel.redirect_type)]];
        }
    }
    shareItem.shareUrl = shareUrl;
    ;
    shareItem.imageSrc = self.startedListViewModel.hotTopicInfo.share_body.src;
    shareItem.title = self.startedListViewModel.hotTopicInfo.share_body.title;
    shareItem.content = self.startedListViewModel.hotTopicInfo.share_body.content;
    shareItem.subjectId = self.startedListViewModel.hotTopicInfo.hotTopicId;
    @weakify(self);
    shareItem.shareResultItemBlock = ^(TTQDetailShareBtnType shareType, NSString * _Nonnull title) {
        @strongify(self);
        NSDictionary *uparams = @{@"event" : @"ryht_fx",
                                 @"action" : @(2),
                                 @"info_type" : @(31),
                                 @"info_id" : @(self.hotTopicInfo.hotTopicId), // 内容id
                                  @"public_type" : title,
        };
        [IMYGAEventHelper postWithPath:@"event" params:uparams headers:nil completed:nil];
    };

    [TTQShareView commonShareViewWith:shareItem];
}


#pragma mark -

- (void)setupPilotPublishButton {
    @weakify(self);
    self.rightPublishButton = [TTQHotTopicPilotPublishButton defaultHotTopicPilotPublishButtonWith:^{
        @strongify(self);
        [self publishTopic];
    }];
    [self.view addSubview:self.rightPublishButton];
    [self.rightPublishButton mas_makeConstraints:^(MASConstraintMaker *make) {
        @strongify(self);
        make.right.mas_equalTo(-30);
        make.bottom.mas_equalTo(-30 - SCREEN_TABBAR_SAFEBOTTOM_MARGIN);
        make.width.and.height.mas_equalTo(self.rightPublishButton.imy_height);
    }];
}

- (void)setupPilotPublishView {
    @weakify(self);
    self.bottomPublishView = [TTQHotTopicPilotPublishView defaultHotTopicPilotPublishView:self.hotTopicInfo
                                                                                andAction:^{
                                                                                    @strongify(self);
                                                                                    [self publishTopic];
                                                                                }];
    [self.view addSubview:self.bottomPublishView];
    [self.bottomPublishView mas_makeConstraints:^(MASConstraintMaker *make) {
        @strongify(self);
        make.left.right.and.bottom.equalTo(self.view);
        make.height.mas_equalTo(self.bottomPublishView.imy_height);
    }];
}

- (void)setupBottomJoinButton {// 参与按钮
    if(self.joinButton != nil){
        return;
    }
    self.joinButton = [[UIButton alloc] initWithFrame:CGRectMake(0, 0, 64, 64)];
    [self.joinButton imy_setImage:@"content_float_icon_publish"];
    [self.joinButton addTarget:self action:@selector(publishTopic) forControlEvents:UIControlEventTouchUpInside];
    // TODO: 设置image和title 的insect
    [self.view addSubview:self.joinButton];
    [self.joinButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(64, 64));
        make.right.equalTo(self.view).offset(-12);
        make.bottom.equalTo(self.view).offset(-SCREEN_TABBAR_SAFEBOTTOM_MARGIN-75);
    }];
    self.joinButton.imyut_eventInfo.eventName = @"ttq_topic_hot_joinButton";
    self.joinButton.imyut_eventInfo.showRadius = 1;
    self.joinButton.imyut_eventInfo.ableToClean = YES;
    @weakify(self);
    [self.joinButton.imyut_eventInfo setExposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
        @strongify(self);
        NSDictionary *uparams = @{@"event" : @"ryht_cytl",
                                 @"action" : @(1),
                                 @"info_type" : @(31),
                                 @"info_id" : @(self.hotTopicInfo.hotTopicId), // 内容id
        };
        [IMYGAEventHelper postWithPath:@"event" params:uparams headers:nil completed:nil];
        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"TopicPublish_entrance",@"publish_entrance":@([self publishEntranceId]),@"action":@(1)} headers:nil completed:nil];
    }];
}

- (void)setupSubviewFrame {
    [self setNavigationBarColor:0];
    [self.navTitleLabel imy_setText:[NSString stringWithFormat:@"#%@", self.hotTopicInfo.title]];

    if (self.rightPublishButton) {
        [self.view bringSubviewToFront:self.rightPublishButton];
        self.scrollView.frame = self.view.frame;
    } else if (self.bottomPublishView) {
        [self.view bringSubviewToFront:self.bottomPublishView];
        self.scrollView.frame = self.view.frame;
        self.scrollView.imy_height -= self.bottomPublishView.imy_height;
    } else if (self.joinButton) {
        [self.view bringSubviewToFront:self.joinButton];
        self.scrollView.frame = self.view.frame;
    }

    CGRect frame = self.scrollView.bounds;
    frame.origin.y = self.headerView.imy_bottom;
    frame.size.height -= (SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT + 44);
    self.pageViewController.view.frame = frame;
    self.scrollView.showsVerticalScrollIndicator = NO;
    self.scrollView.contentSize = CGSizeMake(0, self.headerView.imy_height + self.pageViewController.view.imy_height);

    
}

- (void)prepareUI {
    [self.view imy_setBackgroundColorForKey:kCK_Black_F];
//    [self setupEffectView:self.hotTopicInfo];
    @weakify(self);
    TTQHotTopicInfoViewV2 *headerView =
        [[TTQHotTopicInfoViewV2 alloc] initWithHotInfo:self.hotTopicInfo
            andAction:^(NSInteger index) {
                @strongify(self);
                TTQHotTopicListViewController *vc = [self controllerAtIndex:index];
                [self deselectedController:vc];
                [self.pageViewController setViewControllerAtIndex:index animated:YES];
            }
            cardAction:^(void) {
                @strongify(self);
                [self touchCardAction];
            }
            andFrameChange:^(CGFloat infoHeight) {
                @strongify(self);
                [self.headerView.bgEffectBgView mas_updateConstraints:^(MASConstraintMaker *make) {
                    @strongify(self);
                    make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, self.headerView.curOriHeight - self.headerView.oriIconBottomOffsetY, 0));
                }];
                self.headerView.imy_height = infoHeight;
                [self setNavigationBarColor:self.scrollView.ttq_contentOffsetY];
                self.scrollView.contentSize = CGSizeMake(0, self.headerView.imy_height + self.pageViewController.view.imy_height);
            }];
    [[headerView.collcectButton rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
        @strongify(self);
        [self collectTopicAction];
    }];
    self.headerView = headerView;
    [self setupBottomJoinButton];

    [self.view addSubview:self.headerView.bgEffectBgView];
    
    [self.headerView.bgEffectBgView mas_makeConstraints:^(MASConstraintMaker *make) {
        @strongify(self);
        make.left.right.and.top.bottom.mas_offset(0);
//        make.height.mas_equalTo(imageSize.height);
    }];
    self.headerView.frame = CGRectMake(0, 0, SCREEN_WIDTH, self.headerView.curOriHeight);

    self.scrollView = [[IMYGGesturePassableScrollView alloc] init];

    [self.view imy_setBackgroundColorForKey:kCK_Black_F];
    self.scrollView.backgroundColor = [UIColor clearColor];
    [self.view addSubview:self.scrollView];
    self.scrollView.delegate = self;

    [self.scrollView addSubview:self.headerView];

    [self.view bringSubviewToFront:self.specialNavBar];
    [self.specialNavBar imy_lineViewWithDirection:IMYDirectionDown show:NO margin:0];
    [self setupSubviewFrame];
    [self.scrollView addSubview:self.pageViewController.view];//iOS10 需要先设置self.pageViewController.view.frame  否则pageView里的子页面无frame会加载不出
    [self addChildViewController:self.pageViewController];
    
    IMYCRefreshHeader *refreshHeader = [IMYCRefreshHeader headerWithRefreshingBlock:^{
        @strongify(self);
        //卡片化内容
        [self refreshAction:self.scrollView.ttq_contentOffsetY];
    }];
    refreshHeader.isMXHeader = YES;
    self.scrollView.mj_header = refreshHeader;
    self.scrollView.mj_header.ignoredScrollViewContentInsetTop = -self.specialNavBar.imy_height;
//    self.scrollView.mj_header.layer.zPosition = 1;
    [self.scrollView bringSubviewToFront:self.scrollView.mj_header];
    
    UIView *publishProgressView = [IMYUGCFloatPublishView floatViewInController:self];
    if (publishProgressView) {
        [self.view bringSubviewToFront:publishProgressView];
    }
}

- (void)refreshUI {
    //加磨砂
    @weakify(self);
    [self.headerView updateWith:self.hotTopicInfo
        andAction:^(NSInteger index) {
            @strongify(self);
            TTQHotTopicListViewController *vc = [self controllerAtIndex:index];
            [self deselectedController:vc];
            [self.pageViewController setViewControllerAtIndex:index animated:YES];
//            [self.activityView stopAnimating];
        }
        cardAction:^(void) {
            @strongify(self);
            [self touchCardAction];
        }];
    
    self.headerView.imy_height = self.headerView.curOriHeight;
    self.scrollView.mj_header.ignoredScrollViewContentInsetTop = -self.specialNavBar.imy_height;
}

- (void)setNavigationBarColor:(CGFloat)offset {
    CGFloat alpha = 0;
    CGFloat topOffset = self.headerView.imy_height - SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT - 44;
    if (offset >= topOffset) {
        alpha = 1;
    } else if (offset > 0) {
        alpha = offset/topOffset;
        alpha = MAX(alpha, 0);
        alpha = MIN(alpha, 1);
    } else {
        alpha = 0;
    }

    self.navTitleLabel.alpha = alpha;
    [self.navBackButton imy_setTitleColor:[UIColor ttq_mixColor1:[UIColor imy_colorForKey:kCK_White_A] color2:[UIColor imy_colorForKey:kCK_Black_A] ratio:1 - alpha]];
    if(alpha == 1){
        self.navShareButton.selected = YES;
        [self.headerView changeMenuTabBgCorner:NO];
    } else {
        self.navShareButton.selected = NO;
        [self.headerView changeMenuTabBgCorner:YES];
    }
//    [self.navShareButton imy_setTitleColor:[UIColor ttq_mixColor1:[UIColor imy_colorForKey:kCK_White_A] color2:[UIColor imy_colorForKey:kCK_Black_A] ratio:1 - alpha]];
    self.navBackgroundView.alpha = alpha;

    if (alpha > 0.6) {
        if (self.statusBarStyle != UIStatusBarStyleDefault) {
            self.statusBarStyle = UIStatusBarStyleDefault;
            [[UIApplication sharedApplication] setStatusBarStyle:self.statusBarStyle];
        }
    } else {
        if (self.statusBarStyle != UIStatusBarStyleLightContent) {
            self.statusBarStyle = UIStatusBarStyleLightContent;
            [[UIApplication sharedApplication] setStatusBarStyle:self.statusBarStyle];
        }
    }
}

#pragma mark - 控制器操作

- (void)loadViewControllers {
    [self.controllersArray removeAllObjects];
    if (self.controllersArray == nil) {
        self.controllersArray = [NSMutableArray array];
    }
    
    TTQHotTopicListViewModel *hotViewModel = nil;
    TTQHotTopicListViewModel *newViewModel = nil;
    if (self.postionToLatest) {
        // 定位到最新，初始化的ViewModel为最新
        hotViewModel = [[TTQHotTopicListViewModel alloc] initWithHotTopicID:self.hotTopicID sortType:TTQHotTopicSortHot];
        hotViewModel.is_ad_topic = self.is_ad_topic;
        hotViewModel.enter_time = self.startedListViewModel.enter_time;
        hotViewModel.redirect_type = self.startedListViewModel.redirect_type;
        newViewModel = self.startedListViewModel;
    } else {
        // 没有定位到最新，初始化的ViewModel为最热
        hotViewModel = self.startedListViewModel;
        newViewModel = [[TTQHotTopicListViewModel alloc] initWithHotTopicID:self.hotTopicID sortType:TTQHotTopicSortNew];
        newViewModel.is_ad_topic = self.is_ad_topic;
        newViewModel.enter_time = self.startedListViewModel.enter_time;
        newViewModel.redirect_type = self.startedListViewModel.redirect_type;
    }
    newViewModel.entrance = self.entrance;
    hotViewModel.entrance = self.entrance;
    TTQHotTopicListViewController *hotVC = [[TTQHotTopicListViewController alloc] initWithViewModel:hotViewModel];
    @weakify(self);
    hotVC.didScrollToTop = ^{
        @strongify(self);
        UIScrollView *scrollView = self.scrollView;
        [scrollView setContentOffset:CGPointMake(0, -scrollView.contentInset.top)
                            animated:YES];
        if ([scrollView.delegate
                respondsToSelector:@selector(scrollViewDidScrollToTop:)]) {
            [scrollView.delegate scrollViewDidScrollToTop:scrollView];
        }
    };
    [hotVC setDidScrollBlock:^(UIScrollView *scrollView) {
        @strongify(self);
        if (self.pageViewController.currentPageIndex != 0) {
            return;
        }
        if (!self.subScrollViewCanScroll) {
            scrollView.contentOffset = CGPointMake(0, -scrollView.contentInset.top);
        } else if (scrollView.contentOffset.y < 0) {
            self.subScrollViewCanScroll = NO;
            self.backgroundScrollViewCanScroll = YES;
        }
    }];
    if (self.topic_id > 0) {
        hotVC.topic_id = self.topic_id;
    }

    
    TTQHotTopicListViewController *newVC = [[TTQHotTopicListViewController alloc] initWithViewModel:newViewModel];
    if(self.postionToLatest){
        newVC.hasRequested = YES;
    } else {
        hotVC.hasRequested = YES; // 第一刷已经请求了
    }
    @weakify(newVC);
    newVC.didScrollToTop = ^{
        @strongify(self,newVC);
        if (self.pageViewController.viewControllers.firstObject != newVC) {
            /// 当前tab才处理
            return;
        }
        UIScrollView *scrollView = self.scrollView;
        [scrollView setContentOffset:CGPointMake(0, -scrollView.contentInset.top)
                            animated:YES];
        if ([scrollView.delegate
                respondsToSelector:@selector(scrollViewDidScrollToTop:)]) {
            [scrollView.delegate scrollViewDidScrollToTop:scrollView];
        }
    };
    [newVC setDidScrollBlock:^(UIScrollView *scrollView) {
        @strongify(self);
        if (self.pageViewController.currentPageIndex != 1) {
            return;
        }
        if (!self.subScrollViewCanScroll) {
            scrollView.contentOffset = CGPointMake(0, -scrollView.contentInset.top);
        } else if (scrollView.contentOffset.y < 0) {
            self.subScrollViewCanScroll = NO;
            self.backgroundScrollViewCanScroll = YES;
        }
    }];

    TTQRelateTopicListViewController *relateVC = [TTQRelateTopicListViewController new];
    relateVC.subject_name = self.hotTopicInfo.title;
    relateVC.entrance = self.entrance;
    [relateVC setDidScrollBlock:^(UIScrollView *scrollView) {
        @strongify(self);
        if (self.pageViewController.currentPageIndex != 2) {
            return;
        }
        if (!self.subScrollViewCanScroll) {
            scrollView.contentOffset = CGPointMake(0, -scrollView.contentInset.top);
        } else if (scrollView.contentOffset.y < 0) {
            self.subScrollViewCanScroll = NO;
            self.backgroundScrollViewCanScroll = YES;
        }
    }];

    [self.controllersArray addObjectsFromArray:@[hotVC, newVC, relateVC]];
    [self.pageViewController setViewControllerAtIndex:0 animated:NO];
    // bugfix这里结束后需要重新设置tableview的高度，不然是屏幕高度，会造成列表显示不完整
    hotVC.tableView.imy_height = self.view.imy_height - SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT - 44;
    newVC.tableView.imy_height = self.view.imy_height - SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT - 44;
    newVC.tableView.autoresizingMask = UIViewAutoresizingNone;
    hotVC.tableView.autoresizingMask = UIViewAutoresizingNone;
    self.pageScrollView.bouncesZoom = false;
}

#pragma mark - 请求
- (RACSignal *)requestRemoteDataForType:(NSInteger)type params:(NSDictionary *)parameters {
    @weakify(self);
    NSMutableDictionary *input = [NSMutableDictionary new];
    [input setValuesForKeysWithDictionary:parameters];
    input[@"requestRemoteDataForType"] = @(type);
    if (self.topic_id) {
        input[@"topic_id"] = @(self.topic_id);
    }
    return [[[[[self.startedListViewModel.requestRemoteDataCommand execute:input] deliverOnMainThread] doNext:^(id<IMYHTTPResponse> response) {
        @strongify(self);
        [self finishedRequest:nil];
    }] doError:^(NSError *error) {
        @strongify(self);
        if (![error.domain isEqualToString:RACCommandErrorDomain]) {
            [self finishedRequest:error];
        }
    }] replay];
};

- (void)finishedRequest:(NSError *)error {
    if (error) {
        if (error.code % 1000 == 105) {
            [UIView imy_showTextHUD:IMYString(@"话题不存在")];
            @weakify(self);
            imy_asyncMainBlock(0.5, ^{
                @strongify(self);
                [self imy_pop:YES];
            });
        } else {
            self.navShareButton.hidden = YES;
            if ([IMYNetState networkEnable]) {
                [self.captionView setTitle:MT_Request_Retry andState:IMYCaptionViewStateRetry];
            } else {
                [self.captionView setTitle:MT_Request_NoNet andState:IMYCaptionViewStateRetry];
            }
        }

        return;
    }
    self.hotTopicInfo = self.startedListViewModel.hotTopicInfo;
    
    [self prepareUI];

    [self loadViewControllers];
    
    // 定位到最新Tab
    if (self.postionToLatest) {
        self.headerView.currentIndex = 1;
        TTQHotTopicListViewController *vc = [self controllerAtIndex:1];
        if (vc) {
            [self.pageViewController setViewControllerAtIndex:1 animated:NO];
        }
    }
    
    if (self.hotTopicInfo == nil) {
        self.captionView.state = IMYCaptionViewStateNoResult;
        [self.view bringSubviewToFront:self.specialNavBar];
        [self setNavigationBarColor:200];
        self.navTitleLabel.hidden = YES;
        self.navShareButton.hidden = YES;
    } else {
        self.navTitleLabel.hidden = NO;
        self.navShareButton.hidden = NO;
        self.captionView.state = IMYCaptionViewStateHidden;
    }
    
    [self imyut_pageDidShowed];
    
    [self checkVideoToPlay];
    
    [self showGuidViewIfNeeded];

}

#pragma mark - pageViewController
- (IMYPageViewController *)pageViewController {
    if (_pageViewController == nil) {
        _pageViewController = [[IMYPageViewController alloc] init];
        _pageViewController.view.backgroundColor = [UIColor imy_colorForKey:kCK_Black_F];
        _pageViewController.dataSource = self;
        _pageViewController.delegate = self;
        _pageViewController.scrollView.bounces = NO;
    }
    return _pageViewController;
}

- (UIScrollView *)pageScrollView {
    if (_pageScrollView == nil) {
        _pageScrollView = [self.pageViewController.view imy_findSubviewWithClass:[UIScrollView class]];
    }
    return _pageScrollView;
}

- (TTQHotTopicListViewController *)controllerAtIndex:(NSInteger)index {
    if (index < 0 || index > self.controllersArray.count) {
        return nil;
    }
    TTQHotTopicListViewController *ctr = self.controllersArray[index];
    return ctr;
}

- (void)deselectedController:(TTQHotTopicListViewController *)ctr {
    NSIndexPath *seleted = [ctr.tableView indexPathForSelectedRow];
    if (seleted) {
        [ctr.tableView deselectRowAtIndexPath:seleted animated:NO];
    } else {
        NSArray *cells = [ctr.tableView visibleCells];
        for (UITableViewCell *cell in cells) {
            cell.selected = NO;
            cell.highlighted = NO;
        }
    }
}

#pragma mark-- IMYPageViewControllerDataSource
- (UIViewController *)pageViewController:(IMYPageViewController *)pageViewController controllerAtIndex:(NSUInteger)index {
    TTQHotTopicListViewController *ctr = [self controllerAtIndex:index];
//    [self deselectedController:ctr];

    return ctr;
}

- (NSUInteger)numberOfControllersInPageViewController:(IMYPageViewController *)pageViewController {
    return self.controllersArray.count;
}

#pragma mark-- IMYPageViewControllerDelegate
- (void)pageViewController:(IMYPageViewController *)pageViewController didTransitionFromIndex:(NSUInteger)fromIndex toIndex:(NSUInteger)toIndex {
    UIViewController *previousVC = self.pageViewController[fromIndex];
    if (previousVC) {
        [self deselectedController:(TTQHotTopicListViewController *)previousVC];
    }
    UIViewController *vc = [[self.pageViewController viewControllers] firstObject];
    if (vc) {
        NSUInteger index = [self.controllersArray indexOfObject:vc];
        if (index != NSUIntegerMax) {
            self.headerView.currentIndex = index;
//            [self.activityView stopAnimating];
        }
    }
    if (fromIndex != toIndex) {
        TTQHotTopicListViewController *toVc = self.controllersArray[toIndex];
        [toVc refreshIfNeeded];
        NSMutableDictionary *params = [@{@"event":@"dsq_ryht_tabqh",@"action":@2,@"info_type":@31,@"info_id":@(self.hotTopicID)} mutableCopy];
        NSString *public_info = @"最热tab";
        if (toIndex == 1) {
            public_info = @"最新tab";
        } else if (toIndex == 2) {
            public_info = @"相似话题tab";
        }
        params[@"public_info"] = public_info;
        [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
    }
}

- (void)pageViewController:(IMYPageViewController *)pageViewController willTransitionFromIndex:(NSUInteger)fromIndex toIndex:(NSUInteger)toIndex {
    if (fromIndex != toIndex) {
        if (self.scrollView.contentOffset.y < (self.headerView.imy_height - 44 - SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT)) {
            TTQHotTopicListViewController *toVc = self.controllersArray[toIndex];
            [[toVc tableView] setContentOffset:CGPointMake(0, 0) animated:NO];
        }
    }
}


#pragma mark MXScrollViewDelegate
- (BOOL)scrollView:(MXScrollView *)scrollView shouldScrollWithSubView:(UIScrollView *)subView {
    if ([subView isEqual:self.pageScrollView]) {
        return NO;
    }
    return YES;
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    //    self.pageScrollView.panGestureRecognizer.enabled = NO;
    [self setNavigationBarColor:scrollView.ttq_contentOffsetY];
    BOOL isSuckToTop = NO;
    CGFloat contentOffset = self.headerView.imy_height - 44 - SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT;
    if (contentOffset < 1) {
        return;
    }
    UIScrollView *currentPageScrollView = nil;
    if (self.controllersArray.count) {
        currentPageScrollView = [self.controllersArray[self.pageViewController.currentPageIndex] tableView];
    }
    if (!self.backgroundScrollViewCanScroll) {
        isSuckToTop = YES;
        scrollView.contentOffset = CGPointMake(0, contentOffset);
        self.subScrollViewCanScroll = YES;
    } else if (scrollView.contentOffset.y >= contentOffset) {
        isSuckToTop = YES;
        scrollView.contentOffset = CGPointMake(0, contentOffset);
        if (currentPageScrollView.contentSize.height > currentPageScrollView.imy_height) {
            self.backgroundScrollViewCanScroll = NO;
            // 允许信息流scrollView滚动
            self.subScrollViewCanScroll = YES;
        }
    }
    [self checkVideoToPlay];
}
- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView {
    self.pageScrollView.panGestureRecognizer.enabled = TRUE;
    if (scrollView == self.pageViewController.scrollView) {
        return;
    }
    UIScrollView *subScrollView = [self.controllersArray[self.pageViewController.currentPageIndex] tableView];
    if (subScrollView.contentOffset.y <= -subScrollView.contentInset.top) {
        self.subScrollViewCanScroll = NO;
        self.backgroundScrollViewCanScroll = YES;
    }

}
- (void)scrollViewDidEndDragging:(UIScrollView *)scrollView willDecelerate:(BOOL)decelerate {
    self.pageScrollView.panGestureRecognizer.enabled = TRUE;
    if (scrollView == self.pageViewController.scrollView) {
        return;
    }
    if (decelerate == NO) {
        UIScrollView *subScrollView = [self.controllersArray[self.pageViewController.currentPageIndex] tableView];
        if (subScrollView.contentOffset.y <= -subScrollView.contentInset.top) {
            self.subScrollViewCanScroll = NO;
            self.backgroundScrollViewCanScroll = YES;
        }
    }

}

- (void)scrollViewDidScrollToTop:(UIScrollView *)scrollView {
    TTQHotTopicListViewController *vc = [self controllerAtIndex:self.headerView.currentIndex];
    [vc.tableView setContentOffset:CGPointZero];
}

#pragma mark - 刷新请求
- (void)refreshAction:(CGFloat)offsetY {
    TTQHotTopicListViewController *ctr = [self controllerAtIndex:self.headerView.currentIndex];
    @weakify(self, ctr);
    if (!self.headerView) {
        return;
    }
    if (ctr.viewModel.isRefreshIng) {
        return;
    }
    [[ctr refreshForSignal:NO]
        subscribeNext:^(id x) {
            @strongify(self, ctr);
            TTQHotTopicListViewController *curCtr = [self controllerAtIndex:self.headerView.currentIndex];
            if (!curCtr.viewModel.isRefreshIng || curCtr == ctr) {
                [self.scrollView.mj_header endRefreshing];
                if ([ctr isKindOfClass:TTQHotTopicListViewController.class]) {
                    curCtr.viewModel.hotTopicInfo = ctr.viewModel.hotTopicInfo;
                }
            }
            if ([ctr isKindOfClass:TTQHotTopicListViewController.class]) {
                self.hotTopicInfo = ctr.viewModel.hotTopicInfo;
            }
        CGFloat delayTime = 0.5;
        if(self.scrollView.contentOffset.y > -self.scrollView.contentInset.top){
            delayTime = 0;
        }
        imy_asyncMainBlock(delayTime, ^{
            @strongify(self);
            [self refreshUI];
        });
        }
        error:^(NSError *error) {
            @strongify(self, ctr);
            TTQHotTopicListViewController *curCtr = [self controllerAtIndex:self.headerView.currentIndex];
            if (!curCtr.viewModel.isRefreshIng || curCtr == ctr) {
                [self.scrollView.mj_header endRefreshing];
            }
        }];
}

- (void)checkVideoToPlay {
    TTQHotTopicListViewController *vc = [self controllerAtIndex:self.headerView.currentIndex];
    if ([vc isKindOfClass:TTQHotTopicListViewController.class]) {
        [vc checkScrollWithDelay:0.2];
    }
}

#pragma mark - 引导

- (void)showGuidViewIfNeeded {
    [self.view layoutIfNeeded];
    NSString *key = [NSString stringWithFormat:@"hotTopicViewGuid_%@",[IMYPublicAppHelper shareAppHelper].userid];
    BOOL isShown = [[IMYUserDefaults standardUserDefaults] boolForKey:key];
    if (!isShown) {
        [[IMYUserDefaults standardUserDefaults] setBool:YES forKey:key];
        TTQHotTopicGuideView *view = [[TTQHotTopicGuideView alloc] initWithFrame:self.view.bounds];
        view.firstTargetRect = [self.headerView.collcectButton convertRect:self.headerView.collcectButton.bounds toView:nil];
        UIView *menuView = [self.headerView.menuActionTab viewWithTag:2];
        view.secondTargetRect = [menuView convertRect:menuView.bounds toView:nil];
        [self.view addSubview:view];
        [view show];
    }
}

#pragma mark - 收藏话题

- (void)collectTopicAction {
    if (![IMYNetState networkEnable]) {
        [UIView imy_showTextHUD:MT_Request_NoNetToast];
        return;
    }
    if (![IMYPublicAppHelper shareAppHelper].hasLogin) {
        [UIWindow imy_showTextHUD:kStatusText_unLogin];
        [[IMYURIManager shareURIManager] runActionWithString:@"login"];
        return;
    }
    BOOL isFavortie = !self.hotTopicInfo.is_favorite;
    @weakify(self);
    [[[IMYServerRequest postPath:@"v5/user_collect" host:circle_seeyouyima_com params:@{@"item_type":@"subject",@"item_id":@(self.hotTopicID),@"action":isFavortie?@"collect":@"un_collect"} headers:nil] deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        self.hotTopicInfo.is_favorite = isFavortie;
        [self.headerView refreshCollectState:self.hotTopicInfo.is_favorite];
        if (isFavortie) {
            if ([UIView showCollectSuccessHUD]) {
                [UIView imy_showTextHUD:IMYString(@"已收藏")];
            }
        } else {
            [UIView imy_showTextHUD:IMYString(@"已取消收藏")];
        }
    } error:^(NSError * _Nullable error) {
        [UIView imy_showTextHUD:IMYString(@"网络缓慢，请稍后再试")];
    }];
    [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_ryht_sc",@"action":@2,@"info_type":@31,@"info_id":@(self.hotTopicID)} headers:nil completed:nil];

}

// MARK: - BI

- (NSString *)ga_pageName {
    return @"TTQHotTopicViewController";
}


- (id<UIViewControllerAnimatedTransitioning>)animationControllerForDismissedController:(UIViewController *)dismissed {
    return self.dismissAnimator;
}

- (id<UIViewControllerInteractiveTransitioning>)interactionControllerForDismissal:(id<UIViewControllerAnimatedTransitioning>)animator {
    return self.dismissInteractive.interacting ? self.dismissInteractive : nil;
}

- (IMYSwipePopAnimatedTransition *)dismissAnimator {
    if (!_dismissAnimator) {
        _dismissAnimator = [IMYSwipePopAnimatedTransition new];
    }
    return _dismissAnimator;
}

- (NSInteger)publishEntranceId {
    if (self.fromForumTopic) {
        return 48;
    }
    return 5;
}

@end
