//
//  TTQHotTopicInfoViewV2.m
//  IMYTTQ
//
//  Created by aron on 2019/4/15.
//  Copyright © 2019 MeiYou. All rights reserved.
//

#import "TTQHotTopicInfoViewV2.h"
#import "IMYActionTab.h"
#import "TTQHotTopicInfoModel.h"
#import "UIFont+TTQ.h"
#import "UIImage+TTQ.h"
#import "UIView+TTQ.h"
#import "UIView+TTQFrame.h"
#import <IMYRM80AttributedLabel.h>
#import <IMYRM80AttributedLabelURL.h>
#import <IMYViewKit.h>
#import "UIImageView+TTQ.h"
#import "NSString+TTQ.h"

static NSString *adClickDataFlag = @"AdClick";

@interface TTQHotTopicInfoViewV2 () <IMYRM80AttributedLabelDelegate>
//基本信息
@property (nonatomic, strong) UIView *infoView;

//介绍简介
@property (nonatomic, strong) IMYRM80AttributedLabel *introductionM80Label; //需要截断处理
@property (nonatomic, strong) UILabel *detailLabel;//新的介绍label

//广告banner 778 https://www.tapd.cn/22362561/prong/stories/view/1122362561001055634#
@property (nonatomic, strong) UIView *adInfoView;
@property (nonatomic, strong) UIImageView *adIcon;
@property (nonatomic, strong) UIImageView *adArrow;
@property (nonatomic, strong) UILabel *adTitleLabel;
@property (nonatomic, strong) UILabel *adDetailLabel;




//磨砂玻璃后的背景墙
@property (nonatomic, strong) UIVisualEffectView *bgEffectView; //背景磨砂
@property (nonatomic, strong) UIImageView *bgImgView; //背景图
@property (nonatomic, strong) NSArray<UIImageView *> *bgImgViews;
@property (nonatomic, strong) UIView *bgEffectBgView; //背景图

@property (nonatomic, copy) NSString *introduction;

@property (nonatomic, assign) CGFloat oriHeight; //原始高度
@property (nonatomic, assign) CGFloat unflodHeight; //展开高度
@property (nonatomic, assign) CGFloat curOriHeight; //原始高度
@property (nonatomic, assign) CGFloat oriIconBottomOffsetY; //icon底部下来10像素
@property (nonatomic, copy) void (^frameChangeBlock)(CGFloat);

@property (nonatomic, strong) TTQHotTopicInfoModel *hotInfo;

@property (nonatomic, assign) BOOL isInitialSetup;

@end

@implementation TTQHotTopicInfoViewV2

- (instancetype)initWithFrame:(CGRect)frame
                   andHotInfo:(TTQHotTopicInfoModel *)hotInfo
                    andAction:(void (^)(NSInteger))actionBlock
                   cardAction:(void (^)(void))cardAction
               andFrameChange:(void (^)(CGFloat))frameChangeBlock {
    if (self = [super initWithFrame:frame]) {
        self.isInitialSetup = YES;
        self.introduction = hotInfo.introduction;
        [self setupWith:hotInfo andAction:actionBlock cardAction:cardAction];
        self.frameChangeBlock = frameChangeBlock;
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            self.isInitialSetup = NO;
        });
    }
    return self;
}

- (instancetype)initWithHotInfo:(TTQHotTopicInfoModel *)hotInfo
                      andAction:(void (^)(NSInteger))actionBlock
                     cardAction:(void (^)(void))cardAction
                 andFrameChange:(void (^)(CGFloat))frameChangeBlock {
    return [self initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 0) andHotInfo:hotInfo andAction:actionBlock cardAction:cardAction andFrameChange:frameChangeBlock];
}

- (void)setCurrentIndex:(NSInteger)currentIndex {
    [self.menuActionTab setCurrentIndexWithoutAction:currentIndex];
}

- (NSInteger)currentIndex {
    return self.menuActionTab.currentIndex;
}

- (CGFloat)actionHeight {
    return self.menuActionTab.imy_height;
}

- (void)setupWith:(TTQHotTopicInfoModel *)hotInfo andAction:(void (^)(NSInteger))actionBlock cardAction:(void (^)(void))cardAction {
    self.hotInfo = hotInfo;
    //加磨砂
    [self setupEffectView:hotInfo];
    //加基本信息
    [self setupInfoView:hotInfo];
    //加简介
    [self setupIntroductionView:hotInfo];
    //加广告 778需求【【商业化需求】热议话题页广告位优化】 https://www.tapd.cn/22362561/prong/stories/view/1122362561001055634
    [self setupADInfoWith:hotInfo];
    //加选项 最新最热排序按钮
    [self setupActionView:hotInfo andAction:actionBlock];
    [self imy_lineViewWithDirection:IMYDirectionDown show:NO margin:0];
    
    self.oriHeight = [self updateOriFrame];
    self.imy_height = self.oriHeight;
    self.curOriHeight = self.oriHeight;
    [self remarkConstraints];
}

- (void)updateWith:(TTQHotTopicInfoModel *)hotInfo andAction:(void (^)(NSInteger))actionBlock cardAction:(void (^)(void))cardAction {
    @weakify(self);
    [self.subviews bk_each:^(UIView *obj) {
        @strongify(self);
        if (obj != self) {
            [obj removeFromSuperview];
        }
    }];
    self.introduction = hotInfo.introduction;
    [self setupWith:hotInfo andAction:actionBlock cardAction:cardAction];
}

- (void)remarkConstraints {
    [self.tabBgView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(0);
        make.bottom.mas_offset(0);
        make.width.mas_equalTo(SCREEN_WIDTH);
        make.height.mas_equalTo(44);
    }];
    [self.menuActionTab mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.centerX.and.bottom.mas_offset(0);
        make.width.mas_equalTo(self.menuActionTab.imy_width);
        make.height.mas_equalTo(self.menuActionTab.imy_height);
    }];
    UIView *lastView = self.menuActionTab;
    CGFloat offset = -20;
    
    if (self.adInfoView && self.adInfoView.superview) {
        [self.adInfoView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(12);
            make.right.mas_equalTo(-12);
            make.height.mas_equalTo(70);
            make.bottom.equalTo(self.tabBgView.mas_top).mas_offset(-12);
        }];
        lastView = self.adInfoView;
        offset = -12;
    }
    
    if (self.introductionM80Label && self.introductionM80Label.superview) {
        [self.introductionM80Label mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(12);
            make.right.mas_equalTo(-12);
            make.height.mas_equalTo(self.introductionM80Label.imy_height);
            make.bottom.equalTo(lastView.mas_top).mas_offset(offset);
        }];
        lastView = self.introductionM80Label;
        offset = -12;
    }
    
    [self.infoView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(0);
        make.right.equalTo(self.collcectButton.mas_left).offset(-12);
        make.bottom.equalTo(lastView.mas_top).mas_offset(offset);
        make.height.mas_equalTo(self.infoView.imy_height);
    }];
}

- (CGFloat)updateOriFrame {
    //加选项
    CGFloat height = self.menuActionTab.imy_height;
    
    if (self.adInfoView && self.adInfoView.superview) {
        height = height + self.adInfoView.imy_height + 12;
    }
    
    if (self.introductionM80Label && self.introductionM80Label.superview) {
        // Title高度和间距
        height += self.introductionM80Label.imy_height + 24;
    } else {
        // 没有Title加上固定高度
        height += 40;
    }

    height += 8;
    height += self.infoView.imy_height;
    height += SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT;
    
    self.oriIconBottomOffsetY = height;
    
    return height;
}

#pragma mark -- setup subviews

- (void)setupEffectView:(TTQHotTopicInfoModel *)hotInfo {
    CGSize hSize = CGSizeMake(72, 72);
    CGSize newSize;
    if (hSize.width > 0 && hSize.height > 0) {
        newSize = CGSizeMake(ceil(SCREEN_WIDTH), ceil(hSize.height * (SCREEN_WIDTH / hSize.width)));
    }
    CGSize imageSize = newSize;//[hotInfo.icon ttq_communityImageSizeWithMaxWidth:SCREEN_WIDTH];
    NSUInteger numberOfImgs = ceilf(SCREEN_HEIGHT / imageSize.height);
    if (numberOfImgs <= 0) {
        numberOfImgs = 1;
    }
    
    self.bgEffectBgView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
    self.bgEffectBgView.clipsToBounds = YES;
    
    NSMutableArray *imgsArr = [[NSMutableArray alloc] init];
    for(int i = 0; i < numberOfImgs; i++){
        UIImageView *bgImgView = [[UIImageView alloc] initWithFrame:CGRectMake(0, i*imageSize.height, SCREEN_WIDTH, imageSize.height)];
        bgImgView.contentMode = UIViewContentModeScaleAspectFill;
        bgImgView.backgroundColor = [UIColor imy_colorForKey:kCK_Black_B];
        bgImgView.imy_showViewSize = imageSize;
//        [bgImgView imy_setImageURL:hotInfo.icon resize:CGSizeMake(imageSize.width * SCREEN_SCALE, imageSize.height * SCREEN_SCALE) quality:0 type:IMY_QiNiu_WEBP];
        [imgsArr addObject:bgImgView];
        [self.bgEffectBgView addSubview:bgImgView];
    }
    self.bgImgViews = [imgsArr copy];
    
    //创建模糊view
    UIBlurEffect *blur = [UIBlurEffect effectWithStyle:UIBlurEffectStyleLight];
    UIVisualEffectView *effectView = [[UIVisualEffectView alloc] initWithEffect:blur];
    effectView.contentView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.6]; //中和light还是太白的效果
    effectView.alpha = 0.8;
    [self.bgEffectBgView addSubview:effectView];
    self.bgEffectView = effectView;
    
    [self.bgEffectView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsZero);
    }];
}

- (void)setupActionView:(TTQHotTopicInfoModel *)hotInfo andAction:(void (^)(NSInteger))actionBlock {
    @weakify(self);
    if (self.menuActionTab) {
        self.tabBgView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 44)];
        [self.tabBgView imy_setBackgroundColorForKey:kCK_Black_F];
        UIBezierPath *maskPath = [UIBezierPath bezierPathWithRoundedRect:self.tabBgView.bounds byRoundingCorners:UIRectCornerTopLeft | UIRectCornerTopRight cornerRadii:CGSizeMake(12, 12)];
        CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
        maskLayer.frame = self.tabBgView.bounds;
        maskLayer.path = maskPath.CGPath;
        self.tabBgView.layer.mask = maskLayer;
        [self.tabBgView addSubview:self.menuActionTab];
        [self addSubview:self.tabBgView];
        return;
    }
    self.menuActionTab = [[IMYActionTab alloc] initWithFrame:CGRectMake(0, 0, self.imy_width, 44) andTitle:nil andAction:actionBlock];
    self.menuActionTab.initializationBlock = ^CGFloat(UIButton *button) {
        @strongify(self);
        [button imy_setTitleColorForKey:kCK_Black_M andState:UIControlStateNormal];
        button.titleLabel.font = [UIFont systemFontOfSize:16];
        if ([button isKindOfClass:[IMYTouchEXButton class]]) {
            [(IMYTouchEXButton *)button setExtendTouchInsets:UIEdgeInsetsMake(0, 12, 0, 12)];
        }
        [button sizeToFit];
        button.imy_height = self.menuActionTab.imy_height;
        return 24.0;
    };
    self.menuActionTab.redLine.imy_width = 20;
    self.menuActionTab.selectedBlock = ^(UIButton *button, UIView *redLine) {
        @strongify(self);
        [button imy_setTitleColorForKey:kCK_Red_B andState:UIControlStateNormal];
        [redLine imy_setBackgroundColorForKey:kCK_Red_B];
        button.titleLabel.font = [UIFont boldSystemFontOfSize:17];
        [button sizeToFit];
        button.imy_height = self.menuActionTab.imy_height;
        NSTimeInterval duration = self.isInitialSetup ? 0 : 0.15;
        [UIView animateWithDuration:duration
                         animations:^{
                             redLine.imy_width = 20;
                             redLine.imy_height = 3;
                             redLine.imy_centerX = button.imy_centerX;
                         }
                         completion:nil];
    };
    
    self.menuActionTab.disSelectedBlock = ^(UIButton *button) {
        @strongify(self);
        [button imy_setTitleColorForKey:kCK_Black_M andState:UIControlStateNormal];
        button.titleLabel.font = [UIFont systemFontOfSize:16];
        [button sizeToFit];
        button.imy_height = self.menuActionTab.imy_height;
    };
    self.menuActionTab.titles = @[@"最热", @"最新" , @"相似话题"];
    [self.menuActionTab setCurrentIndexWithoutAction:0];
    self.menuActionTab.redLine.imy_centerX = [self.menuActionTab titleButtonWithIndex:0].imy_centerX;
    
    self.tabBgView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 44)];
    [self.tabBgView imy_setBackgroundColorForKey:kCK_Black_F];
    UIBezierPath *maskPath = [UIBezierPath bezierPathWithRoundedRect:self.tabBgView.bounds byRoundingCorners:UIRectCornerTopLeft | UIRectCornerTopRight cornerRadii:CGSizeMake(12, 12)];
    CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
    maskLayer.frame = self.tabBgView.bounds;
    maskLayer.path = maskPath.CGPath;
    self.tabBgView.layer.mask = maskLayer;
    [self.tabBgView addSubview:self.menuActionTab];
    [self addSubview:self.tabBgView];
    self.menuActionTab.imy_centerX = self.imy_width / 2;
    
}

- (void)changeMenuTabBgCorner:(BOOL)isCorner{
    if(isCorner){
        UIBezierPath *maskPath = [UIBezierPath bezierPathWithRoundedRect:self.tabBgView.bounds byRoundingCorners:UIRectCornerTopLeft | UIRectCornerTopRight cornerRadii:CGSizeMake(12, 12)];
        CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
        maskLayer.frame = self.tabBgView.bounds;
        maskLayer.path = maskPath.CGPath;
        self.tabBgView.layer.mask = maskLayer;
    } else {
        self.tabBgView.layer.mask = nil;
    }
}

- (void)setupIntroductionView:(TTQHotTopicInfoModel *)hotInfo {
    if (hotInfo.card.mode == TTQHotTopicCardInfoModeAD && hotInfo.introduction.length > 0 && imy_isNotEmptyString(hotInfo.card.right_text) && imy_isNotEmptyString(hotInfo.card.redirect_url)) {
        // 处理软文广告的显示
        [self addSubview:self.introductionM80Label];
        self.introductionM80Label.numberOfLines = 3;
        NSString *introduction = [hotInfo.introduction stringByReplacingOccurrencesOfString:@"\r\n" withString:@"\n"];
        NSString *showText = introduction;
        NSString *realShowText = showText;
        NSInteger startCursor = 0;
        NSInteger endCursor = showText.length;
        NSInteger lastLineCount = 0;
        NSInteger maxLineCount = 3;
        NSAttributedString *adTruncationAttributedString = [self adTruncationDotAttributedStringWithString:hotInfo.card.right_text];
        NSInteger lineCount = [self lineCountWithText:showText trancation:adTruncationAttributedString];
        lastLineCount = lineCount;
        
        // 第一次设置做判断
        if (lineCount > maxLineCount) {
            while (startCursor < endCursor) {
                // 超过使用二分法计算最大的显示长度
                NSInteger tmpCursor = (startCursor + endCursor) / 2;
                NSString *tmpShowText = [showText substringToIndex:tmpCursor];
                
                lineCount = [self lineCountWithText:tmpShowText trancation:adTruncationAttributedString];
                if (lineCount > maxLineCount) {
                    endCursor = tmpCursor;
                } else {
                    startCursor = tmpCursor;
                }
                
                if ((lineCount <= maxLineCount && (endCursor - startCursor < 2))) {
                    realShowText = tmpShowText;
                    break;
                } else if (endCursor <= startCursor) {
                    realShowText = [showText substringToIndex:startCursor];
                    break;
                }
            }
            
            // 设置显示
            [self lineCountWithText:realShowText trancation:adTruncationAttributedString];
        } else {
            adTruncationAttributedString = [self adTruncationAttributedStringWithString:hotInfo.card.right_text];
            // 设置显示
            [self lineCountWithText:realShowText trancation:adTruncationAttributedString];
        }
        
        // 处理点击事件
        self.introductionM80Label.delegate = self;
        [self.introductionM80Label addCustomLink:adClickDataFlag forRange:NSMakeRange(self.introductionM80Label.showAttributedString.length - adTruncationAttributedString.length, adTruncationAttributedString.length) linkColor:[UIColor imy_colorWithHexString:@"0x94DBFF"]];
        
    } else if (imy_isNotEmptyString(hotInfo.introduction)) {

        // 处理正常业务的显示
        [self addSubview:self.introductionM80Label];
        self.introductionM80Label.numberOfLines = 3;
        [self.introductionM80Label imyr_setText:hotInfo.introduction ?: @""];
        [self.introductionM80Label imyr_autoAdjustHeight];
    }
}

- (void)setupInfoView:(TTQHotTopicInfoModel *)hotInfo {
    
    self.collcectButton = [[IMYCapsuleButton alloc] initWithFrame:CGRectMake(0, 0, 68, 28)];
    self.collcectButton.imy_right = self.imy_width - 12;
    self.collcectButton.lineWidth = 1/SCREEN_SCALE;
    [self refreshCollectState:hotInfo.is_favorite];
    [self addSubview:self.collcectButton];
    [self.collcectButton setExtendTouchInsets:UIEdgeInsetsMake(10, 0, 0, 10)];
    
    self.infoView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, self.imy_width - 24 - self.collcectButton.imy_width, 72)];//jer在这边修改header高度
    self.infoView.backgroundColor = [UIColor clearColor];
    
    IMYAvatarImageView *iconImgView = [[IMYAvatarImageView alloc] initWithFrame:CGRectMake(12, 0, 72, 72)];
    [iconImgView imy_setShowInscribedCircle:NO];
    iconImgView.tag = -10;
    iconImgView.needShowCicrle = NO;
    iconImgView.layer.cornerRadius = 12;
    iconImgView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.2];
    [self setIconImgView:iconImgView iconUrl:hotInfo.icon];
    [self.infoView addSubview:iconImgView];
    
    UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(iconImgView.imy_right + 12, 0, self.infoView.imy_width - iconImgView.imy_right - 12, 0)];

    UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, contentView.imy_width, 28)];
    titleLabel.tag = -11;
    [titleLabel imy_setTextColorForKey:kCK_White_A];
    [titleLabel setFont:[UIFont ttqMediumFontWith:20]];
    titleLabel.lineBreakMode = NSLineBreakByTruncatingMiddle;
    titleLabel.numberOfLines = 2;
    
    NSString *hotTitle = [NSString stringWithFormat:@"#%@", hotInfo.title];
    hotTitle = [hotTitle stringByReplacingOccurrencesOfString:@"\n" withString:@" "];
    [titleLabel imy_setText:hotTitle];
    [titleLabel imy_resetWithStyle:^(NSMutableParagraphStyle *paragraphStyle) {
        paragraphStyle.minimumLineHeight = 28;
        paragraphStyle.maximumLineHeight = 28;
    } completed:nil];
    [titleLabel imy_resetLineHeight:28];
    [titleLabel sizeToFit];
    [contentView addSubview:titleLabel];
    
    UILabel *descriptionLabel = [[UILabel alloc] initWithFrame:CGRectMake(titleLabel.imy_left, 13, contentView.imy_width, 18)];
    descriptionLabel.tag = -12;
    descriptionLabel.imy_top = titleLabel.imy_bottom + 8;
    [descriptionLabel imy_setTextColorForKey:kCK_White_A];
    [descriptionLabel setFont:[UIFont ttqFontWith:13]];
    [descriptionLabel imy_setText:[hotInfo hotTopicDescriptionString]];
    [contentView addSubview:descriptionLabel];
    
    contentView.imy_height = descriptionLabel.imy_bottom;
    [self.infoView addSubview:contentView];
    contentView.imy_centerY = self.infoView.imy_height/2;

    [self addSubview:self.infoView];

    [self.collcectButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self).offset(-12);
        make.size.mas_equalTo(CGSizeMake(68, 28));
        make.centerY.equalTo(self.infoView);
    }];
    [self.collcectButton imy_addThemeChangedBlock:^(IMYCapsuleButton *weakObject) {
        weakObject.contentColor = [IMYColor colorWithNormal:kCK_Red_A high:nil dis:nil sel:kCK_Clear_A];
        weakObject.titleColor = [IMYColor colorWithNormal:kCK_White_A high:nil dis:nil sel:kCK_White_A];
        weakObject.borderColor = [IMYColor colorWithNormal:kCK_Red_A high:nil dis:nil sel:kCK_White_A];
    }];
}

- (void)refreshCollectState:(BOOL)isCollected {
    [self.collcectButton imy_setTitle:self.hotInfo.is_favorite?IMYString(@"已收藏"):IMYString(@"收藏")];
    self.collcectButton.selected = self.hotInfo.is_favorite;
}

- (void)setupADInfoWith:(TTQHotTopicInfoModel *)hotInfo {
    if (hotInfo.banner && !self.adInfoView.superview) {
        [self addSubview:self.adInfoView];
    }
    if (imy_isNotEmptyString(hotInfo.banner.icon)) {
        [self.adIcon ttq_setImageURL:hotInfo.banner.icon];
        self.adIcon.hidden = NO;
    } else {
        self.adIcon.hidden = YES;
    }
    self.adDetailLabel.text = hotInfo.banner.sub_title;
    self.adTitleLabel.text = hotInfo.banner.main_title;
    [self adInfoLayoutWithModel:hotInfo];
}

- (void)adInfoLayoutWithModel:(TTQHotTopicInfoModel*)hotInfo {
    BOOL bannerIconExist = imy_isNotEmptyString(hotInfo.banner.icon);
    self.adTitleLabel.imy_left = bannerIconExist ? 72 : 12;
    self.adDetailLabel.imy_left = bannerIconExist ? 72 : 12;
    self.adTitleLabel.imy_width = bannerIconExist ? (SCREEN_WIDTH - 72 - 38 - 12) : (SCREEN_WIDTH - 12 - 38);
    self.adDetailLabel.imy_width = bannerIconExist ? (SCREEN_WIDTH - 72 - 38 - 12) : (SCREEN_WIDTH - 12 - 38);
}

- (NSInteger)lineCountWithText:(NSString *)text trancation:(NSAttributedString *)trancation{
    [self.introductionM80Label imyr_setText:nil];
    [self.introductionM80Label appendAttributedText:[[NSAttributedString alloc] initWithString:text attributes:@{NSFontAttributeName : [UIFont systemFontOfSize:13], NSForegroundColorAttributeName : [UIColor imy_colorForKey:kCK_White_A]}]];
    [self.introductionM80Label appendAttributedText:trancation];
    [self.introductionM80Label imyr_autoAdjustHeight];
    NSInteger lineCount = [self.introductionM80Label getLinesCount];
    return lineCount;
}

- (NSAttributedString *)adTruncationDotAttributedStringWithString:(NSString *)text {
    NSString *truncationString = [NSString stringWithFormat:@" %@", text ?: @"查看更多"];
    NSMutableAttributedString *truncationAttributedString = [[NSMutableAttributedString alloc] initWithString:truncationString];
    [truncationAttributedString setFont:[UIFont systemFontOfSize:13 weight:(UIFontWeightSemibold)]];
    [truncationAttributedString setTextColor:[UIColor imy_colorWithHexString:@"0x94DBFF"] range:NSMakeRange(0, truncationAttributedString.string.length)];
    return truncationAttributedString;
}

- (NSAttributedString *)adTruncationAttributedStringWithString:(NSString *)text {
    NSString *truncationString = [NSString stringWithFormat:@" %@", text ?: @"查看更多"];
    NSMutableAttributedString *truncationAttributedString = [[NSMutableAttributedString alloc] initWithString:truncationString];
    [truncationAttributedString setFont:[UIFont systemFontOfSize:13 weight:(UIFontWeightSemibold)]];
    [truncationAttributedString setTextColor:[UIColor imy_colorWithHexString:@"0x94DBFF"] range:NSMakeRange(0, truncationAttributedString.string.length)];
    return truncationAttributedString;
}

- (IMYRM80AttributedLabel *)introductionM80Label {
    if (!_introductionM80Label) {
        IMYRM80AttributedLabel *introductionM80Label = [[IMYRM80AttributedLabel alloc] init];
        introductionM80Label.numberOfLines = 3;
        introductionM80Label.textColor = [UIColor imy_colorForKey:kCK_White_A];
        @weakify(introductionM80Label);
        [introductionM80Label imy_addThemeChangedBlock:^(id weakObject) {
            @strongify(introductionM80Label);
            introductionM80Label.showAttributedString = nil;
            introductionM80Label.textColor = [UIColor imy_colorForKey:kCK_White_A];
        }];
        introductionM80Label.backgroundColor = [UIColor clearColor];
        introductionM80Label.font = [UIFont systemFontOfSize:13];
        introductionM80Label.lineSpacing = 1.5;
        introductionM80Label.paragraphSpacing = 1;
        introductionM80Label.imyr_analysisDynamicEmoticon = NO;
        introductionM80Label.textAlignment = kCTTextAlignmentLeft;
        introductionM80Label.lineBreakMode = NSLineBreakByTruncatingTail;
        introductionM80Label.imy_width = self.imy_width - 30;
        introductionM80Label.imy_left = 15;
        introductionM80Label.imy_top = 10;
        
        _introductionM80Label = introductionM80Label;
    }
    return _introductionM80Label;
}


//ad info view
- (UIView *)adInfoView {
    if (!_adInfoView) {
        _adInfoView = [[UIView alloc]initWithFrame:CGRectMake(12, 0, SCREEN_WIDTH-24, 70)];
        [_adInfoView imy_setBackgroundColorForKey:kCK_White_AN];
        [_adInfoView addSubview:self.adIcon];
        [_adInfoView addSubview:self.adTitleLabel];
        [_adInfoView addSubview:self.adDetailLabel];
        [_adInfoView addSubview:self.adArrow];
        
        _adInfoView.layer.cornerRadius = 12;
        @weakify(self);
        [_adInfoView bk_whenTapped:^{
            @strongify(self);
            if (imy_isNotEmptyString(self.hotInfo.banner.redirect_url)) {
                [[IMYURIManager shareURIManager]runActionWithString:self.hotInfo.banner.redirect_url];;
            }
        }];
    }
    return _adInfoView;
}

- (UIImageView *)adIcon {
    if (!_adIcon) {
        _adIcon = [[UIImageView alloc]initWithFrame:CGRectMake(12, 11, 48, 48)];
        _adIcon.layer.cornerRadius = 8;
        _adIcon.layer.masksToBounds = YES;
        [_adIcon imy_setBackgroundColorForKey:kCK_Black_F];
    }
    return _adIcon;
}

- (UILabel *)adTitleLabel {
    if (!_adTitleLabel) {
        _adTitleLabel = [[UILabel alloc]initWithFrame:CGRectMake(72, 12, SCREEN_WIDTH - 72 - 26 - 24, 24)];
        _adTitleLabel.textColor = [UIColor imy_colorForKey:kCK_Black_AT];
        _adTitleLabel.font = [UIFont systemFontOfSize:17];
        _adTitleLabel.textAlignment = NSTextAlignmentLeft;
    }
    return _adTitleLabel;
}

- (UILabel *)adDetailLabel {
    if (!_adDetailLabel) {
        _adDetailLabel = [[UILabel alloc]initWithFrame:CGRectMake(72, 38, SCREEN_WIDTH - 72 - 26 - 24, 20)];
        _adDetailLabel.textColor = [UIColor imy_colorForKey:kCK_Black_B];
        _adDetailLabel.font = [UIFont systemFontOfSize:14];
        _adDetailLabel.textAlignment = NSTextAlignmentLeft;
    }
    return _adDetailLabel;
}


- (UIImageView *)adArrow {
    if (!_adArrow) {
        _adArrow = [[UIImageView alloc]initWithFrame:CGRectMake(SCREEN_WIDTH - 24 - 12 - 14, 28, 14, 14)];
        [_adArrow imy_setImage:@"all_list_arrow_right_m"];
    }
    return _adArrow;
}

- (void)setIconImgView:(IMYAvatarImageView *)iconView iconUrl:(NSString *)iconUrl {
    iconView.image = [UIImage ttq_imageWithColor:[UIColor imy_colorForKey:kCK_Black_F]];
    if (iconUrl) {
        @weakify(self);
        [iconView setAvatarWithURLString:iconUrl
                             placeholder:iconView.image
                              completion:^(BOOL succeed, UIImage *image) {
                                  if (image) { //会延迟。。。应该可以直接使用
                                      imy_asyncMainBlock(^{
                                          @strongify(self);
                                          [iconView imy_setBackgroundColorForKey:kCK_White_A];
                                          // 头像的图是剪裁过得，ui就要这个方图
                                          for (UIImageView *imageView in self.bgImgViews) {
                                              [imageView imy_setBackgroundColorForKey:kCK_White_A];
                                              [imageView imy_setImage:image];
                                          }
                                      });
                                  }
                              }];
    }
}


#pragma mark - IMYRM80AttributedLabelDelegate
- (void)m80AttributedLabel:(IMYRM80AttributedLabel *)label
             clickedOnLink:(IMYRM80AttributedLabelURL *)linkURL {
    if ([linkURL.linkData isKindOfClass:NSString.class] && [(NSString *)linkURL.linkData isEqualToString:adClickDataFlag]) {
        if (self.hotInfo.card.redirect_url) {
            [[IMYURIManager shareURIManager] runActionWithString:self.hotInfo.card.redirect_url];
        }
    }
}

@end
