//
//  TTQTrendingTopicModel.h
//  IMYTTQ
//
//  Created by mingway on 11/24/17.
//  Copyright © 2017 MeiYou. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TTQRootBaseModel.h"

@interface TTQTrendingTopicModel : TTQRootBaseModel

@property (nonatomic, copy) NSString *icon;
@property (nonatomic, copy) NSString *name;
@property (nonatomic, copy) NSString *introduction;
@property (nonatomic, assign) NSInteger topicID;
@property (nonatomic, assign) NSInteger total_discuss;
@property (nonatomic, copy) NSString *redirect_url;
@property (nonatomic, assign) NSInteger read_count;
@property (nonatomic, assign) BOOL is_show_hot;

@end

typedef NS_ENUM(NSUInteger, TTQTrendingTopicCategoryType) {
    TTQTrendingTopicCategoryTypeNormal,
    TTQTrendingTopicCategoryTypeRecommend,
    TTQTrendingTopicCategoryTypeHot,
    TTQTrendingTopicCategoryTypeRecently
};

@interface TTQTrendingTopicCategoryModel : NSObject

@property (nonatomic, assign) TTQTrendingTopicCategoryType category_type;
@property (nonatomic, copy) NSString *category_name;
@property (nonatomic, assign) NSInteger category_id;
@property (nonatomic, copy) NSArray<TTQTrendingTopicModel *>  *list;

@end
