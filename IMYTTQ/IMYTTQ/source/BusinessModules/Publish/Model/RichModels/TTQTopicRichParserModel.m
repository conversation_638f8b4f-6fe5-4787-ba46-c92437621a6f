//
//  TTQTopicRichParserModel.m
//  IMYTTQ
//
//  Created by Li<PERSON> on 2017/10/10.
//  Copyright © 2017年 MeiYou. All rights reserved.
//

#import "TTQTopicRichParserModel.h"
#import "NSRegularExpression+TTQ.h"
#import "NSString+TTQ.h"
#import <IMYREmoticonManager.h>
#import <NSArray+BlocksKit.h>
#import <IMYBaseKit/NSObject+IMY_YYJSON.h>
#import <TFHpple.h>
#import <IMYVideoPlayer.h>
#import <IMYBaseKit/IMYViewKit.h>

@interface TTQTopicRichParserModel ()
+ (TTQTopicRichParserTextModel *)topicRichParserTextModelWith:(NSString *)content;
+ (TTQTopicRichParserAModel *)topicRichParserAModelWith:(NSString *)content attributes:(NSDictionary *)attributes;
+ (TTQTopicRichParserImgModel *)topicRichParserImgModelWith:(NSString *)desc src:(NSString *)src;
+ (TTQTopicRichParserVideoModel *)topicRichParserVideoModelWithAttributes:(NSDictionary *)attributes;
+ (TTQTopicRichParserYouPinModel *)topicRichParserYouPinModelWithAttributes:(NSDictionary *)attributes;
+ (TTQTopicRichParserYouPlusModel *)topicRichParserYouPlusModelWithAttributes:(NSDictionary *)attributes;
@end

@implementation TTQTopicRichParserModel

#pragma mark - parser
+ (NSArray *)topicRichParser:(NSString *)topicContent parseType:(IMYRTextParseType)parseType {
    if ([NSString ttq_isEmptyString:topicContent]) {
        return nil;
    }
    NSRegularExpression *regular = nil;
    NSString *XPathQuery = nil;
    BOOL emotionParse = parseType & IMYRTextParseTypeEmoji;
    NSMutableArray *regulars = [NSMutableArray array];
    NSMutableArray *XPathQuerys = [NSMutableArray array];
    BOOL urlParse = ((parseType & IMYRTextParseTypeURL) || (parseType & IMYRTextParseTypeForceURL));
    BOOL imgParse = (parseType & IMYRTextParseTypeImg);
    BOOL videoParse = (parseType & IMYRTextParseTypeVideo);
    if (urlParse) {
        [regulars addObject:[NSRegularExpression ttq_A_TagExpression]];
        [XPathQuerys addObject:@"//a"];
    }
    if (imgParse) {
        [regulars addObject:[NSRegularExpression ttq_IMG_TagExpression]];
        [XPathQuerys addObject:@"//img"];
    }
    if (videoParse) {
        [regulars addObject:[NSRegularExpression ttq_video_TagExpression]];
        [XPathQuerys addObject:@"//video"];
    }
    
    [regulars addObject:[NSRegularExpression ttq_youpin_TagExpression]];
    [XPathQuerys addObject:@"//youpin_tag"];
    
    [regulars addObject:[NSRegularExpression ttq_youplus_TagExpression]];
    [XPathQuerys addObject:@"//youplus"];
    
    [regulars addObject:[NSRegularExpression ttq_plugin_TagExpression]];
    [XPathQuerys addObject:@"//plugin"];
    
    if (XPathQuerys.count > 0) {
        NSMutableString *XPathQueryString = [NSMutableString new];
        [XPathQueryString appendString:[XPathQuerys componentsJoinedByString:@" | "]];
        if (XPathQuerys.count > 1) {
            // 添加前缀"(" 后缀")"
            [XPathQueryString insertString:@"(" atIndex:0];
            [XPathQueryString insertString:@")" atIndex:XPathQueryString.length];
        }
        XPathQuery = XPathQueryString;
    }
    
    if (regulars.count) {
        NSArray *regularStrings = [regulars bk_map:^id(NSRegularExpression *obj) {
            if (obj.pattern) {
                return obj.pattern;
            }
            return @"";
        }];
        NSString *regularString = [regularStrings componentsJoinedByString:@"|"];
        regular = [NSRegularExpression regularExpressionWithPattern:regularString options:0 error:nil];
    }
    if (regular == nil) { //无需url跟img解析
        TTQTopicRichParserTextModel *textModel = [TTQTopicRichParserModel topicRichParserTextModelWith:topicContent emotionParse:emotionParse];
        if (textModel) {
            return @[textModel];
        }
        return nil;
    }

    NSArray *matches = [regular matchesInString:topicContent options:0 range:NSMakeRange(0, topicContent.length)];
    if (matches.count == 0) {
        TTQTopicRichParserTextModel *textModel = [TTQTopicRichParserModel topicRichParserTextModelWith:topicContent emotionParse:emotionParse];
        if (textModel) {
            return @[textModel];
        }
        return nil;
    }

    NSMutableArray *stringNodes = [NSMutableArray array];
    //    NSMutableString *htmlMutableString = [[NSMutableString alloc] initWithString:topicContent];
    NSTextCheckingResult *lastMatch = nil;
    TFHpple *doc = [[TFHpple alloc] initWithHTMLData:nil];
    for (NSInteger index = 0; index < matches.count; index++) {
        NSTextCheckingResult *match = matches[index];
        if (index == 0) {
            if (match.range.location > 0) {
                NSString *subString = [topicContent substringToIndex:match.range.location];
                TTQTopicRichParserTextModel *textModel = [TTQTopicRichParserModel topicRichParserTextModelWith:subString emotionParse:emotionParse];
                if (textModel) {
                    [stringNodes addObject:textModel];
                }
            }
        } else {
            NSInteger lastLocation = lastMatch.range.location + lastMatch.range.length;
            if (match.range.location - lastLocation > 0) {
                NSString *subString = [topicContent substringWithRange:NSMakeRange(lastLocation, match.range.location - lastLocation)];
                if (imy_isNotEmptyString([subString imy_trimString])) {
                    TTQTopicRichParserTextModel *textModel = [TTQTopicRichParserModel topicRichParserTextModelWith:subString emotionParse:emotionParse];
                    if (textModel) {
                        [stringNodes addObject:textModel];
                    }
                }
            }
        }
        NSString *subString = [topicContent substringWithRange:match.range];
        if (subString) {
            [doc setValue:[subString dataUsingEncoding:NSUTF8StringEncoding] forKey:@"data"];
            NSArray *aAndImgNodes = [doc searchWithXPathQuery:XPathQuery];
            if (aAndImgNodes.count) {
                for (TFHppleElement *ele in aAndImgNodes) {
                    if ([ele.tagName isEqualToString:@"a"]) {
                        TTQTopicRichParserAModel *aModel = [TTQTopicRichParserModel topicRichParserAModelWith:ele.content attributes:ele.attributes];
                        if (aModel) {
                            [stringNodes addObject:aModel];
                        }
                    } else if ([ele.tagName isEqualToString:@"img"]) {
                        TTQTopicRichParserImgModel *imgModel = [TTQTopicRichParserModel topicRichParserImgModelWith:ele.attributes[@"desc"] src:ele.attributes[@"src"]];
                        if (ele.attributes[@"is_low"]) {
                            NSString *isLow = ele.attributes[@"is_low"];
                            imgModel.isLow = [isLow isEqualToString:@"1"];
                        }
                        if (imgModel) {
                            [stringNodes addObject:imgModel];
                        }
                    } else if ([ele.tagName isEqualToString:@"video"]) {
                        TTQTopicRichParserVideoModel *videoModel = [TTQTopicRichParserModel topicRichParserVideoModelWithAttributes:ele.attributes];
                        if (videoModel) {
                            [stringNodes addObject:videoModel];
                        }
                    } else if ([ele.tagName isEqualToString:@"youpin_tag"]) {
                        TTQTopicRichParserYouPinModel *model = [TTQTopicRichParserModel topicRichParserYouPinModelWithAttributes:ele.attributes];
                        if (model) {
                            [stringNodes addObject:model];
                        }
                    } else if ([ele.tagName isEqualToString:@"youplus"]) {
                        TTQTopicRichParserYouPlusModel *model = [TTQTopicRichParserModel topicRichParserYouPlusModelWithAttributes:ele.attributes];
                        if (model) {
                            [stringNodes addObject:model];
                        }
                    } else if ([ele.tagName isEqualToString:@"plugin"]) {
                        TTQTopicRichParserYouPlusModel *model = [TTQTopicRichParserModel topicRichParserYouPlusModelWithAttributes:ele.attributes];
                        if (model) {
                            model.isAdPlugin = YES;
                            [stringNodes addObject:model];
                        }
                    }
                }
            } else {
                TTQTopicRichParserTextModel *textModel = [TTQTopicRichParserModel topicRichParserTextModelWith:subString emotionParse:emotionParse];
                if (textModel) {
                    [stringNodes addObject:textModel];
                }
            }
        }

        if (index == matches.count - 1) {
            if (match.range.location + match.range.length < topicContent.length) {
                NSString *subString = [topicContent substringFromIndex:match.range.location + match.range.length];
                TTQTopicRichParserTextModel *textModel = [TTQTopicRichParserModel topicRichParserTextModelWith:subString emotionParse:emotionParse];
                if (textModel) {
                    [stringNodes addObject:textModel];
                }
            }
        }

        lastMatch = match;
    }

    return stringNodes.copy;
}

+ (BOOL)hasContainImgWithString:(NSString *)content {
    if (imy_isEmptyString(content)) {
        return NO;
    }
    NSRegularExpression *regex = [NSRegularExpression ttq_IMG_TagExpression];
    NSArray *matches = [regex matchesInString:content options:0 range:NSMakeRange(0, content.length)];
    return matches.count > 0;
}

+ (BOOL)hasContainVideoWithString:(NSString *)content {
    if (imy_isEmptyString(content)) {
        return NO;
    }
    NSRegularExpression *regex = [NSRegularExpression ttq_video_TagExpression];
    NSArray *matches = [regex matchesInString:content options:0 range:NSMakeRange(0, content.length)];
    return matches.count > 0;
}

#pragma mark - 各类模型数据
//内容/文字描述/链接／图片链接
+ (TTQTopicRichParserTextModel *)topicRichParserTextModelWith:(NSString *)content {
    if ([NSString ttq_isEmptyString:content]) {
        return nil;
    }
    TTQTopicRichParserTextModel *textModel = [[TTQTopicRichParserTextModel alloc] init];
    textModel.content = content;
    return textModel;
}

+ (TTQTopicRichParserTextModel *)topicRichParserTextModelWith:(NSString *)content emotionParse:(BOOL)emotionParse {
    if ([NSString ttq_isEmptyString:content]) {
        return nil;
    }
    TTQTopicRichParserTextModel *textModel = [[TTQTopicRichParserTextModel alloc] init];
    textModel.content = content;
    textModel.isEmotion = emotionParse ? [IMYREmoticonManager hasContainEmojiWithString:content] : NO;
    return textModel;
}

+ (TTQTopicRichParserAModel *)topicRichParserAModelWith:(NSString *)content attributes:(NSDictionary *)attributes {
    if ([NSString ttq_isEmptyString:attributes[@"href"]]) {
        return nil;
    }
    TTQTopicRichParserAModel *aModel = [[TTQTopicRichParserAModel alloc] init];
    aModel.content = content;
    aModel.href = attributes[@"href"];
    aModel.image = attributes[@"image"];
    aModel.ltype = [attributes[@"ltype"] integerValue];
    aModel.is_show_btn = [attributes[@"is_show_btn"] integerValue];
    aModel.btn_title = attributes[@"btn_title"];
    aModel.is_ad = [attributes[@"is_ad"] integerValue];
    return aModel;
}

+ (TTQTopicRichParserImgModel *)topicRichParserImgModelWith:(NSString *)desc src:(NSString *)src {
    if ([NSString ttq_isEmptyString:src]) {
        return nil;
    }
    TTQTopicRichParserImgModel *imgModel = [[TTQTopicRichParserImgModel alloc] init];
    imgModel.desc = desc;
    imgModel.src = src;
    return imgModel;
}

+ (TTQTopicRichParserVideoModel *)topicRichParserVideoModelWithAttributes:(NSDictionary *)attributes {
    TTQTopicRichParserVideoModel *videoModel = [attributes toModel:TTQTopicRichParserVideoModel.class];
    return videoModel;
}

+ (TTQTopicRichParserYouPinModel *)topicRichParserYouPinModelWithAttributes:(NSDictionary *)attributes {
    TTQTopicRichParserYouPinModel *youpin = [attributes toModel:TTQTopicRichParserYouPinModel.class];
    return youpin;
}

+ (TTQTopicRichParserYouPlusModel *)topicRichParserYouPlusModelWithAttributes:(NSDictionary *)attributes {
    TTQTopicRichParserYouPlusModel *youplus = [attributes toModel:TTQTopicRichParserYouPlusModel.class];
    return youplus;
}

@end

@implementation TTQTopicRichParserAModel
@end

@implementation TTQTopicRichParserTextModel
@end

@implementation TTQTopicRichParserImgModel
@end

@implementation TTQTopicRichParserTagModel

+ (void)initialize {
    [self bindYYJSONKey:@"id" toProperty:@"tagId"];
}

@end

@implementation TTQTopicRichParserVideoModel

- (IMYVideoModel *) videoModel {
    if (self.src.length == 0) {
        return nil;
    }
    IMYVideoModel *videoModel = [[IMYVideoModel alloc] init];
    videoModel.sdVideoURL = [NSURL URLWithString:[self.src stringByAddingPercentEscapesUsingEncoding:NSUTF8StringEncoding]];
    videoModel.duration = [self timeStringFromSeconds:self.duration];
    videoModel.fileSize = self.size;
    if (self.poster) {
        videoModel.coverImageURL = [NSURL URLWithString:self.poster];
    }
    return videoModel;
}

- (NSString *)timeStringFromSeconds:(NSInteger)seconds {
    int hour = 0;
    int minute = seconds / 60.0f;
    int second = seconds % 60;
    if (minute > 59) {
        hour = minute / 60;
        minute = minute % 60;
        return [NSString stringWithFormat:@"%02d:%02d:%02d", hour, minute, second];
    } else {
        return [NSString stringWithFormat:@"%02d:%02d", minute, second];
    }
}

@end

@implementation TTQTopicRichParserYouPinModel
//<youpin_tag youpin_id="32432" src="https://test-sc.seeyouyima.com/forum/data/5fdaf04cb3e12_227_227.jpg" title="商品标题" rec_reason="推荐理由" tag="优惠券" price="1000" original_price="3000" redirect_url="商品地址" more_prices="1"></youpin_tag>
//
//tag为多个的时候用 || 分隔开来，举例：100减2||200减4
//
//more_prices 1代表销售价后面带 `起`   为0则不带
//price 和 original_price 分的新式下发，举例：1元换算成分就是1000 100

//① 售价+原价；
//② 以￥0.00的形式展示；如价格为整数「元」，则不保留小数；如有「角」，保留1位小数；如有「分」，则保留2位小数；
//③ 多SKU且价格不同，无论售价还是原价，均展示所有sku中的最低价，展示为：￥0.00起；
//④ 规则：售价＜原价，无标签：售价+原价（小字划线价）；售价=原价，无标签：原价不展示；售价≤原价，有标签：原价不展示；
+ (void)initialize {
    [self bindYYJSONKey:@"youpin_id" toProperty:@"youpinID"];
    [self bindYYJSONKey:@"src" toProperty:@"image"];
    [self bindYYJSONKey:@"rec_reason" toProperty:@"reason"];
    [self bindYYJSONKey:@"original_price" toProperty:@"originalPrice"];
    [self bindYYJSONKey:@"redirect_url" toProperty:@"redirectURL"];
}

- (NSDictionary *)imy_yyjsonModelWillTransformDictionary:(NSDictionary *)dict
{
    NSMutableDictionary *copyDic = [NSMutableDictionary dictionaryWithDictionary:dict];
    NSString *tagString = dict[@"tag"];
    if (imy_isNotEmptyString(tagString)) {
        NSArray *array = [tagString componentsSeparatedByString:@"||"];
        if (array.count > 0) {
            copyDic[@"tag"] = array;
        }
    }
    
    NSString *morePrice = dict[@"more_prices"];
    self.isMultiPrice = [morePrice isEqualToString:@"1"];
    [copyDic removeObjectForKey:@"more_prices"];    
    return [copyDic copy];
}

@end

@implementation TTQTopicRichParserYouPlusModel
//<youplus tag="youplus" class="youplus-plugin box" id="18" title="普通插件-商品样式" desc="普通插件-商品样式" type="0" icon="http://test-sc.seeyouyima.com/eimg/20210713/60ed3d50f2c5d_648_648.jpg" price="3" has_btn="0" btn_txt="" created_at="2021-07-13 15:14:41" approve_status="1" approve_remark="" jump_url="https://test-e.meiyou.com/home#!/home" deep_link="" youplus_style="2"></youplus>

//+ (void)initialize {
//    [self bindYYJSONKey:@"youpin_id" toProperty:@"youpinID"];
//}

+ (void)youplusAction:(TTQTopicRichParserYouPlusModel *)model {
    if (model.type == TTQYouPlusPluginTypeApp){
        // 跳转到app store下载
        if (imy_isEmptyString(model.download_ios_url)) {
            [UIWindow imy_showTextHUD:@"啊噢~ 应用走丢了"];
        } else {
            BOOL canOpen = [[UIApplication sharedApplication] canOpenURL:[NSURL URLWithString:model.download_ios_url]];
            if (canOpen) {
                [[UIApplication sharedApplication] openURL:[NSURL URLWithString:model.download_ios_url] options:nil completionHandler:^(BOOL success) {
                    // OK
                }];
            }
        }
    } else if(model.type == TTQYouPlusPluginTypeWechat){
        if (model.wx_handle_type == 0) {
            // 复制微信号到粘贴板
            UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
            NSString *wxAccount = @"";
            if (imy_isNotEmptyString(model.wx_account_wechat)) {
                wxAccount = model.wx_account_wechat;
            }
            pasteboard.string = wxAccount;
            // 吐司提示：微信号复制成功，将跳转微信/n请注意帐号和财产安全
            NSString *str = @"微信号复制成功，将跳转微信\r请注意账号和财产安全";
            [UIWindow imy_showTextHUD:str];
            // 3s后唤醒跳转到微信 weixin://
            imy_asyncMainBlock(2, ^{
                [[UIApplication sharedApplication] openURL:[NSURL URLWithString:model.youplus_url] options:nil completionHandler:nil];
            });
        } else {
            IMYPAGView *imageView = [[IMYPAGView alloc] initWithFrame:CGRectMake(0, 0, [IMYActionMessageBox boxWidth], [IMYActionMessageBox boxWidth]/270*244)];
            imageView.contentMode = UIViewContentModeScaleAspectFill;
            NSString *path = [[NSBundle mainBundle] pathForResource:@"youjia_zhiyin.pag" ofType:nil];
            if (imy_isNotEmptyString(path)) {
                [imageView loadWithURL:[NSURL fileURLWithPath:path] placeholder:nil completed:nil];
            }
            
            IMYActionMessageBox *boxView = [[IMYActionMessageBox alloc] init];
            boxView.style = IMYMessageBoxStyleFlatUpdown;
            [boxView.rightButton imy_setTitle:@"前往关注"];
            [boxView.leftButton imy_setTitle:@"再想想"];
            boxView.contentView = imageView;
            UIView *contentView = [boxView valueForKeyPath:@"contentView"];
            contentView.imy_top = 0;
            
            boxView.rightButton.imy_top = boxView.contentView.imy_bottom + 24;
//                boxView.rightButton.titleColor = [IMYColor colorWithNormal:kCK_White_A high:kCK_White_A];
            
            boxView.rightButton.capType = IMYButtonCapTypeNormal;
            boxView.rightButton.titleLabel.font = [UIFont boldSystemFontOfSize:16];
            [boxView.rightButton imy_setTitleColor:kISY_Title_White];
            boxView.rightButton.type = IMYButtonTypeFillRed;
//                [boxView.rightButton imy_setBackgroundImage:[UIImage imageWithColor:[UIColor imy_colorForKey:kCK_Red_A]]];
            boxView.rightButton.imy_height = 40;
            boxView.rightButton.cornerRadius = 20;
            boxView.rightButton.imy_left = 16;
            boxView.rightButton.imy_width -= 32;
            
            boxView.leftButton.titleLabel.font = [UIFont systemFontOfSize:14];
            boxView.leftButton.imy_top = boxView.rightButton.imy_bottom + 2;
            [boxView.leftButton imy_lineViewWithDirection:IMYDirectionUp show:NO margin:0];
            [boxView.leftButton imy_removeAllCornerRadius];
            
            UIView *containBox = [boxView valueForKeyPath:@"containBox"];
            containBox.imy_height = boxView.leftButton.imy_bottom + 10;
            UIView *lineView = [boxView valueForKeyPath:@"lineView"];
            lineView.hidden = YES;
            @weakify(boxView);
            [boxView setOnActionBlock:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
                @strongify(boxView);
                if (sender == boxView.rightButton) {
                    [IMYPublicShareManager new].shareType(IMYShareTypeWeixiTimeline).title(model.wx_share_txt).imageURL(model.wx_share_icon).fromURL(model.wx_follow_url).share();
                }
                [boxView dismiss];
            }];
            [boxView show];
        }
    } else {
        BOOL canOpen = [[UIApplication sharedApplication] canOpenURL:[NSURL URLWithString:model.deep_link]];
        if (canOpen) {
            [[UIApplication sharedApplication] openURL:[NSURL URLWithString:model.deep_link] options:nil completionHandler:^(BOOL success) {
                // OK
            }];
        } else {
            // 其他全部跳转协议
            [[IMYURIManager shareURIManager] runActionWithURI:[IMYURI uriWithURIString:model.youplus_url]];
        }
    }
}


@end

