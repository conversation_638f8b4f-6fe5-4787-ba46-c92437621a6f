//
//  TTQAiGenerateView.m
//  IMYTTQ
//
//  Created by 林云峰 on 2025/1/8.
//

#import "TTQAiGenerateView.h"
#import "TTQPublishContentUtil.h"
#import <IMYBaseKit/IMYViewKit.h>
#import <SZTextView/SZTextView.h>
#import "TTQPublishContentModel.h"
#import "NSString+TTQ.h"
#import "TTQAiOutputView.h"
#import "TTQAiChanjianView.h"

@interface TTQAiGenerateView ()

@property (nonatomic, strong) TTQAiOutputView *outputingView;
@property (nonatomic, assign) TTQAiGenerateType type;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) TTQAiChanjianView *dataView;
@property (nonatomic, strong) IMYTouchEXButton *closeButton;
@property (nonatomic, strong) RACDisposable *requestDisposable;
@property (nonatomic, strong) TTQAiChanjianModel *chanjianData;
@property (nonatomic, strong) UIScrollView *runseContentScrollView;
@property (nonatomic, copy) NSString *requestContent;
@end

@implementation TTQAiGenerateView

- (instancetype)initWithContentType:(TTQAiGenerateType)type {
    self = [super initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
    if (self) {
        self.type = type;
        [self initViews];
    }
    return self;
}

- (void)initViews {
    [self imy_setBackgroundColorForKey:kCK_White_AN];
    [self setupBarView];
    
    [self addSubview:self.outputingView];
    
    if (self.type == TTQAiGenerateChanjian) {
        self.chanjianData = [TTQAiChanjianModel new];
        [self addSubview:self.dataView];
        self.outputingView.hidden = YES;
    } else if ([self isEditContent]) {
        self.runseContentScrollView = [[UIScrollView alloc] initWithFrame:CGRectMake(12, 12 + SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT, self.imy_width - 12*2, 96)];
        self.runseContentScrollView.layer.cornerRadius = 12;
        self.runseContentScrollView.layer.masksToBounds = YES;
        [self.runseContentScrollView imy_setBackgroundColorForKey:kCK_Black_H];
        [self addSubview:self.runseContentScrollView];
        self.outputingView.hidden = YES;
    } else {
        self.closeButton.hidden = YES;
    }
}

- (BOOL)isEditContent {
    if (self.type == TTQAiGeneraterRunse) {
        return YES;
    }
    if (self.type == TTQAiGeneraterAutoNewLine) {
        return YES;
    }
    return NO;
}

- (void)setupBarView {
    UIView *fakeNavi = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT)];
    [fakeNavi imy_setBackgroundColorForKey:kCK_White_AN];
    [self addSubview:fakeNavi];
    
    UILabel *label = [[UILabel alloc] initWithFrame:CGRectMake(24, SCREEN_STATUSBAR_HEIGHT, fakeNavi.imy_width - 24*2, SCREEN_NAVIGATIONBAR_HEIGHT)];
    label.textAlignment = NSTextAlignmentCenter;
    label.font = [UIFont boldSystemFontOfSize:17];
    [label imy_setTextColorForKey:kCK_Black_A];
    [fakeNavi addSubview:label];
    label.text = IMYString(@"AI帮写");
    self.titleLabel = label;

    IMYTouchEXButton *button = [[IMYTouchEXButton alloc] initWithFrame:CGRectMake(16, SCREEN_STATUSBAR_HEIGHT, 34, 44)];
    [button setExtendTouchInsets:UIEdgeInsetsMake(10, 16, 10, 4)];
    @weakify(self);
    [[button rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
        @strongify(self);
        [self removeFromSuperview];
        if (self.requestDisposable) {
            [self.requestDisposable dispose];
        }
        if (self.removeBlock) {
            self.removeBlock(YES);
        }
    }];
    [button imy_addThemeChangedBlock:^(IMYTouchEXButton *weakObject) {
        [weakObject imy_setImage:@"nav_btn_close_black"];
        if (IMYPublicAppHelper.shareAppHelper.isNight) {
            [weakObject imy_setImage:@"icon_all_close_dark"];
        }
    }];
    [fakeNavi addSubview:button];
    self.closeButton = button;
}

- (TTQAiGenerateType)aiType {
    return self.type;
}

#pragma mark - image

- (void)loadImage:(UIImage *)image {
    self.closeButton.hidden = NO;
    self.chanjianData.image = image;
    [self.dataView loadImage:image];
    [self startUploadImage:image];
}

#pragma mark - output

- (TTQAiOutputView *)outputingView {
    if (!_outputingView) {
        _outputingView = [[TTQAiOutputView alloc] initWithFrame:CGRectMake(0, SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT, self.imy_width, self.imy_height - SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT)];
        _outputingView.type = self.type;
        @weakify(self);
        [_outputingView setSuspendActionBlock:^{
            @strongify(self);
            if (self.suspendActionBlock) {
                self.suspendActionBlock();
            }
        }];
        [_outputingView setRetryActionBlock:^{
            @strongify(self);
            if (self.retryActionBlock) {
                self.retryActionBlock();
            }
        }];
        
        [_outputingView setChangeContentAction:^(id  _Nonnull data) {
            @strongify(self);
            if (self.startRequestBlock) {
                self.startRequestBlock(self.requestContent, self.aiReferer);
            }
            [self outputViewButtonBIEvent:@"换一换"];
        }];
        
        [_outputingView setUseContentBlock:^(NSString * _Nonnull content) {
            NSLog(@"use content = %@",content);
            @strongify(self);
            if (self.useContentBlock) {
                self.useContentBlock(content);
            }
            [self outputViewButtonBIEvent:content? @"使用内容":@"弃用"];
        }];
    }
    return _outputingView;
}

- (void)updateContent:(NSString *)content {
    [self.outputingView updateContent:content];
}

- (void)updateContentComplete {
    [self.outputingView updateContentComplete];
    if (self.outputingView.textView.text.length <= 8 && self.type == TTQAiGenerateChanjian) {
        [UIAlertView imy_showAlertViewWithTitle:@"AI帮写失败" message:@"未识别到产检信息。请检查孕检报告，重新选择照片" cancelButtonTitle:@"取消帮写" otherButtonTitles:@[@"重新选择照片"] handler:^(UIAlertView *alertView, NSInteger buttonIndex) {
            if (buttonIndex == 1) {
                if (self.selectImageBlock) {
                    self.selectImageBlock();
                }
                imy_asyncMainBlock(0.35, ^{
                    /// 产品说要隐藏
                    self.hidden = YES;
                    self.removeBlock(NO);
                });
                [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_aibx_tc",@"action":@2,@"public_type":@"重新选择照片",@"public_info":self.aiReferer} headers:nil completed:nil];
            } else {
                [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_aibx_tc",@"action":@2,@"public_type":@"取消帮写",@"public_info":self.aiReferer} headers:nil completed:nil];
                if (self.useContentBlock) {
                    self.useContentBlock(nil);
                }
            }
        }];
        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_aibx_tc",@"action":@1,@"public_type":@"AI帮写失败",@"public_info":self.aiReferer} headers:nil completed:nil];
    }
}

#pragma mark - 面板操作

- (void)hideRetryView {
    [self.outputingView hideRetryView];
}

- (void)updateRetryView {
    [self.outputingView updateRetryView];
}

#pragma mark - 产检
- (TTQAiChanjianView *)dataView {
    if (!_dataView) {
        _dataView = [[TTQAiChanjianView alloc] initWithFrame:CGRectMake(0, SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT, self.imy_width, self.imy_height - SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT)];
        @weakify(self);
        [_dataView setCompleteBlock:^(TTQAiChanjianModel * _Nonnull data) {
            @strongify(self);
            self.chanjianData.contentType = data.contentType;
            self.chanjianData.money = data.money;
            self.chanjianData.time = data.time;
            self.chanjianData.otherContent = data.otherContent;
            self.requestContent = self.chanjianData.requestContent;
            [self startShowOutputView:self.dataView.imageView];
        }];
        
        [_dataView setRetryActionBlock:^(TTQAIImageScanStatus status) {
            @strongify(self);
            if (status == TTQAIImageUploadFaild) {
                /// 接口请求失败的点击重试
                [self.dataView changeToState:TTQAIImageScaning];
                [self startUploadImage:self.chanjianData.image];
            } else if (status == TTQAIImageScanFaild) {
                /// 识别失败的选择新的
                if (self.selectImageBlock) {
                    self.selectImageBlock();
                }
            }
        }];
    }
    return _dataView;
}

- (void)startUploadImage:(UIImage *)image {
    if (![IMYNetState networkEnable]) {
        [self.dataView changeToState:TTQAIImageUploadFaild];
        return;
    }
//    https://apidoc.seeyouyima.com/doc/67a4756d40b4504f59bba3cb
    imy_asyncBlock(^{
        UIImage *uploadImage = [image imy_normalizedImage];
        /// 这里产检图片质量要求不高，不需要走鲁班那套
        NSString *imageEncode = [UIImageJPEGRepresentation(uploadImage, (CGFloat)0.55f) base64EncodedStringWithOptions:0];
        NSString *signKey = @"f90b42a9919f8278881185";
    #ifdef DEBUG
        if ([IMYURLEnvironmentManager currentType] == IMYURLEnviromentTypeTest) {
            signKey = @"2d5ccd3c4b2c57a3b3500a";
        }
    #endif
        NSString *timestamp = [NSString stringWithFormat:@"%.0f",[[NSDate date] timeIntervalSince1970]];
        NSString *signString = [NSString stringWithFormat:@"time=%@&user_id=%@&%@",timestamp,[IMYPublicAppHelper shareAppHelper].userid,signKey];
        signString = [signString imy_md5];
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        params[@"image_base64"] = imageEncode;
        params[@"time"] = timestamp;
        params[@"sign"] = signString;
        params[@"biz_code"] = @"community";
        params[@"referer"] = self.aiReferer;
//        [self.requestDisposable dispose];
        self.requestDisposable = [[[IMYServerRequest postPath:@"v3/image/extract" host:streamflow_seeyouyima_com params:params headers:nil] deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
            NSString *content = [x responseObject][@"content"];
            if (imy_isNotEmptyString(content)) {
                self.chanjianData.orcContent = content;
                [self.dataView changeToState:TTQAIImageScanSuccess];
            } else {
                [self.dataView changeToState:TTQAIImageScanFaild];
            }
        } error:^(NSError * _Nullable error) {
            NSDictionary *responseMap = [[error af_responseData] imy_jsonObject];
            if (responseMap && [responseMap[@"code"] integerValue] == 410) {
                [self.dataView changeToState:TTQAIImageScanFaild];
            } else {
                [self.dataView changeToState:TTQAIImageUploadFaild];
            }
        }];
    });
}

- (void)startShowOutputView:(TTQAiImageView *)imageView {
    [imageView removeFromSuperview];
    imageView.imy_top += SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT;
    [self addSubview:imageView];
    self.dataView.hidden = YES;
    CGFloat outputViewTop = imageView.imy_bottom;
    self.outputingView.frame = CGRectMake(0, imageView.imy_bottom, self.outputingView.imy_width, self.imy_height - outputViewTop);
    self.outputingView.hidden = NO;
    CGFloat scale = 100/180.f;
    NSTimeInterval duration = 0.3;
    [imageView animateToFrame:CGRectMake(12, 12 + SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT, 100, 100) duration:duration];
    
    
    [UIView animateWithDuration:duration animations:^{
        self.outputingView.frame = CGRectMake(0, 112 + SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT, self.outputingView.imy_width, self.imy_height - imageView.imy_bottom);
    } completion:^(BOOL finished) {
        if (self.startRequestBlock) {
            self.startRequestBlock(self.requestContent, self.aiReferer);
        }
    }];
    self.closeButton.hidden = YES;
}

#pragma mark - 润色

- (void)loadRunseContent:(NSString *)content {
    self.requestContent = content;
    NSString *string = [NSString stringWithFormat:@"原文：%@",content];
    UIFont *font = [UIFont systemFontOfSize:15];
    CGFloat lineHeight = 24;
    CGFloat baselineOffset = (lineHeight - font.lineHeight) / 4;
    
    NSMutableParagraphStyle *paragraphStyle = [NSMutableParagraphStyle new];
    paragraphStyle.maximumLineHeight = lineHeight;
    paragraphStyle.minimumLineHeight = lineHeight;
    NSDictionary *attributes = @{
        NSFontAttributeName: [UIFont systemFontOfSize:15],
        NSParagraphStyleAttributeName: paragraphStyle,
        NSForegroundColorAttributeName: [UIColor imy_colorForKey:kCK_Black_A],
        NSBaselineOffsetAttributeName: @(baselineOffset)
    };

    NSAttributedString *attr = [string ttq_parseTopicsWithTypingAttributes:attributes];
    UILabel *label = [[UILabel alloc] initWithFrame:CGRectMake(12, 12, self.runseContentScrollView.imy_width - 24, 10)];
    label.numberOfLines = 0;
    label.attributedText = attr;
    [label sizeToFit];
    [self.runseContentScrollView addSubview:label];
    self.runseContentScrollView.imy_height = MIN(label.imy_height + 24, 96);
    self.runseContentScrollView.contentSize = CGSizeMake(0, label.imy_height + 24);
    
    self.outputingView.hidden = NO;
    self.outputingView.frame = CGRectMake(0, self.runseContentScrollView.imy_bottom, self.outputingView.imy_width, self.imy_height - self.runseContentScrollView.imy_bottom - 12);
    if (self.startRequestBlock) {
        self.startRequestBlock(content, self.aiReferer);
    }
    self.closeButton.hidden = YES;
}

#pragma mark - BI

- (void)outputViewButtonBIEvent:(NSString *)type {
    [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_aibx_qtan",@"action":@2,@"public_type":type,@"public_info":self.aiReferer} headers:nil completed:nil];
}

@end
