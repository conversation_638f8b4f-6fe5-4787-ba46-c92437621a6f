//
//  TTQAiOutputView.m
//  IMYTTQ
//
//  Created by 林云峰 on 2025/1/8.
//

#import "TTQAiOutputView.h"
#import "TTQPublishContentUtil.h"
#import <IMYBaseKit/IMYViewKit.h>
#import <SZTextView/SZTextView.h>
#import "TTQPublishContentModel.h"
#import "NSString+TTQ.h"
#import "IMYGradientTextLabel.h"

@interface TTQAiOutputView ()

@property (nonatomic, strong) UIView *bgView;
@property (nonatomic, strong) CAGradientLayer *gradientLayer;
@property (nonatomic, strong) IMYPAGView *playIconView; /// 播放动画
@property (nonatomic, strong) IMYPAGView *effectBannerView; /// 背景动画
@property (nonatomic, strong) SZTextView *textView;
@property (nonatomic, strong) IMYGradientTextLabel *titleLabel;
@property (nonatomic, strong) UIView *aiOperationView;
@property (nonatomic, strong) UIView *aiRetryView;
@property (nonatomic, strong) IMYPAGView *pagView;
@property (nonatomic, strong) UIView *aiLoadingView;
@property (nonatomic, strong) YYAnimatedImageView *cursorView;
@property (nonatomic, strong) NSAttributedString *loadingAttributedString;
@property (nonatomic, strong) IMYCapsuleButton *changeButton;
@property (nonatomic, strong) UIButton *stopButton; /// 终止按钮
@property (nonatomic, strong) UIButton *deprecateButton;    // 弃用按钮
@property (nonatomic, strong) UIButton *conformButton;      // 确认按钮

@end

@implementation TTQAiOutputView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self initViews];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    self.aiOperationView.imy_bottom = self.imy_height;
    self.bgView.imy_height = self.aiOperationView.imy_top - 51;
    self.gradientLayer.frame = self.bgView.bounds;
    CAShapeLayer *maskLayer = [CAShapeLayer layer];
    maskLayer.lineWidth = 1;
    UIBezierPath * path = [UIBezierPath bezierPathWithRoundedRect:self.bgView.bounds cornerRadius:12];
    maskLayer.path = path.CGPath;
    maskLayer.fillColor = [UIColor clearColor].CGColor;
    maskLayer.strokeColor = [UIColor blueColor].CGColor;
    
    self.gradientLayer.mask = maskLayer;
//
//    [self addGradientLayerWithCorner:self.bgView withCornerRadius:12 withLineWidth:1*SCREEN_SCALE withColors:@[(id)[UIColor imy_colorForKey:@"BDBEFF"].CGColor,(id)[UIColor imy_colorForKey:@"C39BFF"].CGColor]];
}

- (void)initViews {
    [self imy_setBackgroundColorForKey:kCK_White_AN];
    self.bgView = [[UIView alloc] initWithFrame:CGRectMake(12, 12, self.imy_width - 24, 100)];
    [self.bgView imy_setBackgroundColorForKey:kCK_White_AN];
    [self addSubview:self.bgView];
    [self.bgView addSubview:self.aiRetryView];
    [self.bgView addSubview:self.effectBannerView];
    [self.bgView addSubview:self.playIconView];
    [self.bgView addSubview:self.titleLabel];
    IMYLineView *line = [IMYLineView lineView];
    line.frame = CGRectMake(12, 48, self.bgView.imy_width - 24, 1);
    [self.bgView addSubview:line];
    
    [self setTitleName:@"AI创作中"];
    
    [self addGradientLayerWithCorner:self.bgView withCornerRadius:12 withLineWidth:1*SCREEN_SCALE withColors:@[(id)[UIColor imy_colorForKey:@"BDBEFF"].CGColor,(id)[UIColor imy_colorForKey:@"C39BFF"].CGColor]];
    
    self.textView = [TTQPublishContentUtil publishTextView];
    [self.textView imy_setBackgroundColor:[UIColor clearColor]];
    self.textView.scrollEnabled = YES;
    self.textView.editable = NO;
    self.textView.selectable = NO;
    [self.bgView addSubview:self.textView];
    self.aiRetryView.hidden = YES;
    
    [self.aiRetryView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.bgView);
        make.left.right.bottom.equalTo(self.bgView);
    }];
    [self.textView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@48);
        make.left.right.bottom.equalTo(self.bgView);
    }];
    
    [self addSubview:self.aiOperationView];
    
    self.changeButton = [[UIButton alloc] initWithFrame:CGRectMake(0, 10, 68, 28)];
    self.changeButton.layer.masksToBounds = YES;
    self.changeButton.layer.cornerRadius = 14;
    self.changeButton.layer.borderWidth = 1/SCREEN_SCALE;
    self.changeButton.layer.borderColor = [UIColor imy_colorForKey:kCK_Black_M].CGColor;
    [self.changeButton imy_setTitleColor:kCK_Black_M];
    [self.changeButton imy_setTitle:IMYString(@"换一换")];
    self.changeButton.titleLabel.font = [UIFont systemFontOfSize:13];
    self.changeButton.imy_right = self.bgView.imy_width - 12;
    @weakify(self);
    [[self.changeButton rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
        @strongify(self);
        self.textView.attributedText = nil;
        self.textView.text = nil;
        [self.textView setContentOffset:CGPointZero];
        [self updateContent:@""];
        [self.playIconView play];
        [self.effectBannerView play];
        if (self.changeContentAction) {
            self.changeContentAction(@1);
        }
    }];
    [self.bgView addSubview:self.changeButton];
    self.changeButton.hidden = YES;

}

- (void)addGradientLayerWithCorner:(UIView *)view withCornerRadius:(float)cornerRadius withLineWidth:(float)lineWidth withColors:(NSArray *)colors{
    view.layer.cornerRadius = cornerRadius;
    view.layer.masksToBounds = YES;
    CGRect mapRect = view.bounds;
    CAGradientLayer * gradientLayer = [CAGradientLayer layer];
    gradientLayer.frame = CGRectMake(0, 0, view.frame.size.width, view.frame.size.height);
    gradientLayer.colors = colors;
    gradientLayer.startPoint = CGPointMake(0, 0);
    gradientLayer.endPoint = CGPointMake(0, 1);
    gradientLayer.cornerRadius = cornerRadius;
    
    CAShapeLayer *maskLayer = [CAShapeLayer layer];
    maskLayer.lineWidth = lineWidth;
    UIBezierPath * path = [UIBezierPath bezierPathWithRoundedRect:mapRect cornerRadius:cornerRadius];
    maskLayer.path = path.CGPath;
    maskLayer.fillColor = [UIColor clearColor].CGColor;
    maskLayer.strokeColor = [UIColor blueColor].CGColor;
    
    gradientLayer.mask = maskLayer;
    [view.layer addSublayer:gradientLayer];
    self.gradientLayer = gradientLayer;
}


- (void)setType:(TTQAiGenerateType)type {
    _type = type;
    if (_type == TTQAiGeneraterAutoNewLine) {
        [self updateButtonTitle:IMYString(@"使用分段")];
    }
}

#pragma mark - textView
- (void)updateContentComplete {
    self.aiLoadingView.hidden = YES;
    self.cursorView.hidden = YES;
    self.aiRetryView.hidden= YES;
    if ([self.pagView isPlaying]) {
        [self.pagView stop];
    }
    if ([self.effectBannerView isPlaying]) {
        [self.effectBannerView stop];
    }
    if (self.playIconView.isPlaying) {
        [self.playIconView stop];
        [self.playIconView setProgress:0];
    }
    self.changeButton.hidden = NO;
    self.stopButton.hidden = YES;
    self.deprecateButton.hidden = self.conformButton.hidden = NO;
    [self setTitleName:@"创作好啦"];
    self.textView.userInteractionEnabled = YES;
}

- (void)updateContent:(NSString *)content {
    self.textView.userInteractionEnabled = NO;
    self.textView.hidden = NO;
    [self setTitleName:@"AI创作中"];
    self.changeButton.hidden = YES;
    self.stopButton.hidden = NO;
    self.deprecateButton.hidden = self.conformButton.hidden = YES;
    if (imy_isEmptyString(content)) {
        self.aiLoadingView.hidden = NO;
        [self.textView addSubview:self.aiLoadingView];
        [self.pagView play];
        self.textView.placeholder = @"";
        self.cursorView.hidden = YES;
    } else {
        self.aiLoadingView.hidden = YES;
        self.cursorView.hidden = NO;
        if ([self.pagView isPlaying]) {
            [self.pagView stop];
        }
        NSMutableAttributedString *attributedText = [[NSMutableAttributedString alloc] initWithAttributedString:[content ttq_parseTopicsWithTypingAttributes:[TTQPublishContentUtil textViewTypingAttribute]]];
        [attributedText appendAttributedString:self.loadingAttributedString];
        self.textView.attributedText = attributedText;
        [self.textView layoutIfNeeded];
        NSAttributedString *string = self.textView.attributedText;
        CGRect frame = [self frameOfTextRange:NSMakeRange(string.length - 1, 1) inTextView:self.textView];
        [self.textView addSubview:self.cursorView];
        self.cursorView.imy_left = CGRectGetMinX(frame) + 2 ;
        self.cursorView.imy_top = CGRectGetMinY(frame) + 0.5 * (frame.size.height - 8) + 2;
        CGFloat offsetY = self.textView.contentSize.height - self.textView.imy_height;
        if (offsetY < 0) {
            offsetY = 0;
        }
        /// 保持底部可见
        [self.textView setContentOffset:CGPointMake(0, offsetY)];
    }
}

- (CGRect)frameOfTextRange:(NSRange)range inTextView:(UITextView *)textView
{
    [textView.layoutManager ensureLayoutForTextContainer:textView.textContainer];
    UITextPosition *beginning = textView.beginningOfDocument;
    UITextPosition *start = [textView positionFromPosition:beginning offset:range.location];
    UITextPosition *end = [textView positionFromPosition:start offset:range.length];
    UITextRange *textRange = [textView textRangeFromPosition:start toPosition:end];
    CGRect rect = [textView firstRectForRange:textRange];
    return [textView convertRect:rect fromView:textView.textInputView];
}

- (UIView *)aiLoadingView {
    if (!_aiLoadingView) {
        _aiLoadingView = [[UIView alloc] initWithFrame:CGRectMake(16, 8, 200, 22)];
        [_aiLoadingView addSubview:self.pagView];
        UILabel *label = [[UILabel alloc] initWithFrame:CGRectMake(24, 0, 200, 22)];
        label.font = [UIFont systemFontOfSize:17];
        [label imy_setTextColorForKey:kCK_Black_J];
        label.text = IMYString(@"AI正在编写文案");
        [_aiLoadingView addSubview:label];
        _aiLoadingView.hidden = YES;
    }
    return _aiLoadingView;
}

- (IMYPAGView *)pagView {
    if (!_pagView) {
        _pagView = [[IMYPAGView alloc] initWithFrame:CGRectMake(0, 0, 20, 22)];
        NSString *path = [[NSBundle mainBundle] pathForResource:@"ugc_ai_text_loading" ofType:@"pag"];
        [_pagView loadWithURL:[NSURL fileURLWithPath:path] placeholder:nil completed:nil];
    }
    return _pagView;
}

- (YYAnimatedImageView *)cursorView {
    if (!_cursorView) {
        _cursorView = [[YYAnimatedImageView alloc] initWithFrame:CGRectMake(0, 0, 8, 8)];
        NSURL *url = [NSURL fileURLWithPath:[[NSBundle mainBundle] pathForResource:@"ugc_ai_text_tail" ofType:@"apng"]];
        [_cursorView imy_setOriginalImageURL:url];
        _cursorView.contentMode = UIViewContentModeScaleAspectFit;
    }
    return _cursorView;
}

- (NSAttributedString *)loadingAttributedString {
    if (!_loadingAttributedString) {
        NSTextAttachment *loading = [NSTextAttachment new];
        loading.image = [UIImage imy_imageForKey:@"ugc_cursor"];
        loading.bounds = CGRectMake(0, 0, 12, 12);
        NSMutableAttributedString *attributedString = [NSMutableAttributedString attributedStringWithAttachment:loading];
        /// 对齐textView的排版设置，避免修改textView的font
        [attributedString addAttributes:[TTQPublishContentUtil textViewTypingAttribute] range:NSMakeRange(0, attributedString.length)];
        _loadingAttributedString = attributedString;
    }
    return _loadingAttributedString;
}

#pragma mark - 面板操作

- (void)hideRetryView {
    self.aiRetryView.hidden = YES;
    self.textView.hidden = NO;
}

- (void)updateRetryView {
    self.aiRetryView.hidden = NO;
    self.textView.hidden = YES;
    self.textView.text = nil;
    self.textView.attributedText = nil;
    UILabel *label = [self.aiRetryView viewWithTag:1];
    label.text = [IMYNetState networkEnable]?IMYString(@"网络超时，请重试"):IMYString(@"网络不见了，请检查网络");
}

- (UIView *)aiOperationView {
    if (!_aiOperationView) {
        CGFloat height = 40 + 36 + SCREEN_TABBAR_SAFEBOTTOM_MARGIN;
        _aiOperationView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, height)];
        [_aiOperationView imy_setBackgroundColorForKey:kCK_White_AN];
        UIButton *button = [[UIButton alloc] initWithFrame:CGRectMake(0, 8, 136, 32)];
        button.imy_centerX = SCREEN_WIDTH/2;
        [button imy_setImage:@"icon_close_black_16"];
        [button imy_setTitle:@"终止AI编写"];
        button.imageEdgeInsets = UIEdgeInsetsMake(0, -1, 0, 1);
        button.titleEdgeInsets = UIEdgeInsetsMake(0, 1, 0, -1);
        [button imy_setTitleColorForKey:kCK_Black_M andState:UIControlStateNormal];
        button.layer.cornerRadius = 16;
        button.layer.borderWidth = 1/SCREEN_SCALE;
        button.titleLabel.font = [UIFont systemFontOfSize:15];
        [button imy_addThemeChangedBlock:^(id weakObject) {
            button.layer.borderColor = [UIColor imy_colorForKey:kCK_Black_C].CGColor;
        }];
        @weakify(self);
        [[button rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
            @strongify(self);
            if (self.suspendActionBlock) {
                self.suspendActionBlock();
            }
            [self updateContentComplete];
        }];
        [_aiOperationView addSubview:button];
        self.stopButton = button;
        [_aiOperationView addSubview:self.deprecateButton];
        [_aiOperationView addSubview:self.conformButton];
        CGFloat leftMargin = (self.imy_width - self.deprecateButton.imy_width - self.conformButton.imy_width - 20)/2;
        self.deprecateButton.imy_left = leftMargin;
        self.conformButton.imy_left = self.deprecateButton.imy_right + 20;
        self.deprecateButton.hidden = self.conformButton.hidden = YES;
    }
    return _aiOperationView;
}
- (UIView *)aiRetryView {
    if (!_aiRetryView) {
        UIView *retryView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, self.bgView.imy_width , 73)];
        [retryView imy_setBackgroundColorForKey:kCK_White_AN];
        _aiRetryView = retryView;
        
        UIView *container = [[UIView alloc] initWithFrame:CGRectMake(0, 0, self.bgView.imy_width, 0)];
        [_aiRetryView addSubview:container];
        UILabel *label = [[UILabel alloc] initWithFrame:CGRectMake(12, 0, container.imy_width - 24, 21)];
        label.textAlignment = NSTextAlignmentCenter;
        label.font = [UIFont systemFontOfSize:15];
        label.text = @"网络超时，请重试";
        label.tag = 1;
        [label imy_setTextColorForKey:kCK_Black_B];
        [container addSubview:label];
        
        UIButton *button = [[UIButton alloc] initWithFrame:CGRectMake(0, label.imy_bottom + 20, 92, 32)];
        button.imy_centerX = _aiRetryView.imy_width/2;
        button.titleLabel.font = [UIFont systemFontOfSize:15];
        [button imy_setTitleColorForKey:kCK_Red_B andState:UIControlStateNormal];
        [button imy_setTitle:IMYString(@"重新加载")];
        button.layer.cornerRadius = 16;
        button.layer.borderColor = [UIColor imy_colorForKey:kCK_Red_B].CGColor;
        button.layer.borderWidth = 1/SCREEN_SCALE;
        [container addSubview:button];
        @weakify(self);
        [[button rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
            @strongify(self);
            if (![IMYNetState networkEnable]) {
                [self updateRetryView];
                return;
            }
            self.aiRetryView.hidden = YES;
            self.textView.hidden = NO;
            if (self.retryActionBlock) {
                self.retryActionBlock();
            }
        }];
        CGFloat height = button.imy_bottom;
        container.imy_height = height;
        
        [container mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(retryView);
            make.height.equalTo(@(height));
            make.centerY.equalTo(retryView);
        }];
    }
    return _aiRetryView;
}

- (void)updateButtonTitle:(NSString *)title {
    [self.conformButton imy_setTitle:title];
}

#pragma mark - 顶部UI

- (IMYPAGView *)playIconView {
    if (!_playIconView) {
        _playIconView = [[IMYPAGView alloc] initWithFrame:CGRectMake(12, 12, 24, 24)];
        NSString *path = [[NSBundle mainBundle] pathForResource:@"ai_loading_icon" ofType:@"pag"];
        if (imy_isNotEmptyString(path)) {
            [_playIconView loadWithURL:[NSURL fileURLWithPath:path] placeholder:nil completed:nil];
        }
    }
    return _playIconView;
}

- (IMYPAGView *)effectBannerView {
    if (!_effectBannerView) {
        _effectBannerView = [[IMYPAGView alloc] initWithFrame:CGRectMake(0, 0, self.imy_width - 24, 120)];
        _effectBannerView.contentMode = UIViewContentModeScaleAspectFill;
        _effectBannerView.clipsToBounds = YES;
        NSString *path = [[NSBundle mainBundle] pathForResource:@"ai_textview_bg" ofType:@"pag"];
        if (imy_isNotEmptyString(path)) {
            [_effectBannerView loadWithURL:[NSURL fileURLWithPath:path] placeholder:nil completed:nil];
        }
        [_effectBannerView imy_addThemeChangedBlock:^(IMYPAGView *weakObject) {
            weakObject.hidden = [IMYPublicAppHelper shareAppHelper].isNight;
        }];
    }
    return _effectBannerView;
}

- (IMYGradientTextLabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[IMYGradientTextLabel alloc] initWithFont:[UIFont boldSystemFontOfSize:17] colors:@[[UIColor imy_colorForKey:@"#5988FE"],[UIColor imy_colorForKey:@"#B54EFF"]]];
        _titleLabel.frame = CGRectMake(40, 12, 120, 24);
    }
    return _titleLabel;
}

- (void)setTitleName:(NSString *)titleName {
    CGFloat width = [titleName boundingRectWithSize:CGSizeMake(CGFLOAT_MAX, 24) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:[UIFont boldSystemFontOfSize:17]} context:nil].size.width;
    self.titleLabel.imy_width = ceil(width);
    self.titleLabel.text = titleName;
}

- (UIButton *)deprecateButton {
    if (!_deprecateButton) {
        _deprecateButton = [[UIButton alloc] initWithFrame:CGRectMake(0, 8, 62, 32)];
        _deprecateButton.layer.cornerRadius = 16;
        _deprecateButton.layer.borderWidth = 1/SCREEN_SCALE;
        _deprecateButton.titleLabel.font = [UIFont systemFontOfSize:15];
        [_deprecateButton imy_setTitle:IMYString(@"弃用")];
        [_deprecateButton imy_addThemeChangedBlock:^(UIButton *button) {
            button.layer.borderColor = [UIColor imy_colorForKey:kCK_Black_M].CGColor;
            [button imy_setTitleColor:kCK_Black_M];
        }];
        @weakify(self);
        [[_deprecateButton rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
            @strongify(self);
            if (self.useContentBlock) {
                self.useContentBlock(nil);
            }
        }];
    }
    return _deprecateButton;
}

- (UIButton *)conformButton {
    if (!_conformButton) {
        _conformButton = [[UIButton alloc] initWithFrame:CGRectMake(0, 8, 92, 32)];
        _conformButton.layer.cornerRadius = 16;
        _conformButton.layer.borderWidth = 1/SCREEN_SCALE;
        _conformButton.titleLabel.font = [UIFont systemFontOfSize:15];
        [_conformButton imy_setTitle:IMYString(@"使用内容")];
        [_conformButton imy_addThemeChangedBlock:^(UIButton *button) {
            button.layer.borderColor = [UIColor imy_colorForKey:kCK_Red_B].CGColor;
            [button imy_setTitleColor:kCK_Red_B];
        }];
        @weakify(self);
        [[_conformButton rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
            @strongify(self);
            if (self.useContentBlock) {
                self.useContentBlock(self.textView.text);
            }
        }];

    }
    return _conformButton;
}

@end
