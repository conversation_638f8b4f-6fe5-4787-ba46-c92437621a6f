//
//  TTQAiOutputView.h
//  IMYTTQ
//
//  Created by 林云峰 on 2025/1/8.
//

#import <UIKit/UIKit.h>
#import "TTQAiGenerateView.h"

NS_ASSUME_NONNULL_BEGIN

@class SZTextView;

@interface TTQAiOutputView : UIView
/// 终止AI输出
@property (nonatomic, copy) void (^suspendActionBlock)(void);
/// 重试
@property (nonatomic, copy) void (^retryActionBlock)(void);

@property (nonatomic, copy) void (^changeContentAction)(id data);

@property (nonatomic, copy) void (^useContentBlock)(NSString *content);

@property (nonatomic, strong, readonly) SZTextView *textView;
@property (nonatomic, assign) TTQAiGenerateType type;

- (void)setTitleName:(NSString *)titleName;
- (void)updateContent:(NSString *)content;
- (void)hideRetryView;
- (void)updateRetryView;
/// 外部调用
- (void)updateContentComplete;
- (void)updateButtonTitle:(NSString *)title;

@end

NS_ASSUME_NONNULL_END
