//
//  TTQPublishToolView.m
//  IMYTTQ
//
//  Created by <PERSON><PERSON> on 2017/2/17.
//  Copyright © 2017年 MeiYou. All rights reserved.
//

#import "TTQPublishToolView.h"
#import "TTQInsetsLabel.h"
#import "TTQMacro.h"
#import "UIFont+TTQ.h"


@interface TTQPublishToolMenuItem ()
@property (nonatomic, assign) NSInteger itemId;
@property (nonatomic, copy) NSString *iconFontName;
@property (nonatomic, copy) NSString *title;
@property (nonatomic, copy) NSString *selectedIconFontName;
@property (nonatomic, copy) NSString *selectedTitle;
@property (nonatomic, assign) BOOL origiSelected;
@property (nonatomic, copy) void (^tapAction)(id sender);
@property (nonatomic, weak) id sender;
@end

@implementation TTQPublishToolMenuItem

+ (TTQPublishToolMenuItem *)emotionItem {
    return [[TTQPublishToolMenuItem alloc] init].ItemID(TTQPublishToolMenuItemTypeForEmotion).ItemTitle(IMYString(@"表情")).ItemIconFontName(@"\U0000e6ee").ItemSelectedTitle(IMYString(@"键盘")).ItemSelectedIconFontName(@"\U0000e6ea");
}

+ (TTQPublishToolMenuItem *)cameraItem {
    return [[TTQPublishToolMenuItem alloc] init].ItemID(TTQPublishToolMenuItemTypeForCamera).ItemTitle(IMYString(@"图片")).ItemIconFontName(@"\U0000e608");
}

+ (TTQPublishToolMenuItem *)voteItem {
    return [[TTQPublishToolMenuItem alloc] init].ItemID(TTQPublishToolMenuItemTypeForVote).ItemTitle(IMYString(@"投票")).ItemIconFontName(@"\U0000e6f1");
}

+ (TTQPublishToolMenuItem *)hotTopicItem {
    return [[TTQPublishToolMenuItem alloc] init].ItemID(TTQPublishToolMenuItemTypeForlToolTopic).ItemTitle(IMYString(@"热议话题")).ItemIconFontName(@"\U0000e669");
}

+ (TTQPublishToolMenuItem *)anonymityItem {
    return [[TTQPublishToolMenuItem alloc] init].ItemID(TTQPublishToolMenuItemTypeForAnonymity).ItemTitle(IMYString(@"公开")).ItemIconFontName(@"\U0000e668").ItemSelectedTitle(IMYString(@"匿名")).ItemSelectedIconFontName(@"\U0000e66a");
}

+ (TTQPublishToolMenuItem *)experienceTemplateItem {
    return [[TTQPublishToolMenuItem alloc] init].ItemID(TTQPublishToolMenuItemTypeForExperienceTemplate).ItemTitle(IMYString(@"模板")).ItemIconFontName(@"\U0000e6e5").ItemSelectedTitle(IMYString(@"键盘")).ItemSelectedIconFontName(@"\U0000e6e6");
}

+ (TTQPublishToolMenuItem *)aiToolItem {
    return [[TTQPublishToolMenuItem alloc] init].ItemID(TTQPublishToolMenuItemTypeForAiTool).ItemTitle(IMYString(@"发帖灵感")).ItemIconFontName(@"\U0000e6e5").ItemSelectedTitle(IMYString(@"发帖灵感")).ItemSelectedIconFontName(@"\U0000e6e6");
}

- (PublishToolMenuItemID)ItemID {
    return ^TTQPublishToolMenuItem *(NSInteger itemId) {
        self.itemId = itemId;
        return self;
    };
}

- (PublishToolMenuItemID)ItemOrigiSelected {
    return ^TTQPublishToolMenuItem *(NSInteger origiSelected) {
        self.origiSelected = origiSelected != 0;
        return self;
    };
}

- (PublishToolMenuItemTitle)ItemTitle {
    return ^TTQPublishToolMenuItem *(NSString *title) {
        self.title = title;
        return self;
    };
}

- (PublishToolMenuItemTitle)ItemIconFontName {
    return ^TTQPublishToolMenuItem *(NSString *iconFontName) {
        self.iconFontName = iconFontName;
        return self;
    };
}

- (PublishToolMenuItemTitle)ItemSelectedTitle {
    return ^TTQPublishToolMenuItem *(NSString *selectedTitle) {
        self.selectedTitle = selectedTitle;
        return self;
    };
}

- (PublishToolMenuItemTitle)ItemSelectedIconFontName {
    return ^TTQPublishToolMenuItem *(NSString *selectedIconFontName) {
        self.selectedIconFontName = selectedIconFontName;
        return self;
    };
}

- (PublishToolMenuItemAction)ItemTapAction {
    return ^TTQPublishToolMenuItem *(EventAction tapAction) {
        self.tapAction = tapAction;
        return self;
    };
}

- (void)setTapAction:(void (^)(id))tapAction {
    _tapAction = tapAction;
    if ([self.sender isKindOfClass:[UIControl class]]) {
        [(UIControl *)self.sender bk_removeEventHandlersForControlEvents:UIControlEventTouchUpInside];
        if (tapAction) {
            [(UIControl *)self bk_addEventHandler:tapAction forControlEvents:UIControlEventTouchUpInside];
        }
    }
}
@end

@interface TTQPublishToolView ()
@property (nonatomic, strong) NSArray<TTQPublishButton *> *itemBtns; //按钮集合
@property (nonatomic, strong) UIImageView *templateBadge;   /// 模板功能红标

@end

@implementation TTQPublishToolView

+ (TTQPublishToolView *)publishToolView {
    TTQPublishToolView *toolView = [[TTQPublishToolView alloc] initWithFrame:CGRectMake(0, 0, 0, 60.0)];
    return toolView;
}

- (instancetype)init {
    if (self = [super init]) {
        [self setupView];
        self.backgroundColor = [UIColor redColor];
    }
    return self;
}

- (instancetype)initWithCoder:(NSCoder *)aDecoder {
    if (self = [super initWithCoder:aDecoder]) {
        [self setupView];
    }
    return self;
}

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setupView];
    }
    return self;
}

- (void)setPublishToolState:(TTQPublishToolState)publishToolState {
    _publishToolState = publishToolState;
    if (publishToolState == TTQPublishToolStateNormalWithOnlyImage) {
        [self updateViewForNormalWithOnlyImage];
    } else if (publishToolState == TTQPublishToolStateVote) {
        [self updateViewForVote];
    } else if (publishToolState == TTQPublishToolStateOnlyPackUP) {
        [self updateViewForPackUP];
    } else if (publishToolState == TTQPublishToolStateShowAiTool) {
        [self updateViewForNormal];
        UIButton *button = [self buttonForType:TTQPublishToolMenuItemTypeForAiTool];
        button.selected = YES;
    }else {
        [self updateViewForNormal];
    }
}

#pragma mark - 布局
- (void)setItems:(NSArray<TTQPublishToolMenuItem *> *)items_ {
    _items = items_;
    [self setupView];
}

- (void)setupView {
    NSArray *items = self.items;
    [self.subviews bk_each:^(UIView *subView) {
        if ([subView isKindOfClass:TTQPublishButton.class] || [subView isKindOfClass:IMYTouchEXButton.class]) {
            [subView removeFromSuperview];
        }
    }];
    if (self.templateBadge.superview) {
        [self.templateBadge removeFromSuperview];
        self.templateBadge = nil;
    }
    
    //匿名
    self.moreActionButton = [[IMYTouchEXButton alloc] init];
    self.moreActionButton.extendTouchInsets = UIEdgeInsetsMake(10, 20, 10, 10);
    [self.moreActionButton imy_addThemeChangedBlock:^(IMYTouchEXButton *weakObject) {
        UIImage *image = [UIImage imy_imageForKey:@"release_bar_icon_more"];
        [weakObject setImage:image.imy_getNightStyleTopBarImage forState:UIControlStateNormal];
    }];
    [self addSubview:self.moreActionButton];
    [self.moreActionButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.width.mas_equalTo(22);
        make.height.mas_equalTo(22);
        make.right.equalTo(self).offset(-12);
    }];
    [self.moreActionButton addTarget:self action:@selector(anonymousButtonAction:) forControlEvents:UIControlEventTouchUpInside];
    
    NSMutableArray *itemBtns = [NSMutableArray array];
    for (TTQPublishToolMenuItem *item in items) {
        TTQPublishButton *btn = [TTQPublishButton buttonWithType:UIButtonTypeCustom];
        btn.tag = item.itemId;
        BOOL mAnonymityn = NO;
        // 1 图片, 2 表情, 3 热议, 4 投票, 5 匿名， 6 模板
        if (item.itemId == 1) {
            //图片
            [btn setImage:[UIImage imy_imageForKey:@"editor_icon_img"].imy_getNightStyleTopBarImage forState:UIControlStateNormal];
            [btn setImage:[UIImage imy_imageForKey:@"editor_icon_img"] forState:UIControlStateSelected];
            [btn setImage:[UIImage imy_imageForKey:@"editor_icon_img_pressed"] forState:UIControlStateHighlighted];
            [btn setImage:[UIImage imy_imageForKey:@"editor_icon_img_fail"] forState:UIControlStateDisabled];
            self.picButton = btn;
        }else if (item.itemId == 2) {
            //表情
            [btn setImage:[UIImage imy_imageForKey:@"editor_icon_emoji"].imy_getNightStyleTopBarImage forState:UIControlStateNormal];
            [btn setImage:[UIImage imy_imageForKey:@"editor_icon_keyboard"].imy_getNightStyleTopBarImage forState:UIControlStateSelected];
            [btn setImage:[UIImage imy_imageForKey:@"editor_icon_emoji_pressed"] forState:UIControlStateHighlighted];
            [btn setImage:[UIImage imy_imageForKey:@"editor_icon_emoji_fail"] forState:UIControlStateDisabled];
        }else if (item.itemId == 4){
            //投票
            [btn setImage:[UIImage imy_imageForKey:@"editor_icon_vote"].imy_getNightStyleTopBarImage forState:UIControlStateNormal];
            [btn setImage:[UIImage imy_imageForKey:@"editor_icon_vote"] forState:UIControlStateSelected];
            [btn setImage:[UIImage imy_imageForKey:@"editor_icon_vote_pressed"] forState:UIControlStateHighlighted];
            [btn setImage:[UIImage imy_imageForKey:@"editor_icon_vote_fail"] forState:UIControlStateDisabled];
        } else if (item.itemId == 5){
            //匿名 这里看看要不要走配置
        } else if (item.itemId == TTQPublishToolMenuItemTypeForExperienceTemplate) {
            [btn setImage:[UIImage imy_imageForKey:@"post_bar_icon_template"].imy_getNightStyleTopBarImage forState:UIControlStateNormal];
            [btn setImage:[UIImage imy_imageForKey:@"editor_icon_keyboard"].imy_getNightStyleTopBarImage forState:UIControlStateSelected];
            [btn setImage:[UIImage imy_imageForKey:@"post_bar_icon_template"] forState:UIControlStateHighlighted];
            [btn setImage:[UIImage imy_imageForKey:@"post_bar_icon_template_gray"] forState:UIControlStateDisabled]; 
            btn.hidden = YES;
            
        } else if (item.itemId == TTQPublishToolMenuItemTypeForlToolTopic) {
            [btn setImage:[UIImage imy_imageForKey:@"editor_tool_icon_topic"].imy_getNightStyleTopBarImage forState:UIControlStateNormal];
            [btn setImage:[UIImage imy_imageForKey:@"editor_tool_icon_topic"].imy_getNightStyleTopBarImage forState:UIControlStateSelected];
            [btn setImage:[UIImage imy_imageForKey:@"editor_tool_icon_topic_pink"] forState:UIControlStateHighlighted];
            [btn setImage:[UIImage imy_imageForKey:@"editor_icon_topic_gray"] forState:UIControlStateDisabled];
            self.topicButton = btn;
        } else if (item.itemId == TTQPublishToolMenuItemTypeForAiTool) {
            [btn setImage:[UIImage imy_imageForKey:@"fbj_bar_ai"] forState:UIControlStateNormal];
            [btn setImage:[UIImage imy_imageForKey:@"editor_icon_keyboard"].imy_getNightStyleTopBarImage forState:UIControlStateSelected];
            [btn setImage:[UIImage imy_imageForKey:@"fbj_bar_ai"] forState:UIControlStateHighlighted];
            [btn setImage:[UIImage imy_imageForKey:@"fbj_bar_ai"] forState:UIControlStateDisabled];
            self.aiButton = btn;
        }
        btn.selected = item.origiSelected;
        [btn addTarget:self action:@selector(buttonAction:) forControlEvents:UIControlEventTouchUpInside];
        [self addSubview:btn];
        [itemBtns addObject:btn];
        if (item.itemId == TTQPublishToolMenuItemTypeForExperienceTemplate) {
            [self showTemplateBadgeIfNeeded:btn];
        }
    }
        
    self.itemBtns = itemBtns.copy;
    
    //布局呀
    if (self.itemBtns.count == 0) {
        return;
    }

    @weakify(self);
    for (NSInteger index = 0; index < self.itemBtns.count; index++) {
        TTQPublishButton *btn = self.itemBtns[index];
        TTQPublishButton *preBtn = nil;
        if (index > 0) {
            preBtn = self.itemBtns[index - 1];
        }
        [btn mas_makeConstraints:^(MASConstraintMaker *make) {
            @strongify(self);
            make.top.and.bottom.offset(0.0);
            if (self.itemBtns.count - 1 == index && btn.tag == 5) {
                //最后一个按钮是匿名按钮的情况
                make.width.mas_equalTo(46+32);//按钮宽46，距离左右16
                make.right.equalTo(self).offset(0);
            } else {
                if (preBtn) {
                    make.left.equalTo(preBtn.mas_right);
                } else {
                    make.left.offset(10.0);
                }
                make.width.mas_equalTo(22+28);//按钮宽26，距离左右14
            }
        }];
    }
    [self setPublishToolState:TTQPublishToolStateNormal];
}

-(void)hideExperienceTemplate{
    // 模板少于三个的时候要做真正的移除按钮，因为测试后台可以把顺序配到前面，导致前面空了
    if([self viewWithTag:TTQPublishToolMenuItemTypeForExperienceTemplate].superview != nil){
        TTQPublishButton *btn = [self viewWithTag:TTQPublishToolMenuItemTypeForExperienceTemplate];
        [btn removeFromSuperview];
        NSMutableArray *mutItemBtns =  [self.itemBtns mutableCopy];
        [mutItemBtns removeObject:btn];
        self.itemBtns = [mutItemBtns copy];
        @weakify(self);
        for (NSInteger index = 0; index < self.itemBtns.count; index++) {
            TTQPublishButton *btn = self.itemBtns[index];
            TTQPublishButton *preBtn = nil;
            if (index > 0) {
                preBtn = self.itemBtns[index - 1];
            }
            [btn mas_remakeConstraints:^(MASConstraintMaker *make) {
                @strongify(self);
                make.top.and.bottom.offset(0.0);
                if (self.itemBtns.count - 1 == index && btn.tag == 5) {
                    //最后一个按钮是匿名按钮的情况
                    make.width.mas_equalTo(46+32);//按钮宽46，距离左右16
                    make.right.equalTo(self).offset(0);
                } else {
                    if (preBtn) {
                        make.left.equalTo(preBtn.mas_right);
                    } else {
                        make.left.offset(10.0);
                    }
                    make.width.mas_equalTo(22+28);//按钮宽26，距离左右14
                }
            }];
        }
        if (self.templateBadge) {
            [self.templateBadge removeFromSuperview];
        }
    }
}


- (void)anonymousButtonAction:(IMYTouchEXButton*)sender {
    if (self.clickMoreBtn) {
        self.clickMoreBtn(sender);
    }
}

- (void)updateViewForNormal {
    for (TTQPublishButton *btn in self.itemBtns) {
        if (btn.tag == TTQPublishToolMenuItemTypeForlToolTopic) {
            continue;
        }
        btn.enabled = YES;
        btn.selected = NO;
    }
    [self changeStateWithVideoSelect:self.videoSelected];
}

- (void)updateViewForNormalWithOnlyImage {
    for (TTQPublishButton *btn in self.itemBtns) {
        if (btn.tag == TTQPublishToolMenuItemTypeForlToolTopic) {
            continue;
        }
        btn.enabled = YES;
        btn.selected = NO;
    }
    [self changeStateWithVideoSelect:self.videoSelected];
}

- (void)updateViewForVote {
    for (TTQPublishButton *btn in self.itemBtns) {
        if (btn.tag == TTQPublishToolMenuItemTypeForlToolTopic) {
            continue;
        }
        btn.enabled = NO;
        if (btn.tag == TTQPublishToolMenuItemTypeForEmotion) {
            btn.selected = NO;
        } else if (btn.tag == TTQPublishToolMenuItemTypeForVote) {
            btn.enabled = YES;
        } else if (btn.tag == TTQPublishToolMenuItemTypeForAiTool) {
            btn.enabled = YES;
        }
    }
    [self changeStateWithVideoSelect:self.videoSelected];
}

- (void)updateViewForPackUP {
    for (TTQPublishButton *btn in self.itemBtns) {
        if (btn.tag == TTQPublishToolMenuItemTypeForlToolTopic) {
            continue;
        }
        btn.enabled = NO;
        if (btn.tag == TTQPublishToolMenuItemTypeForEmotion) {
            btn.selected = NO;
        }
    }
    [self changeStateWithVideoSelect:self.videoSelected];
}

- (void)setEmotionBtnStatus:(BOOL)isEmotion {
    TTQPublishButton *emotionBtn = [self.itemBtns bk_match:^BOOL(TTQPublishButton *obj) {
        return obj.tag == TTQPublishToolMenuItemTypeForEmotion;
    }];
    emotionBtn.selected = isEmotion;
}

- (void)setTemplateBtnStatus:(BOOL)enableTemplate {
    TTQPublishButton *emotionBtn = [self.itemBtns bk_match:^BOOL(TTQPublishButton *obj) {
        return obj.tag == TTQPublishToolMenuItemTypeForExperienceTemplate;
    }];
    emotionBtn.selected = enableTemplate;
}

- (void)setVoteBtnStatus:(BOOL)enableVote {
    TTQPublishButton *voteBtn = [self.itemBtns bk_match:^BOOL(TTQPublishButton *obj) {
        return obj.tag == TTQPublishToolMenuItemTypeForVote;
    }];
    voteBtn.enabled = enableVote;
}

- (void)setTopicBtnStatus:(BOOL)enable {
    TTQPublishButton *topicBtn = [self.itemBtns bk_match:^BOOL(TTQPublishButton *obj) {
        return obj.tag == TTQPublishToolMenuItemTypeForlToolTopic;
    }];
    topicBtn.enabled = enable;
}

- (void)changeStateWithVideoSelect:(BOOL)videoSelect{
    for (TTQPublishButton *btn in self.itemBtns) {
        if (btn.tag == TTQPublishToolMenuItemTypeForlToolTopic) {
            continue;
        }
        if (btn.tag == TTQPublishToolMenuItemTypeForEmotion) {
            btn.selected = NO;
        } else if (btn.tag != TTQPublishToolMenuItemTypeForAiTool) {
            btn.enabled = !videoSelect;
        }
    }
}

#pragma mark - 更新button 状态 - yes:可以点击 ，no ： 不能点击

- (void)buttonAction:(UIButton *)sender {
    sender.selected = !sender.selected;
    for (IMYButton *btn in self.itemBtns) {
        if (btn != sender) {
            btn.selected = NO;
        }
    }
    TTQPublishToolMenuItem *item = [self.items match:^BOOL(TTQPublishToolMenuItem *element) {
        return element.itemId == sender.tag;
    }];
    if (item && item.tapAction) {
        item.tapAction(sender);
    }
}

- (void)updateAllBtnStatus:(BOOL)canEnable{
    for (TTQPublishButton *btn in self.itemBtns) {
        if (btn.tag != TTQPublishToolMenuItemTypeForAnonymity) {
            //匿名不处理
            if (btn.tag == TTQPublishToolMenuItemTypeForEmotion) {
                btn.selected = NO;
            }
            btn.enabled = canEnable;
            if(self.videoSelected && btn.tag != TTQPublishToolMenuItemTypeForEmotion && btn.tag != TTQPublishToolMenuItemTypeForAiTool){
                btn.enabled = NO;
            }
        }else{
            btn.enabled = YES;
        }
    }
}

- (TTQPublishToolMenuItem *)itemForType:(TTQPublishToolMenuItemType)type {
    TTQPublishToolMenuItem *item = [self.items match:^BOOL(TTQPublishToolMenuItem *element) {
        return element.itemId == type;
    }];
    return item;
}

- (void)showTemplateBadgeIfNeeded:(TTQPublishButton *)templateButton {
    NSString *key = [self templateBadgeKey];
    if (![[IMYUserDefaults standardUserDefaults] objectForKey:key]) {
        /// 没点击，或展示过
        self.templateBadge = [[UIImageView alloc] initWithFrame:CGRectMake(00, 0, 26, 14)];
        [self.templateBadge imy_setImageForKey:@"post_bar_icon_label"];
        UILabel *label = [[UILabel alloc] initWithFrame:self.templateBadge.bounds];
        label.font = [UIFont systemFontOfSize:9];
        [label imy_setTextColorForKey:kCK_White_A];
        label.text = IMYString(@"模板");
        label.textAlignment = NSTextAlignmentCenter;
        [self.templateBadge addSubview:label];
        [templateButton.superview addSubview:self.templateBadge];
        [self.templateBadge mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(templateButton.mas_right).offset(-14);
            make.centerY.equalTo(templateButton.mas_top).offset(11);
            make.size.mas_equalTo(CGSizeMake(26, 14));
        }];
        @weakify(self);
        [[RACObserve(templateButton, selected) takeUntil:templateButton.rac_willDeallocSignal] subscribeNext:^(id  _Nullable x) {
            @strongify(self);
            if ([x boolValue]) {
                /// 点击过了就不再显示
                self.templateBadge.hidden = YES;
                [[IMYUserDefaults standardUserDefaults] setObject:@1 forKey:key];
            }
        }];
        
        [[RACObserve(templateButton, hidden) takeUntil:templateButton.rac_willDeallocSignal] subscribeNext:^(id  _Nullable x) {
            @strongify(self);
            self.templateBadge.hidden = [x boolValue];
        }];

    }
}

- (NSString *)templateBadgeKey {
    return [NSString stringWithFormat:@"publish_template_%@",[IMYPublicAppHelper shareAppHelper].userid];;
}

- (UIButton *)buttonForType:(TTQPublishToolMenuItemType)type {
    UIButton *button = [self.itemBtns match:^BOOL(UIButton *element) {
        return element.tag == type;
    }];
    return button;
}

@end

#pragma mark - TTQPublishSelectionToolView
@interface TTQPublishSelectionToolView ()
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *detailLabel;
@property (nonatomic, strong) IMYSwitch *statusSwitch;
@property (nonatomic, strong) UIView *lineView;
@end

@implementation TTQPublishSelectionToolView

+ (TTQPublishSelectionToolView *)publishSelectionToolView:(NSString *)title detail:(NSString *)detail switchState:(BOOL)switchState {
    TTQPublishSelectionToolView *slectionView = [[TTQPublishSelectionToolView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 44.0)];
    [slectionView setupWith:title detail:detail switchState:switchState];
    return slectionView;
}

- (instancetype)init {
    if (self = [super init]) {
        [self setupView];
    }
    return self;
}

- (instancetype)initWithCoder:(NSCoder *)aDecoder {
    if (self = [super initWithCoder:aDecoder]) {
        [self setupView];
    }
    return self;
}

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setupView];
    }
    return self;
}

- (BOOL)isHidenLine {
    return self.lineView.hidden;
}

- (void)setHidenLine:(BOOL)hidenLine {
    self.lineView.hidden = hidenLine;
}

- (BOOL)isSwitchOn {
    return self.statusSwitch.isOn;
}

- (void)setSwitchOn:(BOOL)switchOn {
    [self.statusSwitch setOn:switchOn animated:YES];
}

- (void)setOnDidStateChanged:(void (^)(IMYSwitch *, BOOL))onDidStateChanged {
    _onDidStateChanged = onDidStateChanged;
    [self.statusSwitch setOnDidStateChanged:onDidStateChanged];
}

- (void)setupWith:(NSString *)title detail:(NSString *)detail switchState:(BOOL)switchState {
    [self.statusSwitch setOn:switchState animated:NO];
    self.titleLabel.text = title;
    self.detailLabel.text = detail;
}

- (void)setupView {
    self.statusSwitch = [[IMYSwitch alloc] init];
    [self.statusSwitch setOnDidStateChanged:self.onDidStateChanged];
    [self addSubview:self.statusSwitch];
    [self.statusSwitch mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.offset(-15.0);
        make.centerY.offset(0.0);
        make.width.mas_equalTo(52.0);
        make.height.mas_equalTo(30.0);
    }];

    self.titleLabel = [[UILabel alloc] init];
    [self.titleLabel imy_setTextColorForKey:kCK_Black_A];
    [self.titleLabel setFont:[UIFont systemFontOfSize:17.0]];
    [self addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.offset(15.0);
        make.centerY.offset(0.0);
        make.height.mas_equalTo(24.0);
        make.width.mas_lessThanOrEqualTo(SCREEN_WIDTH - 76.0);
    }];

    self.detailLabel = [[UILabel alloc] init];
    [self.detailLabel imy_setTextColorForKey:kCK_Black_C];
    [self.detailLabel setFont:[UIFont systemFontOfSize:14.0]];
    self.detailLabel.textAlignment = NSTextAlignmentRight;
    [self addSubview:self.detailLabel];
    @weakify(self);
    [self.detailLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        @strongify(self);
        make.left.mas_equalTo(self.titleLabel.mas_right).mas_offset(10.0);
        make.right.mas_equalTo(self.statusSwitch.mas_left).mas_offset(-10.0);
        make.height.mas_equalTo(24.0);
        make.centerY.offset(0.0);
    }];

    self.lineView = [[UIView alloc] init];
    [self.lineView imy_setBackgroundColorForKey:kCK_Black_E];
    [self addSubview:self.lineView];
    [self.lineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.offset(15.0);
        make.height.mas_equalTo(0.5);
        make.right.and.top.mas_offset(0.0);
    }];
}

@end

#pragma mark - TTQPublishButton
@interface TTQPublishButton ()
@property (nonatomic, strong) NSString *iconFontName;
@property (nonatomic, strong) NSString *title;
@property (nonatomic, strong) NSString *seletedIconFontName;
@property (nonatomic, strong) NSString *seletedTitle;
@end

@implementation TTQPublishButton

- (instancetype)init {
    if (self = [super init]) {
        [self initSetup];
    }
    return self;
}

- (instancetype)initWithCoder:(NSCoder *)aDecoder {
    if (self = [super initWithCoder:aDecoder]) {
        [self initSetup];
    }
    return self;
}

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self initSetup];
    }
    return self;
}

- (void)initSetup {
    self.titleLabel.textAlignment = NSTextAlignmentCenter;
    self.textDrawing = YES;
    [self imy_setTitleColor:kCK_Black_D];
    [self.titleLabel setFont:[UIFont systemFontOfSize:12.0]];
}


- (NSMutableParagraphStyle *)paragraphStyle {
    NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
    [paragraphStyle setLineSpacing:5];
    paragraphStyle.alignment = NSTextAlignmentCenter;
    return paragraphStyle;
}

- (NSMutableAttributedString *)attributedStringWith:(NSString *)iconFontName title:(NSString *)title color:(UIColor *)color {
    NSMutableString *mutableTitle = [NSMutableString string];
    if (iconFontName) {
        [mutableTitle appendString:iconFontName];
    }
    if (title) {
        if (iconFontName) {
            [mutableTitle appendString:@"\n"];
        }
        [mutableTitle appendString:title];
    }
    NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc]
        initWithString:mutableTitle
            attributes:@{NSFontAttributeName: [UIFont systemFontOfSize:12]}];
    UIFont *iconFont = [UIFont fontWithName:TTQIconFontName size:26];
    if (iconFont) {
        [attributedString addAttributes:@{NSFontAttributeName: iconFont} range:NSMakeRange(0, 1)];
    }
    if (color) {
        [attributedString addAttributes:@{NSForegroundColorAttributeName: color} range:NSMakeRange(0, mutableTitle.length)];
    }
    NSMutableParagraphStyle *paragraphStyle = [self paragraphStyle];
    [attributedString addAttribute:NSParagraphStyleAttributeName value:paragraphStyle range:NSMakeRange(0, mutableTitle.length)];
    return attributedString;
}

- (void)setIconFontName:(NSString *)iconFontName title:(NSString *)title {
    if (self.iconFontName == nil) {
        self.iconFontName = iconFontName;
    }
    if (self.title == nil) {
        self.title = title;
    }
    if (iconFontName || title) {
        [self setAttributedTitle:[self attributedStringWith:iconFontName title:title color:[UIColor imy_colorForKey:kCK_Black_A]] forState:UIControlStateNormal];
        [self setAttributedTitle:[self attributedStringWith:iconFontName title:title color:[UIColor imy_colorForKey:kCK_Red_B]] forState:UIControlStateHighlighted];
        [self setAttributedTitle:[self attributedStringWith:iconFontName title:title color:[UIColor imy_colorForKey:kCK_Black_E]] forState:UIControlStateDisabled];
    }
}

- (void)setSeletedIconFontName:(NSString *)iconFontName title:(NSString *)title {
    if (self.seletedIconFontName == nil) {
        self.seletedIconFontName = iconFontName;
    }
    if (self.seletedTitle == nil) {
        self.seletedTitle = title;
    }
    if (iconFontName || title) {
        [self setAttributedTitle:[self attributedStringWith:iconFontName title:title color:[UIColor imy_colorForKey:kCK_Black_A]] forState:UIControlStateSelected];
        [self setAttributedTitle:[self attributedStringWith:iconFontName title:title color:[UIColor imy_colorForKey:kCK_Red_B]] forState:UIControlStateHighlighted | UIControlStateSelected];
        [self setAttributedTitle:[self attributedStringWith:iconFontName title:title color:[UIColor imy_colorForKey:kCK_Black_E]] forState:UIControlStateDisabled | UIControlStateSelected];
    }
}

- (void)setTextDrawing:(BOOL)textDrawing {
    _textDrawing = textDrawing;
    if (!textDrawing) {
        self.titleLabel.numberOfLines = 1;
        [self setIconFontName:self.iconFontName title:nil];
        [self setSeletedIconFontName:self.seletedIconFontName title:nil];
    } else {
        self.titleLabel.numberOfLines = 2;
        [self setIconFontName:self.iconFontName title:self.title];
        [self setSeletedIconFontName:self.seletedIconFontName title:self.seletedTitle];
    }
    [self setNeedsDisplay];
}

- (CGRect)titleRectForContentRect:(CGRect)contentRect {
    if (self.isTextDrawing) {
        return CGRectMake(0, 5, CGRectGetWidth(self.bounds), CGRectGetHeight(self.bounds) - 5);
    } else {
        return CGRectMake(0, 2, CGRectGetWidth(self.bounds), CGRectGetHeight(self.bounds) - 2);
    }
}

@end

#pragma mark - TTQPublishSubjectSelectionToolView
@interface TTQPublishSubjectSelectionToolView ()
@property (nonatomic, strong) TTQInsetsLabel *titleLabel;
@property (nonatomic, strong) UILabel *tipLabel;
@property (nonatomic, strong) UILabel *rightArrow;
@end

@implementation TTQPublishSubjectSelectionToolView
+ (TTQPublishSubjectSelectionToolView *)publishSelectionToolView:(NSString *)title selected:(BOOL)selected unavailableTip:(BOOL)unavailableTip {
    TTQPublishSubjectSelectionToolView *slectionView = [[TTQPublishSubjectSelectionToolView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 44.0) unavailableTip:unavailableTip];
    [slectionView setTitle:title selected:selected unavailableTip:unavailableTip];
    return slectionView;
}

- (instancetype)initWithFrame:(CGRect)frame unavailableTip:(BOOL)unavailableTip {
    if (self = [super initWithFrame:frame]) {
        [self initSetup:unavailableTip];
    }
    return self;
}

- (instancetype)init {
    if (self = [super init]) {
        [self initSetup:NO];
    }
    return self;
}

- (instancetype)initWithCoder:(NSCoder *)aDecoder {
    if (self = [super initWithCoder:aDecoder]) {
        [self initSetup:NO];
    }
    return self;
}

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self initSetup:NO];
    }
    return self;
}

- (void)setMoreAction:(void (^)())moreAction selectAction:(void (^)())selectAction {
    self.titleLabel.userInteractionEnabled = YES;
    [self.titleLabel bk_whenTapped:selectAction];
    self.tipLabel.userInteractionEnabled = YES;
    [self.tipLabel bk_whenTapped:moreAction];
}

- (void)initSetup:(BOOL)unavailableTip {
    self.titleLabel = [[TTQInsetsLabel alloc] init];
    self.titleLabel.insets = UIEdgeInsetsMake(0, 10, 0, 10);
    self.titleLabel.font = [UIFont ttqFontWith:16];
    [self.titleLabel imy_setTextColorForKey:kCK_Black_C];
    [self.titleLabel imy_setBackgroundColorForKey:kCK_Black_H];
    self.titleLabel.textAlignment = NSTextAlignmentCenter;
    self.titleLabel.lineBreakMode = NSLineBreakByTruncatingMiddle;
    self.titleLabel.layer.masksToBounds = YES;
    self.titleLabel.layer.cornerRadius = 15;
    [self addSubview:self.titleLabel];
    if (unavailableTip) {
        return;
    }

    self.rightArrow = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 8, 12)];
    self.rightArrow.backgroundColor = [UIColor clearColor];
    self.rightArrow.text = @"\U0000e61c";
    self.rightArrow.font = [UIFont ttqIconFontWith:16];
    self.rightArrow.textAlignment = NSTextAlignmentRight;
    [self.rightArrow imy_setTextColorForKey:kCK_Black_J];
    [self.rightArrow sizeToFit];
    self.rightArrow.imy_right = self.imy_right - 13;
    self.rightArrow.imy_centerY = self.imy_height / 2;
    self.rightArrow.autoresizingMask = UIViewAutoresizingFlexibleRightMargin | UIViewAutoresizingFlexibleHeight;
    [self addSubview:self.rightArrow];

    self.tipLabel = [[UILabel alloc] init];
    self.tipLabel.font = [UIFont ttqFontWith:16];
    [self.tipLabel imy_setText:@"更多话题"];
    [self.tipLabel imy_setTextColorForKey:kCK_Black_C];
    self.tipLabel.textAlignment = NSTextAlignmentRight;
    [self.tipLabel sizeToFit];
    self.tipLabel.imy_right = self.rightArrow.imy_left - 5;
    self.tipLabel.imy_centerY = self.imy_height / 2;
    self.titleLabel.imy_height = self.imy_height;
    self.tipLabel.autoresizingMask = UIViewAutoresizingFlexibleRightMargin | UIViewAutoresizingFlexibleHeight;
    [self addSubview:self.tipLabel];
}

- (void)updateTitleFrame {
    self.titleLabel.imy_height = 30;
    self.titleLabel.imy_centerY = self.imy_height / 2;
    self.titleLabel.imy_left = 15;
    CGFloat maxWidth = (self.tipLabel ? self.tipLabel.imy_left - 5 - 15 : self.imy_width - 30);
    self.titleLabel.imy_width = MIN(self.titleLabel.imy_width, maxWidth);
}

- (void)setTitleForUnselected:(NSString *)title {
    [self.titleLabel imy_setTextColorForKey:kCK_Black_C];
    [self.titleLabel imy_setBackgroundColorForKey:kCK_Black_H];
    self.titleLabel.attributedText = nil;
    self.titleLabel.text = title;
    [self.titleLabel sizeToFit];
    [self updateTitleFrame];
}

- (void)setTitleForSelected:(NSString *)title unavailableTip:(BOOL)unavailableTip {
    [self.titleLabel imy_setTextColorForKey:kCK_Red_B];
    [self.titleLabel setBackgroundColor:[[UIColor imy_colorForKey:kCK_Red_B] colorWithAlphaComponent:0.1]];
    if (!unavailableTip) {
        NSMutableAttributedString *titleAttri = [[NSMutableAttributedString alloc] initWithString:title attributes:@{NSFontAttributeName: self.titleLabel.font, NSForegroundColorAttributeName: self.titleLabel.textColor}];
        [titleAttri appendAttributedString:[[NSAttributedString alloc] initWithString:@"  \U0000e666" attributes:@{NSFontAttributeName: [UIFont ttqIconFontWith:self.titleLabel.font.pointSize], NSForegroundColorAttributeName: [self.titleLabel.textColor colorWithAlphaComponent:0.5]}]];
        self.titleLabel.text = nil;
        self.titleLabel.attributedText = titleAttri;
    } else {
        self.titleLabel.attributedText = nil;
        self.titleLabel.text = title;
    }

    [self.titleLabel sizeToFit];
    [self updateTitleFrame];
}

- (void)setTitle:(NSString *)title selected:(BOOL)selected unavailableTip:(BOOL)unavailableTip {
    if (selected) {
        [self setTitleForSelected:title unavailableTip:unavailableTip];
    } else {
        [self setTitleForUnselected:title];
    }
}
@end


#pragma mark - 推荐话题cell

@interface TTQPublishSubjectCollectionViewCell : UICollectionViewCell
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, assign) BOOL showTagIcon;
@property (nonatomic, strong) UIImageView *topicIcon;
@property (nonatomic, assign) BOOL isHot;
@property (nonatomic, strong) UILabel *readCountLabel;
@end

@implementation TTQPublishSubjectCollectionViewCell

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self initSubviews];
    }
    return self;
}

- (void)initSubviews {
    self.layer.masksToBounds = YES;
    self.layer.cornerRadius = 13;
    self.contentView.backgroundColor = [UIColor imy_colorForKey:@"ffedf3"];
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.font = [UIFont ttqFontWith:14];
    [self.titleLabel imy_setTextColorForKey:kCK_Red_B];
    [self.contentView addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.equalTo(self.contentView);
        make.left.equalTo(self.contentView).offset(12);
    }];
    
    self.readCountLabel = [[UILabel alloc] init];
    self.readCountLabel.font = [UIFont ttqFontWith:13];
    [self.readCountLabel imy_setTextColorForKey:kCK_Black_M];
    [self.contentView addSubview:self.readCountLabel];
    
    [self.readCountLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.titleLabel.mas_right).offset(8);
        make.centerY.equalTo(self.titleLabel);
    }];
    
    self.topicIcon = [[UIImageView alloc] initWithFrame:CGRectMake(12, 6, 14, 14)];
    self.topicIcon.image = [UIImage imageNamed:@"editor_icon_topic_nor"];
    [self.contentView addSubview:self.topicIcon];
    self.topicIcon.hidden = YES;
    @weakify(self);
    [self imy_addThemeChangedBlock:^(id weakObject) {
        @strongify(self);
        if (self.showTagIcon) {
            self.contentView.backgroundColor = [UIColor imy_colorForKey:@"#ffedf3"];
            [self.titleLabel imy_setTextColorForKey:kCK_Red_B];
        } else {
            BOOL isNight = [IMYPublicAppHelper shareAppHelper].isNight;
            self.contentView.backgroundColor = [UIColor imy_colorForKey:isNight ? @"#333333" : @"#F2F2F5"];
            [self.titleLabel imy_setTextColorForKey:isNight ? @"#eaeaea" : kCK_Black_M];
        }
    }];
}

- (void)setShowTagIcon:(BOOL)showTagIcon {
    _showTagIcon = showTagIcon;
    if (showTagIcon) {
        self.topicIcon.hidden = NO;
        self.titleLabel.font = [UIFont ttqFontWith:13];
        [self.titleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.top.bottom.equalTo(self.contentView);
            make.left.equalTo(self.contentView).offset(30);
        }];
        BOOL isNight = [IMYPublicAppHelper shareAppHelper].isNight;
        self.contentView.backgroundColor = [UIColor imy_colorForKey:isNight ? @"#333333" : @"#F2F2F5"];
        [self.titleLabel imy_setTextColorForKey:isNight ? @"#eaeaea" : kCK_Black_M];
        self.layer.cornerRadius = 13;

    } else {
        self.topicIcon.hidden = YES;
        [self.titleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(UIEdgeInsetsMake(0, 12, 0, 12));
        }];
        self.contentView.backgroundColor = [UIColor imy_colorForKey:@"#ffedf3"];
        [self.titleLabel imy_setTextColorForKey:kCK_Red_B];
        self.layer.cornerRadius = 13;
    }
}

- (void)setIsHot:(BOOL)isHot {
    _isHot = isHot;
    [self.topicIcon imy_setImageForKey:isHot?@"ttq_hot_topic":@"editor_icon_topic_nor"];
}

- (void)updateReadCount:(NSInteger)readCount {
    if (readCount > 0 && self.isHot) {
        self.readCountLabel.hidden = NO;
        self.readCountLabel.text = [NSString stringWithFormat:@"%@阅读",[NSString stringShowWithCount:readCount]];
    } else {
        self.readCountLabel.hidden = YES;
    }
}

@end


#pragma mark - TTQPublishSubjectToolView

@interface TTQPublishSubjectToolView () <UICollectionViewDelegate, UICollectionViewDataSource>
// 未选中话题
@property (nonatomic, strong) UIView *addTopicView;
// 选中话题
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIImageView *prefixIcon;
@property (nonatomic, strong) UIImageView *suffixIcon;
@property (nonatomic, strong) UIView *selectedTopicView;
// 推荐话题区域
@property (nonatomic, strong) UICollectionView *collectionView;
@property (nonatomic, strong) UILabel *categoryTitleLabel;
@property (nonatomic, assign) BOOL isPublishTopic;
@end

@implementation TTQPublishSubjectToolView


- (instancetype)initWithOnlyTopicStyle:(BOOL)onlyTopic {
    return [self initWithOnlyTopicStyle:onlyTopic isPublishTopic:NO];
}

- (instancetype)initWithOnlyTopicStyle:(BOOL)onlyTopic isPublishTopic:(CGFloat)isPublishTopic {
    self = [super init];
    if (self) {
        self.isPublishTopic = isPublishTopic;
        self.onlyShowTopic = onlyTopic;
        if (self.onlyShowTopic) {
            [self initOnlyTopciStyle];
        } else {
            [self commomInit];
        }
    }
    return self;
}

- (void)commomInit {
    [self imy_setBackgroundColorForKey:kCK_Black_G];
    
    // 没有话题时
    [self addSubview:self.addTopicView];
    [self.addTopicView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self);
        make.left.equalTo(self);
        make.height.mas_equalTo(21+12+6);
        make.width.mas_equalTo(SCREEN_WIDTH);
    }];
    
    [self addSubview:self.collectionView];
    self.collectionView.frame = CGRectMake(12,  21+12+6,  SCREEN_WIDTH-12, 26+24);
    
    [self addSubview:self.categoryTitleLabel];
    self.categoryTitleLabel.frame = CGRectMake(12 + 60, 0, 28, 20);
    self.categoryTitleLabel.imy_centerY = self.collectionView.imy_centerY;
    
    self.collectionView.alpha = 0.2;
    self.categoryTitleLabel.alpha = 0.2;
   
    // 有话题时
    UIView *containerView = [UIView new];
    containerView.backgroundColor = [[UIColor imy_colorForKey:kCK_Red_B] colorWithAlphaComponent:0.08];
    containerView.layer.cornerRadius = 13;
    containerView.clipsToBounds = YES;
    [self addSubview:containerView];
    self.selectedTopicView = containerView;
    self.selectedTopicView.hidden = YES;

    self.prefixIcon = [UIImageView new];
    self.prefixIcon.image = [UIImage imy_imageForKey:@"release_topic_selected"];
    [containerView addSubview:self.prefixIcon];
    [self.prefixIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.mas_equalTo(16);
        make.centerY.equalTo(containerView);
        make.left.equalTo(containerView).mas_offset(8);
    }];
    
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.font = [UIFont ttqFontWith:14];
    [self.titleLabel imy_setTextColorForKey:kCK_Red_B];
    [containerView addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.prefixIcon.mas_right).offset(4);
        make.height.mas_equalTo(20);
        make.centerY.equalTo(containerView);
    }];

    self.suffixIcon = [UIImageView new];
    self.suffixIcon.image = [UIImage imy_imageForKey:@"editor_icon_close_pink"];
    [containerView addSubview:self.suffixIcon];
    [self.suffixIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.mas_equalTo(16);
        make.centerY.equalTo(containerView);
        make.left.equalTo(self.titleLabel.mas_right).mas_offset(4);
    }];
    
    [self.selectedTopicView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self).offset(12);
        make.left.equalTo(self).offset(self.isPublishTopic ? 12 : 16);
        make.height.mas_equalTo(26);
        make.right.equalTo(self.suffixIcon).mas_offset(8);
    }];
    
    UIControl *tapBtn = [[UIControl alloc] init];
    [containerView addSubview:tapBtn];
    [tapBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.selectedTopicView).mas_equalTo(UIEdgeInsetsMake(-7, -7, -7, -7));
    }];
    @weakify(self);
    [[tapBtn rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
        @strongify(self);
        if (self.addTopicHandler) {
            self.addTopicHandler(NSNotFound);
        }
    }];
    
    UIControl *closeBtn = [[UIControl alloc] init];
    [containerView addSubview:closeBtn];
    [closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.suffixIcon).mas_equalTo(UIEdgeInsetsMake(-7, -7, -7, -7));
    }];
    [[closeBtn rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
        // 回到未选中话题状态
        @strongify(self);
        if (self.removeTopicHandler) {
            self.removeTopicHandler();
        }
    }];
}

- (void)initOnlyTopciStyle {
    [self addSubview:self.collectionView];
    self.collectionView.frame = CGRectMake(0,  0,  SCREEN_WIDTH, 50);
    self.collectionView.contentInset = UIEdgeInsetsMake(0, 12, 0, 12);
    self.collectionView.backgroundColor = [UIColor imy_colorForKey:kCK_White_AN];
    self.collectionView.alpha = 0.2;
    self.categoryTitleLabel.alpha = 0.2;
   
    // 有话题时
    UIView *containerView = [UIView new];
    containerView.backgroundColor = [UIColor imy_colorForKey:@"#FFEDF3"];
    containerView.layer.cornerRadius = 13;
    containerView.clipsToBounds = YES;
    [self addSubview:containerView];
    self.selectedTopicView = containerView;
    self.selectedTopicView.hidden = YES;

    self.prefixIcon = [UIImageView new];
    self.prefixIcon.image = [UIImage imy_imageForKey:@"editor_icon_topic_sel"];
    [containerView addSubview:self.prefixIcon];
    [self.prefixIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.mas_equalTo(14);
        make.centerY.equalTo(containerView);
        make.left.equalTo(containerView).mas_offset(12);
    }];
    
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.font = [UIFont ttqFontWith:12];
    [self.titleLabel imy_setTextColorForKey:kCK_Red_B];
    [containerView addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.prefixIcon.mas_right).offset(4);
        make.height.mas_equalTo(20);
        make.centerY.equalTo(containerView);
    }];

    self.suffixIcon = [UIImageView new];
    self.suffixIcon.image = [UIImage imy_imageForKey:@"release_topic_del"];
    [containerView addSubview:self.suffixIcon];
    [self.suffixIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.mas_equalTo(16);
        make.centerY.equalTo(containerView);
        make.left.equalTo(self.titleLabel.mas_right).mas_offset(4);
    }];
    
    [self.selectedTopicView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self).offset(12);
        make.left.equalTo(self).offset(12);
        make.height.mas_equalTo(26);
        make.right.equalTo(self.suffixIcon).mas_offset(12);
    }];
    
    UIControl *tapBtn = [[UIControl alloc] init];
    [containerView addSubview:tapBtn];
    [tapBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.selectedTopicView).mas_equalTo(UIEdgeInsetsMake(-7, -7, -7, -7));
    }];
    @weakify(self);
    [[tapBtn rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
        @strongify(self);
        if (self.addTopicHandler) {
            self.addTopicHandler(NSNotFound);
        }
    }];
    
    UIControl *closeBtn = [[UIControl alloc] init];
    [containerView addSubview:closeBtn];
    [closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.suffixIcon).mas_equalTo(UIEdgeInsetsMake(-7, -7, -7, -7));
    }];
    [[closeBtn rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
        // 回到未选中话题状态
        @strongify(self);
        if (self.removeTopicHandler) {
            self.removeTopicHandler();
        }
    }];

}

// 更新话题
- (void)setTitle:(NSString *)title showDeleteBtn:(BOOL)showDeleteBtn {
    if (imy_isEmptyString(title)) {
        if (!self.onlyShowTopic) {
            [self imy_setBackgroundColorForKey:kCK_Black_G];
            self.addTopicView.hidden = NO;
        }
        self.selectedTopicView.hidden = YES;
        [self mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(self.topicArray.count == 0 ? 45 : 83);
            make.width.mas_equalTo(SCREEN_WIDTH);
            if (self.onlyShowTopic) {
                make.height.mas_equalTo(50);
            }
        }];
        [self showSubjectCollectionViewWithAnimationDelay:0];
    } else {
        [self imy_setBackgroundColorForKey:kCK_White_AN];
        if (!self.onlyShowTopic) {
            self.addTopicView.hidden = YES;
            self.categoryTitleLabel.hidden = YES;
        }

        self.collectionView.hidden = YES;
        self.selectedTopicView.hidden = NO;
        
        self.suffixIcon.hidden = !showDeleteBtn;
        [self.suffixIcon mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(showDeleteBtn?16:0);
        }];
            
        self.titleLabel.text = title;
        
        [self mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(50);
            make.width.mas_equalTo(SCREEN_WIDTH);
        }];
    }
}

- (void)showSubjectCollectionViewWithAnimationDelay:(NSTimeInterval)delay {
    if (self.topicArray.count == 0) {
        self.categoryTitleLabel.hidden = YES;
        self.collectionView.hidden = YES;
        [self mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(45);
            make.width.mas_equalTo(SCREEN_WIDTH);
        }];
    } else {
        [self.collectionView reloadData];
        [self mas_updateConstraints:^(MASConstraintMaker *make) {
            if (self.onlyShowTopic) {
                make.height.mas_equalTo(50);
            } else {
                make.height.mas_equalTo(83+6);
            }
            make.width.mas_equalTo(SCREEN_WIDTH);
        }];
        imy_asyncMainBlock(delay, ^{
            self.categoryTitleLabel.hidden = YES;
            self.collectionView.hidden = NO;
            if (!self.onlyShowTopic) {
                self.collectionView.imy_left = 12 + 60;
            }
//            self.categoryTitleLabel.imy_left = 12 + 60;
            self.collectionView.alpha = 0.2;
            self.categoryTitleLabel.alpha = 0.2;
            
            [UIView animateWithDuration:0.2 animations:^{
                self.collectionView.alpha = 1;
                if (!self.onlyShowTopic) {
                    self.collectionView.imy_left = 12;
                }
//                self.categoryTitleLabel.imy_left = 12;
            }];
        });
    }
}

- (void)setTopicCategory:(NSString *)category topicArray:(NSArray *)array {
    self.categoryTitleLabel.text = category;
    self.topicArray = array;
    [self.collectionView reloadData];
    if (imy_isEmptyString(self.titleLabel.text)) {
        [self showSubjectCollectionViewWithAnimationDelay:0.2];
    }
}

- (void)removeTopicItem:(NSString *)topicName {
    NSMutableArray *array = [NSMutableArray arrayWithArray:self.topicArray];
    NSDictionary *item = nil;
    for (NSDictionary *topic in array) {
        if ([topic[@"name"] isEqualToString:topicName]) {
            item = topic;
            break;
        }
    }
    if (item) {
        NSIndexPath *indexPath = [NSIndexPath indexPathForItem:[array indexOfObject:item] inSection:0];
        [array removeObject:item];
        self.topicArray = array;

        [self.collectionView performBatchUpdates:^{
            [self.collectionView deleteItemsAtIndexPaths:@[indexPath]];
        } completion:^(BOOL finished) {
            [self.collectionView reloadData];
        }];
    }
}

#pragma mark - public

#pragma mark - collectionview delegate & datasource

#pragma mark - UICollectionViewDataSource

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.topicArray.count;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    TTQPublishSubjectCollectionViewCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"cell" forIndexPath:indexPath];
    NSDictionary *dic = self.topicArray[indexPath.item];
    NSString *text = dic[@"name"];
    cell.titleLabel.text = text;
    cell.showTagIcon = self.onlyShowTopic;
    BOOL isHot = [dic[@"is_show_hot"] boolValue];
    cell.isHot = isHot;
    [cell updateReadCount:[dic[@"read_count"] integerValue]];
    cell.imyut_eventInfo.eventName = [NSString stringWithFormat:@"ttq_publish_recommend_topic_%@",text];
    cell.imyut_eventInfo.showRadius = 0.95;
    cell.imyut_eventInfo.ableToClean = YES;
    @weakify(self);
    cell.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
        @strongify(self);
        // cell重用，但不会重复曝光，只能把曝光的时间戳保存到实例属性中
        NSMutableDictionary *biParams = [@{@"event":@"ttq_twfby_ryhtbg", @"action":@(1), @"info_id":dic[@"id"], @"public_type":self.categoryTitleLabel.text} mutableCopy];
        NSString *bgdj_key = [NSString stringWithFormat:@"%@_%@",IMYPublicAppHelper.shareAppHelper.userid, params[@"event_time"]];
        biParams[@"bgdj_key"] = bgdj_key;
        if (isHot) {
            biParams[@"public_type"] = @"热点";
        }
        [IMYGAEventHelper postWithPath:@"event" params:biParams headers:nil completed:nil];
    };
    cell.imyut_eventInfo.clickedBlock = ^(__kindof UIView *view, NSDictionary *params) {
        @strongify(self);
        NSMutableDictionary *biParams = [@{@"event":@"ttq_twfby_djryht", @"action":@(2), @"info_id":dic[@"id"], @"public_type":self.categoryTitleLabel.text} mutableCopy];
        NSString *bgdj_key = [NSString stringWithFormat:@"%@_%@",IMYPublicAppHelper.shareAppHelper.userid, params[@"event_time"]];
        biParams[@"bgdj_key"] = bgdj_key;
        if (isHot) {
            biParams[@"public_type"] = @"热点";
        }
        [IMYGAEventHelper postWithPath:@"event" params:biParams headers:nil completed:nil];
    };
    return cell;
}

#pragma mark - UICollectionViewDelegateFlowLayout

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    if (self.addTopicHandler) {
        self.addTopicHandler(indexPath.item);
    }
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    NSDictionary *dic = self.topicArray[indexPath.item];
    NSString *text = dic[@"name"];
    if (self.onlyShowTopic) {
        NSDictionary *attribute = @{NSFontAttributeName: [UIFont ttqFontWith:13]};
        CGFloat width = ceilf([text sizeWithAttributes:attribute].width);
        if ([dic[@"is_show_hot"] boolValue]) {
            NSInteger read_count = [dic[@"read_count"] integerValue];
            if (read_count > 0) {
                /// 要展示阅读数据
                text = [NSString stringWithFormat:@"%@阅读",[NSString stringShowWithCount:read_count]];
                CGFloat readWidth = ceilf([text sizeWithAttributes:attribute].width);
                width += (readWidth + 8);
            }
        }
        return CGSizeMake(width + 30+12, 26);
    } else {
        NSDictionary *attribute = @{NSFontAttributeName: [UIFont ttqFontWith:14]};
        CGFloat width = ceilf([text sizeWithAttributes:attribute].width);
        return CGSizeMake(width + 12*2, 26);
    }
}

#pragma mark - getter & setter

- (UIView *)addTopicView {
    if (!_addTopicView) {
        _addTopicView = [[UIView alloc] init];
        [_addTopicView imy_setBackgroundColorForKey:kCK_Black_G];
        UILabel *noTopicLabel = [[UILabel alloc] init];
        noTopicLabel.text = @"添加话题";
        noTopicLabel.font = [UIFont ttqMediumFontWith:15];
        noTopicLabel.textColor = [UIColor imy_colorWithHexString:@"333"];
        [_addTopicView addSubview:noTopicLabel];
        [noTopicLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(_addTopicView).offset(12);
            make.left.equalTo(_addTopicView).offset(36);
            make.height.mas_equalTo(21);
        }];
        UIImageView *noTopicIconView = [[UIImageView alloc] init];
        [noTopicIconView imy_setImageForKey:@"editor_icon_topic"];
        [_addTopicView addSubview:noTopicIconView];
        [noTopicIconView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(_addTopicView).offset(12);
            make.centerY.equalTo(noTopicLabel);
            make.height.width.mas_equalTo(16);
        }];
        
        UILabel *leadLabel = [[UILabel alloc] init];
        leadLabel.text = @"添加话题可以让更多人看到哦";
        leadLabel.font = [UIFont ttqFontWith:13];
        leadLabel.textColor = [UIColor imy_colorWithHexString:@"999"];
        [_addTopicView addSubview:leadLabel];
        [leadLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(_addTopicView).offset(-28);
            make.centerY.equalTo(noTopicLabel);
        }];
        
        UIImageView *arrowImageView = [[UIImageView alloc] init];
        [arrowImageView imy_setImageForKey:@"icon_arrow"];
        [_addTopicView addSubview:arrowImageView];
        [arrowImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(_addTopicView).offset(-12);
            make.centerY.equalTo(noTopicLabel);
            make.width.mas_equalTo(8);
            make.height.mas_equalTo(14);
        }];
        
        @weakify(self);
        [_addTopicView bk_whenTapped:^{
            @strongify(self);
            if (self.addTopicHandler) {
                self.addTopicHandler(NSNotFound);
            }
        }];

    }
    return _addTopicView;
}

- (UILabel *)categoryTitleLabel {
    if (!_categoryTitleLabel) {
        _categoryTitleLabel = [[UILabel alloc] init];
        [_categoryTitleLabel imy_setBackgroundColorForKey:kCK_Black_G];
        _categoryTitleLabel.font = [UIFont ttqFontWith:14];
        [_categoryTitleLabel imy_setTextColorForKey:kCK_Black_M];
    }
    return _categoryTitleLabel;
}

- (UICollectionView *)collectionView {
    if (!_collectionView) {
        UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
        layout.scrollDirection = UICollectionViewScrollDirectionHorizontal;
        layout.minimumInteritemSpacing = 8;
        _collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:layout];
        [_collectionView registerClass:[TTQPublishSubjectCollectionViewCell class] forCellWithReuseIdentifier:@"cell"];
        [_collectionView imy_setBackgroundColorForKey:kCK_Black_G];
        _collectionView.delegate = self;
        _collectionView.dataSource = self;
        _collectionView.showsHorizontalScrollIndicator = NO;
    }
    return _collectionView;
}

@end
