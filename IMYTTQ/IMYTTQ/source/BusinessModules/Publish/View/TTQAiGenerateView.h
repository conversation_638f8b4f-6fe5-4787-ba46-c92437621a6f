//
//  TTQAiGenerateView.h
//  IMYTTQ
//
//  Created by 林云峰 on 2025/1/23.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, TTQAiGenerateType) {
    TTQAiGenerateNormal,      /// 正常
    TTQAiGenerate<PERSON><PERSON><PERSON><PERSON>,    /// 产检单
    TTQAiGeneraterRunse,       /// 润色
    TTQAiGeneraterAutoNewLine   /// 自动换行
};

@class IMYUGCNotePublishImage;

@interface TTQAiGenerateView : UIView

/// 终止AI输出
@property (nonatomic, copy) void (^suspendActionBlock)(void);
/// 重试
@property (nonatomic, copy) void (^retryActionBlock)(void);

@property (nonatomic, copy) void (^startRequestBlock)(NSString *content, NSString *referer);

@property (nonatomic, copy) void (^useContentBlock)(NSString *content);
/// 重新选择图片
@property (nonatomic, copy) void (^selectImageBlock)(void);
/// BOOL ：realRemove 是否最终要移除
@property (nonatomic, copy) void (^removeBlock)(BOOL realRemove);

@property (nonatomic, copy) NSString *aiReferer;
/// 产检使用的图片信息，最终ai生成结束或取消时，要加到发布器
@property (nonatomic, strong) IMYUGCNotePublishImage *imageData;

- (instancetype)initWithContentType:(TTQAiGenerateType)type;
- (void)loadRunseContent:(NSString *)content;
- (void)loadImage:(UIImage *)image;
- (void)updateContent:(NSString *)content;
- (void)hideRetryView;
- (void)updateRetryView;
/// 外部调用
- (void)updateContentComplete;
- (TTQAiGenerateType)aiType;
/// 是否是编辑内容类型的AI
- (BOOL)isEditContent;
@end

NS_ASSUME_NONNULL_END
