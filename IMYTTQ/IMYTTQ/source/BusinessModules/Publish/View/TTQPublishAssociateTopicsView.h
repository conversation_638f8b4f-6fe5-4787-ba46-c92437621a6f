//
//  TTQPublishAssociateTopicsView.h
//  IMYTTQ
//
//  Created by 林云峰 on 2024/8/23.
//

#import <UIKit/UIKit.h>
#import "TTQTopicSubjectModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface TTQPublishAssociateTopicsView : UIView

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) UIView *loadingView;
@property (nonatomic, strong) UIActivityIndicatorView *indicatorView;
@property (nonatomic, strong) UILabel *loadingLabel;
@property (nonatomic, copy) void(^didSelectCellBlock)(TTQTopicSubjectModel *model);

- (void)beginToEdit;

- (void)reloadTableWithKeyWord:(NSString *)text publishContent:(NSString *)publishContent;
- (void)reloadTableWithNilData;

@end

@interface TTQPublishAssociateTopicCell : UITableViewCell

@property (nonatomic, strong) UILabel *topicLabel;
@property (nonatomic, strong) UILabel *countLabel;
@property (nonatomic, strong) UIImageView *addIcon;
@property (nonatomic, strong) UIImageView *topicIcon;
- (void)updateData:(TTQTopicSubjectModel *)topic;
- (void)updateHot:(BOOL)isHot;
@end

NS_ASSUME_NONNULL_END
