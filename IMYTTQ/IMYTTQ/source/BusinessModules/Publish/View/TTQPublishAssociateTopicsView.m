//
//  TTQPublishAssociateTopicsView.m
//  IMYTTQ
//
//  Created by 林云峰 on 2024/8/23.
//

#import "TTQPublishAssociateTopicsView.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import "TTQTrendingTopicModel.h"

@implementation TTQPublishAssociateTopicCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self prepareUI];
    }
    return self;
}

- (void)prepareUI
{
    [self.contentView imy_setBackgroundColorForKey:kCK_White_AN];
    
    self.topicIcon = [[UIImageView alloc] initWithFrame:CGRectMake(12, 8, 14, 14)];
    [self.contentView addSubview:self.topicIcon];
    
    UILabel *topicLabel = [[UILabel alloc] initWithFrame:CGRectMake(12, 5, 200, 20)];
    topicLabel.font = [UIFont systemFontOfSize:14];
    [topicLabel imy_setTextColorForKey:kCK_Black_A];
    self.topicLabel = topicLabel;
    [self addSubview:self.topicLabel];
    
    UILabel *countLabel = [[UILabel alloc] initWithFrame:CGRectMake(12, 6.5, 50, 17)];
    countLabel.font = [UIFont systemFontOfSize:12];
    [countLabel imy_setTextColorForKey:kCK_Black_B];
    countLabel.textAlignment = NSTextAlignmentRight;
    self.countLabel = countLabel;
    [self addSubview:self.countLabel];
    
    self.addIcon = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"topic_icon_add"]];
    [self.contentView addSubview:self.addIcon];
    
    [self.topicLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(30);
        make.centerY.mas_equalTo(self);
    }];
    
    [self.countLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.mas_equalTo(-12);
        make.centerY.mas_equalTo(self);
        make.leading.mas_greaterThanOrEqualTo(self.topicLabel.mas_trailing).offset(15);
    }];
    
    [self.addIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.countLabel);
        make.right.equalTo(self.countLabel.mas_left).offset(-4);
        make.size.mas_equalTo(CGSizeMake(10, 10));
    }];
    // 设置抗压缩优先级
    [self.countLabel setContentCompressionResistancePriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
    [self.topicLabel setContentCompressionResistancePriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
}

- (void)updateData:(TTQTopicSubjectModel *)topic {
    NSString *str = [NSString stringWithFormat:@"%@",topic.name];
    self.topicLabel.text = str;
    [self.countLabel imy_setTextColorForKey:kCK_Black_B];
    self.addIcon.hidden = YES;
    if(topic.talk_count > 0){
        self.countLabel.text = [NSString stringWithFormat:@"%@%@",[NSString stringShowWithCount:topic.talk_count],IMYString(@"阅读")];
    } else if (topic.subjectID < 1) {
        self.addIcon.hidden = NO;
        /// 没有匹配
        self.countLabel.text = @"添加新话题";
        [self.countLabel imy_setTextColorForKey:@"#4F7CB0"];
    }
    else {
        self.countLabel.text = @"";
    }
    [self updateHot:topic.is_show_hot];
}

- (void)updateHot:(BOOL)isHot {
    [self.topicIcon imy_setImageForKey:isHot?@"ttq_hot_topic":@"editor_icon_topic_nor"];
}
@end



@interface TTQPublishAssociateTopicsView () <UITextFieldDelegate>

@property (nonatomic, strong) NSArray<TTQTopicSubjectModel *> *dataSource;
@property (nonatomic, copy) NSString *keywords;
@property (nonatomic, copy) NSString *publishContent;
@property (nonatomic, assign) BOOL disableSelect;
@property (nonatomic, strong) UILabel *errorView;
@end


@implementation TTQPublishAssociateTopicsView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self prepareUI];
    }
    return self;
}

- (instancetype)init
{
    self = [super init];
    if (self) {
        [self prepareUI];
    }
    return self;
}

- (void)prepareUI
{
    [self imy_setBackgroundColorForKey:kCK_White_AN];
    
    IMYLineView *lineView = [self imy_lineViewWithDirection:IMYDirectionUp show:YES margin:0];
    lineView.colorKey = kCK_Black_E;
        
    CGFloat loadingHeight = 26;
    UIView *loadingView = [[UIView alloc] initWithFrame:CGRectMake(12, 0, loadingHeight, loadingHeight)];
    self.loadingView = loadingView;
    self.loadingView.hidden = YES;
    self.loadingView.center = self.imy_selfcenter;
    [self addSubview:loadingView];
    
    UIActivityIndicatorView *indicatorView = [[UIActivityIndicatorView alloc] initWithFrame:CGRectMake(0, 0, 26, 26)];
    self.indicatorView = indicatorView;
    [self.loadingView addSubview:indicatorView];
            
    UITableView *tableView = [[UITableView alloc] initWithFrame:CGRectMake(0, 0, self.imy_width, self.imy_height) style:UITableViewStylePlain];
    tableView.backgroundColor = [UIColor clearColor];
    tableView.autoresizingMask = UIViewAutoresizingFlexibleHeight | UIViewAutoresizingFlexibleWidth;
    tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    tableView.delegate = self;
    tableView.dataSource = self;
    self.tableView = tableView;
    [self addSubview:self.tableView];
    
    [self.tableView registerClass:NSClassFromString(@"TTQPublishAssociateTopicCell") forCellReuseIdentifier:@"TTQPublishAssociateTopicCell"];
    
    self.errorView = [[UILabel alloc] initWithFrame:self.bounds];
    [self.errorView imy_setBackgroundColorForKey:kCK_White_AN];
    self.errorView.textAlignment = NSTextAlignmentCenter;
    self.errorView.font = [UIFont systemFontOfSize:13];
    [self.errorView imy_setTextColorForKey:kCK_Black_C];
    self.errorView.text = IMYString(@"网络不见了，请检查网络");
    [self addSubview:self.errorView];
    self.errorView.hidden = YES;
    
    [self bringSubviewToFront:lineView];
}

- (void)reloadTableWithKeyWord:(NSString *)text publishContent:(NSString *)publishContent {
    self.dataSource = nil;
    self.tableView.hidden = YES;
    self.tableView.contentOffset = CGPointZero;
    self.loadingView.hidden = NO;
    self.errorView.hidden = YES;
    [self.indicatorView startAnimating];
    self.keywords = text;
    if (publishContent.length > 500) {
        publishContent = [publishContent substringToIndex:500];
    }
    self.publishContent = publishContent;
    [self requestAssociateList:text];
}

- (void)reloadTableWithNilData{
    self.dataSource = nil;
    self.tableView.hidden = YES;
    self.tableView.contentOffset = CGPointZero;
    self.loadingView.hidden = NO;
    self.errorView.hidden = YES;
    self.keywords = nil;
    self.publishContent = nil;
    [self.indicatorView startAnimating];
}

- (void)requestAssociateList:(NSString *)text{
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if(imy_isNotEmptyString(self.keywords)){
        params[@"name"] = self.keywords;
        params[@"referer"] = @"tag_recommend";
    } else {
        params[@"name"] = self.publishContent;
        params[@"referer"] = @"text_recommend";
    }
    @weakify(self);
    [[[IMYPublicServerRequest getPath:@"v2/subject_search" host:circle_seeyouyima_com params:params headers:nil] deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        NSArray *topics = [[x responseObject][@"subject_list"] toModels:[TTQTrendingTopicModel class]];
        topics = [topics map:^id _Nonnull(TTQTrendingTopicModel *element) {
            TTQTopicSubjectModel *model = [TTQTopicSubjectModel new];
            model.subjectID = element.topicID;
            model.name = element.name;
            model.talk_count = element.read_count;
            model.is_show_hot = element.is_show_hot;
            return model;
        }];
        self.dataSource = topics;
        [self configTableAndReload];
        self.errorView.hidden = YES;
    } error:^(NSError * _Nullable error) {
        @strongify(self);
        if (![IMYNetState networkEnable]) {
            self.errorView.hidden = NO;
        } else {
            self.errorView.hidden = YES;
        }
    }];
}

-(void)dataSourceKeyWordOnly:(NSString *)keyword{
    // 只按keyword建一个数据的数组展示
    TTQTopicSubjectModel *model = [[TTQTopicSubjectModel alloc] init];
    model.name = keyword;
//    model.isUserCreate = YES;
    NSArray *dataSource = @[model];
    self.dataSource = dataSource;
}

-(void)configTableAndReload{
    self.disableSelect = NO;
    self.loadingView.hidden = YES;
    self.tableView.hidden = NO;
    [self.indicatorView stopAnimating];
    [self.tableView reloadData];
}

#pragma mark - UITableViewDataSource, UITableViewDelegate

-(NSInteger)numberOfSectionsInTableView:(UITableView *)tableView{
    return 1;
}

-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return self.dataSource.count;
}

-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 30;
}

-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    TTQPublishAssociateTopicCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(TTQPublishAssociateTopicCell.class) forIndexPath:indexPath];
    TTQTopicSubjectModel *obj = self.dataSource[indexPath.row];
    [cell updateData:obj];
    cell.imyut_eventInfo.eventName = [NSString stringWithFormat:@"ttq_associate_topic_%p",obj];
    cell.imyut_eventInfo.showRadius = 1;
    @weakify(self,obj);
    NSInteger floor = indexPath.row + 1;
    [cell.imyut_eventInfo setExposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
        @strongify(self,obj);
        [self postBiAction:1 data:obj floor:floor];
    }];
    return cell;
}

-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    // 创建话题回调给发布器
    if(self.didSelectCellBlock && !self.disableSelect){
        self.disableSelect = YES;
        TTQTopicSubjectModel *obj = self.dataSource[indexPath.row];
        NSInteger floor = indexPath.row + 1;
        [self postBiAction:2 data:obj floor:floor];
        self.didSelectCellBlock(self.dataSource[indexPath.row]);
    }
}

- (void)postBiAction:(NSInteger)action data:(TTQTopicSubjectModel *)topic floor:(NSInteger)floor {
    NSMutableDictionary *params = [NSMutableDictionary dictionaryWithCapacity:1];
    params[@"action"] = @(action);
    params[@"event"] = @"ttq_twfby_httjy";
    params[@"info_id"] = @(topic.subjectID);
    if (topic.subjectID < 1) {
        params[@"public_type"] = @"用户输入";
    } else if (topic.is_show_hot) {
        params[@"public_type"] = @"热点";
    } else if ([topic.source isEqualToString:@"admin"]) {
        params[@"public_type"] = @"热议";
    }  else {
        params[@"public_type"] = @"推荐";
    }
    params[@"public_info"] = @(floor);
    [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
}


@end
