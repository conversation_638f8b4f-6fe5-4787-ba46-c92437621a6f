//
//  TTQPublishTopicViewModel.m
//  IMYTTQ
//
//  Created by king on 16/5/4.
//  Copyright © 2016年 MeiYou. All rights reserved.
//

#import "TTQPublishTopicViewModel.h"
#import "TTQPublishContentModel.h"
#import "NSString+TTQ.h"
#import "TTQJumpType.h"
#import "TTQPublishTopicSuccessorViewModel.h"
#import "TTQHttpHelper.h"
#import "TTQABTestConfig.h"
#import "TTQNewbeeTaskManager.h"
#import "TTQHome5TopicModel.h"
#import <GCDObjC.h>
#import "TTQPublishHelper.h"
#import "TTQPublishToolView.h"
#import <IMYBaseKit/LDEventSource.h>
#import "UGCSSERequester.h"

#define kTTQPublishTemplateList @"kTTQPublishTemplateList_848"

IMY_KYLIN_FUNC_MAINTAB_ASYNC {
    [TTQPublishTopicConfiguration requestExperienceTemplateListWithCompletionHandler:nil];
}

@interface TTQPublishTopicViewModel ()
@property (nonatomic, strong) NSArray *publishToolItemIDs; //底部按钮的ID
@property (nonatomic, strong) CADisplayLink *displayLink;
@property (nonatomic, assign) NSUInteger flowContentIndex;  /// 流式内容的输出位置
@property (nonatomic, strong) UGCSSERequester *sseRequester;
@property (nonatomic, assign) NSInteger timeout;
@property (nonatomic, strong) TTQForumModel *forum;

@end

@implementation TTQPublishTopicViewModel
+ (instancetype)viewModelWithForum:(TTQForumModel *)forum {
    return [[self alloc] initWithForum:forum];
}

- (instancetype)initWithForum:(TTQForumModel *)forum {
    self = [self init];
    if (self) {
        self.forum = forum;
        if (forum == nil) {
            self.publishConfiguration.subjectModel = [TTQPublishTopicConfiguration defaultPublishSubjectFromDoor];
        }
        [self commentInit];
    }
    return self;
}
- (instancetype)initWithforum_id:(NSInteger)forum_id {
    self = [self init];
    if (self) {
        self.forum_id = forum_id;
        if (forum_id <= 0) {
            self.publishConfiguration.subjectModel = [TTQPublishTopicConfiguration defaultPublishSubjectFromDoor];
        } else {
            self.forum = [TTQForumModel forumOfID:forum_id];
            if (!self.forum) {
                @weakify(self);
                [[TTQForumHelper getForumByID:forum_id] subscribeNext:^(TTQForumModel *forum) {
                    @strongify(self);
                    self.forum = forum;
                    if (imy_isNotEmptyString(self.forum_name)) {
                        self.forum_name = forum.name;
                    }
                }];
            }
        }
        [self commentInit];
    }
    return self;
}

- (void)commentInit {
    self.imageNameForQiNiuMutablDic = [NSMutableDictionary dictionary];
    @weakify(self);
    [[RACObserve(self.publishConfiguration, subjectModel) takeUntil:self.rac_willDeallocSignal] subscribeNext:^(id x) {
        @strongify(self);
        if (self.subjectModel == nil) {
            if (self.publishConfiguration.subjectModel.subjectID > 0 && self.publishConfiguration.subjectModel.name) {
                self.subjectModel = self.publishConfiguration.subjectModel;
            }
        }
    }];
    IMYCConfigsGroup *group = [[IMYConfigsCenter sharedInstance] groupForKeyPath:@"community.publish_tool"];
    if ([group boolForKey:@"is_open_ai_title"]) {
        self.aiTitleWordCount = [group integerForKey:@"ai_title_word_limit"];
    }
    self.polish_up_word_limit = [group integerForKey:@"polish_up_word_limit"];
}

#pragma mark - 埋点

/// 发帖成功与否的埋点上报
/// @param success 发帖是否成功 0失败 1成功
/// @param topicID 帖子ID
/// @param imageCount 图片数量
/// @param code 请求错误码
/// @param errorMsg 请求错误的描述信息
/// @param time 得到响应, 计算的请求耗费的时间
- (void)postBIEventTopicPostStatus:(NSInteger)success topicID:(NSInteger)topicID imageCounts:(NSInteger)imageCount errorCode:(NSInteger)code errorMessage:(NSString*)errorMsg requestCostTime:(NSTimeInterval)time {
    if (imy_isEmptyString(errorMsg)) {
        errorMsg = @"";
    }
    [TTQCommonHelp GATopicPostSuccessRate:success topicID:topicID imageCounts:imageCount errorCode:code errorMessage:errorMsg requestCostTime:time eventType:1];
}

///发帖成功后上报图片编辑状态
- (void)postTopicSuccess:(NSDictionary*)dic {
    if (dic && [dic.allKeys containsObject:@"hasImages"]) {
        NSNumber *hasImages = dic[@"hasImages"];
        if (hasImages.integerValue == 0) {
            return;//说明帖子是纯文字  直接返回不上报
        }
    }
    NSNumber *hasEditImage = dic[@"isEdit"];
    //hasEditImage是否有编辑过图片 0否 1是  TODO:要根据图片编辑页回传的字段来判断. BI用topic_id来作为key, 也是够懒的
    [TTQCommonHelp GAEventForEventWith:@{@"event":@"ttq_twfb",@"action":@(2),@"topic_id":hasEditImage}];
}

///发帖成功后上报是否带有gif
- (void)reportHasGifAfterPost:(NSDictionary *)dic {
    if (dic && [dic.allKeys containsObject:@"hasImages"]) {
        NSNumber *hasImages = dic[@"hasImages"];
        if (hasImages.integerValue == 0) {
            return;//说明帖子是纯文字  直接返回不上报
        }
    }
    NSNumber *hasGif = dic[@"hasGif"];
    //hasGif是否有编辑过图片 2否 1是
    [TTQCommonHelp GAEventForEventWith:@{@"event":@"ttq_twfb_gif",@"action":@(2),@"public_type":hasGif}];
}


#pragma mark - 照片保存
/// 保存图片到相册
/// @param image 需要保存到相册的图片
/// @param imagePath 需要保存到相册的图片的path
/// @param cropImage 需要保存的图片对应的裁剪的原图
- (void)saveImageToAlbum:(UIImage *)image imageCachePath:(NSString*)imagePath cropImageURL:(NSString *)cropImage {
    [[PHPhotoLibrary sharedPhotoLibrary] performChanges:^{
        [PHAssetChangeRequest creationRequestForAssetFromImage:image];
    } completionHandler:^(BOOL success, NSError * _Nullable error) {
        if (error) {
            [UIWindow imy_showTextHUD:@"保存图片到相册失败了"];
        } else {
            //发帖完毕, 且图片保存成功需要删除缓存
            if ([[NSFileManager defaultManager] fileExistsAtPath:imagePath]) {
                BOOL removed = [[NSFileManager defaultManager] removeItemAtPath:imagePath error:nil];
                if (removed == NO) {
                    NSLog(@"删除图片缓存失败了, 检查下路径吧");
                }
            }
            
            if ([[NSFileManager defaultManager] fileExistsAtPath:cropImage]) {
                BOOL removed = [[NSFileManager defaultManager] removeItemAtPath:cropImage error:nil];
                if (removed == NO) {
                    NSLog(@"删除裁剪后的图片缓存失败了, 检查下路径吧");
                }
            }
        }
    }];
}

#pragma mark - 草稿保存

- (NSTimer *)saveTimer {
    if (_saveTimer == nil) {
        @weakify(self);
        _saveTimer = [NSTimer bk_scheduledTimerWithTimeInterval:10
                                                     block:^(NSTimer *timer) {
                                                         @strongify(self);
                                                         [self saveDraftAction:NO];
                                                     }
                                                   repeats:YES];
    }
    return _saveTimer;
}

- (BOOL)isDraftEmpty:(TTQPublishModel*)draft {
    BOOL isEmpty = NO;
    if (draft.items.count == 0 && imy_isEmptyString(draft.content) &&  imy_isEmptyString(draft.title)) {
        isEmpty = YES;
    }
    return isEmpty;
}

- (void)invalidateSaveTimer {
    if (_saveTimer) { //停止计时
        [self.saveTimer invalidate];
        self.saveTimer = nil;
    }
}

- (void)pauseSaveTimer {
    if (_saveTimer) {
        //随便设置一个很长的时间，最后会把时间改成是10秒，先暂停
        [self.saveTimer setFireDate:[NSDate distantFuture]];
    }
}

//常规10s后开始保存草稿
- (void)startSaveTimer {
    if (self.saveTimer) {
        [self.saveTimer setFireDate:[NSDate dateWithTimeIntervalSinceNow:10]];
    }
}

//内存不足后30s后开始保存草稿
- (void)startSaveTimerForMedian {
    if (self.saveTimer) {
        [self.saveTimer setFireDate:[NSDate dateWithTimeIntervalSinceNow:30]];
    }
}

- (void)deletedDraf {
    [self invalidateSaveTimer];
    if ([self.draft deleteDraft]) {
        [TTQPublishModel clearNonDataForImages];
    }
}

- (void)saveDraftAction:(BOOL)await {
    //暂停计时
    [self pauseSaveTimer];
    TTQPublishModel *draft = [self updateDraft];

    @weakify(self);
    if ([UIDevice imy_dualCore] && !await) {
        //多核情况下若是不等待就立马保存
        [[GCDQueue backgroundPriorityGlobalQueue] queueBlock:^{
            @strongify(self);
            if ([self isDraftEmpty:draft]) {
                return;//空的草稿不保存
            }
            @synchronized (self) {
                if ([self.draft saveToDB] || [draft saveToDB]) {
                    [TTQPublishModel clearNonDataForImages];
                }
            }
        }];
    } else {
        [NSObject imy_asyncBlock:^{
            @strongify(self);
            if ([self.draft saveToDB] || [draft saveToDB]) {
                [TTQPublishModel clearNonDataForImages];
            }
        }];
    }
    [self startSaveTimer];
}

- (TTQPublishModel *)updateDraft {
    TTQPublishModel *model = self.draft;
    model.publishStatus = TTQPublishDraftStatusNormal;
    model.publishErrorMsg = nil;
    model.from_type = self.from_type;
    model.title = self.titleStr;
    model.referer = self.referer;
    if (self.isEdit) {
        model.isEdit = self.isEdit;
        model.topicId = self.topic_id;
    }
    
    if ([self.titleStr isEqualToString:@""]) {
        model.title = nil;
    }
    model.voteTexts = self.voteAry;
    if (self.subjectModel.isSelected) {
        model.subjectModel = self.subjectModel;
    } else {
        model.subjectModel = nil;
    }
    // 视频类型
    if(self.video == nil){
        model.video = nil;
        model.videoCover = nil;
        model.showVideo = NO;
    } else {
        model.showVideo = YES;
        model.modeType = TTQPublishDraftModeTypeVideo;
        NSMutableDictionary *videoCoverDic = [NSMutableDictionary dictionary];
        model.videoCover = self.videoCover;
        
        NSMutableDictionary *videoDic = [NSMutableDictionary dictionary];
        videoDic[@"identifier"] = self.video.identifier;
        videoDic[@"duration"] = self.video.duration;
        model.video = [videoDic copy];
    }
    
    NSMutableArray *images = [[NSMutableArray alloc] init];
    NSMutableArray *urls = [NSMutableArray array];
    NSMutableArray *imageKeyPathes = [NSMutableArray array];
    NSArray *validDataSource = [self validDataSources];
    NSMutableArray *items = [NSMutableArray array];
    NSMutableArray *newImages = [validDataSource mutableCopy];
    
    for (TTQPublishContentModel *model in validDataSource) {
        if (model.richContentType == TTQPublishContentTypeText) {
            TTQPublishTextModel *tModel = (TTQPublishTextModel *)model;
            [items addObject:tModel.text];
        } else if (model.richContentType == TTQPublishContentTypeImage) {
            // 新版发布器直接保存json
            TTQPublishImageModel *imageModel = (TTQPublishImageModel *) model;
            NSMutableDictionary *item = [NSMutableDictionary dictionary];
            item[@"url"] = imageModel.publishImage.assetUrl? :imageModel.publishImage.editUrl;
            item[@"imageName"] = imageModel.publishImage.imageName;
            item[@"additionalTitle"] = imageModel.additionalTitle;
            NSString *itemURL = [NSString stringWithFormat:@"%@%@",item[@"url"],[self.class uuidString]];
            item[@"imageKeyPath"] = imy_isEmptyString(imageModel.publishImage.imageKeyPath)? itemURL : imageModel.publishImage.imageKeyPath;
            if (!(item[@"editURL"] == nil && imageModel.publishImage.editUrl == nil)) {
                // 未编辑图片不进行赋值，跟原model保持一致
                item[@"editURL"] = imageModel.publishImage.editUrl? :imageModel.publishImage.assetUrl;
            }
            item[@"isGif"] = @(imageModel.publishImage.isGif);
            [items addObject:item];

            NSString *url = imageModel.publishImage.editUrl;
            if (imy_isEmptyString(url)) {
               url  = imageModel.publishImage.assetUrl;
            }
            [urls addObject:url];
            [imageKeyPathes addObject:item[@"imageKeyPath"]];
        }
    }
    if (newImages.count == 0 && self.originalDraft.topicImagesArray == nil) {
        model.topicImagesArray = nil;
    } else {
        model.topicImagesArray = [newImages copy]; // 这个是新发布器的 TTQPublishTextModel+TTQPublishImageModel（取publishNewImage）数组
    }
    model.imageKeyPathes = imageKeyPathes;
    model.urls = urls;
    model.items = items.copy;
    model.aiReferer = self.aiReferer;
    return model;
}

+ (NSString *)uuidString {
    CFUUIDRef uuid_ref = CFUUIDCreate(NULL);
    CFStringRef uuid_string_ref= CFUUIDCreateString(NULL, uuid_ref);
    NSString *uuid = [NSString stringWithString:(__bridge NSString* )uuid_string_ref];
    CFRelease(uuid_ref);
    CFRelease(uuid_string_ref);
    return [uuid lowercaseString];
}

- (BOOL)draftChanged {
    return [self editDraftChanged];
}

- (BOOL)editDraftChanged {
    if (self.draft.title != nil && self.originalDraft.title != nil) {
        if (![self.draft.title isEqualToString:self.originalDraft.title]) {
            return YES;
        }
    } else if(!(self.draft.title == nil && self.originalDraft.title == nil)){
        return YES;
    }
    
    if (self.draft.content != nil && self.originalDraft.content != nil) {
        if (![self.draft.content isEqualToString:self.originalDraft.content]) {
            return YES;
        }
    } else if(!(self.draft.content == nil && self.originalDraft.content == nil)){
        return YES;
    }
    
    if (self.draft.isAnonymous != self.originalDraft.isAnonymous) {
        return YES;
    }
    if (self.draft.visible != self.originalDraft.visible) {
        return YES;
    }
    if (self.draft.product_id != self.originalDraft.product_id) {
        return YES;
    }
    if (self.draft.forum_id != self.originalDraft.forum_id) {
        return YES;
    }
    if (self.draft.isRichModel != self.originalDraft.isRichModel) {
        return YES;
    }
    if (self.draft.showVote != self.originalDraft.showVote) {
        return YES;
    }
    if (self.draft.showVideo != self.originalDraft.showVideo) {
        return YES;
    }
    // 比对下视频内容和视频封面
    if (self.draft.video != nil && self.originalDraft.video != nil) {
        if(![self.draft.video isEqualToDictionary:self.originalDraft.video]){
            return YES;
        }
        if (self.draft.videoCover.assetUrl != nil && self.originalDraft.videoCover.assetUrl != nil) {
            if(![self.draft.videoCover.assetUrl isEqualToString:self.originalDraft.videoCover.assetUrl]){
                return YES;
            }
        }
        if (self.draft.videoCover.filePath != nil && self.originalDraft.videoCover.filePath != nil) {
            if(![self.draft.videoCover.filePath isEqualToString:self.originalDraft.videoCover.filePath]){
                return YES;
            }
        }
    }
    if (self.draft.voteAvailable != self.originalDraft.voteAvailable) {
        return YES;
    }
    if (self.draft.subjectModel != nil && self.originalDraft.subjectModel != nil) {
        if (self.draft.subjectModel.subjectID != self.originalDraft.subjectModel.subjectID) {
            return YES;
        }
        if (![self.draft.subjectModel.redirect_url isEqualToString:self.originalDraft.subjectModel.redirect_url]) {
            return YES;
        }
        if (![self.draft.subjectModel.name isEqualToString:self.originalDraft.subjectModel.name]) {
            return YES;
        }
        if (self.draft.subjectModel.isSelected != self.originalDraft.subjectModel.isSelected) {
            return YES;
        }
    } else if(self.draft.subjectModel == nil && self.originalDraft.subjectModel == nil){
        
    } else {
        return YES;
    }
    
    // TODO: 新版草稿不比对urls和items，比对topicimagearray
    if (self.draft.topicImagesArray != nil && self.originalDraft.topicImagesArray != nil) {
        if (self.draft.topicImagesArray.count != self.originalDraft.topicImagesArray.count) {
            return YES;
        } else {
            for (int i = 0; i < self.draft.topicImagesArray.count; i++) {
                TTQPublishContentModel *contentModel = [self.draft.topicImagesArray objectAtIndex:i];
                TTQPublishContentModel *oriContentModel = [self.originalDraft.topicImagesArray objectAtIndex:i];
                
                if(contentModel.richContentType != oriContentModel.richContentType){
                    return YES;
                } else if(contentModel.richContentType == TTQPublishContentTypeText){
                    TTQPublishTextModel *textModel = [self.draft.topicImagesArray objectAtIndex:i];
                    TTQPublishTextModel *oriTextModel = [self.originalDraft.topicImagesArray objectAtIndex:i];if(![textModel.text isEqualToString:oriTextModel.text]){
                        return YES;
                    }
                } else if(contentModel.richContentType == TTQPublishContentTypeImage){
                    TTQPublishImageModel *textModel = [self.draft.topicImagesArray objectAtIndex:i];
                    TTQPublishImageModel *oriTextModel = [self.originalDraft.topicImagesArray objectAtIndex:i];
                    // 应该只需要比对assetUrl，图片在加载过程中存到了本地不算编辑过
                    if(![textModel.publishNewImage.assetUrl isEqualToString:oriTextModel.publishNewImage.assetUrl]){
                        return YES;
                    }
                    if(textModel.additionalTitle == nil && oriTextModel.additionalTitle == nil){
                        
                    } else if(![textModel.additionalTitle isEqualToString:oriTextModel.additionalTitle]){
                        return YES;
                    }
                }
            }
        }
    } else if(!(self.draft.topicImagesArray == nil && self.originalDraft.topicImagesArray == nil)){
        return YES;
    }
    return NO;
}

- (BOOL)showAnonymousView {
    return self.publishConfiguration.is_anonymous;
}

- (BOOL)showSubjectView {
    BOOL show = NO;
    for (NSNumber *itemID in self.publishToolItemIDs) {
        NSInteger itemIDWithInt = itemID.integerValue;
        if (itemIDWithInt == 3) {
            show = YES;
            break;
        }
    }
    return show;
}

- (BOOL)shouldShowPopView {
    NSString *key = @"ttq_publish_template_pop_showed";
    BOOL showed = [[IMYUserDefaults standardUserDefaults] boolForKey:key];
    if (!showed) {
        [[IMYUserDefaults standardUserDefaults] setBool:YES forKey:key];
    }
    return !showed;
}

- (NSArray *)publishToolItemIDs {
    if (!_publishToolItemIDs) {
        IMYSwitchModel *switchModel = [[IMYDoorManager sharedManager] switchForType:@"publish_toolbar_cfgs"];
        if (switchModel.status) {
            NSArray *ids = [switchModel.dataDictionary objectForKey:@"toolbar"];
            if ([ids isKindOfClass:[NSArray class]] && ids.count) {
                //去重--为什么不用@distinctUnionOfObjects.self，因为出来顺序不对٩(˃̶͈̀௰˂̶͈́)و
                NSMutableArray *mutableIds = [NSMutableArray array];
                for (NSNumber *idNum in ids) {
                    if (![mutableIds containsObject:idNum]) {
                        [mutableIds addObject:idNum];
                    }
                }
                [mutableIds addObject:@10];
                _publishToolItemIDs = mutableIds.copy;
            }
        }
        if (!_publishToolItemIDs) {
            _publishToolItemIDs = @[];
        }
    }
    return _publishToolItemIDs;
}
///
- (void)resetPublishToolItem_865 {
    IMYCConfigsGroup *group = [[IMYConfigsCenter sharedInstance] groupForKeyPath:@"community.publish_tool"];
    NSArray *tools = [group arrayForKey:@"toolbar"];
    tools = [tools sort:^BOOL(NSDictionary *_Nonnull element1, NSDictionary *_Nonnull element2) {
        NSInteger  index1 = [element1[@"sort"] integerValue];
        NSInteger index2 = [element2[@"sort"] integerValue];
        return index1 < index2;
    }];
    NSMutableArray *array = [NSMutableArray arrayWithCapacity:tools.count];
    for (NSDictionary *tool in tools) {
        NSInteger toolId = [tool[@"type"] integerValue];
        [array addObject:@([self itemFrom865Id:toolId])];
    }
    if (array.count == 0) {
        array = @[@(TTQPublishToolMenuItemTypeForCamera),@(TTQPublishToolMenuItemTypeForlToolTopic),@(TTQPublishToolMenuItemTypeForVote),@(TTQPublishToolMenuItemTypeForEmotion),@(TTQPublishToolMenuItemTypeForExperienceTemplate)];
    }
    _publishToolItemIDs = array;
}

- (TTQPublishToolMenuItemType)itemFrom865Id:(NSInteger)type {
    if (type == 1) {
        return TTQPublishToolMenuItemTypeForCamera;
    } else if (type == 2) {
        return TTQPublishToolMenuItemTypeForEmotion;
    }  else if (type == 4) {
        return TTQPublishToolMenuItemTypeForVote;
    } else if (type == 5) {
        return TTQPublishToolMenuItemTypeForAnonymity;
    } else if (type == 6) {
        return TTQPublishToolMenuItemTypeForExperienceTemplate;
    } else if (type == 7) {
        return TTQPublishToolMenuItemTypeForlToolTopic;
    }  else if (type == 3) {
        return 3;
    } else if (type == 9) {
        return TTQPublishToolMenuItemTypeForAiTool;
    } else {
        return 0;
    }
}


- (void)setForum:(TTQForumModel *)forum {
    _forum = forum;
    if (forum) {
        self.forum_id = forum.forum_id;
    }
}

- (void)setForum_name:(NSString *)forum_name {
    _forum_name = [forum_name copy];
    self.draft.forum_name = [forum_name copy];
}

- (TTQPublishTopicConfiguration *)publishConfiguration {
    if (_publishConfiguration == nil) {
        _publishConfiguration = [TTQPublishTopicConfiguration newestPublishConfiguration];
    }
    return _publishConfiguration;
}

#pragma mark - valid datasource

- (NSArray *)validDataSources {
    NSArray *dataSources =
            [self.dataSource bk_select:^BOOL(TTQPublishContentModel *obj) {
                if (obj.richContentType == TTQPublishContentTypeText) {
                    if (imy_isEmptyString([(TTQPublishTextModel *) obj text])) {
                        return NO;
                    } else {
                        [(TTQPublishTextModel *) obj
                                setText:[(TTQPublishTextModel *) obj text]];
                    }
                } else if (obj.richContentType == TTQPublishContentTypeImage) {
                    TTQPublishImageModel *ugcContent = (TTQPublishImageModel *)obj;
                    if(ugcContent.publishNewImage.imageObj){
                        // TODO: 这里需要测试旧草稿第一次转上来 没有渲染到的情况是否能过valid
                    } else if(ugcContent.publishNewImage.sourceType == IMYGPublishImageSourceTypeApp){
                        // 图片已经在本地了，不会丢失，也可以算，针对草稿箱多图image根本加载没那么快
                    } else if(ugcContent.publishNewImage.sourceType == IMYGPublishImageSourceTypeNet && imy_isNotEmptyString(ugcContent.publishNewImage.assetUrl)){
                        // 图片是从网络编辑过来的，也算正常的一张
                    } else if(ugcContent.publishNewImage.sourceType == IMYGPublishImageSourceTypeAlbum && ugcContent.publishNewImage.assetNonExist) {
                        return NO;
                    }
                }
                return YES;
            }];
    return dataSources;
}

- (NSArray<TTQPublishTextModel *> *)onlyTextContentData {
    return [self.dataSource filter:^BOOL(TTQPublishContentModel *obj) {
        return obj.richContentType == TTQPublishContentTypeText;
    }];
}

- (NSArray *)onlyImageContentData {
    return [self.dataSource filter:^BOOL(TTQPublishContentModel *obj) {
        return obj.richContentType == TTQPublishContentTypeImage;
    }];
}

- (NSString *)current_content {
    TTQPublishTextModel *model = [self.dataSource match:^BOOL(TTQPublishContentModel *obj) {
        return obj.richContentType == TTQPublishContentTypeText;
    }];
    return [model.text imy_trimString];
}

- (NSUInteger)current_content_word_num {
    NSString *currentCount = [self current_content];
    if (!currentCount) {
        return 0;
    }
    return [[currentCount ttq_parseTopicsWithTypingAttributes:@{}].string ttq_textLength];
}
#pragma mark - 子接口数据获取

- (void)fetchRecommendSubjectsWithCompletionHandler:(void(^)(void))completionHandler {
    self.subjectsCategory = [TTQPublishHelper sharedInstance].isRecommendTopics;
    NSString *text = [[self onlyTextContentData].firstObject text];
    NSDictionary *params = nil;
    if (text.length) {
        params = @{@"keyword":text};
    }
    
    [[[IMYServerRequest getPath:@"v5/publish_subject_recommend" host:circle_seeyouyima_com params:params headers:nil] deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
        NSDictionary *dic = [x responseObject];
        BOOL isHot = self.subjectsCategory == 0;
        if (isHot) { // 热议
            self.recommendSubjects = dic[@"hot"];
        } else {
            self.recommendSubjects = dic[@"recommend"];
        }
        if (self.recommendSubjects.count == 0) {
            /// 如果没拿到数据，就用另外一个数据去兜底
            self.recommendSubjects = isHot?dic[@"recommend"]:dic[@"hot"];
            self.subjectsCategory = isHot?1:0;
            isHot = !isHot;
        }
        if (self.recommendSubjects.count) {
            /// 有数据才切换
            [TTQPublishHelper sharedInstance].isRecommendTopics = isHot ? 1 : 0;
        }
        if (completionHandler) {
            completionHandler();
        }
    } error:^(NSError * _Nullable error) {
        self.recommendSubjects = nil;
        if (completionHandler) {
            completionHandler();
        }
    }];
}

- (void)requestExperienceTemplateListWithCompletionHandler:(void (^)(BOOL changeToPicker, TTQPublishTemplateModel *fillModel))completionHandler; {
    // 先读取缓存
    __block BOOL callback = NO;
    NSString *cacheKey = kTTQPublishTemplateList;
    if (self.templateTypeID > 0 || self.subjectModel || self.currentTemplateID > 0) {
        cacheKey = [NSString stringWithFormat:@"%@_%@_%@_%ld_%ld",kTTQPublishTemplateList, @(self.templateType), @(self.templateTypeID),self.subjectModel.subjectID,self.currentTemplateID];
    }
    NSDictionary *dic = [[IMYCacheHelper sharedCacheManager] objectForKey:cacheKey];
    NSArray *dicArray = dic[@"exp_tpl"];
    NSArray *newArray = [dicArray toModels:TTQPublishTemplateModel.class];
    dicArray = dic[@"sub_tpl"];
    NSArray *subArray = [dicArray toModels:TTQPublishTemplateModel.class];
    if (subArray.count > 0) {
        self.subTemplateList = subArray;
        NSMutableArray *allArray = [NSMutableArray arrayWithArray:subArray];
        [allArray addObjectsFromArray:newArray];
        newArray = [allArray copy];
    }
    BOOL hitCache = newArray.count?YES:NO;
    if (newArray.count == 0 && self.templateTypeID > 0) {// 暂时先读取通用模板
        dic = [[IMYCacheHelper sharedCacheManager] objectForKey:kTTQPublishTemplateList];
        dicArray = dic[@"exp_tpl"];
        newArray = [dicArray toModels:TTQPublishTemplateModel.class];
        dicArray = dic[@"sub_tpl"];
        subArray = [dicArray toModels:TTQPublishTemplateModel.class];
        if (subArray.count > 0) {
            self.subTemplateList = subArray;
            NSMutableArray *allArray = [NSMutableArray arrayWithArray:subArray];
            [allArray addObjectsFromArray:newArray];
            newArray = [allArray copy];
        }
    }
    if (newArray.count > 0) {
        self.templateList = newArray;
        if (hitCache) {
            /// 有命中缓存的才直接回调
            callback = YES;
            [self templateListHandler:completionHandler withData:newArray];
        }
    }
    
    // 请求最新接口
    NSMutableDictionary *params = [NSMutableDictionary dictionaryWithCapacity:0];
    if (self.templateTypeID > 0) {
        params = @{@"item_id":@(self.templateTypeID), @"item_type":@(self.templateType)};
    } else {
        if (self.subjectModel) {
            params[@"subject_id"] = @(self.subjectModel.subjectID);
        }
        if (self.currentTemplateID) {
            params[@"item_id"] = @(self.currentTemplateID);
            params[@"item_type"] = @3;
        }
    }
    @weakify(self);
    [[[IMYServerRequest getPath:@"v5/publish_config" host:circle_seeyouyima_com params:params headers:nil] deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        NSDictionary *dic = [x responseObject];
        [[IMYCacheHelper sharedCacheManager] setObject:dic forKey:cacheKey];
        
        NSArray *dicArray = dic[@"exp_tpl"];
        NSArray *newArray = [dicArray toModels:TTQPublishTemplateModel.class];
        dicArray = dic[@"sub_tpl"];
        NSArray *subArray = [dicArray toModels:TTQPublishTemplateModel.class];
        if (subArray.count > 0) {
            self.subTemplateList = subArray;
            NSMutableArray *allArray = [NSMutableArray arrayWithArray:subArray];
            [allArray addObjectsFromArray:newArray];
            newArray = [allArray copy];
        }
        if (![self.templateList isEqualToArray:newArray] && !callback && !hitCache) {
            callback = YES;
            self.templateList = newArray;
            /// 只使用一次回调，请求只更新数据
            [self templateListHandler:completionHandler withData:newArray];
        }

    } error:^(NSError * _Nullable error) {
        if (!callback) {
            callback = YES;
            completionHandler(NO, nil);
        }
    }];
    imy_asyncMainBlock(1, ^{
        /// 1秒内无结果返回就主动结束交互
        if (!callback && completionHandler) {
            completionHandler(NO,nil);
        }
    });
}

- (void)templateListHandler:(void (^)(BOOL changeToPicker, TTQPublishTemplateModel *fillModel))completionHandler withData:(NSArray *)newArray {
    if (completionHandler) {
        /// 定位逻辑
        if (self.templateTypeID > 0) {
            // 这里是旧的经验模版逻辑， 如果返回的sub_tpl只有一条数据就自动上屏，不切换picker，如果多于1个就切换Picker，不上屏
            if (self.subTemplateList.count == 1) {
                completionHandler(NO, self.subTemplateList.firstObject);
            } else if (self.subTemplateList.count > 1) {
                completionHandler(YES, nil);
            }
        } else {
            /// 884版本模板逻辑
            if (self.currentTemplateID) {
                /// 如果匹配到带入的模板id，就自动上屏，不切换
                TTQPublishTemplateModel *matchObj = [newArray match:^BOOL(TTQPublishTemplateModel *element) {
                    return element.templateID == self.currentTemplateID;
                }];
                if (matchObj) {
                    completionHandler(NO, matchObj);
                    return;
                }
            }
            /// 如果带了话题，且匹配到了该话题绑定的模板，就切换picker
            if (self.subjectModel.subjectID) {
                TTQPublishTemplateModel *matchObj = newArray.firstObject;
                if (matchObj.subject_id == self.subjectModel.subjectID) {
                    completionHandler(YES, nil);
                    return;
                }
            }
        }
        completionHandler(NO, nil);
    }
    
}

#pragma mark - 文章内容，校验模板标题

// 过滤模板内容，发布的时候，移除无效的小标题
- (NSString *)filterUselessTemplateTitle:(NSMutableString *)allContent skipImageTag:(BOOL)skipImageTag {
    /// 8.84版本，产品确认不过模板了（黄菲）
    return allContent;
    if (self.draft.templateModel) {
        // 找到第一个小标题
        NSRange firstRange = NSMakeRange(NSNotFound, 0);
        for (TTQPublishTemplateItemModel *item in self.draft.templateModel.sub_titles) {
            NSRange range =  [allContent rangeOfString:[NSString stringWithFormat:@"%@",item.title] options:0];
            if (range.location != NSNotFound) {
                NSString *preString = [allContent substringToIndex:range.location];
                if (imy_isBlankString(preString)) {// 如果前面为空，说明找到的这个可以作为小标题，否则小标题前面必须有换行
                    firstRange = range;
                    break;
                } else if ([preString hasSuffix:@"\n"]) { // 如果小标题前面有换行，说明这个的确是小标题
                    if (firstRange.location == NSNotFound) {
                        firstRange = range;
                    } else if (range.location < firstRange.location ) {
                        firstRange = range;
                    }
                }
            }
        }
        if (firstRange.location == NSNotFound) {// 找不到任何模板内的小标题
            return [allContent copy];
        }
        
        // 遍历中间的小标题
        NSRange currentRange = firstRange;
        NSUInteger startLocation = currentRange.location + currentRange.length;
        NSRange nextRange = NSMakeRange(0, 0);
        NSRange deleteRange = NSMakeRange(0, 0);
        while (nextRange.location != NSNotFound) {// 当找不到下个标题，遍历结束
            nextRange = [self rangeOfNextTitleAtTitleArray:self.draft.templateModel.sub_titles allContent:allContent startLocation:startLocation];
            if (nextRange.location != NSNotFound) { // 找到下一个标题
                NSString *subString = [allContent substringWithRange:NSMakeRange(startLocation, nextRange.location-startLocation)];
                if (imy_isBlankString(subString)) {// 说明当前标题没有内容，移除当前小标题，如果前面有换行，要移除
                    // 移除标题和后面的空行
                    deleteRange = NSMakeRange(currentRange.location, currentRange.length + subString.length);
                    [allContent deleteCharactersInRange:deleteRange];
                } else if (skipImageTag && [subString containsString:@"<@@@img@@@>"]) { // 删除图片标签，为了计算数字，图片不占字数
                    NSRange imgRange = [allContent rangeOfString:@"<@@@img@@@>" options:0 range:NSMakeRange(startLocation, allContent.length - startLocation)];
                    deleteRange = imgRange;
                    [allContent deleteCharactersInRange:deleteRange];
                }
                currentRange = NSMakeRange(nextRange.location - deleteRange.length, nextRange.length);
                startLocation = currentRange.location + currentRange.length;
                deleteRange = NSMakeRange(0, 0);
            }
        }
        // 最后一个标题
        if (currentRange.location != NSNotFound && currentRange.length > 0) {
            startLocation = currentRange.location + currentRange.length;
            NSString *subString = [allContent substringWithRange:NSMakeRange(startLocation, allContent.length - startLocation)];
            if (imy_isBlankString(subString)) {// 说明当前标题没有内容，移除当前小标题，如果前面有换行，要移除
                // 移除标题和后面的空行
                deleteRange = NSMakeRange(currentRange.location, currentRange.length + subString.length);
                [allContent deleteCharactersInRange:deleteRange];
            }
        }
    }
    return [allContent copy];
}

- (NSRange)rangeOfNextTitleAtTitleArray:(NSArray<TTQPublishTemplateItemModel *> *)titleArray allContent:(NSString *)allContent startLocation:(NSUInteger)startLocation {
    NSRange nextRange = NSMakeRange(NSNotFound, 0);
    for (TTQPublishTemplateItemModel *item in  titleArray) {
        NSRange range =  [allContent rangeOfString:[NSString stringWithFormat:@"\n%@",item.title] options:0 range:NSMakeRange(startLocation, allContent.length - startLocation)];
        if (range.location != NSNotFound) {
            if (nextRange.location == NSNotFound) {
                nextRange = range;
            } else if (range.location < nextRange.location ) {
                nextRange = range;
            }
        }
    }
    return nextRange;
}

// 发送文本校验，图片不计算字符，小于8个字符返回NO
- (BOOL)checkPostContent {
    NSMutableString *content = [NSMutableString string];
    NSInteger index = 0;
    NSInteger lastType = 2;
    // 合并所有的发布内容：包括文字和图片
    for (TTQPublishContentModel *model in self.dataSource) {
        index++;
        if (model.richContentType == TTQPublishContentTypeText) {
            NSString *text = [[(TTQPublishTextModel *) model text] imy_trimString];
            if (text.length > 0) {
                if (lastType == TTQPublishContentTypeText) {
                    [content appendString:@"\n"];
                }
                [content appendString:text];
                lastType = TTQPublishContentTypeText;
            }
        }
    }
    // 过滤模板标题和图片，计算用户实际输入的文字字数
    NSString *finalContent = [self filterUselessTemplateTitle:content skipImageTag:YES];// 图片不计算字符，要删掉图片占位标签，方便最后计算字数
    finalContent = [[finalContent ttq_stringWithoutTopics] imy_trimString];
    /// 美柚表情按一个字符算
    finalContent = [finalContent ttq_parseTopicsWithTypingAttributes:@{}].string;
    return finalContent.ttq_textLength >= 8;
}

// 发布过滤，接入图片的地址，移除无效的小标题
- (void)getAllContentStringAndImages:(void(^)(NSArray *images, NSString *content))completionHandler {
    NSMutableString *content = [NSMutableString string];
    NSInteger index = 0;
    NSInteger lastType = 2;
    NSMutableArray *images = [NSMutableArray array];
    for (TTQPublishContentModel *model in self.dataSource) {
        index++;
        if (model.richContentType == TTQPublishContentTypeText) {
            NSString *text = [[(TTQPublishTextModel *) model text] imy_trimString];
            if (text.length > 0) {
                if (lastType == TTQPublishContentTypeText) {
                    [content appendString:@"\n"];
                }
                [content appendString:text];
                lastType = TTQPublishContentTypeText;
            }
        } else if (model.richContentType == TTQPublishContentTypeImage) {
            //图片的src来源于刚刚上传成功后,服务器的返回
            TTQPublishImageModel *imageModel = (TTQPublishImageModel *) model;
            if (imageModel.publishImage.imageName) {
                [images addObject:imageModel.publishImage.imageName];
                [content appendFormat:@"<img src=\"%@\" ",
                                      imageModel.publishImage.imageName];
                if (imageModel.additionalTitle) {
                    [content appendFormat:@"desc=\"%@\" ", imageModel.additionalTitle];
                }
                [content appendString:@"/>\n"];
                lastType = TTQPublishContentTypeImage;
            }
        }
    }
    NSString *finalContent = [self filterUselessTemplateTitle:content skipImageTag:NO];// 移除无效小标题
    if (completionHandler) {
        completionHandler(images, finalContent);
    }
}

/// 给发布队列用的，以前的旧逻辑需要等图片上传完成，才能拼接完整的字符串，现在图片上传后置，就不行了，先用固定标识替代下 $picurl$
- (void)getAllContentStringAndImagesForPublishManager:(void(^)(NSArray *images, NSString *content))completionHandler {
    NSMutableString *content = [NSMutableString string];
    NSInteger index = 0;
    NSInteger lastType = 2;
    NSMutableArray *images = [NSMutableArray array];
    for (TTQPublishContentModel *model in self.dataSource) {
        index++;
        if (model.richContentType == TTQPublishContentTypeText) {
            NSString *text = [[(TTQPublishTextModel *) model text] imy_trimString];
            if (text.length > 0) {
                if (lastType == TTQPublishContentTypeText) {
                    [content appendString:@"\n"];
                }
                [content appendString:text];
                lastType = TTQPublishContentTypeText;
            }
        } else if (model.richContentType == TTQPublishContentTypeImage) {
            //图片的src来源于刚刚上传成功后,服务器的返回
            TTQPublishImageModel *imageModel = (TTQPublishImageModel *) model;
            NSString *imageName = @"&&picurl&&";
            if (imageModel.publishNewImage.assetUrl || imageModel.publishNewImage.imageObj) {
                [content appendFormat:@"<img src=\"%@\" ", imageName];
                if (imageModel.additionalTitle) {
                    [content appendFormat:@"desc=\"%@\" ", imageModel.additionalTitle];
                }
                /// 现在图片都是在底部，且不带注释了，不要拼换行符
                [content appendString:@"/>"];
                lastType = TTQPublishContentTypeImage;
            }
        }
    }
    NSString *finalContent = [self filterUselessTemplateTitle:content skipImageTag:NO];// 移除无效小标题
    finalContent = [finalContent imy_trimString];
    if (completionHandler) {
        completionHandler(images, finalContent);
    }
}

#pragma mark - 流式内容输出

- (UGCSSERequester *)sseRequester {
    if (!_sseRequester) {
        _sseRequester = [UGCSSERequester new];
    }
    return _sseRequester;
}

- (void)startSSERequest {
    self.sseRequester.requestContent = self.promise_content;
    self.sseRequester.stream_type = self.stream_type;
    self.sseRequester.referer = self.aiReferer;
    self.draft.flowContent = nil;
    self.draft.flowPrintContent = nil;
    self.draft.flowComplete = NO;
    [self.sseRequester startRequestWithOutput:^(NSDictionary * _Nonnull data, BOOL finish, NSError * _Nonnull error) {
        if (finish) {
            if (imy_isNotEmptyString(self.draft.flowContent)) {
                /// 有数据才能认为是成功
                self.draft.flowComplete = YES;
            }
        }
        if (!error && data) {
            NSArray *messages = data[@"messages"];
            NSString *content = messages.firstObject[@"content"];
            if (imy_isNotEmptyString(content)) {
                if (imy_isNotEmptyString(self.draft.flowContent)) {
                    self.draft.flowContent = [NSString stringWithFormat:@"%@%@",self.draft.flowContent, content];
                } else {
                    self.draft.flowContent = content;
                }
            }
        } else if (error) {
            /// 报错了
            if (self.aiOutputErrorBlock) {
                self.aiOutputErrorBlock(error);
            }
        }
    }];
    self.isUpdatingFlow = YES;
    [self startFlowContentTimer];
    if (self.updateFlowContentBlock) {
        self.updateFlowContentBlock(self.draft);
    }
}

- (void)startFlowContentTimer {
    if (self.displayLink) {
        [self.displayLink invalidate];
    }
    self.timeout = 0;
    self.flowContentIndex = 0;
    self.displayLink = [CADisplayLink displayLinkWithTarget:self selector:@selector(refreshFlowContent)];
    self.displayLink.frameInterval = 2;

    [self.displayLink addToRunLoop:[NSRunLoop currentRunLoop] forMode:NSDefaultRunLoopMode];
}

- (void)refreshFlowContent {
    if (self.flowContentIndex >= self.draft.flowContent.length) {
        if (self.draft.flowComplete) {
            [self stopFlowContentTimer];
            return;
        }
        self.timeout ++;
        if (self.timeout > 30*5) {
            /// 超时了，目前定为5秒
            self.draft.flowComplete = YES;
            [self stopFlowContentTimer];
            [self.sseRequester cancelRequest];
            /// 报错了
            if (self.aiOutputErrorBlock) {
                self.aiOutputErrorBlock([NSError new]);
            }
            [IMYErrorTraces postWithType:IMYErrorTraceTypePageTimeout pageName:NSStringFromClass([UIViewController imy_currentTopViewController].class) category:IMYErrorTraceCategoryCommunity message:@"流式请求超时" detail:nil];
        }
        return;
    }
    self.timeout = 0;
    self.flowContentIndex ++;
    NSString *content = [self.draft.flowContent substringToIndex:self.flowContentIndex];
    self.draft.flowPrintContent = content;
    if (self.draft.flowComplete) {
        self.isUpdatingFlow = NO;
    }
    if (self.updateFlowContentBlock) {
        self.updateFlowContentBlock(self.draft);
    }
}

- (void)stopFlowContentTimer {
    [self.displayLink invalidate];
    self.isUpdatingFlow = NO;
    
}

- (void)cancleSSERequest {
    [self.sseRequester cancelRequest];
    [self stopFlowContentTimer];
//    self.stream_type = nil;
//    self.promise_content = nil;
}

- (BOOL)isAiOutput {
    return imy_isNotEmptyString(self.promise_content) && imy_isNotEmptyString(self.stream_type);
}

+ (BOOL)openPkStyle {
    IMYCConfigsGroup *group = [[IMYConfigsCenter sharedInstance] groupForKeyPath:@"community.publish_tool"];
    if (group && [group boolForKey:@"is_open_pk_vote"]) {
        return YES;
    }
    return NO;
}

- (BOOL)enableAiChanJian {
    if (self.aiInit) {
        return NO;
    }
    if (self.isEdit) {
        return NO;
    }
    if (self.publishFromType == TTQPublishFromDraftList) {
        return NO;
    }
    return [self.class openAIChanJian];
}

+ (BOOL)openAIChanJian {
    if ([IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModePregnancy) {
        IMYCConfigsGroup *group = [[IMYConfigsCenter sharedInstance] groupForKeyPath:@"community.publish_tool"];
        BOOL isOpen = [group boolForKey:@"is_open_prenatal_checkup_ai"];
        if (isOpen && imy_isNotEmptyString([self aiChanjianButtonText])) {
            return YES;
        }
    }
    return NO;
}

+ (NSString *)aiChanjianPrompt {
    IMYCConfigsGroup *group = [[IMYConfigsCenter sharedInstance] groupForKeyPath:@"community.publish_tool"];
    NSString *text = [group stringForKey:@"prenatal_checkup_guide_text"];
    return text;
}

+ (NSString *)aiChanjianButtonText {
    IMYCConfigsGroup *group = [[IMYConfigsCenter sharedInstance] groupForKeyPath:@"community.publish_tool"];
    NSString *text = [group stringForKey:@"prenatal_checkup_button_text"];
    return text;

}

+ (BOOL)enablePolish {
    IMYCConfigsGroup *group = [[IMYConfigsCenter sharedInstance] groupForKeyPath:@"community.publish_tool"];
    return [group boolForKey:@"is_open_polish_up"];
}

/// 是否允许选择圈子
+ (BOOL)enableSelectForum {
    IMYCConfigsGroup *group = [[IMYConfigsCenter sharedInstance] groupForKeyPath:@"community.publish_tool"];
    return [group boolForKey:@"is_open_choose_circle"];
}
/// 是否允许匿名
+ (BOOL)enableSelectVisible {
    IMYCConfigsGroup *group = [[IMYConfigsCenter sharedInstance] groupForKeyPath:@"community.publish_tool"];
    return [group boolForKey:@"set_visible_range"];
}
+ (NSString *)emptyForumContent {
    IMYCConfigsGroup *group = [[IMYConfigsCenter sharedInstance] groupForKeyPath:@"community.publish_tool"];
    NSString *text = [group stringForKey:@"choose_circle_guide_text"];
    return imy_isNotEmptyString(text)?text:@"选圈子可让更多人看到";
}

+ (NSInteger)newlineLimitCount {
    IMYCConfigsGroup *group = [[IMYConfigsCenter sharedInstance] groupForKeyPath:@"community.publish_tool"];
    NSInteger count = [group integerForKey:@"Intelligent_segmentation_word_limit"];
    return count?count:120;
}

#pragma mark - ai标题

- (void)requestAiTitle:(void(^)(NSArray *titles, NSError *error))completionHandler forceRequest:(BOOL)forceRequest {
    IMYCConfigsGroup *group = [[IMYConfigsCenter sharedInstance] groupForKeyPath:@"community.publish_tool"];
    if (![group boolForKey:@"is_open_ai_title"]) {
        return;
    }
    if (self.aiTitleWordCount < 1) {
        return;
    }
    if (!self.hasRequestAITitle || forceRequest) {
        self.hasRequestAITitle = YES;
        NSString *signKey = @"f90b42a9919f8278881185";
    #ifdef DEBUG
        if ([IMYURLEnvironmentManager currentType] == IMYURLEnviromentTypeTest) {
            signKey = @"2d5ccd3c4b2c57a3b3500a";
        }
    #endif
        NSString *timestamp = [NSString stringWithFormat:@"%.0f",[[NSDate date] timeIntervalSince1970]];
        NSString *signString = [NSString stringWithFormat:@"time=%@&user_id=%@&%@",timestamp,[IMYPublicAppHelper shareAppHelper].userid,signKey];
        signString = [signString imy_md5];
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        NSString *content = [self current_content];
        if (imy_isNotEmptyString(self.titleStr)) {
            content = [NSString stringWithFormat:@"%@\n%@",self.titleStr,content];
        }
        params[@"text"] = content;
        params[@"time"] = timestamp;
        params[@"sign"] = signString;
        params[@"biz_code"] = @"community";
        params[@"referer"] = @"generate_title";
        [[[IMYServerRequest postPath:@"v3/text/generate" host:streamflow_seeyouyima_com params:params headers:nil] deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
            NSArray *titles = [x responseObject][@"content"];
            if (completionHandler) {
                completionHandler(titles, nil);
            }
        } error:^(NSError * _Nullable error) {
            if (completionHandler) {
                completionHandler(nil, error);
            }
        }];
    }
}


@end

@implementation TTQPublishTopicConfiguration

+ (TTQPublishTopicConfiguration *)defaultPublishConfiguration {
    TTQPublishTopicConfiguration *configuration = [TTQPublishTopicConfiguration new];
    configuration.limit_image = 9;
    configuration.vote_max_items = 6;
    configuration.is_anonymous = YES;
    configuration.anonymous_level = 0;
    return configuration;
}

+ (TTQPublishTopicConfiguration *)newestPublishConfiguration {
    TTQPublishTopicConfiguration *configuration = [self defaultPublishConfiguration];
    IMYSwitchModel *switchModel = [[IMYDoorManager sharedManager] switchForType:@"ttq_publish_topic_config"];
    if (switchModel) {
        [configuration imy_setPropertyWithDictionary:switchModel.dataDictionary];
    }
    return configuration;
}

+ (TTQTopicSubjectModel *)defaultPublishSubjectFromDoor {
    return nil;
    IMYSwitchModel *switchModel = [[IMYDoorManager sharedManager] switchForType:@"subject_recommend"];
    if (switchModel.status) {
        TTQTopicSubjectModel *subject = [TTQTopicSubjectModel new];
        NSDictionary *subjectDic = [switchModel.dataDictionary objectForKey:@"subject"];
        subject.subjectID = [[subjectDic objectForKey:@"id"] integerValue];
        subject.name = [subjectDic objectForKey:@"name"];
        return subject;
    }
    return nil;
}

- (instancetype)init {
    if (self = [super init]) {
        self.bodyContentMaxNum = 3000;
        self.bodyContentMinNum = 6;
        self.titleMaxNum = 30;
        self.titleMinNum = 2;
        self.vote_min_items = 3;
    }
    return self;
}

+ (void)requestExperienceTemplateListWithCompletionHandler:(void (^)(void))completionHandler {
    @weakify(self);
    [[[IMYServerRequest getPath:@"v5/publish_config" host:circle_seeyouyima_com params:nil headers:nil] deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        NSDictionary *dic = [x responseObject];
        [[IMYCacheHelper sharedCacheManager] setObject:dic forKey:@"kTTQPublishTemplateList_848"];
    } error:^(NSError * _Nullable error) {

    }];
}

@end
