//
//  TTQPublishTopicViewModel.h
//  IMYTTQ
//
//  Created by king on 16/5/4.
//  Copyright © 2016年 MeiYou. All rights reserved.
//

#import "TTQForumTagModel.h"
#import "TTQPublishModel.h"
#import "TTQTopicSubjectModel.h"
#import "TTQTopicsViewModel.h"
#import "TTQViewModel.h"
#import "TTQPublishTemplateModel.h"
#import <IMYUGC/IMYUGCNotePublishImage.h>

typedef NS_ENUM(NSUInteger, TTQPublishFromType) {
    ///来自新建
    TTQPublishFromNew,
    ///来自恢复
    TTQPublishFromRestore,
    ///来自草稿箱
    TTQPublishFromDraftList,
    ///来自编辑帖子
    TTQPublishFromEdit
};

@interface TTQPublishTopicConfiguration : NSObject
@property (nonatomic, assign) NSInteger vote_max_items; //可发布的最多投票选项
@property (nonatomic, assign) NSInteger vote_min_items; //可发布的最少投票选项
@property (nonatomic, assign) NSInteger limit_image; //可发布的最多张数的照片
@property (nonatomic, assign) BOOL is_anonymous; //是否可匿名发帖
@property (nonatomic, assign) NSUInteger anonymous_level; //匿名等级
@property (nonatomic, assign) NSInteger bodyContentMaxNum; //正文最多汉字
@property (nonatomic, assign) NSInteger bodyContentMinNum; //正文最少汉字
@property (nonatomic, assign) NSInteger titleMaxNum; //标题最多汉字
@property (nonatomic, assign) NSInteger titleMinNum; //标题最多汉字

@property (nonatomic, assign) BOOL isExperiencePublish;// 是否从发经验入口过来

@property (nonatomic, copy) TTQTopicSubjectModel *subjectModel; //配置的默认热议标签
+ (TTQPublishTopicConfiguration *)defaultPublishConfiguration;
+ (TTQPublishTopicConfiguration *)newestPublishConfiguration;
+ (TTQTopicSubjectModel *)defaultPublishSubjectFromDoor;

+ (void)requestExperienceTemplateListWithCompletionHandler:(void (^)(void))completionHandler;
@end

@interface TTQPublishTopicViewModel : TTQViewModel
@property (nonatomic, assign) NSInteger forum_id;
@property (nonatomic, copy) NSString *forum_name;
@property (nonatomic, assign) NSInteger topic_id;
@property (nonatomic, assign) NSInteger theme_id; // 小视频挑战赛主题id
@property (nonatomic, copy) NSString *theme_title; // 小视频挑战赛主题
@property (nonatomic, assign) NSInteger from_type; // h5小视频主题页发布图文，不跳转归圈页
@property (nonatomic, assign) BOOL isEdit;  /// 是否为编辑帖，用from_type的话，编辑帖存草稿后，from_type会发生变化
@property (nonatomic, strong) UIImage *defaultImage; //默认图片
@property (nonatomic, strong) NSArray<NSString *> *defaultImages;   /// 默认图片地址，与defaultImage互斥，
@property (nonatomic, copy) NSString *inletSource; //入口类型 - -她她圈首页，-圈子详情页，-首页浮层 若是没传就不区分来源
///报告是否提交了
@property (nonatomic, assign) NSInteger draftId;
@property (nonatomic, strong) TTQPublishModel *draft;
@property (nonatomic, strong) TTQPublishModel *originalDraft; // 用来比对有没有编辑过内容，保存草稿弹窗
@property (nonatomic, strong) NSMutableDictionary *imageNameForQiNiuMutablDic; //上传至七牛对应的图片名称

@property (nonatomic, strong) TTQPublishTopicConfiguration *publishConfiguration; //发布的配置
@property (nonatomic, copy) TTQTopicSubjectModel *subjectModel; //选择的热议标签--草稿箱1，默认2
@property (nonatomic, assign) BOOL unavailableSubject; //热议话题不可变化
@property (nonatomic, strong, readonly) NSArray *publishToolItemIDs; //底部按钮的ID
@property (nonatomic, assign) TTQPublishFromType publishFromType;// 区分编辑帖子or发布帖子
@property(nonatomic, strong) NSString *titleStr;
@property(nonatomic, strong) NSArray *voteAry;//投票数据
@property(nonatomic, strong) NSArray *drafVoteAry;//投票草稿数据
// 861新发布器新增字段：
@property (nonatomic, strong) IMYUGCNotePublishImage *videoCover; // 视频封面
@property (nonatomic, strong) IMYAssetModel *video; // 视频资源

@property (nonatomic, assign) BOOL isGoodPregnancy;//7.8.2孕期待产包需求  标记是否为 接好孕 活动

@property (nonatomic, copy) NSArray  *recommendSubjects;
@property (nonatomic, assign) NSInteger  subjectsCategory;
// 经验模板
@property (nonatomic, copy) NSArray<TTQPublishTemplateModel *>  *templateList;// 模板列表
@property (nonatomic, copy) NSArray<TTQPublishTemplateModel *>  *subTemplateList;// 模板列表
@property (nonatomic, assign) NSInteger currentTemplateID;
@property (nonatomic, assign) NSInteger templateTypeID;
@property (nonatomic, assign) NSInteger templateType;// 有通用模板和针对不同身份下发的模板
// @广告赏金任务
@property (nonatomic, assign) NSInteger  taskID;
@property (nonatomic, assign) NSInteger  taskType;

@property (nonatomic, assign) BOOL autoEnterEidt;   /// 进入时，是否自动进入编辑
@property (nonatomic, copy) NSString *referer;  /// 引用，活动页调过来的。
@property (nonatomic, assign) NSInteger referenced_id;  /// 发文时要关联的帖子id,（蹲蹲）
// 草稿保存
@property(nonatomic, strong) NSTimer *saveTimer; //隔10s自动保存草稿
@property (nonatomic, copy) NSString *defaultContent;   /// 默认文案
@property (nonatomic, copy) NSString *jump_url_after_publish; /// 发布完后需立即跳转的地址
@property (nonatomic, assign) NSInteger bi_index; /// bi埋点
#pragma mark - ai 发文
@property (nonatomic, assign) BOOL isAiOutput;      /// 是否Ai生文
@property (nonatomic, copy) void (^updateFlowContentBlock)(TTQPublishModel *model);
@property (nonatomic, copy) void (^aiOutputErrorBlock)(NSError *error);
@property (nonatomic, assign) BOOL isUpdatingFlow;  /// 是否正在更新流式内容
@property (nonatomic, assign) BOOL aiInit;  /// 初始是不是AI生文场景
@property (nonatomic, copy) NSString *promise_content;
@property (nonatomic, copy) NSString *stream_type;
@property (nonatomic, copy) NSString *aiReferer;    /// ai场景的referer
@property (nonatomic, copy) NSString *bubble_text;
@property (nonatomic, copy) NSString *bubble_position;
@property (nonatomic, assign) BOOL hasRequestAITitle;   /// 是否请求过AI标题
@property (nonatomic, assign) NSUInteger aiTitleWordCount;  /// 请求ai标题的字数要求
@property (nonatomic, assign) NSUInteger polish_up_word_limit;  /// 润色字数限制
@property (nonatomic, assign) NSInteger open_ai_prenatal;   /// 
/// datasource的子数组，返回只有文本的数据
- (NSArray *)onlyTextContentData;
/// datasource的子数组，返回只有图片的数据
- (NSArray *)onlyImageContentData;

- (NSString *)current_content;
- (NSUInteger)current_content_word_num;

+ (instancetype)viewModelWithForum:(TTQForumModel *)forum;
- (instancetype)initWithforum_id:(NSInteger)forum_id;
///显示匿名栏
- (BOOL)showAnonymousView;

//显示左下角固定的添加话题入口
- (BOOL)showSubjectView;
// 首次进入显示模板提示气泡
- (BOOL)shouldShowPopView;

- (BOOL)draftChanged;
- (BOOL)editDraftChanged;

///BI 埋点 上报帖子发布是否成功
- (void)postBIEventTopicPostStatus:(NSInteger)success topicID:(NSInteger)topicID imageCounts:(NSInteger)imageCount errorCode:(NSInteger)code errorMessage:(NSString*)errorMsg requestCostTime:(NSTimeInterval)time;

///BI 埋点  帖子发布成功后, 顺带上报是否有图片编辑
- (void)postTopicSuccess:(NSDictionary*)dic;
- (void)reportHasGifAfterPost:(NSDictionary *)dic;

// 校验正文
- (BOOL)checkPostContent;
// 生成正文，上报
- (void)getAllContentStringAndImages:(void(^)(NSArray *images, NSString *content))completionHandler;
- (void)saveImageToAlbum:(UIImage *)image imageCachePath:(NSString*)imagePath cropImageURL:(NSString *)cropImage;

- (void)fetchRecommendSubjectsWithCompletionHandler:(void(^)(void))completionHandler;
/// changeToPicker 是否切换picker, 是否有需要填充的模板，这里是互斥的
- (void)requestExperienceTemplateListWithCompletionHandler:(void (^)(BOOL changeToPicker, TTQPublishTemplateModel *fillModel))completionHandler;

// 自动保存草稿
- (TTQPublishModel *)updateDraft;
- (void)deletedDraf;
- (void)invalidateSaveTimer;
- (void)startSaveTimerForMedian;
- (void)startSaveTimer;
- (void)saveDraftAction:(BOOL)await;
- (BOOL)isDraftEmpty:(TTQPublishModel*)draft;

/// 给发布队列用的，以前的旧逻辑需要等图片上传完成，才能拼接完整的字符串，现在图片上传后置，就不行了，先用固定标识替代下 &&picurl&&
- (void)getAllContentStringAndImagesForPublishManager:(void(^)(NSArray *images, NSString *content))completionHandler;
/// 865样式下新的工具栏
- (void)resetPublishToolItem_865;
/// 更新流式内容
- (void)startFlowContentTimer;
/// 停止更新
- (void)stopFlowContentTimer;

- (void)startSSERequest;
- (void)cancleSSERequest;
+ (BOOL)openPkStyle;
- (BOOL)enableAiChanJian;
/// 是否启用产检
+ (BOOL)openAIChanJian;
/// 产检功能入口文案
+ (NSString *)aiChanjianPrompt;
+ (NSString *)aiChanjianButtonText;
- (BOOL)aiChanjian;
- (void)requestAiTitle:(void(^)(NSArray *titles, NSError *error))completionHandler forceRequest:(BOOL)forceRequest;
+ (BOOL)enablePolish;
/// 是否允许选择圈子
+ (BOOL)enableSelectForum;
/// 是否允许匿名
+ (BOOL)enableSelectVisible;
+ (NSString *)emptyForumContent;
/// 分段字数限制
+ (NSInteger)newlineLimitCount;
@end
