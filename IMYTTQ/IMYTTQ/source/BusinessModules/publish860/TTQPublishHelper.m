//
//  TTQPublishHelper.m
//  IMYTTQ
//
//  Created by ltj on 2023/8/2.
//

#import "TTQPublishHelper.h"
#import <IMYUGC/IMYUGCNotePublishImage.h>
#import <UIImage+GIF.h>
#import "TTQPublishModel.h"
#import <IMYUGC/IMYUGCPhotoHelper.h>

@implementation TTQPublishHelper

+ (instancetype)sharedInstance {
    static id instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [self new];
        [instance createDirectory]; // 创建图片存储沙盒路径
    });
    return instance;
}

/// 从相册中选中后的图片压缩处理后保存到本地沙盒
- (void)startSaveImageOperationWithImageModel:(IMYUGCNotePublishImage *)imageModel {
    @weakify(self);
    IMYAssetModel *assetModel = [[IMYAssetModel alloc] initWithIdentifier:imageModel.assetUrl];
    [NSObject imy_asyncBlock:^{
        if (imageModel.isGif) {
            [assetModel requestImageData:^(NSData * _Nonnull imageData, NSDictionary<NSString *,id> * _Nonnull info, BOOL isGIF, BOOL isHEIC) {
                imy_asyncBlock(^{
                    @strongify(self);
                    if (imageData) {
                        UIImage *image = [UIImage sd_animatedGIFWithData:imageData];
                        UIImage *compressImage = [image imy_lubanCompressedResult].image;
                        imageModel.imageObj = compressImage;
                        imageModel.filePath = [[TTQPublishHelper sharedInstance] saveImage:compressImage withImageModel:imageModel];
                        imageModel.isGif = YES;
                        imageModel.sourceType = IMYGPublishImageSourceTypeApp;
                    }
                });
                
            }];
        } else {
            [assetModel requestFullScreenImageWithCompletion:^(UIImage * _Nonnull result, NSDictionary<NSString *,id> * _Nonnull info) {
                imy_asyncBlock(^{
                    @strongify(self);
                    if (result) {
                        UIImage *compressImage  = [result imy_lubanCompressedResult].image;
                        imageModel.imageObj = compressImage;
                        imageModel.filePath = [[TTQPublishHelper sharedInstance] saveImage:compressImage withImageModel:imageModel];
                        imageModel.isGif = NO;
                        imageModel.sourceType = IMYGPublishImageSourceTypeApp;
                    }
                });
            } andProgressHandler:nil];
        }
    } onLevel:IMYQueueLevelBackground afterSecond:0 forKey:assetModel.identifier];
}

-(void)fastSaveImageOperationWithImageModel:(IMYUGCNotePublishImage *)imageModel completion:(void (^ __nullable)(BOOL finished))completion{
    @weakify(self);
    IMYAssetModel *assetModel = [[IMYAssetModel alloc] initWithIdentifier:imageModel.assetUrl];
    if(assetModel.identifier == nil){
        // 图片丢失
        imageModel.assetNonExist = YES;
        if(imageModel.sourceType == IMYGPublishImageSourceTypeAlbum){
            // 相册删除的走到这里不需要再去下载了
            return;
        }
    }
    if(imageModel.sourceType == IMYGPublishImageSourceTypeApp && imy_isNotEmptyString(imageModel.filePath)){
        // 已经在本地的也不需要下载了
        return;
    }
    if (imageModel.isGif) {
        [assetModel requestImageData:^(NSData * _Nonnull imageData, NSDictionary<NSString *,id> * _Nonnull info, BOOL isGIF, BOOL isHEIC) {
            imy_asyncBlock(^{
                @strongify(self);
                if (imageData) {
                    UIImage *image = [UIImage sd_animatedGIFWithData:imageData];
                    UIImage *compressImage = [image imy_lubanCompressedResult].image;
                    imageModel.imageObj = compressImage;
                    imageModel.thubImg = [compressImage imy_scaledImageAtCenter2WithSize:CGSizeMake(180*SCREEN_SCALE, 180*SCREEN_SCALE)];
                    imageModel.filePath = [[TTQPublishHelper sharedInstance] saveImage:compressImage withImageModel:imageModel];
                    imageModel.sourceType = IMYGPublishImageSourceTypeApp;
                    imageModel.isGif = YES;
                    if(completion){
                        completion(YES);
                    }
                }
            });
            
        }];
    } else {
        [assetModel requestFullScreenImageWithCompletion:^(UIImage * _Nonnull result, NSDictionary<NSString *,id> * _Nonnull info) {
            imy_asyncBlock(^{
                @strongify(self);
                if (result) {
                    UIImage *compressImage  = [result imy_lubanCompressedResult].image;
                    imageModel.imageObj = compressImage;
                    imageModel.thubImg = [compressImage imy_scaledImageAtCenter2WithSize:CGSizeMake(180*SCREEN_SCALE, 180*SCREEN_SCALE)];
                    imageModel.filePath = [[TTQPublishHelper sharedInstance] saveImage:compressImage withImageModel:imageModel];
                    imageModel.isGif = NO;
                    imageModel.sourceType = IMYGPublishImageSourceTypeApp;
                    
                    //增加一个方法手机卖点数据用于分析, 此处约耗时 0.1~0.2s
                    imageModel.customParams = [self.class getCustomParamsWithOriginImage:result compressImage:compressImage];
                    
                    if(completion){
                        completion(YES);
                    }
                }
            });
        } andProgressHandler:nil];
    }
}
+ (NSDictionary *)getCustomParamsWithOriginImage:(UIImage *)originImage compressImage:(UIImage *)compressImage {
    if (![originImage isKindOfClass:UIImage.class] || ![compressImage isKindOfClass:UIImage.class]) {
        return nil;
    }

    CGSize originSize = originImage.size;
    CGSize compressSize = compressImage.size;

    if (originSize.width <= 0 || originSize.height <= 0 || compressSize.width <= 0 || compressSize.height <= 0) {
        return nil;
    }

    NSMutableDictionary *params = @{}.mutableCopy;

    CGFloat dimension_ratio = (compressSize.width * compressSize.height * 1.0) / (originSize.width * originSize.height);
    CGFloat width_ratio = (compressSize.width * 1.0) / originSize.width;
    params[@"dimension_ratio"] = [NSString stringWithFormat:@"%0.2lf", dimension_ratio];
    params[@"width_ratio"] = [NSString stringWithFormat:@"%0.2lf", width_ratio];
    params[@"upload_width"] = @(compressSize.width);
    params[@"upload_height"] = @(compressSize.height);

    NSData *originData = [originImage imy_toData];
    NSData *compressData = [compressImage imy_toData];
    if (originData.length > 0 && compressData.length > 0) {
        CGFloat filesize_ratio = compressData.length * 1.0 / originData.length;
        params[@"filesize_ratio"] = [NSString stringWithFormat:@"%0.2lf", filesize_ratio];
        params[@"upload_filesize"] = @((NSInteger)(compressData.length / 1024));
    }
    
    return [params copy];
}

/// 从相册中选中后的图片压缩处理后保存到本地沙盒

- (NSString *)saveImage:(UIImage *)image withImageModel:(IMYUGCNotePublishImage *)imageModel {
    NSString *fileName = [[TTQPublishHelper sharedInstance] fileNameWithImageModel:imageModel];
    NSData *imageData = [image imy_toData];
    NSString *finalPath = [[TTQPublishHelper sharedInstance].documentPath stringByAppendingPathComponent:fileName];
    
    if ([[NSFileManager defaultManager] fileExistsAtPath:finalPath]) {
        return fileName;
    }
    BOOL result = [imageData writeToFile:finalPath atomically:YES];
    if (result) {
        if (!imy_isBlankString(fileName)) {
            [TTQPublishHelper sharedInstance].tmpDocumentImageDictionary[fileName] = imageModel.assetUrl?:[NSString stringWithFormat:@"%p",imageModel];
        }
        return fileName;
    }
    return nil;
}

- (NSString *)fileNameWithImageModel:(IMYUGCNotePublishImage *)imageModel {
    IMYAssetModel *assetModel = [[IMYAssetModel alloc] initWithIdentifier:imageModel.assetUrl];
    NSString *fileName = [assetModel.identifier stringByReplacingOccurrencesOfString:@"/" withString:@"__"];
    if(imy_isEmptyString(fileName)){
        // 视频封面从剪辑图片和帧选取带回来的是没有 asset的，直接image
        fileName = @"screen_shot";
    }
    fileName = [fileName stringByAppendingFormat:@"%@", @([[NSDate date] timeIntervalSince1970] * 1000)];
    NSString *extension =imageModel.isGif ? @"gif" : @"jpg";
    return fileName = [fileName stringByAppendingPathExtension:extension];
}

- (void)createDirectory {
    self.tmpDocumentImageDictionary = [NSMutableDictionary dictionary];
    NSString *docDirectory = [NSString imy_documentsDirectory];
    NSString *publishDirectory = [docDirectory stringByAppendingPathComponent:@"com.imyg.publish"];
    BOOL isDir = NO;
    BOOL existed = [[NSFileManager defaultManager] fileExistsAtPath:publishDirectory isDirectory:&isDir];
    if ( !(isDir == YES && existed == YES) ) {//如果文件夹不存在
        [[NSFileManager defaultManager] createDirectoryAtPath:publishDirectory withIntermediateDirectories:YES attributes:nil error:nil];
    }
    self.documentPath = publishDirectory;
}

- (void)deleteLocalImage:(IMYUGCNotePublishImage*)image{
    if (image.sourceType != IMYGPublishImageSourceTypeApp) {
        return;
    }
    [self deleteCacheImageWithPath:image.filePath];
}

- (void)deleteCacheImageWithPath:(NSString *)filePath {
    if (imy_isEmptyString(filePath)) {
        return;
    }
    NSString *finalPath = [self.documentPath stringByAppendingPathComponent:filePath];

    BOOL isExistence = [[NSFileManager defaultManager] fileExistsAtPath:finalPath];
    if (isExistence) {
        NSError *error;
        BOOL isRemove = [[NSFileManager defaultManager] removeItemAtPath:finalPath error:&error];
        if (isRemove) {
            NSLog(@"%@",@"删除成功");
        }else{
            NSLog(@"删除失败--error:%@",error);
        }
    }else{
        NSLog(@"文件不存在，请确认路径");
    }

}

-(NSUInteger)draftId{
    NSUInteger draftId;
    NSArray *drafts = [TTQPublishModel searchWithWhere:nil orderBy:@"draftId desc" offset:0 count:0];
    if (drafts.count) {
        TTQPublishModel *lastModel = drafts[0];
        draftId = lastModel.draftId + 1;
    } else {
        draftId = 1;
    }
    return draftId;
}

@end
