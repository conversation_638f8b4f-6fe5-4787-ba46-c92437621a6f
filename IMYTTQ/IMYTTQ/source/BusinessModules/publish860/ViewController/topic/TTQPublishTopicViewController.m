//
//  TTQPublishTopicViewController.m
//  IMYTTQ
//
//  Created by <PERSON><PERSON> on 2017/9/21.
//  Copyright © 2017年 MeiYou. All rights reserved.
//

#import "IMYUGCPublishManager.h"
#import "MBProgressHUD+TTQ.h"
#import "NSString+TTQ.h"
#import "SVRPathManager.h"
#import "TTQCommonHelp.h"
#import "TTQJumpType.h"
#import "TTQPostVoteShowView.h"
#import "TTQPostVoteView.h"
#import "TTQPublishContentModel.h"
#import "TTQPublishContentUtil.h"
#import "TTQPublishExperienceTemplatePickerView.h"
#import "TTQPublishTakeImageUtil.h"
#import "TTQPublishTextCell.h"
#import "TTQPublishToolView.h"
#import "TTQPublishTopicViewController.h"
#import "TTQPublishVoteView.h"
#import "TTQPublishWithRichImageView.h"
#import "TTQTopicTableView.h"
#import "UIFont+TTQ.h"
#import "UIScrollView+TTQ.h"
#import "UIViewController+TTQTopBar.h"
#import <IMYAccount/IMYAccountServerURL.h>
#import <IMYBaseKit/UIImage+IMYPublic.h>
#import <IMYURIManager.h>
#import <SDWebImage/UIImage+GIF.h>
#import <SZTextView.h>

#import "TTQPublishContentView.h"
#import <IMYSVR/AVAsset+SVRVideoInfo.h>
#import <IMYSVR/SVRCoverPickerViewController.h>
#import "TTQPublishHelper.h"
#import "TTQABTestConfig.h"
#import "IMYUGCNotePublishImagePickerView.h"
#import <IMYUGC/IMYCKInputPlaceholder.h>
#import <IMYCommonKit/IMYDragableHalfScreenContainerView.h>
#import "TTQPublishTopBannerView.h"
#import <IMYBaseKit/IMYVKEmotionKeyboardView.h>
#import "TTQPublishAssociateTopicsView.h"
#import <IMYUGC/IMYHugTipsView.h>
#import "TTQPublishInspirationView.h"
#import "TTQAiGenerateView.h"
#import <IMYUGC/IMYUGC.h>
#import "TTQPostTitleView.h"
#import "TTQPublishSettingViewController.h"

#define kPublishTopicImagePickerViewHeight 130

static CGFloat bottomToolViewNormalH = 44;
static CGFloat bottomToolViewUpH = 44;

@interface TTQPublishTopicViewController () <UITableViewDelegate, UITableViewDataSource, TTQPublishTextEditDelegate, UITextFieldDelegate, UIGestureRecognizerDelegate, IMYVKEmotionKeyboardDelegate, IMYAssetPickerControllerDelegate>
@property (nonatomic, strong) UIView *subjectContainerView;
@property (nonatomic, strong) TTQPublishSubjectToolView *subjectView; //话题选择固定在左下角的view
@property (nonatomic, strong) TTQPublishToolView *bottomToolView;     //底部工具栏
@property (nonatomic, strong) TTQPublishVoteView *voteView;           //投票帖
@property (nonatomic, strong) UILabel *contentCountLabel;             //正文字数
@property (nonatomic, strong) UIView *emotionView;                    //表情---统一使用
@property (nonatomic, strong) TTQTopicTableView *tableView;           //图文展示编辑的表格
@property (nonatomic, strong) UIView *tableFooterView;                //图文底部投票+匿名的部分
@property(nonatomic, strong) TTQPostTitleView *postTitleView;//标题

@property (nonatomic, strong) TTQPublishVideoView *videoView; //图文底部视频，和投票互斥
@property (nonatomic, assign) BOOL loadThumStart; // 视频帧截取封面加载开始标志
@property (nonatomic, assign) BOOL loadThumComplete; // 视频帧截取封面加载完成标志
@property (nonatomic, strong) NSArray *imagesArray;
@property (nonatomic, strong) AVAssetImageGenerator *pickGenerator;

@property (nonatomic, assign) CGFloat keyBoardHeigh;                  //键盘高度
@property (nonatomic, strong) NSIndexPath *activeIndexPath;           //用以记录当前活跃的cell的indexpath
@property (nonatomic, strong) TTQPublishTextCell *activeTextCell;     //用以记录当前活跃的text cell
@property (nonatomic, assign) NSInteger activeTextInputModel;         //用以记录插入图片前的输入模式，是否是表情
@property (nonatomic, strong) TTQPublishTakeImageUtil *takeImageUtil; //获取图片的抽取

@property (nonatomic, strong) UITapGestureRecognizer *tableTapGesture;

@property (nonatomic, assign) CGFloat safeBottom_Margin; //整个view距离底部多远的距离
@property (nonatomic, assign) BOOL keyBoardShowed;

@property (nonatomic, strong) TTQPostVoteBackgroundView *postVoteBackgroundView; //投票弹窗半透明背景
@property (nonatomic, assign) BOOL isVoteShow;                                   // yes:禁止该页面键盘监听操作
@property (nonatomic, strong) UILabel *mLimitLabel;                              //限制输入字数
@property (nonatomic, assign) BOOL isShowVoteView;                               //是否展示了投票弹窗
@property (nonatomic, assign) BOOL isEmojBtnClick;                               //是否是点击emojicon 唤起键盘

@property (nonatomic, strong) NSMutableArray *tagsArray;
@property (nonatomic, strong) NSMutableArray *placeHolderArray;

@property (nonatomic, assign) BOOL needAddExperience;
@property (nonatomic, assign) BOOL autoFillTemplate;        // 进入发布页是否需要自动填充模板
@property (nonatomic, assign) BOOL isExperienceTemplatePickerShowing;
@property (nonatomic, strong) TTQPublishExperienceTemplatePickerView *experienceTemplatePickerView;
@property (nonatomic, strong) TTQPostVoteShowView *voteShowView;
@property (nonatomic, assign) NSRange selectRange;
@property (nonatomic, strong) NSIndexPath *templateIndexPath; // 模板要插入的位置索引

@property (nonatomic, assign) BOOL didEdit;
@property (nonatomic, assign) BOOL didEditWithTemplate; // 自动弹起的经验模板不上报埋点，后续继续上报
@property (nonatomic, assign) BOOL hasShownExperience; // 是否已经弹起过模板列表了
@property (nonatomic, strong) IMYPhotoBrowser *videoBrowser; // 如果没有更换视频资源，那就可以继续播放
@property (nonatomic, strong) IMYUGCNotePublishImagePickerView *imagePickerView; // 图片选择器
/// 记录第几次刷新话题
@property (nonatomic, assign) NSUInteger refreshTopicsIndex;
@property (nonatomic, assign) BOOL reportTextViewChange;
@property (nonatomic, assign) BOOL reportTitleViewChange;
@property (nonatomic, strong) TTQPublishAssociateTopicsView *associateTopicsView;
@property (nonatomic, strong) NSMutableArray *selectedTopics;
@property (nonatomic, strong) IMYHugTipsView *topicTipsView;
@property (nonatomic, strong) NSMutableArray *deletedImageFileCaches;
@property (nonatomic, strong) TTQPublishInspirationView *inspirationView;
@property (nonatomic, strong) NSDictionary *placeholderData;    /// 创建时随机
@property (nonatomic, strong) TTQAiGenerateView *aiOutputView;
@property (nonatomic, assign) BOOL hasShowHugTips;
@property (nonatomic, strong) IMYUGCNotePublishImage *aiImageData;
@property (nonatomic, assign) BOOL hasShowAutoNewline;  /// 是否展示过自动换行


@end

@implementation TTQPublishTopicViewController

+ (TTQPublishTopicViewController *)publishTopicViewController:
    (TTQPublishTopicViewModel *)viewModel {
    TTQPublishTopicViewController *publishCtr =
        [[TTQPublishTopicViewController alloc] init];
    viewModel.draft.dontNeedEmptyVoteTitle = NO; //默认不需要添加空标题
    publishCtr.viewModel = viewModel;
    return publishCtr;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.selectRange = NSMakeRange(NSNotFound, 0);
    self.deletedImageFileCaches = [NSMutableArray arrayWithCapacity:1];
    self.selectedTopics = [NSMutableArray arrayWithCapacity:1];
    [self initPlaceHolderData];
    // 先加载草稿箱
    // iOS11没有再次错开时间差调用，第一次无法加载出来
    @weakify(self);
    self.safeBottom_Margin = SCREEN_TABBAR_SAFEBOTTOM_MARGIN;
    [UIWindow imy_showLoadingHUD];
    [self loadDraftAction:^(BOOL loadSuccess) {
        @strongify(self);
        imy_asyncMainBlock(^{
            @strongify(self);
            [UIWindow imy_hideHUD];
            if (self.viewModel.onlyImageContentData.count) {
                [self addBottomImagePickerIfNeeded:self.viewModel.onlyImageContentData];
            }
            if (self.viewModel.publishFromType == TTQPublishFromNew) {
                if (self.viewModel.defaultImage) {
                    [self.takeImageUtil addDefaultImage:self.viewModel.defaultImage];
                } else if (self.viewModel.defaultImages) {
                    
                }
                [self postTopicBIAction];
            }
            /// 显示推荐
            [self updateRecommendTopics];
            [self requestAiTitleWithForce:NO];
            if (self.viewModel.isAiOutput) {
                [self startRequestAi];
            }
            [self showPolishTipIfNeeded];
            if (self.viewModel.open_ai_prenatal && [TTQPublishTopicViewModel openAIChanJian]) {
                [self chanjianAction];
            }
        });
    }];

    [self setupView];
    [self addKeyboardShowOrHidenNotification];

    self.tagsArray = [NSMutableArray new];
    self.placeHolderArray = [NSMutableArray new];

    [[IMYUserDefaults standardUserDefaults] removeObjectForKey:@"IMY_TTQ_DRAFT_EDITING_DRAFT_ID"];
    [[[[[NSNotificationCenter defaultCenter] rac_addObserverForName:UIApplicationWillTerminateNotification object:nil] deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(id x) {
        @strongify(self)
        TTQPublishModel *draft = [self.viewModel updateDraft];
        if ([self.viewModel isDraftEmpty:draft]) {
            return;
        }
        if (self.viewModel.draftChanged) {
            draft.saveByTerminate = YES;
            [draft saveToDB];
            [[IMYUserDefaults standardUserDefaults] setObject:@(self.viewModel.draftId) forKey:@"IMY_TTQ_DRAFT_EDITING_DRAFT_ID"];
        }
    }];
    
    [self.viewModel requestExperienceTemplateListWithCompletionHandler:^(BOOL changeToPicker, TTQPublishTemplateModel *fillModel) {
        [self showExperienceTemplateTool:changeToPicker fillModel:fillModel];
    }];
    
    self.view.imyut_eventInfo.eventName = [NSString stringWithFormat:@"ttq_top_publish_edit_%p",self];
    [self.view.imyut_eventInfo setExposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
        @strongify(self);
        [self pageExposureEvent];
    }];
}

-(void)reloadAllViews{
    @weakify(self);
    [UIWindow imy_showLoadingHUD];
    [self hideAssetPicker];
    [self setBottomToolViewState:TTQPublishToolStateNormal];
    [self loadDraftAction:^(BOOL loadSuccess) {
        @strongify(self);
        imy_asyncMainBlock(^{
            @strongify(self);
            [UIWindow imy_hideHUD];
            // 填充title
            self.mLimitLabel.text = [self titleForContentCountLabel];
            [self updateRightBtn];
            // 填充内容
            [self.tableView reloadData];
            // 填充话题
            // footer处的投票或视频处理下
            if (self.viewModel.draft.voteTexts.count > 0) {
                NSMutableArray *ary = [NSMutableArray array];
                if ([self needSetVoteTitleEmpty]) {
                    //有草稿且无flag,则说明是从797之前版本升级上来的 这时候塞入一个空字符串到数组中,也就是把标题置空
                    [ary addObject:@""];
                }
                for (NSString *str in self.viewModel.draft.voteTexts) {
                    if (imy_isNotEmptyString(str)) {
                        [ary addObject:str];
                    } else {
                        [ary addObject:@""];
                    }
                }
                self.viewModel.drafVoteAry = [NSArray arrayWithArray:ary];
                [self layoutVoteView:ary];
            }
            if(self.viewModel.video != nil){
                [self layoutVideoView:self.viewModel.video cover:self.viewModel.videoCover];
            }
            [self setBottomToolViewState:TTQPublishToolStateNormal];
            if (self.viewModel.onlyImageContentData.count) {
                [self addBottomImagePickerIfNeeded:self.viewModel.onlyImageContentData];
            }
            // 更新右上角发布按钮状态
            [self updateRightBtn];
            /// 显示推荐
            [self updateRecommendTopics];
            [self requestAiTitleWithForce:NO];
            [self showPolishTipIfNeeded];
        });
    }];
}

- (void)setupStatusBarStyle {
    if ([IMYPublicAppHelper shareAppHelper].isNight) {
        self.statusBarStyle = UIStatusBarStyleLightContent;
    } else {
        self.statusBarStyle = UIStatusBarStyleDefault;
    }
    [[UIApplication sharedApplication] setStatusBarStyle:self.statusBarStyle];
}

- (void)dealloc {
    [TTQCommonHelp sharedCommonHelp].isShowKeyBoard = NO;
    [self.viewModel invalidateSaveTimer];
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
    [self.viewModel startSaveTimerForMedian];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    //处理程序进入后台时，保持键盘原有响应
    [TTQCommonHelp sharedCommonHelp].isShowKeyBoard = YES;
    //不能滑动返回
    self.navigationController.interactivePopGestureRecognizer.enabled = NO;
    self.tableView.tag = 1;
    [self setupStatusBarStyle];
    if (self.viewModel.autoEnterEidt) {
        self.viewModel.autoEnterEidt = NO;
        [self positionAtIndex:[NSIndexPath indexPathForRow:0 inSection:0]];
    }
    if (!self.viewModel.isAiOutput && !self.hasShowHugTips) {
        if ([[IMYUserDefaults standardUserDefaults] boolForKey:@"ttq_publish_showNewLinePop"]) {
            IMYHugTipsView *tipView = [IMYHugTipsView tipsView];
            [tipView updateText:@"点这里，可以使用智能分段功能哦"];
            [tipView showInView:self.view targetView:self.bottomToolView.aiButton];
            tipView.imy_bottom += 16;
            self.topicTipsView = tipView;
            [[IMYUserDefaults standardUserDefaults] setBool:NO forKey:@"ttq_publish_showNewLinePop"];
            self.hasShowHugTips = YES;
        } else if (![IMYHugTipsView hasShownGuideWithKey:@"ttq_publish_topic"]) {
            IMYHugTipsView *tipView = [IMYHugTipsView tipsView];
            [tipView updateText:@"功能更新：可以添加多个话题了"];
            [tipView showInView:self.view targetView:self.bottomToolView.topicButton];
            tipView.imy_bottom += 16;
            self.topicTipsView = tipView;
            [IMYHugTipsView setShownWithKey:@"ttq_publish_topic"];
            self.hasShowHugTips = YES;
        } else if (![IMYHugTipsView hasShownGuideWithKey:@"ttq_publish_selectForum"] && [TTQPublishTopicViewModel enableSelectForum]) {
            IMYHugTipsView *tipView = [IMYHugTipsView tipsView];
            tipView.arrowDirection = IMYDirectionRight;
            [tipView updateText:@"可以选择发帖圈子啦"];
            [tipView showInView:self.view targetView:self.bottomToolView.moreActionButton];
            self.topicTipsView = tipView;
            [IMYHugTipsView setShownWithKey:@"ttq_publish_selectForum"];
            self.hasShowHugTips = YES;
        }
    }
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    [self endEditing]; //强制所有的编辑停止
    [[[UIApplication sharedApplication].delegate window].subviews
        bk_each:^(UIView *subview) {
            if ([subview isKindOfClass:[IMYActionSheet class]]) {
                [((IMYActionSheet *)subview) dismiss];
            }
        }];
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    [TTQCommonHelp sharedCommonHelp].isShowKeyBoard = NO;
    self.navigationController.interactivePopGestureRecognizer.enabled = YES;
    
    [self.viewModel stopFlowContentTimer];
}

- (void)goBackToPreWithDeleteDraft {
    [self.viewModel deletedDraf];
    self.viewModel.draft = nil;
    [self imy_pop:YES];
}

- (void)goSucessorWithDeleteDraft {
    [self saveImagesToAlbums];
    [self.viewModel deletedDraf];
    self.viewModel.draft = nil;
}

///发帖成功以后保存图片到相册
- (void)saveImagesToAlbums {
    for (NSInteger i = 0; i < self.viewModel.dataSource.count; i++) {
        if ([self.viewModel.dataSource[i] isKindOfClass:TTQPublishImageModel.class]) {
            TTQPublishImageModel *model = self.viewModel.dataSource[i];
            if (imy_isNotEmptyString(model.publishImage.imageKeyPath) &&
                [[NSFileManager defaultManager] fileExistsAtPath:model.publishImage.imageKeyPath]) {
                NSData *imageData = [NSData dataWithContentsOfFile:model.publishImage.imageKeyPath];
                if (imageData) {
                    [self.viewModel saveImageToAlbum:[UIImage imageWithData:imageData] imageCachePath:model.publishImage.imageKeyPath cropImageURL:model.publishImage.editUrl];
                }
            }
        }
    }
}

- (void)goBackToPre {
    [self.viewModel invalidateSaveTimer];
    self.viewModel.draft = nil;
    [self imy_pop:YES];
}

#pragma mark - 强制所有的编辑停止
- (void)endEditing {
    [self.view endEditing:YES];
}

#pragma mark - 保存草稿

- (NSArray *)pubishVoteTexts {
    if (!self.voteView.isHidden) {
        return self.voteView.voteTexts;
    }
    return nil;
}

- (void)scrollViewDidEndDragging:(UIScrollView *)scrollView willDecelerate:(BOOL)decelerate {
    [self scrollViewDidEndScroll:scrollView];
    if (self.topicTipsView) {
        [self.topicTipsView removeFromSuperview];
        self.topicTipsView = nil;
    }
}

// 完全停止滚动再处理键盘隐藏
- (void)scrollViewDidEndScroll:(UIScrollView *)scrollView {
    // 停止滚动以后再改变顶部的segmentView的index
    if (self.keyBoardShowed) {
        [self endEditing];
    }
}

- (void)cleanAllEditingContentWhenChangeTemplate {
    TTQPublishTextModel *textModel = [TTQPublishTextModel new];
    textModel.text = @"";
    self.viewModel.dataSource = @[textModel];
}

- (void)loadDraftAction:(void (^)(BOOL loadSuccess))completed {
    //没有草稿的的话，就去获取下
    NSMutableDictionary *imageKVC = nil;
    BOOL existDraft = YES;
    if (self.viewModel.draft == nil) {
        TTQPublishModel *draft =
            [TTQPublishModel draftWithDraftId:self.viewModel.draftId];
        if (draft && self.viewModel.publishFromType == TTQPublishFromDraftList) {
            self.viewModel.isEdit = draft.isEdit;
            self.viewModel.topic_id = draft.topicId;
            self.viewModel.referer = draft.referer;
            self.viewModel.aiReferer = draft.aiReferer;
            [self initPlaceHolderData];
            [self.tableView reloadData];
        }
        imageKVC = [NSMutableDictionary dictionary];
        NSInteger count = MIN(draft.urls.count, draft.imageKeyPathes.count);
        for (NSInteger index = 0; index < count; index++) {
            NSString *imageKeyPath = @""; // imageModel的唯一key
            if (imy_isNotEmptyString(draft.imageKeyPathes[index])) {
                imageKeyPath = draft.imageKeyPathes[index];
            }
            NSString *assetURL = imageKeyPath;                                                      //相册原图路径
            UIImage *image = [self getDraftImageFromLocalPath:assetURL draftURL:draft.urls[index]]; //获取图片
            BOOL showImageByDoor = [TTQCommonHelp getPublishToolBottomItemShowStatus:TTQPublishBottomToolItem_Image];
            if (assetURL.length && image && showImageByDoor) {
                imageKVC[assetURL] = image;
                if (self.viewModel.isEdit) { // 二次编辑
                    // fix 达人编辑帖子 图片带过来不一致 导致弹出编辑变更弹窗
                    NSString *localUrl = @"";
                    if ([assetURL containsString:@"imy-default/SDWebImageCache"]) {
                        //达人编辑的图片是SDWebimage的缓存图片
                        NSArray *imageURLStringArr = [assetURL componentsSeparatedByString:@"markdot"];
                        if (imageURLStringArr.count > 1) {
                            localUrl = imageURLStringArr[0];
                        }
                    }
                    if (imy_isNotEmptyString(localUrl)) {
                        NSMutableArray *urls = [draft.urls mutableCopy];
                        urls[index] = localUrl;
                        draft.urls = [urls copy];
                    }
                }
            }
        }
        if (draft == nil || !draft.isRichModel) {
            existDraft = NO;
            self.viewModel.draft = [TTQPublishModel new];
            self.viewModel.draft.lastEditDate = [NSDate imy_today];
            self.viewModel.draft.forum_id = self.viewModel.forum_id;
            self.viewModel.draft.orignal_forum_id = self.viewModel.forum_id;
            self.viewModel.draft.forum_name = self.viewModel.forum_name;
            self.viewModel.draft.draftId = self.viewModel.draftId;
            self.viewModel.draft.isRichModel = YES;
            if (draft) {
                NSMutableArray *items = [NSMutableArray array];
                if (draft.content) {
                    [items addObject:draft.content];
                }
                for (NSInteger index = 0; index < count; index++) {
                    NSString *assetURL = draft.urls[index];
                    NSString *imageKeyPath = @""; // imageModel的唯一key
                    if (imy_isNotEmptyString(draft.imageKeyPathes[index])) {
                        imageKeyPath = draft.imageKeyPathes[index];
                    }
                    if (assetURL && imy_isNotEmptyString(imageKeyPath)) {
                        NSMutableDictionary *dic = [NSMutableDictionary dictionary];
                        dic[@"url"] = assetURL;
                        if (index < draft.imageNamesForQiNiu.count) {
                            NSString *imageName = draft.imageNamesForQiNiu[index];
                            if (imy_isNotEmptyString(imageName)) {
                                dic[@"imageName"] = imageName;
                            }
                        }
                        [items addObject:dic];
                    }
                }
                self.viewModel.draft.items = items.copy;
            } else if (self.viewModel.defaultImages) {
                [self addDefaultImagesFromUrlWhenCreate];
            }
            [draft deleteDraft];
        } else {
            self.viewModel.draft = draft;
            if (self.viewModel.publishFromType == TTQPublishFromDraftList) { // 存草稿箱打开
                self.viewModel.from_type = self.viewModel.draft.from_type;
            }
            if(draft.video != nil){
                NSString *identifier = [draft.video objectForKey:@"identifier"];
                IMYAssetModel *video = [[IMYAssetModel alloc] initWithIdentifier:identifier];
                if(video != nil){
                    self.viewModel.video = video;
                }
                // video cover
                self.viewModel.videoCover = draft.videoCover;
                
                // 下载！！！！
                IMYAssetModel *assetModel = [[IMYAssetModel alloc] initWithIdentifier:draft.videoCover.assetUrl];
                if (draft.videoCover.imageObj == nil) {
                    if (draft.videoCover.isGif) {
                        if (draft.videoCover.sourceType == IMYGPublishImageSourceTypeApp) {// 从沙盒读取图片
                            NSString *finalPath = [[TTQPublishHelper sharedInstance].documentPath stringByAppendingPathComponent:draft.videoCover.filePath];
                            NSData *data = [NSData dataWithContentsOfFile:finalPath];
                            UIImage *finalImage = [UIImage sd_animatedGIFWithData:data];
                            self.viewModel.videoCover.imageObj = finalImage;
                        } else {// 从相册读取图片
                            @weakify(self);
                            [assetModel requestImageData:^(NSData * _Nonnull imageData, NSDictionary<NSString *,id> * _Nonnull info, BOOL isGIF, BOOL isHEIC) {
                                @strongify(self);
                                if (imageData) {
                                    UIImage *image = [UIImage sd_animatedGIFWithData:imageData];
                                    self.viewModel.videoCover.imageObj = [image imy_lubanCompressedResult].image;
                                }
                            }];
                        }
                    } else {
                        UIImage *finalImage = nil;
                        if (draft.videoCover.sourceType == IMYGPublishImageSourceTypeApp) {
                            NSString *finalPath = [[TTQPublishHelper sharedInstance].documentPath stringByAppendingPathComponent:draft.videoCover.filePath];
                            finalImage = [[UIImage alloc] initWithContentsOfFile:finalPath];
                        } else {
                            finalImage = [assetModel thumbnailImageWithSize:CGSizeMake(SCREEN_HEIGHT/2.0, SCREEN_HEIGHT/2.0)];
                        }
                        self.viewModel.videoCover.imageObj = finalImage;
                    }
                }
            }
        }
        if (self.viewModel.isEdit && self.viewModel.currentTemplateID > 0) {
            TTQPublishTemplateModel *templateModel = [[TTQPublishTemplateModel alloc] init];
            templateModel.templateID = self.viewModel.currentTemplateID;
            self.viewModel.draft.templateModel = templateModel;
        }
    }
    NSMutableArray *dataSource = [NSMutableArray arrayWithCapacity:10];
    TTQPublishTextModel *textModel = nil;
    NSMutableArray *itemsCopy = [self.viewModel.draft.items mutableCopy];
    NSInteger imageCount = 0;
    /// 合并文本，给865样式使用
    NSMutableString *contentString = [NSMutableString string];
    for (int i = 0; i < itemsCopy.count; i++) {
        TTQPublishContentModel *contentModel;
        id item = self.viewModel.draft.items[i];
        if ([item isKindOfClass:[NSString class]]) {
            contentModel = [TTQPublishTextModel new];
            [(TTQPublishTextModel *)contentModel setText:item];
            if (![item isEqualToString:@"\n"]) {
                /// 旧样式的图片与下个元素的换行符要舍弃
                if (contentString.length) {
                    [contentString appendFormat:@"\n%@",item];
                } else {
                    [contentString appendString:item];
                }
            }
        } else {
            contentModel = self.viewModel.draft.topicImagesArray[i];
            if(contentModel == nil){
               // 编辑过来的兜底
                NSString *assetURL = item[@"url"];
                IMYUGCNotePublishImage *imageM = [[IMYUGCNotePublishImage alloc] initWithAssetUrl:assetURL];
                imageM.sourceType = IMYGPublishImageSourceTypeNet;
                TTQPublishImageModel *imageModel = [TTQPublishImageModel new];
                imageModel.isPublishTopic = YES;
                if (item[@"additionalTitle"]) {
                    imageModel.additionalTitle = item[@"additionalTitle"];
                }
                imageModel.publishNewImage = imageM;
                contentModel = imageModel;
            } else {
                // 这里就判断是否图片已经被删除了，删除了就不加进去展示了
                TTQPublishImageModel *imageContent = (TTQPublishImageModel *)contentModel;
                IMYUGCNotePublishImage *imageModel = imageContent.publishNewImage;
                IMYAssetModel *assetModel = [[IMYAssetModel alloc] initWithIdentifier:imageModel.assetUrl];
                if (imageModel.sourceType == IMYGPublishImageSourceTypeNet) {
                    
                } else if (imageModel.sourceType == IMYGPublishImageSourceTypeApp) {
                    NSString *finalPath = [[TTQPublishHelper sharedInstance].documentPath stringByAppendingPathComponent:imageModel.filePath];
                    UIImage *image = [[UIImage alloc] initWithContentsOfFile:finalPath];
                    imageModel.imageObj = image;
                } else if (assetModel.identifier == nil){
                    // 图片丢失
                    imageModel.assetNonExist = YES;
                    contentModel = nil;
                }
            }
        }
        if (contentModel) {
            if (contentModel.richContentType == TTQPublishContentTypeText) {
                if (textModel == nil) {
                    textModel = (TTQPublishTextModel *)contentModel;
                } else {
                    textModel.text = [textModel.text stringByAppendingString:@"\n"];
                    textModel.text = [textModel.text
                        stringByAppendingString:[(TTQPublishTextModel *)
                                                        contentModel text]];
                }
            } else {
                if (imageCount >= 9) {
                    continue;
                }
                [dataSource addObject:contentModel];
                imageCount ++;
            }
        }
    }

    self.viewModel.draft.items = [itemsCopy copy];
    if (_postTitleView) {
        [self setupContent];
    }
    /// 新样式就合并所有的text
    textModel = [TTQPublishTextModel new];
    
    if (imy_isNotEmptyString(self.viewModel.draft.title) && ![IMYCKFeedsHelper praiseStyleExp] && ![IMYCKFeedsHelper feedsExp889]) {
        contentString = [NSString stringWithFormat:@"%@ %@",self.viewModel.draft.title, contentString];
    } else if (contentString.length == 0 && self.viewModel.defaultContent.length) {
        /// 有预设文字的
        contentString = self.viewModel.defaultContent;
    }
    if (self.viewModel.subjectModel && !self.viewModel.isAiOutput) {
        contentString = [NSString stringWithFormat:@"%@#%@ ",contentString, self.viewModel.subjectModel.name];
    } else if (self.viewModel.draft.subjectModel) {
        if ([contentString rangeOfString:self.viewModel.draft.subjectModel.name].length == 0) {
            /// 不带话题的就拼接话题
            contentString = [NSString stringWithFormat:@"%@ #%@ ",contentString, self.viewModel.draft.subjectModel.name];
        }
    }
    textModel.text = contentString;
    [dataSource insertObject:textModel atIndex:0];
    self.viewModel.dataSource = dataSource.copy;

    BOOL showSubject = [TTQCommonHelp getPublishToolBottomItemShowStatus:TTQPublishBottomToolItem_HotTopic]; // door开关展示热议话题
    //是否匿名了的处理(若是不出现匿名的操作，那应该是直接不匿名了)
    if (!self.viewModel.showAnonymousView) {
        self.viewModel.draft.isAnonymous = NO;
    }
    self.viewModel.draft.lastEditDate = [NSDate imy_today];
    if (self.viewModel.referenced_id) {
        self.viewModel.draft.referenced_id = self.viewModel.referenced_id;
    }
    self.viewModel.originalDraft = [self.viewModel.draft copy];
    completed(existDraft);
}

- (void)addDefaultImagesFromUrlWhenCreate {
    NSMutableArray *array = [NSMutableArray arrayWithCapacity:1];
    for (NSString *url in self.viewModel.defaultImages) {
        if ([url hasPrefix:@"http"]) {
            IMYUGCNotePublishImage *imageM = [[IMYUGCNotePublishImage alloc] initWithAssetUrl:url];
            imageM.sourceType = IMYGPublishImageSourceTypeNet;
            TTQPublishImageModel *imageModel = [TTQPublishImageModel new];
            imageModel.isPublishTopic = YES;
            imageModel.publishNewImage = imageM;
            [array addObject:imageModel];
        } else if (imy_isNotEmptyString(url)) {
            /// 可能是图片id
            PHAsset *asset = [PHAsset fetchAssetsWithLocalIdentifiers:@[url] options:nil].firstObject;
            if (asset) {
                IMYUGCNotePublishImage *imageM = [[IMYUGCNotePublishImage alloc] initWithAssetUrl:url];
                imageM.sourceType = IMYGPublishImageSourceTypeAlbum;
                TTQPublishImageModel *imageModel = [TTQPublishImageModel new];
                imageModel.isPublishTopic = YES;
                imageModel.publishNewImage = imageM;
                [array addObject:imageModel];
            }
        }
    }
    self.viewModel.draft.items = array;
    self.viewModel.draft.topicImagesArray = [array mutableCopy];
}

#pragma mark - 发帖操作

//真正发帖前的工作, 是否可以继续操作
- (BOOL)preparationToPost {
    [IMYEventHelper event:@"fsy-fs"
               attributes:@{@"来源": (self.viewModel.inletSource ?: @"")}];
    [self.view endEditing:YES];
    @weakify(self);
    if (![self checkPublishEnable]) {
        return NO;
    }
    if ([self shouldShowNewlineAlert]) {
        return NO;
    }
    BOOL isEdit = self.viewModel.isEdit;
    if (isEdit) {
        IMYUGCPublishTask *task = [[IMYUGCPublishManager shareInstance] imy_getTaskWithTopicId:self.viewModel.topic_id];
        if (self.publish_entrance) {
            task.publish_entrance = self.publish_entrance;
        }
        if (task && [task isWatingInQueue]) {
            [UIWindow imy_showTextHUD:@"内容正在发布中，请稍后再试"];
            return NO;
        }
    }

    [self.viewModel invalidateSaveTimer];
    [self.viewModel updateDraft];
    self.viewModel.draft.publishStatus = TTQPublishDraftStatusPublishing;
    [self.viewModel.draft saveToDB];
    NSArray *dataSource = self.viewModel.dataSource;
    if (self.viewModel.video) {
        /// 视频
        IMYUGCVideoObject *obj = [IMYUGCVideoObject videoModelWithAsset:self.viewModel.video.fetchPhAsset];
        obj.coverImage = self.viewModel.videoCover.imageObj;
        IMYUGCPublishTask *task = [IMYUGCPublishTask taskWithDraftId:self.viewModel.draftId contentId:self.viewModel.topic_id videoViewModel:self.viewModel video:obj isEdit:isEdit];
        task.topics = [self.selectedTopics map:^id _Nonnull(NSDictionary *element) {
            NSMutableDictionary *obj = [NSMutableDictionary dictionaryWithCapacity:2];
            obj[@"name"] = element[@"name"];
            obj[@"id"] = element[@"id"];
            return obj;
        }];
        task.is_aigc = self.viewModel.draft.useAi?1:0;
        task.publishTipPopId = self.publishTipPopId;
        task.publish_entrance = self.publish_entrance;
        if (self.viewModel.draft.referenced_id) {
            task.referenced_id = self.viewModel.draft.referenced_id;
        }
        [[IMYUGCPublishManager shareInstance] imy_startTask:task];
    } else {
        /// 发布的走队列
        NSMutableArray *ugcImageArray = [NSMutableArray array];
        for (TTQPublishImageModel *imageModel in dataSource) {
            if (imageModel.richContentType == TTQPublishContentTypeImage) {
                IMYUGCImageObject *obj = [[IMYUGCImageObject alloc] init];
                if (imageModel.publishNewImage.sourceType == IMYGPublishImageSourceTypeNet) {
                    obj.url = imageModel.publishNewImage.assetUrl;
                } else {
                    obj.localIdentifier = imageModel.publishNewImage.assetUrl;
                }
                obj.businessKey = @"ttq";
                obj.image = imageModel.publishNewImage.imageObj;
                obj.localPath = [imageModel.publishNewImage fullFileCachePath];
                obj.isGif = imageModel.publishNewImage.isGif;
                obj.customParams = [imageModel.publishNewImage.customParams copy];

                [ugcImageArray addObject:obj];
            }
        }

        IMYUGCPublishTask *task = [IMYUGCPublishTask taskWithDraftId:self.viewModel.draftId contentId:self.viewModel.topic_id ttqViewModel:self.viewModel images:ugcImageArray isEdit:isEdit];
        task.publish_entrance = self.publish_entrance;
        task.publishTipPopId = self.publishTipPopId;
        task.topics = [self.selectedTopics map:^id _Nonnull(NSDictionary *element) {
            NSMutableDictionary *obj = [NSMutableDictionary dictionaryWithCapacity:2];
            obj[@"name"] = element[@"name"];
            obj[@"id"] = element[@"id"];
            return obj;
        }];
        if (isEdit) {
            task.topicId = self.viewModel.topic_id;
        }
        task.bi_index = self.viewModel.bi_index;
        task.is_aigc = self.viewModel.draft.useAi?1:0;
        task.is_aiTitle = self.viewModel.draft.useAiTitle?1:0;
        if (self.viewModel.draft.referenced_id) {
            task.referenced_id = self.viewModel.draft.referenced_id;
        }
        [[IMYUGCPublishManager shareInstance] imy_startTask:task];
    }
    if (imy_isNotEmptyString(self.viewModel.jump_url_after_publish)) {
        BOOL success = [[IMYURIManager shareURIManager] runActionWithString:self.viewModel.jump_url_after_publish];
        if (!success) {
            [self dismissViewControllerAnimated:YES completion:nil];
        } else {
            imy_asyncMainBlock(0.35, ^{
                NSLog(@"v = %@",self.navigationController.viewControllers.firstObject);
                if ([self.navigationController.viewControllers.firstObject isKindOfClass:NSClassFromString(@"TTQPublish860ViewController")]) {
                    NSMutableArray *viewControllers = [NSMutableArray arrayWithArray:self.navigationController.viewControllers];
                    [viewControllers removeObjectAtIndex:0];
                    self.navigationController.viewControllers = viewControllers;
                }
            });
        }
    } else {
        [self dismissViewControllerAnimated:YES completion:nil];
    }
    [self aiPublishBIEvent];
    return YES;
}

- (BOOL)checkPublishEnable {
    @weakify(self);
    if ([IMYPublicAppHelper shareAppHelper].cannotAccess) {
        NSString *title = IMYString(@"违反平台规范被封号");
        [UIAlertController imy_showAlertViewWithTitle:title message:IMYString(@"可以到”帮助与反馈“里申诉反馈") cancelButtonTitle:IMYString(@"取消") otherButtonTitles:@[IMYString(@"去反馈")] handler:^(UIAlertController *alertController, NSInteger buttonIndex) {
            if (buttonIndex == 1) {
                [[IMYURIManager shareURIManager] runActionWithString:@"meiyou:///qiyu/chat?params=eyJncm91cElkIjogNDgwODE1ODc4fQ=="];
            }
        }];
        return NO;
    }

    if (self.isShowVoteView) {
        if (self.viewModel.voteAry.count < self.viewModel.publishConfiguration.vote_min_items) {
            [UIWindow imy_showTextHUD:IMYString(@"投票至少两个项哦~")];
            return NO;
        }
    }

    if (![self.viewModel checkPostContent]) {
        [UIWindow imy_showTextHUD:@"内容不少于8个字"];
        return NO;
    }
    if (self.viewModel.video) {
        /// 带了视频的，发布前检查下
        PHAsset *phAsset = [PHAsset fetchAssetsWithLocalIdentifiers:@[self.viewModel.video.identifier] options:nil].firstObject;
        if (!phAsset) {
            [UIWindow imy_showTextHUD:@"视频缺失，请重新选择视频"];
            return NO;
        }
    }

    if (![IMYNetState networkEnable]) {
        [UIWindow imy_showTextHUD:MT_Request_NoNetToast];
        return NO;
    }
    return YES;
}

#pragma mark - 发布成功弹窗

#pragma mark - nav's action

- (void)imy_topLeftButtonTouchupInside {
    TTQPublishTextModel *textModel = self.viewModel.dataSource.firstObject;
    if (![textModel isKindOfClass:[TTQPublishTextModel class]]) {
        textModel = nil;
    }
    [self.viewModel updateDraft];
    switch (self.viewModel.publishFromType) {
        case TTQPublishFromNew: {
            BOOL hasVote = NO;
            if (self.viewModel.voteAry.count > 0) {
                hasVote = YES;
            }
            BOOL hasVideo = NO;
            if(self.viewModel.video != nil){
                hasVideo = YES;
            }
            if (self.viewModel.dataSource.count > 1 || imy_isNotEmptyString(textModel.text) || imy_isNotEmptyString(self.viewModel.titleStr) || hasVote || hasVideo) {
                [self alertSaveDraftMenu];
            } else {
                [self goBackToPreWithDeleteDraft];
            }
        } break;
        case TTQPublishFromRestore: {
            if (self.viewModel.draftChanged) {
                [self alertSaveDraftMenu];
            } else {
                [self goBackToPre];
                [UIWindow imy_showTextHUD:IMYString(@"内容已存在草稿箱")];
            }
        } break;
        case TTQPublishFromDraftList: {
            if (self.viewModel.editDraftChanged) {
                [self alertSaveDraftMenu];
            } else {
                // 投票
                if ([self isEditorVote]) {
                    [self alertSaveDraftMenu];
                } else {
                    [self goBackToPre];
                }
            }
        } break;
        case TTQPublishFromEdit: {
            if (self.viewModel.draft.voteTexts.count == 0) {
                self.viewModel.draft.voteTexts = nil;
            }
            if (self.viewModel.editDraftChanged) {
                [self alertSaveDraftMenu];
            } else {
                [self goBackToPreWithDeleteDraft];
            }
        } break;
    }
}

#pragma mark - 模板逻辑
- (void)showExperienceTemplateTool:(BOOL)changeToPicker fillModel:(TTQPublishTemplateItemModel *)fillModel {
    TTQPublishButton *templateToolView = [self.bottomToolView viewWithTag:TTQPublishToolMenuItemTypeForExperienceTemplate];
    [self.experienceTemplatePickerView setTemplateArray:self.viewModel.templateList];
    if (self.viewModel.templateList.count >= 3) {
        templateToolView.hidden = NO;
    } else {
        templateToolView.hidden = YES;
        [self.bottomToolView hideExperienceTemplate];
    }
    if (self.viewModel.isAiOutput) {
        /// AI的不走这个逻辑
        return;
    }
    if (self.viewModel.publishFromType != TTQPublishFromNew) {
        return;
    }
    
    if (!self.isViewActived) {
        imy_asyncMainBlock(0.05, ^{
            [self showExperienceTemplateTool:changeToPicker fillModel:fillModel];
        });
        return;
    }
    if (fillModel) {
    /// 需要填充数据的
        if (!self.viewModel.draft.templateModel || ![self.viewModel.draft.templateModel isEqual:fillModel]) {
            self.needAddExperience = YES;
            self.autoFillTemplate = YES;
            self.viewModel.draft.templateModel = fillModel;
            [self.tableView reloadData];
            [self.bottomToolView setTemplateBtnStatus:NO];
            self.panAssetShowBlock(YES, YES);
        }
    } else if (changeToPicker) { // 展示模板列表
        if (self.didEdit) {
            /// 用户操作了，啥也不干
            return;
        }
        self.isExperienceTemplatePickerShowing = YES;
        templateToolView.selected = YES;
        self.hasShownExperience = YES;
        UITextView *textView = [self validTextView];
        if (textView == nil) { // 如果当前没有活跃的文本框，就找到文末的文本框
            textView = [self nethermostTextView];
        }
        textView.inputView = self.experienceTemplatePickerView;
        [textView reloadInputViews];
        if (!textView.isFirstResponder) {
            self.didEditWithTemplate = YES;
            [textView becomeFirstResponder];
        }
    } else if (!self.didEdit && self.panAssetShowBlock) {
        self.panAssetShowBlock(YES, YES);
    }
}

#pragma mark - 是否编辑过投票
- (BOOL)isEditorVote {
    BOOL hasVote = NO;
    if (self.viewModel.drafVoteAry.count > 0) {
        //对比投票内容
        if (self.viewModel.drafVoteAry.count == self.viewModel.voteAry.count) {
            for (int i = 0; i < self.viewModel.drafVoteAry.count; i++) {
                NSString *str1 = self.viewModel.drafVoteAry[i]; //草稿数据
                NSString *str2 = self.viewModel.voteAry[i];     //编辑数据
                if (![str1 isEqualToString:str2]) {
                    hasVote = YES;
                    break;
                }
            }
        } else {
            hasVote = YES;
        }
    } else {
        if (self.viewModel.voteAry.count > 0) {
            hasVote = YES;
        }
    }
    return hasVote;
}

- (void)alertSaveDraftMenu {
    @weakify(self);
    NSString *title;
    NSArray *otherTitlesArray = @[IMYString(@"存草稿"), IMYString(@"清空内容")];
    switch (self.viewModel.publishFromType) {
        case TTQPublishFromNew:
        case TTQPublishFromEdit: {
            title = IMYString(@"是否保存到草稿箱？");
        } break;
        case TTQPublishFromRestore: {
            title = IMYString(@"是否保存到草稿箱？");
        } break;
        case TTQPublishFromDraftList: {
            title = IMYString(@"是否保存本次编辑？");
        } break;
    }
    
    IMYActionSheet *sheet = [IMYActionSheet
        sheetWithCancelTitle:IMYString(@"取消")
                 otherTitles:otherTitlesArray
                     summary:title
                  showInView:nil
                      action:^(NSInteger index) {
                          @strongify(self);
                          if (index == 1) {
                              self.viewModel.draft.saveByTerminate = NO;
                              self.viewModel.draft.dontNeedEmptyVoteTitle = YES;
                              self.viewModel.draft.aiReferer = self.viewModel.aiReferer;
                              [IMYEventHelper event:@"bccg"];
                              TTQPublishTextModel *textModel = self.viewModel.dataSource.firstObject;
                              if (![textModel isKindOfClass:[TTQPublishTextModel class]]) {
                                  textModel = nil;
                              }
                              BOOL shouldDelete = imy_isEmptyString(textModel.text) && imy_isEmptyString(self.viewModel.titleStr) && (self.viewModel.voteAry.count == 0) && (self.viewModel.video == nil);
                              for (TTQPublishContentModel *contentModel in self.viewModel.dataSource) {
                                  if ([contentModel isKindOfClass:[TTQPublishTextModel class]]) {
                                      if (imy_isNotEmptyString(((TTQPublishTextModel *)contentModel).text)) {
                                          shouldDelete = NO;
                                          break;
                                      }
                                  } else if ([contentModel isKindOfClass:[TTQPublishImageModel class]]) {
                                      if (((TTQPublishImageModel *)contentModel).publishNewImage) {
                                          shouldDelete = NO;
                                          break;
                                      }
                                  } else if ([contentModel isKindOfClass:[TTQPublishContentModel class]]) {
                                  }
                              }

                              //是否编辑过投票，从草稿箱进入
                              if (self.viewModel.publishFromType == TTQPublishFromDraftList && [self isEditorVote] && self.viewModel.voteAry.count > 0) {
                                  shouldDelete = NO;
                              }

                              if (self.viewModel.publishFromType == TTQPublishFromDraftList && shouldDelete) {
                                  self.viewModel.draft.modeType = TTQPublishDraftModeTypeTopic; // 视频转图文
                                  [self.viewModel.draft saveToDB];
                                  [UIWindow imy_showTextHUD:IMYString(@"已更新")];
                              } else {
                                  [self.viewModel.draft saveToDB];
                                  if (self.viewModel.publishFromType == TTQPublishFromDraftList) {
                                      [UIWindow imy_showTextHUD:IMYString(@"保存成功")];
                                  } else {
                                      [UIWindow imy_showTextHUD:IMYString(@"已保存至草稿箱")];
                                  }
                                  [[NSNotificationCenter defaultCenter] postNotificationName:@"TTQPublishDraftSave" object:nil];
                              }
                              for (NSString *deleteFile in self.deletedImageFileCaches) {
                                  [[TTQPublishHelper sharedInstance] deleteCacheImageWithPath:deleteFile];
                              }
                              [self goBackToPre];
                          } else if (index == 2) {
                              switch (self.viewModel.publishFromType) {
                                  case TTQPublishFromNew:
                                  case TTQPublishFromEdit:{
                                      [self goBackToPreWithDeleteDraft];
                                  } break;
                                  case TTQPublishFromRestore: {
                                      [self goBackToPreWithDeleteDraft];
                                  } break;
                                  case TTQPublishFromDraftList: {
                                      [self.viewModel.originalDraft saveToDB];
                                      [self goBackToPre];
                                  } break;
                              }
                          }
                          NSString *actionName = @"取消";
                          if (index) {
                              actionName = otherTitlesArray[index - 1];
                          }
                          [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_bjy_bctc",@"action":@2,@"public_type":actionName} headers:nil completed:nil];
                      }];
    [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_bjy_bctc",@"action":@1} headers:nil completed:nil];
}

///发送按钮--点击事件
- (void)imy_topRightButtonTouchupInside {
    if (self.viewModel.isUpdatingFlow) {
        /// 正在更新流式内容，不可操作
        return;
    }
    [self endEditing]; //停止所有编辑
    NSInteger textCount = [TTQPublishContentUtil validataTextCount:self.viewModel.dataSource];
    if (textCount < 8) {
        [UIWindow imy_showTextHUD:@"内容不少于8个字"];
        [self postBiWithSuccess:NO];
        return;
    }
    BOOL success = [self preparationToPost];
    [self postBiWithSuccess:success];
}

#pragma mark - 标题view
- (UIView *)postTitleView{
    if (!_postTitleView) {
        @weakify(self)
        _postTitleView = [[TTQPostTitleView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 60)];
        _postTitleView.updateTitleHeightBlock = ^(CGFloat changeHeight) {
            @strongify(self)
            [self reloadTableHeadViewHeight:changeHeight];
        };
        _postTitleView.textTitleChangeBlock = ^(NSString * _Nonnull titleText) {
            @strongify(self)
            self.viewModel.titleStr = titleText;
            [self updateRightBtn];
            if (!self.reportTitleViewChange) {
                self.reportTitleViewChange = YES;
                [self postBIwithPublishInfoType:@"改动标题"];
            }
        };
        _postTitleView.textTitleShouldEditorBlock = ^{
            //处理8p 兼容bug
            @strongify(self);
            [self publishToolDidEdit];
            [self.bottomToolView setPublishToolState:TTQPublishToolStateNormal];
            [self.bottomToolView updateAllBtnStatus:NO];
            [self hideAssetPicker];
            [self hideInspirationViewIfNeeded];
            [self postBIwithPublishInfoType:@"编辑标题区域"];
        };
        [_postTitleView setTitleRetryBlock:^{
            @strongify(self);
            [self requestAiTitleWithForce:YES];
        }];
        [_postTitleView setClickAITitleBlock:^(NSString * _Nonnull title) {
            @strongify(self);
            self.viewModel.draft.useAiTitle = YES;
        }];
    }
    return _postTitleView;
}

- (void)reloadTableHeadViewHeight:(CGFloat)changeHeight{
    @weakify(self);
    imy_asyncMainBlock(^{
        @strongify(self);
        CGRect rect = self.postTitleView.frame;
        rect.size.height = changeHeight;
        self.postTitleView.frame = rect;
        self.postTitleView.superview.imy_height = self.postTitleView.imy_bottom;
        self.tableView.tableHeaderView = self.postTitleView.superview;
    });
}


#pragma mark - 标题／默认文字等

- (void)initPlaceHolderData {
    NSDictionary *data = [TTQCommonHelp resourceDataWithCode:@"publisher_placeholder"];
    NSArray *list = data[@"material_list"];
    if (list.count) {
        NSUInteger index = arc4random()%list.count;
        self.placeholderData = [list imy_objectAtIndex:index];
    }
}

- (NSString *)placeHolderConfigContentText {
    if (self.placeholderData && self.placeholderData[@"subtitle"]) {
        return self.placeholderData[@"subtitle"];
    }
    return [IMYCKInputPlaceholder publish_ttq_body];
}

- (NSString *)navigationItemTitle {
    if (self.viewModel.publishFromType == TTQPublishFromEdit) {
        return IMYString(@"编辑帖子");
    }
    return [IMYCKFeedsHelper praiseStyleExp]?@"分享":IMYString(@"发帖子");
}

- (NSString *)topRightButtonTitle {
    return @"发布";
}

- (NSString *)titleForContentCountLabel {
    NSInteger cutOut = [TTQPublishContentUtil
        cutOutTextForOverflow:self.viewModel.dataSource
                       maxNum:self.viewModel.publishConfiguration
                                  .bodyContentMaxNum];
    if (cutOut >= 0) {
        return @"0";
    } else if (cutOut < -100) {
        return @"";
    } else if (cutOut < 0) {
        return [NSString stringWithFormat:@"%ld", ABS(cutOut)];
    }
    return nil;
}

- (NSString *)contentWarning {
    return [NSString stringWithFormat:@"内容最多%ld字哦~",
                                      (long)self.viewModel.publishConfiguration
                                          .bodyContentMaxNum];
}

- (NSString *)contentWarningForImages {
    NSInteger maxNum = 9;
    return [NSString
        stringWithFormat:@"最多可以添加%ld张图",maxNum];
}

#pragma mark - IMYEmoticonViewDelegate 表情面板的回调方法

- (void)didTouchEmojiView:(IMYVKEmotionKeyboardView *)emojiView
             touchedEmoji:(NSString *)string {
    if (self.activeTextCell && [self emojiInsertEnabled]) {
        NSInteger textLength = self.activeTextCell.textView.text.ttq_textLength;
        if (textLength > 2998) {
            //键盘表情算两个字符
            return;
        }
        [IMYREmoticonManager emojiInsertWithTextView:self.activeTextCell.textView emojiKey:string maxEmotionCount:10 attributedDisplay:YES];
        [self.activeTextCell textViewDidChange:self.activeTextCell.textView];
    }
}
// 点击表情面板中的删除按键
- (void)didDelEmojiView:(IMYVKEmotionKeyboardView *)emojiView {
    if (self.activeTextCell) {
        NSRange range = self.activeTextCell.textView.selectedRange;
        [IMYREmoticonManager emojiDeleteWithTextView:self.activeTextCell.textView attributedDisplay:YES];
        if (range.length == 0 &&
            range.location <= 0) { //这一步是为了知道是否在段头了还在点击删除按钮
            [self.activeTextCell textView:self.activeTextCell.textView
                  shouldChangeTextInRange:range
                          replacementText:nil];
        } else {
            [self.activeTextCell textViewDidChange:self.activeTextCell.textView];
        }
    }
}

- (BOOL)emojiInsertEnabled {
    NSInteger emojiCount = 0;
    for (TTQPublishTextModel *model in self.viewModel.dataSource) {
        if ([model isKindOfClass:[TTQPublishTextModel class]]) {
            emojiCount += [IMYREmoticonManager emojiCount:model.text];
            if (emojiCount > 9) {
                [UIWindow imy_showTextHUD:@"最多可输入10个表情"];
                return NO;
            }
        }
    }
    return YES;
}

#pragma mark - 输入文字或者更改图片均进行保存

- (void)richDidEndEditing {
    [self.viewModel saveDraftAction:NO];
}

#pragma mark - 话题相关

- (void)updateRecommendTopics {
    @weakify(self);
    if (!self.viewModel.unavailableSubject) { // 用户可以更换热议话题
        [self.viewModel fetchRecommendSubjectsWithCompletionHandler:^{
            @strongify(self)
            [self.subjectView setTopicCategory:self.viewModel.subjectsCategory == 0 ? @"热议" : @"推荐" topicArray:self.viewModel.recommendSubjects]; // 更新推荐话题
            [self.subjectContainerView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.width.mas_equalTo(SCREEN_WIDTH);
                make.left.equalTo(self.view);
                make.bottom.equalTo(self.bottomToolView.mas_top);
                make.height.mas_equalTo(48);
            }];
            [self.tableView mas_remakeConstraints:^(MASConstraintMaker *make) {
                @strongify(self);
                make.left.top.and.right.mas_equalTo(0.0);
                make.bottom.mas_equalTo(self.bottomToolView.mas_top).offset(self.subjectView.topicArray.count?-(self.subjectContainerView.imy_height):0);
            }];
        }];
    }
}

#pragma mark - AI 标题

/// 重新请求时，force为YES
- (void)requestAiTitleWithForce:(BOOL)force {
    if ([IMYCKFeedsHelper feedsExp889] || [IMYCKFeedsHelper praiseStyleExp]) {
        /// 进入标题时才能请求
        BOOL shouldRequest = NO;
        if (force) {
            shouldRequest = YES;
        } else if ([[[self.viewModel current_content] ttq_parseTopicsWithTypingAttributes:@{}].string ttq_textLength] >= self.viewModel.aiTitleWordCount) {
            /// 美柚表情按1个字符算
            shouldRequest = YES;
        } else {
            self.viewModel.hasRequestAITitle = NO;
        }
        [self.postTitleView hideAiTitleView:!shouldRequest];
        if (shouldRequest) {
            @weakify(self);
            [self.viewModel requestAiTitle:^(NSArray *titles, NSError *error) {
                @strongify(self);
                NSString *currentContent = [[self.viewModel current_content] ttq_parseTopicsWithTypingAttributes:@{}].string;
                if ([currentContent ttq_textLength] < self.viewModel.aiTitleWordCount) {
                    return;
                }
                [self.postTitleView loadAITitles:titles];
                if (force && error) {
                    if (error) {
                        if ([IMYNetState networkEnable]) {
                            [UIView imy_showTextHUD:@"标题生成失败，请重试"];
                            [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_bjq_cxscaibt",@"action":@2,@"public_type":@"服务异常"} headers:nil completed:nil];
                        } else {
                            [UIView imy_showTextHUD:kStatusText_networkDisconnectNoCache];
                            [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_bjq_cxscaibt",@"action":@2,@"public_type":@"网络异常"} headers:nil completed:nil];
                        }
                    }
                } else if (force) {
                    [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_bjq_cxscaibt",@"action":@2,@"public_type":@"正常"} headers:nil completed:nil];
                }
            } forceRequest:force];
        }
    }
}

#pragma mark - 布局相关

- (BOOL)hideNavBarBottomLine {
    return YES;
}

- (void)setupView {
    [self.view imy_setBackgroundColorForKey:kCK_White_ANP];
    [self setupBottomToolView];
    [self setupTable];
    [self setupTableFooter];
    [self setupContent];
    [self.view bringSubviewToFront:self.subjectContainerView];
    [self.view bringSubviewToFront:self.bottomToolView];

    [self.view insertSubview:self.mLimitLabel aboveSubview:self.subjectView];
    
    if (self.viewModel.isAiOutput) {
        [self setBottomToolViewState:TTQPublishToolStateHidden];
        self.aiOutputView = [self creatAIViewWithType:TTQAiGenerateNormal];
        [self.navigationController.view addSubview:self.aiOutputView];
    } else if ([self canShowPanAssetPickerWhenInit] && !self.viewModel.subjectModel) {
        [self setBottomToolViewState:TTQPublishToolStateShowPanAssetPick];
    } else {
        [self setBottomToolViewState:TTQPublishToolStateNormal];
    }
    if (self.bottomToolView.aiButton.hidden) {
        [self inspirationView];
    }
}

- (void)setupTable {
    self.tableView = [[TTQTopicTableView alloc] init];
    [self.tableView imy_setBackgroundColorForKey:kCK_White_AN];
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.tableView.estimatedRowHeight = 0;
    self.tableView.estimatedSectionHeaderHeight = 0;
    self.tableView.estimatedSectionFooterHeight = 0;
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    [self.tableView registerClass:[TTQPublishTextCell class]
           forCellReuseIdentifier:NSStringFromClass([TTQPublishTextCell class])];
    [self.view addSubview:self.tableView];
    
    UIView *headerView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 0)];
    if ([TTQPublishTopBannerView shouldShowBanner]) {
        TTQPublishTopBannerView *banner = [TTQPublishTopBannerView defaultView];
        [headerView addSubview:banner];
        headerView.imy_height = banner.imy_height;
        banner.publishPopId = self.publishTipPopId;
    }
    if ([IMYCKFeedsHelper praiseStyleExp] || [IMYCKFeedsHelper feedsExp889]) {
        [headerView addSubview:self.postTitleView];
        self.postTitleView.imy_top = headerView.imy_height;
        headerView.imy_height = self.postTitleView.imy_bottom;
    }
    if (headerView.imy_height > 0) {
        self.tableView.tableHeaderView = headerView;
    }
    @weakify(self);
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        @strongify(self);
        make.left.top.and.right.mas_equalTo(0.0);
        make.bottom.mas_equalTo(self.bottomToolView.mas_top).offset(-(self.subjectContainerView.imy_height));
    }];

    self.tableTapGesture = [[UITapGestureRecognizer alloc]
        initWithTarget:self
                action:@selector(didTapTableView:)];
    self.tableTapGesture.delegate = self;
    self.tableTapGesture.cancelsTouchesInView = NO;
    [self.tableView addGestureRecognizer:self.tableTapGesture];
}

- (TTQPublishToolMenuItem *)emotionItem {
    @weakify(self);
    TTQPublishToolMenuItem *emotionItem = [TTQPublishToolMenuItem emotionItem];
    void (^emotionAction)(TTQPublishButton *sender) =
        ^(TTQPublishButton *sender) {
            @strongify(self);
            [self publishToolDidEdit];
            if (self.isExperienceTemplatePickerShowing) { // 取消选中
                self.isExperienceTemplatePickerShowing = NO;
                [self.bottomToolView setTemplateBtnStatus:NO];
            }
            [IMYEventHelper event:@"fb-bq"];
            UITextView *textView = [self validTextView];
            if (textView == nil) { // 如果当前没有活跃的文本框，就找到文末的文本框
                textView = [self nethermostTextView];
            }
            [self changeContentTextViewInput:textView];
            if (!textView.isFirstResponder) {
                self.isEmojBtnClick = YES;
                [textView becomeFirstResponder];
            }
            [self postBIwithPublishInfoType:@"表情icon"];
        };
    emotionItem.ItemTapAction(emotionAction);
    return emotionItem;
}

- (TTQPublishToolMenuItem *)topicItem {
    @weakify(self);
    TTQPublishToolMenuItem *topicItem = [TTQPublishToolMenuItem hotTopicItem];
    void (^emotionAction)(TTQPublishButton *sender) =
        ^(TTQPublishButton *sender) {
            @strongify(self);
            [self publishToolDidEdit];
            if (self.isExperienceTemplatePickerShowing) { // 取消选中
                self.isExperienceTemplatePickerShowing = NO;
                [self.bottomToolView setTemplateBtnStatus:NO];
            }
            [self.activeTextCell insertTopicAction];
            [self hideAssetPicker];
            [self postBIwithPublishInfoType:@"话题icon"];
        };
    topicItem.ItemTapAction(emotionAction);
    return topicItem;
}

- (TTQPublishToolMenuItem *)experienceTemplateItem {
    @weakify(self);
    TTQPublishToolMenuItem *emotionItem = [TTQPublishToolMenuItem experienceTemplateItem];
    void (^emotionAction)(TTQPublishButton *sender) =
        ^(TTQPublishButton *sender) {
            @strongify(self);
            [self publishToolDidEdit];
            UITextView *textView = [self validTextView];
            if (textView == nil) { // 如果当前没有活跃的文本框，就找到文末的文本框
                textView = [self nethermostTextView];
            }
            if (self.isExperienceTemplatePickerShowing) { // 关掉面板
                textView.inputView = nil;
                [textView reloadInputViews];
                self.isExperienceTemplatePickerShowing = NO;
                [self.bottomToolView setTemplateBtnStatus:NO];
                return;
            }
            self.isExperienceTemplatePickerShowing = YES;
            [self.bottomToolView setEmotionBtnStatus:NO];
            textView.inputView = self.experienceTemplatePickerView;
            [self.experienceTemplatePickerView setTemplateArray:self.viewModel.templateList];
            [textView reloadInputViews];
            if (!textView.isFirstResponder) {
                [textView becomeFirstResponder];
            }
            [self postBIwithPublishInfoType:@"模板icon"];
        };
    emotionItem.ItemTapAction(emotionAction);
    return emotionItem;
}

- (TTQPublishToolMenuItem *)cameraItem {
    @weakify(self);
    TTQPublishToolMenuItem *cameraItem = [TTQPublishToolMenuItem cameraItem];
    void (^cameraAction)(TTQPublishButton *sender) = ^(TTQPublishButton *sender) {
        @strongify(self);
        [self publishToolDidEdit];
        if (self.isExperienceTemplatePickerShowing) { // 取消选中
            self.isExperienceTemplatePickerShowing = NO;
            [self.bottomToolView setTemplateBtnStatus:NO];
        }
        NSIndexPath *activeIndexPath = self.activeIndexPath;
        TTQPublishTextCell *activeTextCell = self.activeTextCell;
        NSInteger activeTextInputModel = activeTextCell.textView.inputView != nil;
        if (self.panAssetPicker) {
            [self.panAssetPicker upToTopAnimation];
        } else {
            [self takeImage];
            [self endEditing];
        }

        self.activeIndexPath = activeIndexPath;
        self.activeTextCell = activeTextCell;
        self.activeTextInputModel = activeTextInputModel;
        [self postBIwithPublishInfoType:@"图片icon"];
    };
    cameraItem.ItemTapAction(cameraAction);
    return cameraItem;
}

- (TTQPublishToolMenuItem *)voteItem {
    @weakify(self);
    TTQPublishToolMenuItem *voteItem = [TTQPublishToolMenuItem voteItem];
    void (^voteAction)(TTQPublishButton *sender) = ^(TTQPublishButton *sender) {
        @strongify(self);
        [self publishToolDidEdit];
        if (self.isExperienceTemplatePickerShowing) { // 取消选中
            self.isExperienceTemplatePickerShowing = NO;
            [self.bottomToolView setTemplateBtnStatus:NO];
        }
        [self pushNewVoteView];
        if (self.panAssetPicker) {
            [self setBottomToolViewState:TTQPublishToolStateNormal];
            [self hideAssetPicker];
        }
        [self postBIwithPublishInfoType:@"投票icon"];
    };
    if (voteItem.ItemTapAction) {
        voteItem.ItemTapAction(voteAction);
    }
    return voteItem;
}

- (TTQPublishToolMenuItem *)aiToolItem {
    TTQPublishToolMenuItem *emotionItem = [TTQPublishToolMenuItem aiToolItem];
    @weakify(self);
    void (^itemAction)(TTQPublishButton *sender) =
        ^(TTQPublishButton *sender) {
            @strongify(self);
            [self aiToolItemAction:sender];
            [self postBIwithPublishInfoType:@"创作助手icon"];
        };
    emotionItem.ItemTapAction(itemAction);
    return emotionItem;

}

- (void)aiToolItemAction:(TTQPublishButton *)sender {
    [self publishToolDidEdit];
    [self hideAssetPicker];
    self.isExperienceTemplatePickerShowing = NO;
    [self.bottomToolView setEmotionBtnStatus:NO];
    if (sender.selected) {
        [UIView setAnimationsEnabled:NO];
        [self.view endEditing:YES];
        [UIView setAnimationsEnabled:YES];
        [self setBottomToolViewState:TTQPublishToolStateShowAiTool];
        self.inspirationView.imy_top = self.view.imy_height;
        [UIView animateWithDuration:0.25 animations:^{
            self.inspirationView.imy_bottom = self.view.imy_height;
        }];
    } else {
        [self.activeTextCell becomeFirstResponder];
    }
}

/// 设置底部栏按钮
- (void)setupBottomToolView {
    @weakify(self);
    NSMutableArray *items = [NSMutableArray array];
    [self.viewModel resetPublishToolItem_865];
    BOOL insertAiTool = YES;
    if ([TTQPublishInspirationView items].count == 0) {
        insertAiTool = NO;
    }

    for (NSNumber *itemID in self.viewModel.publishToolItemIDs) {
        NSInteger itemIDWithInt = itemID.integerValue;
        if (itemIDWithInt == TTQPublishToolMenuItemTypeForEmotion) {
            [items addObject:[self emotionItem]];
        } else if (itemIDWithInt == TTQPublishToolMenuItemTypeForCamera &&
                   self.viewModel.publishConfiguration.limit_image > 0) {
            [items addObject:[self cameraItem]];
        } else if (itemIDWithInt == TTQPublishToolMenuItemTypeForVote &&
                   self.viewModel.publishConfiguration.vote_max_items > 0) {
            [items addObject:[self voteItem]];
        } else if (itemIDWithInt == TTQPublishToolMenuItemTypeForExperienceTemplate) {
            [items addObject:[self experienceTemplateItem]];
        } else if (itemIDWithInt == TTQPublishToolMenuItemTypeForlToolTopic) {
            [items addObject:[self topicItem]];
        } else if (itemIDWithInt == TTQPublishToolMenuItemTypeForAiTool && insertAiTool) {
            [items addObject:[self aiToolItem]];
        }
    }
    self.bottomToolView = [TTQPublishToolView publishToolView];
    [self.bottomToolView imy_showLineForDirection:IMYDirectionUp];
    [self.bottomToolView imy_setBackgroundColorForKey:kCK_White_AN];
    self.bottomToolView.clickMoreBtn = ^(IMYTouchEXButton *sender) {
        @strongify(self);
        //匿名按钮点击
        [self publishToolDidEdit];
        [self bottomToolMoreButtonAction];
        [self postBIwithPublishInfoType:@"权限管理icon"];
    };
    [self.bottomToolView setItems:items.copy];
    bottomToolViewNormalH = items.count ? 44 : 0;
    bottomToolViewUpH = items.count ? 44 : 0;
    [self updateVoteEnable];
    [self.view addSubview:self.bottomToolView];
    [self.bottomToolView mas_makeConstraints:^(MASConstraintMaker *make) {
        @strongify(self);
        make.left.and.right.mas_equalTo(0.0);
        make.height.mas_equalTo(bottomToolViewNormalH);
        make.bottom.mas_offset(-self.safeBottom_Margin);
    }];
    if (![TTQPublishTopicViewModel enableSelectForum] && ![TTQPublishTopicViewModel enableSelectVisible]) {
        self.bottomToolView.moreActionButton.hidden = YES;
    }

    UIView *subjectContainerView = [UIView new];
    [subjectContainerView imy_setBackgroundColorForKey:kCK_White_AN];
    [self.view addSubview:subjectContainerView];
    self.subjectContainerView = subjectContainerView;
    [subjectContainerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(SCREEN_WIDTH);
        make.left.equalTo(self.view);
        make.bottom.equalTo(self.bottomToolView.mas_top);
        make.height.equalTo(@48);
    }];

    self.subjectView = [[TTQPublishSubjectToolView alloc] initWithOnlyTopicStyle:YES isPublishTopic:YES];
    [subjectContainerView addSubview:self.subjectView];
    [self.subjectView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(subjectContainerView);
    }];
    [self.subjectView setAddTopicHandler:^(NSInteger index) {
        @strongify(self);
        [self publishToolDidEdit];
        if (index < self.subjectView.topicArray.count) { // 已经选中话题
            NSDictionary *dic = self.subjectView.topicArray[index];
            TTQTopicSubjectModel *subject = [TTQTopicSubjectModel new];
            subject.subjectID = [dic[@"id"] integerValue];
            subject.name = dic[@"name"];
            if ([self.activeTextCell didSelectSubject:subject.name isAssociate:NO]) {
                /// 插入成功就移除
                [self.subjectView removeTopicItem:dic[@"name"]];
            }
            [self.selectedTopics addObject:dic];
        }
    }];
    // 字数限制
    self.mLimitLabel = [[UILabel alloc] init];
    self.mLimitLabel.textAlignment = NSTextAlignmentRight;
    [self.mLimitLabel imy_setTextColorForKey:kCK_Black_C];
    [self.mLimitLabel setFont:[UIFont systemFontOfSize:12.0]];
    self.mLimitLabel.text = @"";
    [self.view addSubview:self.mLimitLabel];

    [self.mLimitLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.bottomToolView.mas_top).offset(self.subjectView.topicArray.count == 0 ?-16:(- 8 - self.subjectContainerView.imy_height));
        make.right.equalTo(self.view).offset(-16);
    }];
    
    [self.view addSubview:self.associateTopicsView];
    self.associateTopicsView.hidden = YES;
    [self.associateTopicsView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.bottomToolView.mas_top);
        make.size.mas_equalTo(CGSizeMake(SCREEN_WIDTH, 180));
    }];
}

- (TTQPublishAssociateTopicsView *)associateTopicsView {
    if (!_associateTopicsView) {
        _associateTopicsView = [[TTQPublishAssociateTopicsView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 180)];
        @weakify(self);
        [_associateTopicsView setDidSelectCellBlock:^(TTQTopicSubjectModel * _Nonnull model) {
            @strongify(self);
            [self.activeTextCell didSelectSubject:model.name isAssociate:YES];
            /// 保存下选中的话题
            NSMutableDictionary *subjectData = [NSMutableDictionary dictionaryWithCapacity:1];
            subjectData[@"name"] = model.name;
            subjectData[@"id"] = @(model.subjectID);
            [self.selectedTopics addObject:subjectData];
        }];
    }
    return _associateTopicsView;
}

- (void)upperBottomTools:(BOOL)isUp {
    if (isUp) {
        [self setBottomToolViewState:TTQPublishToolStateShowPanAssetPick];
    } else {
        [self setBottomToolViewState:TTQPublishToolStateNormal];
    }
}

- (void)setupTableFooter {
    self.tableView.tableFooterView =
        [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 0)];
    self.tableView.tableFooterView.userInteractionEnabled = YES;

    self.tableFooterView =
        [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 0)];

    @weakify(self);
    self.contentCountLabel = [[UILabel alloc] init];
    self.contentCountLabel.textAlignment = NSTextAlignmentRight;
    [self.contentCountLabel imy_setTextColorForKey:kCK_Black_D];
    [self.contentCountLabel setFont:[UIFont systemFontOfSize:16.0]];
    [self.tableFooterView addSubview:self.contentCountLabel];
    self.contentCountLabel.frame = CGRectMake(0, 1, SCREEN_WIDTH - 15, 15);
    self.contentCountLabel.hidden = YES;

    self.voteView = [[TTQPublishVoteView alloc] initWithLimit:5];
    self.voteView.addVoteBlock =
        ^(NSString *errorMessage, UITextField *textField) {
            @strongify(self);
            [self remakeFooterConstraints:NO];
            if (self.voteView.isFirstResponder ||
                [self validTextView].isFirstResponder ||
                [self validTextViewForImageCell].isFirstResponder) {
                [self scrollToVoteViewAnimation:NO];
                [textField becomeFirstResponder];
            } else {
                [self scrollToVoteViewAnimation:YES];
            }
        };
    self.voteView.didEditingVoteBlock = ^(UITextField *textField) {
        @strongify(self);
        [self setBottomToolViewState:TTQPublishToolStateVote];
    };
    self.voteView.didEndEditingVoteBlock = ^(UITextField *textField) {
        @strongify(self);
        [self.viewModel saveDraftAction:NO];
    };
    self.voteView.changeVotesBlock =
        ^(NSArray *voteTexts, NSString *errorMessage, NSInteger changeNum) {
            @strongify(self);
            if (voteTexts.count > 0) {
                [self addVoteViewWith:changeNum < 0];
            } else {
                [self removeVoteViewWith:YES];
            }
        };
    if (self.viewModel.publishConfiguration.vote_max_items > 0) {
        [self.voteView
            setMaximun:self.viewModel.publishConfiguration.vote_max_items];
        [self.voteView setVoteTexts:self.viewModel.draft.voteTexts];
    }

    // 底部有可能是视频view
    [self setupVideoView];
    
    // 底部有图片
    [self setupPickerImageViews];

    [self.tableView addSubview:self.tableFooterView];
    [self remakeFooterConstraints:NO];

    BOOL showVoteView = [TTQCommonHelp getPublishToolBottomItemShowStatus:TTQPublishBottomToolItem_Vote];
    self.viewModel.draft.voteAvailable = showVoteView;
    if (self.viewModel.draft.voteTexts.count > 0 && showVoteView) {
        NSMutableArray *ary = [NSMutableArray array];
        if ([self needSetVoteTitleEmpty]) {
            //有草稿且无flag,则说明是从797之前版本升级上来的 这时候塞入一个空字符串到数组中,也就是把标题置空
            [ary addObject:@""];
        }
        for (NSString *str in self.viewModel.draft.voteTexts) {
            if (imy_isNotEmptyString(str)) {
                [ary addObject:str];
            } else {
                [ary addObject:@""];
            }
        }
        self.viewModel.drafVoteAry = [NSArray arrayWithArray:ary];
        [self layoutVoteView:ary];
    }
    
    if(self.viewModel.video != nil){
        [self layoutVideoView:self.viewModel.video cover:self.viewModel.videoCover];
    }
}

- (void)resetFooterViewPisition:(BOOL)resetInset {
    if (resetInset) {
        UIView *view = self.tableView.tableFooterView;
        view.imy_height = self.tableFooterView.imy_height;
        self.tableView.tableFooterView = view;
    }
    CGFloat maxTableViewBottom =
        MAX(self.tableView.imy_height, self.tableView.ttq_contentSizeH);
    self.tableFooterView.imy_bottom = maxTableViewBottom;
    [self.tableView bringSubviewToFront:self.tableFooterView];
}

- (void)setupContent {
    if (_postTitleView) {
        _postTitleView.titleStr = self.viewModel.draft.title;
        self.viewModel.titleStr = _postTitleView.titleStr;
    }

    self.mLimitLabel.text = [self titleForContentCountLabel];
    [self updateRightBtn];
}

- (void)remakeFooterConstraints:(BOOL)animationted {
    if (!animationted) {
        [CATransaction begin];
        [CATransaction setDisableActions:YES];
    }

    if (self.voteView.superview) {
        UIView *lastView = self.contentCountLabel;
        CGFloat offsetH = 1.0;
        CGFloat footerH = 16;
        if (self.voteView.superview) {
            self.voteView.imy_top = lastView.imy_bottom + offsetH;
            self.voteView.imy_left = 15.0;
            self.voteView.imy_right = self.tableFooterView.imy_width - 15;
            lastView = self.voteView;
            footerH += self.voteView.imy_height + offsetH;
            offsetH = 10.0;
        }
        footerH += 1;

        CGFloat offset = footerH - self.tableFooterView.imy_height;
        self.tableFooterView.imy_bottom -= offset;
        self.tableFooterView.imy_height = footerH;
    } else {
        self.tableFooterView.imy_bottom += self.tableFooterView.imy_height;
        self.tableFooterView.imy_height = 0;
    }

    [self resetFooterViewPisition:YES];

    if (!animationted) {
        [CATransaction commit];
    }
}

- (void)addVoteViewWith:(BOOL)isRemarkConstraints {
    [CATransaction begin];
    [CATransaction setDisableActions:YES];
    if (self.voteView.superview == nil) {
        [self.tableFooterView addSubview:self.voteView];
    }
    if (isRemarkConstraints) {
        [self remakeFooterConstraints:YES];
    }
    [CATransaction commit];
}

- (void)removeVoteViewWith:(BOOL)isRemarkConstraints {
    [CATransaction begin];
    [CATransaction setDisableActions:YES];
    if (self.voteView.superview != nil) {
        [self.voteView removeFromSuperview];
    }
    if (isRemarkConstraints) {
        [self remakeFooterConstraints:YES];
    }
    [CATransaction commit];
}

- (void)addBottomImagePickerIfNeeded:(NSArray<TTQPublishContentModel *> *)images {
    NSMutableArray *imageModels = [[NSMutableArray alloc] initWithArray:self.imagePickerView.imageArray];
    for (TTQPublishImageModel *imageModel in images) {
        [imageModels addObject:imageModel.publishNewImage];
    }
    if (!self.imagePickerView) {
        [self setupPickerImageViews];
    }
    [self.imagePickerView updateImageData:imageModels isScrollLastIndex:YES];
    if (self.imagePickerView.superview == nil && imageModels.count > 0) {
        [self addPickerViewWith:YES];
    }
}

- (void)addPickerViewWith:(BOOL)isRemarkConstraints {
    if (!self.imagePickerView) return;
    [CATransaction begin];
    [CATransaction setDisableActions:YES];
    if (self.imagePickerView.superview == nil) {
        [self.tableFooterView addSubview:self.imagePickerView];
        
        CGFloat tableFootViewHeight = self.tableFooterView.imy_height;
        if (self.voteShowView && [self.tableFooterView.subviews containsObject:self.voteShowView]) {
            tableFootViewHeight = self.voteShowView.imy_height + 12 + self.imagePickerView.imy_top;
            self.imagePickerView.imy_top = self.voteShowView.imy_bottom + 12;
            tableFootViewHeight += self.imagePickerView.imy_height;
        } else {
            tableFootViewHeight = self.imagePickerView.imy_height;
            self.imagePickerView.imy_top = 0;
        }
        self.tableFooterView.frame = CGRectMake(0, 0, SCREEN_WIDTH, tableFootViewHeight);
        self.tableView.tableFooterView = self.tableFooterView;
        [self.tableView reloadData];
    }
    [CATransaction commit];
}

- (void)removePickerViewWith:(BOOL)isRemarkConstraints {
    if (!self.imagePickerView) return;
    [CATransaction begin];
    [CATransaction setDisableActions:YES];
    if (self.imagePickerView.superview != nil) {
        [self.imagePickerView removeFromSuperview];
    }
    if (self.voteShowView && [self.tableFooterView.subviews containsObject:self.voteShowView]) {
        self.tableFooterView.frame = CGRectMake(0, 0, SCREEN_WIDTH, self.voteShowView.imy_height);
    } else {
        self.tableFooterView.frame = CGRectZero;
    }
    self.tableView.tableFooterView = self.tableFooterView;
    [self.tableView reloadData];
    [CATransaction commit];
}

- (void)setBottomToolViewState:(TTQPublishToolState)publishToolState {
    self.bottomToolView.publishToolState = publishToolState;
    if (publishToolState == TTQPublishToolStateNormal) { // 收起工具栏
        @weakify(self);
        [self.bottomToolView mas_updateConstraints:^(MASConstraintMaker *make) {
            @strongify(self);
            make.height.mas_equalTo(bottomToolViewNormalH);
            make.bottom.mas_equalTo(-self.safeBottom_Margin);
        }];
    } else if (publishToolState == TTQPublishToolStateShowPanAssetPick || publishToolState == TTQPublishToolStateShowAiTool) {
        [self.bottomToolView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(bottomToolViewUpH);
            make.bottom.mas_equalTo(-286 - self.safeBottom_Margin);
        }];
    } else if (publishToolState == TTQPublishToolStateHidden) {
        [self.bottomToolView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.bottom.mas_equalTo(bottomToolViewUpH + 48);
        }];
    } else {
        [self.bottomToolView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(bottomToolViewUpH);
        }];
    }
    [self updateVoteEnable];
    if (publishToolState != TTQPublishToolStateShowAiTool) {
        [self hideInspirationViewIfNeeded];
    }
}

- (BOOL)isShowVote {
    return self.voteView.voteTexts.count > 0 && self.voteView.superview != nil;
}

- (void)updateVoteEnable {
    if (self.viewModel.isEdit && self.viewModel.draft.showVote == NO) {
        [self.bottomToolView setVoteBtnStatus:NO];
    }
}

- (void)hideInspirationViewIfNeeded {
    if (_inspirationView && _inspirationView.imy_top < self.view.imy_height) {
        [UIView animateWithDuration:0.25 animations:^{
            self.inspirationView.imy_top = self.view.imy_height;
        }];
    } else if (_inspirationView) {
        /// 动画可能异常
        self.inspirationView.imy_top = self.view.imy_height;
    }
}
#pragma mark - 图片选择器
- (void)setupPickerImageViews {
    // 图片选择器
    self.imagePickerView = [[IMYUGCNotePublishImagePickerView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, kPublishTopicImagePickerViewHeight)];
    self.imagePickerView.addImageByOutControl = YES;
    self.imagePickerView.allowEmptyImage = YES;
    [self.imagePickerView updateLayoutWithHeight:kPublishTopicImagePickerViewHeight width:kPublishTopicImagePickerViewHeight style:IMYUGCNotePublishImagePickerStylePublishTopic];
    @weakify(self);
    // 这里加一个点击回调通知编辑器已经开始编辑：
    [self.imagePickerView setDidBeginEditing:^{
        @strongify(self);
        [self publishToolDidEdit];
        if (self.panAssetPicker) {
            [self.panAssetPicker upToTopAnimation];
        } else {
            [self takeImage];
        }
        [self postBIwithPublishInfoType:@"图片+icon"];
    }];
    
    [self.imagePickerView setDidDeleteImageHandler:^(IMYUGCNotePublishImage * _Nonnull imageModel, NSArray<IMYUGCNotePublishImage *> * _Nonnull currentImages) {
        @strongify(self);
        BOOL findObj = NO;
        for (TTQPublishContentModel *contentModel in self.viewModel.dataSource) {
            if ([contentModel isKindOfClass:[TTQPublishImageModel class]]) {
                TTQPublishImageModel *imgModel = (TTQPublishImageModel *)contentModel;
                if (imgModel.publishNewImage == imageModel) {
                    [self deletedImage:contentModel];
                    findObj = YES;
                    break;
                }
            }
        }
        /// 可能存在没找到的，用下面方法再试一次
        if (!findObj) {
            for (TTQPublishContentModel *contentModel in self.viewModel.dataSource) {
                if ([contentModel isKindOfClass:[TTQPublishImageModel class]] &&
                    [((TTQPublishImageModel *)contentModel).publishNewImage.assetUrl isEqualToString:imageModel.assetUrl]) {
                    [self deletedImage:contentModel];
                    break;
                }
            }
        }
        
        if (self.imagePickerView.imageArray.count <= 0) {
            [self removePickerViewWith:YES];
        }
    }];
    
    [self.imagePickerView setDidChangedImagesOrderHandler:^(NSArray<IMYUGCNotePublishImage *> * _Nonnull currentImages) {
        @strongify(self);
        NSMutableArray *dataSource = [NSMutableArray arrayWithArray:self.viewModel.dataSource];
        TTQPublishTextModel *textModel = nil;
        for (TTQPublishContentModel *obj in dataSource) {
            if (obj.richContentType == TTQPublishContentTypeText) {
                textModel = obj;
                [dataSource removeObject:obj];
                break;
            }
        }
        /// 调整图片排序
        dataSource = [[dataSource sort:^BOOL(TTQPublishImageModel *element1, TTQPublishImageModel *element2) {
            NSInteger index1 = [currentImages indexOfObject:element1.publishNewImage];
            NSInteger index2 = [currentImages indexOfObject:element2.publishNewImage];
            return index1 < index2;
        }] mutableCopy];
        if (textModel) {
            [dataSource insertObject:textModel atIndex:0];
        }
        self.viewModel.dataSource = dataSource;
        if (self.panAssetPicker) {
            NSMutableArray *assetModels = [NSMutableArray arrayWithCapacity:currentImages.count];
            for (IMYUGCNotePublishImage *imageModel in currentImages) {
                IMYAssetModel *asset = [[IMYAssetModel alloc] initWithIdentifier:imageModel.assetUrl];
                if (asset) {
                    [assetModels addObject:asset];
                }
            }
            self.panAssetPicker.selectedAssetArray = assetModels;
            [self.panAssetPicker reloadCollectionView];
        }
    }];
    _imagePickerView.maxCount = 9;
}

- (void)removePickerImageViewsWithArray:(NSArray <IMYAssetModel *>*)assets {
    if (!self.imagePickerView) return;
    
    NSMutableArray *imageModels = [[NSMutableArray alloc] initWithArray:self.imagePickerView.imageArray];
    for (IMYAssetModel *assetModel in assets) {
        for (IMYUGCNotePublishImage *imageM in self.imagePickerView.imageArray) {
            if ([imageM.assetUrl isEqualToString:assetModel.identifier]) {
                [imageModels removeObject:imageM];
                for (TTQPublishContentModel *contentModel in self.viewModel.dataSource) {
                    if ([contentModel isKindOfClass:[TTQPublishImageModel class]] &&
                        [((TTQPublishImageModel *)contentModel).publishNewImage.assetUrl isEqualToString:assetModel.identifier]) {
                        [self deletedImage:contentModel];
                    }
                }
            }
        }
    }
    [self.imagePickerView updateImageData:imageModels];
    if (self.imagePickerView.superview && imageModels.count == 0) {
        [self removePickerViewWith:YES];
    }
}

#pragma mark - 视频

- (void)setupVideoView {
    self.videoView = [[TTQPublishVideoView alloc] initWithFrame:CGRectMake(12, 4, 126, 168)];
}

- (void)layoutVideoView:(IMYAssetModel *)video cover:(IMYUGCNotePublishImage *)cover {
    // data
    self.viewModel.video = video;
    self.viewModel.videoCover = cover;

    // ui
    @weakify(self);
    CGSize realVideoSize = [self videoRealSize];
    if(CGSizeEqualToSize(realVideoSize, CGSizeZero)){
        // 闪退！！！！！！！没拿到avasset
        [UIWindow imy_showLoadingHUD];
        [self.viewModel.video requestVideoAVAsset:^(AVAsset * _Nonnull avAsset, NSDictionary * _Nonnull info) {
            @strongify(self);
            [self layoutContinueVideoView:video cover:cover];
            [UIWindow imy_hideHUD];
        }];
        return;
    }
    [self layoutContinueVideoView:video cover:cover];
}

- (void)layoutContinueVideoView:(IMYAssetModel *)video cover:(IMYUGCNotePublishImage *)cover{
    CGSize realVideoSize = [self videoRealSize];
    [self loadVideoThumbnailData]; // 提前加载封面和视频预览
    if(realVideoSize.height > realVideoSize.width){
        // 竖视频
        self.videoView.videoCoverImgV.contentMode = UIViewContentModeScaleAspectFill;
    } else {
        self.videoView.videoCoverImgV.contentMode = UIViewContentModeScaleAspectFit;
    }
    self.videoView.videoCoverImgV.image = cover.imageObj;
    self.videoView.videoTimeLabel.text = video.duration;
    [self.videoView.videoTimeLabel sizeToFit];

    // 工具栏联动：隐藏匿名、除表情外其他图标置灰
    self.bottomToolView.videoSelected = YES;
    if (self.bottomToolView.publishToolState != TTQPublishToolStateShowPanAssetPick) {
        [self setBottomToolViewState:TTQPublishToolStateNormal];
    } else {
        self.bottomToolView.publishToolState = TTQPublishToolStateShowPanAssetPick;
    }

    @weakify(self);
    self.videoView.clickDeleteIconBlock = ^{
        @strongify(self);
        // 删除视频
        [self endEditing];
        imy_asyncMainBlock(^{
            @strongify(self);
            // TODO: 视频数据置空
            [self clearVideo];
            if (self.bottomToolView.publishToolState != TTQPublishToolStateShowPanAssetPick) {
                [self setBottomToolViewState:TTQPublishToolStateNormal];
            } else {
                self.bottomToolView.publishToolState = TTQPublishToolStateShowPanAssetPick;
            }
            self.tableFooterView.frame = CGRectMake(0, 0, SCREEN_WIDTH, 0);
            self.tableView.tableFooterView = self.tableFooterView;
            [self.tableView reloadData];
            if (self.panAssetPicker) {
                /// 清空相册选中
                self.panAssetPicker.selectedAssetArray = [@[] mutableCopy];
                [self.panAssetPicker reloadCollectionView];
            }
        });
    };

    self.videoView.clickPlayIconBlock = ^{
        @strongify(self);
        // 进入视频预览页面
        [self endEditing];
        IMYPhotoBrowser *browser = [[IMYPhotoBrowser alloc] init];
        browser.showType = IMYBrowserTypeNormal;
        IMYAssetModel *asset = self.viewModel.video;
        IMYPhoto *photo = [[IMYPhoto alloc] init];
        photo.assetModel = asset;
        browser.photos = @[photo];
        browser.currentPhotoIndex = 0;
        browser.allowSelectionVideo = YES;
        browser.currentSelectionType = IMYAssetTypeVideo;
        browser.autoPlayVideo = YES;
        [browser show];
    };

    self.videoView.clickChangeCoverIconBlock = ^{
        @strongify(self);
        // 进入换封面逻辑
        [self endEditing];
        [self selectCoverImage];
        [self hideAssetPicker];
        if (self.bottomToolView.publishToolState == TTQPublishToolStateShowPanAssetPick) {
            [self setBottomToolViewState:TTQPublishToolStateNormal];
        }
    };

    [self.tableFooterView addSubview:self.videoView];

    self.tableFooterView.frame = CGRectMake(0, 0, SCREEN_WIDTH, self.videoView.imy_height);
    self.tableView.tableFooterView = self.tableFooterView;
    self.viewModel.draft.showVideo = YES;
    [self.tableView reloadData];
    
    // 这里复用投票滚动到footer的方法
    if ([self validTextView].isFirstResponder || [self validTextViewForImageCell].isFirstResponder) {
        [self scrollToVoteViewAnimation:NO];
    } else {
        [self scrollToVoteViewAnimation:YES];
    }
}

- (void)clearVideo {
    if (self.viewModel.originalDraft.videoCover && [self.viewModel.originalDraft.videoCover.filePath isEqualToString:self.viewModel.videoCover.filePath]) {
        [self.deletedImageFileCaches addObject:self.viewModel.videoCover.filePath];
    } else {
        [[TTQPublishHelper sharedInstance] deleteCacheImageWithPath:self.viewModel.videoCover.filePath];
    }
    self.loadThumStart = NO;
    self.loadThumComplete = NO;
    self.viewModel.video = nil;
    self.viewModel.videoCover = nil;
    self.viewModel.draft.showVideo = NO;
    self.bottomToolView.videoSelected = NO;
    [self.videoView removeFromSuperview];
}

#pragma mark - 视频封面

- (void)loadVideoThumbnailData {
    // 这里加载视频帧截取数据，如果很慢的话看优化将取帧时间间隔增大：
    @weakify(self);
    [self imy_asyncBlock:^{
        @strongify(self);
        AVURLAsset *asset = self.viewModel.video.avAsset;
        NSTimeInterval duration = [asset svr_avAssetVideoTrackDuration];

        CMTime startTime = kCMTimeZero;
        NSMutableArray *array = [NSMutableArray array];
        NSInteger itemCount = floor((SCREEN_WIDTH - 40.f) / 30);
        CMTime addTime = CMTimeMake(duration * 1000.f / itemCount, 1000);
        CMTime endTime = CMTimeMake(duration * 1000, 1000);
        while (CMTIME_COMPARE_INLINE(startTime, <, endTime)) {
            [array addObject:[NSValue valueWithCMTime:startTime]];
            startTime = CMTimeAdd(startTime, addTime);
        }
        // 第一帧取第0.1s   规避有些视频并不是从第0s开始的
        array[0] = [NSValue valueWithCMTime:CMTimeMake(0.1 * 1000, 1000)];
        __block int index = 0;
        NSMutableArray *imageArray = [NSMutableArray array];
        [self.pickGenerator generateCGImagesAsynchronouslyForTimes:array
                                                 completionHandler:^(CMTime requestedTime, CGImageRef _Nullable image, CMTime actualTime, AVAssetImageGeneratorResult result, NSError *_Nullable error) {
                                                     @strongify(self);
                                                     if (result == AVAssetImageGeneratorSucceeded) {
                                                         self.loadThumStart = YES;
                                                         UIImage *img = [[UIImage alloc] initWithCGImage:image];
                                                         [imageArray addObject:img];
                                                         index++;
                                                         if (index == itemCount) {
                                                             self.loadThumComplete = YES;
                                                             self.loadThumStart = NO;
                                                             self.imagesArray = imageArray;
                                                         }
                                                     }
                                                 }];
    }];
}

- (AVAssetImageGenerator *)pickGenerator {
    AVURLAsset *asset = self.viewModel.video.avAsset;
    AVAssetImageGenerator *pickGenerator = [[AVAssetImageGenerator alloc] initWithAsset:asset];
    pickGenerator.appliesPreferredTrackTransform = YES;
    pickGenerator.requestedTimeToleranceBefore = kCMTimeZero;
    pickGenerator.requestedTimeToleranceAfter = kCMTimeZero;
    pickGenerator.maximumSize = CGSizeMake(60 * SCREEN_SCALE, 100 * SCREEN_SCALE);
    _pickGenerator = pickGenerator;
    return _pickGenerator;
}

- (void)selectCoverImage {
    if (!self.loadThumComplete) {
        // TODO: 帧截取没加载完，需要loading继续读取
        if(!self.loadThumStart){
            // 视频还没处理中
            [self loadVideoThumbnailData];
        }
    }
    @weakify(self);
    AVURLAsset *asset = self.viewModel.video.avAsset;
    CGSize size = [self videoRealSize];
    if(CGSizeEqualToSize(size, CGSizeZero)){
        // 闪退！！！！！！！没拿到avasset
        [UIWindow imy_showLoadingHUD];
        [self.viewModel.video requestVideoAVAsset:^(AVAsset * _Nonnull avAsset, NSDictionary * _Nonnull info) {
            @strongify(self);
            // 拿到再跑吧，todo：这边可能请求多次，要规避
            [UIWindow imy_hideHUD];
            [self selectCoverImage];
        }];
        return;
    }
    SVRMediaConfig *config = [[SVRMediaConfig alloc] init];
    config.outputSize = size;
    config.ratioSize = size;
    config.relativeOutputFilePath = [asset.URL absoluteString];
    config.videoAsset = asset;
    
    [IMYActionSheet sheetWithCancelTitle:@"取消" otherTitles:@[@"从视频截取", @"从相册选择"] summary:nil showInView:nil action:^(NSInteger index) {
        @strongify(self);
        if (index != 0) {
            if (index == 1) {
                SVRCoverPickerViewController *vc = [SVRCoverPickerViewController new];
                vc.shouldReleativePath = YES;
                vc.config = config;
                vc.imagesArray = self.imagesArray;
                vc.finishHandler = ^(UIImage *image) {
                    @strongify(self);
                    // data
                    IMYUGCNotePublishImage *imageModel = [[IMYUGCNotePublishImage alloc] initWithImage:image];
                    self.viewModel.videoCover = imageModel;
                    imy_asyncMainBlock(^{
                        // ui
                        self.videoView.videoCoverImgV.image = imageModel.imageObj;
                    });
                };
                [self.navigationController pushViewController:vc animated:YES];
            } else {
                // 从相册选取
                if (IMYAssetAuthorizationStatusNotAuthorized == [IMYAssetsManager authorizationStatus]) {
                    [UIWindow imy_showTextHUD:IMYString(@"相册照片无法显示啦，请在系统设置-隐私-照片中打开美柚开关")];
                } else {
                    IMYAssetPickerController *vc = [[IMYAssetPickerController alloc] init];
                    vc.styleType = IMYAssetPickerUITypeSingle;
                    vc.delegate = self;
                    vc.ratioSize = config.outputSize;
                    [[UIViewController imy_currentViewControlloer] imy_present:vc animated:YES];
                }
            }
        }
    }];
}

-(CGSize)videoRealSize{
    AVURLAsset *asset = self.viewModel.video.avAsset;
    CGSize realSize = CGSizeApplyAffineTransform(asset.naturalSize, asset.preferredTransform);
    CGSize size = [[[asset tracksWithMediaType:AVMediaTypeVideo] objectAtIndex:0] naturalSize];
    AVAssetTrack *assetTrack = [[asset tracksWithMediaType:AVMediaTypeVideo] objectAtIndex:0];
    CGAffineTransform transform = assetTrack.preferredTransform;
    CGFloat tfA = transform.a;
    CGFloat tfB = transform.b;
    CGFloat tfC = transform.c;
    CGFloat tfD = transform.d;
    BOOL isPortrait = NO;
    if(tfA == 0 && tfB == 1.0 && tfC == -1.0 && tfD == 0){
        isPortrait = true;
    } else if(tfA == 0 && tfB == -1.0 && tfC == 1.0 && tfD == 0){
        isPortrait = true;
    }
    if(isPortrait){
        size = CGSizeMake(size.height, size.width);
    }
    return size;
}

#pragma mark - IMYAssetPickerControllerDelegate

- (void)assetPickerController:(IMYAssetPickerController *)assetPickerController didCropImage:(UIImage *)image {
    [UIWindow imy_showTextHUD:@"已更改封面"];
    // 直接将剪裁过的image压缩存在本地
    IMYUGCNotePublishImage *imageM = [[IMYUGCNotePublishImage alloc] initWithImage:image];
    self.viewModel.videoCover = imageM;
    // ui
    self.videoView.videoCoverImgV.image = imageM.imageObj; // 压缩过的
}
/// 选中图片
- (void)assetPickerController:(IMYAssetPickerController *)assetPickerController didSelectAsset:(IMYAssetModel *)asset {
    if (assetPickerController == self.panAssetPicker) {
        if (self.panAssetPicker.isFullScreenStatus) {
            /// 全屏时不响应单点
            return;
        }
        /// 是半窗相册选中图片
        if ([self.takeImageUtil respondsToSelector:@selector(assetPickerController:didSelectAssets:)]) {
            [self.takeImageUtil assetPickerController:assetPickerController didSelectAssets:@[asset]];
        }
    }
    [self publishToolDidEdit];
}

/// 取消选中图片
- (void)assetPickerController:(IMYAssetPickerController *)assetPickerController didDeselectAsset:(IMYAssetModel *)asset {
    if (assetPickerController == self.panAssetPicker) {
        if (self.panAssetPicker.isFullScreenStatus) {
            /// 全屏时不响应单点
            return;
        }
        if (asset.assetType == IMYAssetTypeVideo) {
            [self clearVideo];
            self.bottomToolView.publishToolState = TTQPublishToolStateShowPanAssetPick;
            self.tableFooterView.frame = CGRectMake(0, 0, SCREEN_WIDTH, 0);
            self.tableView.tableFooterView = self.tableFooterView;
            [self.tableView reloadData];
        } else {
            /// 是半窗相册取消选中图片
            [self removePickerImageViewsWithArray:@[asset]];
        }
    }
}

#pragma mark - 判断投票视图是否在window 上
- (BOOL)windowIsContainVoteView {
    NSArray *array = [[UIApplication sharedApplication] windows];
    UIView *view = nil;
    for (UIView *window in array) {
        if (CGRectGetHeight(window.bounds) == SCREEN_HEIGHT) {
            view = window;
            break;
        }
    }
    if (!view) {
        return NO;
    }
    return [self.postVoteBackgroundView isDescendantOfView:view];
}

- (void)addKeyboardShowOrHidenNotification {
    @weakify(self);
    [[[[[NSNotificationCenter defaultCenter]
        rac_addObserverForName:UIKeyboardWillShowNotification
                        object:nil] takeUntil:self.rac_willDeallocSignal]
        deliverOnMainThread] subscribeNext:^(NSNotification *notification) {
        @strongify(self);
        if ([self windowIsContainVoteView]) {
            return;
        }
        if ([self.activeTextCell.textView isFirstResponder]) {
            [self.bottomToolView updateAllBtnStatus:YES];
            [self updateVoteEnable];
        }
        if (self.isEmojBtnClick || (self.activeTextCell.textView.inputView == self.emotionView)) {
            [self.bottomToolView setEmotionBtnStatus:YES];
            self.isEmojBtnClick = NO;
        }
        [self hideInspirationViewIfNeeded];
        CGRect keyboardRect =
            [notification.userInfo[UIKeyboardFrameEndUserInfoKey] CGRectValue];
        if (keyboardRect.origin.y < [UIScreen mainScreen].bounds.size.height) {
            float keyboardHeight = keyboardRect.size.height;
            self.keyBoardHeigh = keyboardHeight;
            NSUInteger curve =
                [notification.userInfo[UIKeyboardAnimationCurveUserInfoKey]
                    integerValue];
            curve = curve << 16;
            double duration =
                [notification.userInfo[UIKeyboardAnimationDurationUserInfoKey]
                    doubleValue];
            [self.bottomToolView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.bottom.mas_equalTo(-keyboardHeight); // 键盘高度
            }];
            [self.tableView mas_remakeConstraints:^(MASConstraintMaker *make) {
                @strongify(self);
                make.left.top.and.right.mas_equalTo(0.0);
                make.bottom.mas_equalTo(self.bottomToolView.mas_top).offset(self.subjectView.topicArray.count?-(self.subjectContainerView.imy_height):0);
            }];

            if (self.topicTipsView && self.topicTipsView.superview) {
                if (self.topicTipsView.arrowDirection == IMYDirectionRight) {
                    self.topicTipsView.imy_centerY = self.view.imy_height - keyboardHeight - SCREEN_TABBAR_SAFEBOTTOM_MARGIN - self.bottomToolView.imy_height/2;
                } else {
                    self.topicTipsView.imy_bottom = self.view.imy_height - keyboardHeight - SCREEN_TABBAR_SAFEBOTTOM_MARGIN - self.bottomToolView.imy_height + 35;
                }
            }
            [self animateWithDuration:duration];
        }
    }];
    [[[[[NSNotificationCenter defaultCenter]
        rac_addObserverForName:UIKeyboardWillHideNotification
                        object:nil] takeUntil:self.rac_willDeallocSignal]
        deliverOnMainThread] subscribeNext:^(NSNotification *notification) {
        @strongify(self);
        if ([self windowIsContainVoteView]) {
            return;
        }
        [self.bottomToolView updateAllBtnStatus:YES]; // 键盘收起，更新底部工具栏的状态
        NSUInteger curve =
            [notification.userInfo[UIKeyboardAnimationCurveUserInfoKey]
                integerValue];
        curve = curve << 16;
        double duration =
            [notification.userInfo[UIKeyboardAnimationDurationUserInfoKey]
                doubleValue];
        self.keyBoardHeigh = 0;
        [self setBottomToolViewState:TTQPublishToolStateNormal];
        if (self.topicTipsView && self.topicTipsView.superview) {
            self.topicTipsView.imy_bottom = self.view.imy_height - SCREEN_TABBAR_SAFEBOTTOM_MARGIN - self.bottomToolView.imy_height;
        }
        [self animateWithDuration:duration];
        if (self.topicTipsView) {
            [self.topicTipsView removeFromSuperview];
            self.topicTipsView = nil;
        }
    }];

    [[[[[NSNotificationCenter defaultCenter]
        rac_addObserverForName:UIKeyboardDidShowNotification
                        object:nil] takeUntil:self.rac_willDeallocSignal]
        deliverOnMainThread] subscribeNext:^(NSNotification *notification) {
        @strongify(self);
        if ([self windowIsContainVoteView]) {
            return;
        }
        self.keyBoardShowed = YES;
    }];

    [[[[[NSNotificationCenter defaultCenter]
        rac_addObserverForName:UIKeyboardDidHideNotification
                        object:nil] takeUntil:self.rac_willDeallocSignal]
        deliverOnMainThread] subscribeNext:^(NSNotification *notification) {
        @strongify(self);
        if ([self windowIsContainVoteView]) {
            return;
        }
        self.keyBoardShowed = NO;
    }];

    [[[RACSignal merge:@[
        RACObserve(self.tableView, frame),
        RACObserve(self.tableView, contentSize)
    ]] deliverOnMainThread] subscribeNext:^(id x) {
        @strongify(self);
        [self resetFooterViewPisition:NO];
    }];
}

- (UIView *)emotionView {
    if (_emotionView == nil) {
        IMYVKEmotionKeyboardView *emotionView = [IMYVKEmotionKeyboardView keyboardViewWithDelegate:self];
        [emotionView resetKeyboardWithShowSticker:NO];
        _emotionView = emotionView;
    }
    return _emotionView;
}

- (void)updateRightBtn {
    // TODO: 与外部容器导航栏发布按钮联动
    // 输入内容少于8个字时，【发布】为置灰不可点击状态
    BOOL isRed = [self.viewModel checkPostContent];
    if (self.publishRightBtnState) {
        self.publishRightBtnState(isRed);
    }
}

#pragma mark - 弹出投票半弹窗
- (void)pushNewVoteView {
    //处理键盘切换展示逻辑
    CGFloat mTime = 0.0;
    if (self.keyBoardShowed) {
        [self.view endEditing:YES];
        mTime = 0.1;
    }
    @weakify(self)
        imy_asyncMainBlock(mTime, ^{
            @strongify(self)
                NSArray *array = [[UIApplication sharedApplication] windows];
            UIView *view = nil;
            for (UIView *window in array) {
                if (CGRectGetHeight(window.bounds) == SCREEN_HEIGHT) {
                    view = window;
                    break;
                }
            }
            if (!view) {
                return;
            }
            [view addSubview:self.postVoteBackgroundView];
            [self.postVoteBackgroundView pushKeyBorad];
        });
}

/// 投票弹窗view新样式
- (UIView *)postVoteBackgroundView {
    @weakify(self) if (!_postVoteBackgroundView) {
        _postVoteBackgroundView = [[TTQPostVoteBackgroundView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
        _postVoteBackgroundView.cancleViewBlock = ^{
            @strongify(self)
                self.isVoteShow = NO;
        };
        _postVoteBackgroundView.completeEditorBlock = ^(NSMutableArray *_Nonnull textAry) {
            @strongify(self)
                self.viewModel.draft.dontNeedEmptyVoteTitle = YES;
            [self layoutVoteView:textAry]; //创建投票弹窗
            if ([self validTextView].isFirstResponder ||
                [self validTextViewForImageCell].isFirstResponder) {
                [self scrollToVoteViewAnimation:NO];
            } else {
                [self scrollToVoteViewAnimation:YES];
            }
        };
        //草稿进入有数时
        if (self.viewModel.draft.voteTexts.count > 0) {
            NSMutableArray *voteTitleArray = [NSMutableArray arrayWithArray:self.viewModel.draft.voteTexts];
            if ([self needSetVoteTitleEmpty]) {
                //有草稿且无flag,则说明是从797之前版本升级上来的 这时候塞入一个空字符串到数组中,也就是把标题置空
                [voteTitleArray insertObject:@"" atIndex:0];
            }
            _postVoteBackgroundView.drafAry = voteTitleArray.copy;
        }
    }
    return _postVoteBackgroundView;
}

#pragma mark - 布局投票view
- (void)layoutVoteView:(NSMutableArray *)ary {
    self.viewModel.voteAry = [NSArray arrayWithArray:ary];
    @weakify(self) for (id tmpView in [self.tableFooterView subviews]) {
        if ([tmpView isKindOfClass:[TTQPostVoteShowView class]]) {
            [tmpView removeFromSuperview];
            break;
        }
    }
    BOOL isPk = ary.count < 4 && [TTQPublishTopicViewModel openPkStyle];
    //以下计算需要高度用到的count 需要排除标题
    CGFloat itemHeight = isPk?24:21;
    CGFloat backgroundHeight = 45 + 16 * 2 + (ary.count - 1) * itemHeight + (ary.count - 2) * 16; //行高21；行间距16；16 * 2是上下距离; 44 是标题栏
    TTQPostVoteShowView *view = [[TTQPostVoteShowView alloc] initWithFrame:CGRectMake(12, 10, SCREEN_WIDTH - 24, backgroundHeight) withAry:ary isPk:isPk];
    view.clickDeleteIconBlcok = ^{
        @strongify(self)
        [self endEditing];
        IMYActionSheet *sheet = [IMYActionSheet sheetWithCancelTitle:IMYString(@"取消") otherTitles:@[IMYString(@"删除")] summary:IMYString(@"要删除投票吗？") showInView:self.navigationController.view action:^(NSInteger index) {
            @strongify(self);
            if (index == 1) {
                imy_asyncMainBlock(^{
                    @strongify(self);
                    [self.postVoteBackgroundView deleteAry];
                    self.viewModel.voteAry = nil;
                    self.voteShowView = nil;
                    [view removeFromSuperview];
                    if (self.imagePickerView && [self.tableFooterView.subviews containsObject:self.imagePickerView]) {
                        self.tableFooterView.frame = CGRectMake(0, 0, SCREEN_WIDTH, self.imagePickerView.imy_height);
                        self.imagePickerView.imy_top = 0;
                        self.tableView.tableFooterView = self.tableFooterView;
                    } else {
                        self.tableFooterView.frame = CGRectMake(0, 0, SCREEN_WIDTH, 0);
                        self.tableView.tableFooterView = self.tableFooterView;
                    }
                    // 重新计算cell高度
                    [self.tableView reloadData];
                    self.isShowVoteView = NO;
                    self.viewModel.draft.voteAvailable = NO;
                });
            }
        }];
        sheet.destructiveButtonTextColor = [IMYColor colorWithNormal:kCK_Red_D];
        sheet.destructiveButtonIndex = 1;
    };

    view.clickBgViewBlcok = ^{
        @strongify(self)
            [self pushNewVoteView];
    };
    self.voteShowView = view;
    [self.tableFooterView addSubview:view];
    
    CGFloat tableFootViewHeight = backgroundHeight + 20;
    view.imy_top = 0;
    if (self.imagePickerView && [self.tableFooterView.subviews containsObject:self.imagePickerView]) {
        self.imagePickerView.imy_top = view.imy_bottom + 12;
        tableFootViewHeight = backgroundHeight + 12 + self.imagePickerView.imy_height;
    } else {
        tableFootViewHeight = backgroundHeight;
    }
    self.tableFooterView.frame = CGRectMake(0, 0, SCREEN_WIDTH, tableFootViewHeight);
    self.tableView.tableFooterView = self.tableFooterView;
    [self.tableView reloadData];
    self.isShowVoteView = YES;
    self.viewModel.draft.showVote = YES;
}

- (void)changeContentTextViewInput:(UITextView *)textView {
    if (self.isExperienceTemplatePickerShowing) { // 取消选中
        self.isExperienceTemplatePickerShowing = NO;
        [self.bottomToolView setTemplateBtnStatus:NO];
        // 切换成键盘
        textView.inputView = nil;
        [textView reloadInputViews];
        [self.bottomToolView setEmotionBtnStatus:NO];
        return;
    }
    
    if (textView.inputView != self.emotionView) {
        textView.inputView = [self emotionView];
        [textView reloadInputViews];
        [self.bottomToolView setEmotionBtnStatus:YES];
    } else {
        textView.inputView = nil;
        [textView reloadInputViews];
        [self.bottomToolView setEmotionBtnStatus:NO];
    }
}

- (void)resetKeyboard:(UITextView *)textView {
    if (self.isExperienceTemplatePickerShowing) { // 取消选中
        self.isExperienceTemplatePickerShowing = NO;
        [self.bottomToolView setTemplateBtnStatus:NO];
    }
    if (textView.inputView != nil) {
        textView.inputView = nil;
        [textView reloadInputViews];
    }
    [self.bottomToolView setEmotionBtnStatus:NO];
    self.bottomToolView.aiButton.selected = NO;
}

- (void)scrollToVoteViewAnimation:(BOOL)animation {
    [self.tableView scrollRectToVisible:self.tableFooterView.frame
                               animated:animation];
}

- (void)animateWithDuration:(NSTimeInterval)duration {
    [UIView animateWithDuration:duration
                     animations:^{
                         [self.view layoutSubviews];
                     }
                     completion:^(BOOL finished){}];
}

- (void)showWaterMark {
    if (![IMYPublicAppHelper Yunqi]) {
        // 柚宝宝系列的APP不处理
        if (![[IMYUserDefaults standardUserDefaults] boolForKey:@"tata_img_guide_watermark"]) {
            [[IMYUserDefaults standardUserDefaults] setBool:YES forKey:@"tata_img_guide_watermark"];
            [[IMYUserDefaults standardUserDefaults] synchronize];
            if ([IMYPublicAppHelper shareAppHelper].hasWatermark) {
                NSString *message = IMYString(@"帖子发布成功后，图片将自动添加美\n柚水印。可在\"我-设置-通用-图片水\n印设置\"中进行修改");

                UIAlertController *alert = [UIAlertController alertControllerWithTitle:IMYString(@"图片将自动添加美柚水印") message:message preferredStyle:UIAlertControllerStyleAlert];

                UIAlertAction *cancle = [UIAlertAction actionWithTitle:IMYString(@"我知道了") style:UIAlertActionStyleCancel handler:^(UIAlertAction *_Nonnull action){

                }];

                [cancle setValue:[UIColor imy_colorForKey:kCK_Red_B] forKey:@"_titleTextColor"];
                [alert addAction:cancle];

                [self presentViewController:alert animated:true completion:nil];
            }
        }
    }
}

#pragma mark - valid textview

- (UITextView *)validTextView {
    if (self.activeTextCell.isFirstResponder) {
        return self.activeTextCell.textView;
    }
    return nil;
}

// 找到最底下的文本框
- (UITextView *)nethermostTextView {
    NSIndexPath *indexPath = [NSIndexPath indexPathForRow:MAX(0, self.viewModel.onlyTextContentData.count - 1)
                               inSection:0];
    if (indexPath.row >= 0 &&
        indexPath.row < [self.tableView numberOfRowsInSection:0]) {
        UITableViewCell *cell = [self.tableView cellForRowAtIndexPath:indexPath];
        if (cell == nil) {
            [self.tableView scrollToRowAtIndexPath:indexPath
                                  atScrollPosition:UITableViewScrollPositionMiddle
                                          animated:NO];
            cell = [self.tableView cellForRowAtIndexPath:indexPath];
        }
        if ([cell isKindOfClass:[TTQPublishTextCell class]]) {
            return [(TTQPublishTextCell *)cell textView];
        }
    }
    return nil;
}

#pragma mark - valid titleField | imagecell
- (UITextView *)validTextViewForImageCell {
    return nil;
}

#pragma mark - 获取图片
- (TTQPublishTakeImageUtil *)takeImageUtil {
    if (_takeImageUtil == nil) {
        @weakify(self);
        _takeImageUtil = [[TTQPublishTakeImageUtil alloc] init];
        _takeImageUtil.takeImagesBlock = ^(NSArray *images) {
            imy_asyncMainBlock(^{ // 插入图片的回调
                @strongify(self);
                if (images.count) {
                    NSMutableArray *imageModels = [NSMutableArray array];
                    // 插入图片
                    for (IMYUGCNotePublishImage *obj in images) {
                        TTQPublishImageModel *model = [TTQPublishImageModel new];
                        model.publishNewImage = obj;
                        model.isPublishTopic = YES;
                        model.contentH = [TTQPublishContentUtil computeHeightForImage:model];
                        [imageModels addObject:model];
                    }
                    if (imageModels.count) {
                        [self insertImages:imageModels];
                        imy_asyncMainBlock(0.5, ^{
                            @strongify(self);
                            [self showWaterMark];
                        });
                    }
                } else if (!self.aiOutputView) {
                    if ([self.activeTextCell isKindOfClass:[TTQPublishTextCell class]]) {
                        // 如果没有选中任何图片，且有当前活跃的输入框，需要把键盘弹出来，并定位到活跃输入款的位置
                        NSInteger totalRowCount = self.viewModel.onlyTextContentData.count;
                        if (self.activeIndexPath.row < totalRowCount) {
                            TTQPublishTextModel *textModel =
                                self.viewModel.dataSource[self.activeIndexPath.row];
                            textModel.selectedRange = [self.activeTextCell selectedRange];
                            textModel.shouldUpdateSelectedRange = YES;
                        }
                        if (self.activeIndexPath &&
                            [self.tableView cellForRowAtIndexPath:self.activeIndexPath] ==
                                nil) {
                            [self.tableView
                                scrollToRowAtIndexPath:self.activeIndexPath
                                      atScrollPosition:UITableViewScrollPositionBottom
                                              animated:NO];
                            self.activeTextCell =
                                [self.tableView cellForRowAtIndexPath:self.activeIndexPath];
                        }
                        if (self.activeTextInputModel &&
                            !self.activeTextCell.textView.inputView) {
                            [self resetKeyboard:self.activeTextCell.textView];
                        }
                        [self.activeTextCell becomeFirstResponder];
                    }
                    self.activeTextInputModel = 0;
                    if (self.aiImageData) {
                        self.takeImageUtil.takeImagesBlock(@[self.aiImageData]);
                        self.aiImageData = nil;
                    }
                }

            });
        };

        _takeImageUtil.takeVideoBlock = ^(IMYUGCNotePublishImage *image, IMYAssetModel *asset) {
            @strongify(self);
            imy_asyncMainBlock(^{ // 插入视频的回调
                if (image != nil) {
                    [self layoutVideoView:asset cover:image];
                } else {
                    if ([self.activeTextCell isKindOfClass:[TTQPublishTextCell class]]) {
                        // 如果没有选中任何图片，且有当前活跃的输入框，需要把键盘弹出来，并定位到活跃输入款的位置
                        NSInteger totalRowCount = self.viewModel.onlyTextContentData.count;
                        if (self.activeIndexPath.row < totalRowCount) {
                            TTQPublishTextModel *textModel =
                                self.viewModel.dataSource[self.activeIndexPath.row];
                            textModel.selectedRange = [self.activeTextCell selectedRange];
                            textModel.shouldUpdateSelectedRange = YES;
                        }
                        if (self.activeIndexPath &&
                            [self.tableView cellForRowAtIndexPath:self.activeIndexPath] ==
                                nil) {
                            [self.tableView
                                scrollToRowAtIndexPath:self.activeIndexPath
                                      atScrollPosition:UITableViewScrollPositionBottom
                                              animated:NO];
                            self.activeTextCell =
                                [self.tableView cellForRowAtIndexPath:self.activeIndexPath];
                        }
                        if (self.activeTextInputModel &&
                            !self.activeTextCell.textView.inputView) {
                            [self resetKeyboard:self.activeTextCell.textView];
                        }
                        [self.activeTextCell becomeFirstResponder];
                    }
                    self.activeTextInputModel = 0;
                }
            });
        };
        [_takeImageUtil setAiImageBlock:^(IMYAssetModel *imageAsset) {
            @strongify(self);
            if (!self.aiOutputView) {
                self.viewModel.aiReferer = @"pregnancy_test";
                self.aiOutputView = [self creatAIViewWithType:TTQAiGenerateChanjian];
                self.aiOutputView.hidden = YES;
            }
            [self.navigationController.view addSubview:self.aiOutputView];
            CGFloat targetWidth = SCREEN_WIDTH *SCREEN_SCALE;
            CGSize targetSize = CGSizeMake(SCREEN_WIDTH*SCREEN_SCALE, SCREEN_HEIGHT *SCREEN_SCALE);
            if (imageAsset.fetchPhAsset.pixelWidth) {
                targetSize = CGSizeMake(targetWidth, ceilf(1.f*imageAsset.fetchPhAsset.pixelHeight*targetWidth/imageAsset.fetchPhAsset.pixelWidth));
            }
            /// 这里不用原图尺寸，改为屏幕大小的
            [self.aiOutputView loadImage:[imageAsset thumbnailImageWithSize:targetSize]];
            [UIView animateWithDuration:0.15 animations:^{
                self.aiOutputView.hidden = NO;
            }];
            if (self.takeImageUtil.takeImagesBlock) {
                IMYUGCNotePublishImage *imageData = [self.takeImageUtil transformPublishImageFromAsset:imageAsset];
                [[TTQPublishHelper sharedInstance] fastSaveImageOperationWithImageModel:imageData completion:^(BOOL finished) {

                }];
                self.aiOutputView.imageData = imageData;
                self.aiImageData = imageData;
            }
        }];
    }
    return _takeImageUtil;
}

- (void)takeImage {
    [self endEditing];
    NSInteger maxNum = 9;
    NSInteger curImageNum = [TTQPublishContentUtil validataImageCount:self.viewModel.dataSource];
    if (curImageNum >= maxNum) {
        [UIWindow imy_showTextHUD:IMYString([self contentWarningForImages])];
        return;
    }
    BOOL canAddVideo = YES;
    // 如果已经选择图片，则不能选择视频
//    if (curImageNum > 0) {
//        canAddVideo = NO;
//    }
    // 如果已经有投票，则不能选择视频
    if (self.isShowVoteView) {
        canAddVideo = NO;
    }
    if(self.viewModel.draft.isAnonymous){
        canAddVideo = NO;
    }
    if(self.viewModel.isEdit){ // 仅有图文贴可以编辑，所以不能选视频
        canAddVideo = NO;
    }
    [self.takeImageUtil takeImage:(maxNum - curImageNum) canAddVideo:canAddVideo];
}

- (void)setPanAssetPicker:(IMYPanAssetPickerController *)panAssetPicker {
    _panAssetPicker = panAssetPicker;
    if (panAssetPicker) {
        if (self.viewModel.defaultImage) {
            panAssetPicker.maximumNumberOfSelection = 8;
        } else if (self.viewModel.defaultImages.count) {
            panAssetPicker.maximumNumberOfSelection = MAX(0,9 - self.viewModel.defaultImages.count);
        }
        @weakify(self);
        [panAssetPicker setClickDoneActionBlock:^{
            @strongify(self);
            /// 是半窗相册选中图片
            /// 清空原有的数据
            NSMutableArray *dataSource = [NSMutableArray arrayWithArray:self.viewModel.dataSource];
            dataSource = [dataSource filter:^BOOL(TTQPublishImageModel *element) {
                if ([element isKindOfClass:TTQPublishImageModel.class]) {
                    if (!element.publishNewImage.assetUrl || [element.publishNewImage.assetUrl hasPrefix:@"http"]) {
                        /// 保留带进来的图片
                        return YES;
                    }
                    return NO;
                }
                return YES;
            }];
            NSArray *imageModels = [dataSource filter:^BOOL(TTQPublishImageModel *element) {
                return [element isKindOfClass:TTQPublishImageModel.class];
            }];

            self.viewModel.dataSource = dataSource;
            imageModels = [imageModels flatMap:^id _Nonnull(TTQPublishImageModel *element) {
                /// 转成pickerView需要的类型
                return element.publishNewImage;
            }];
            if (self.imagePickerView) {
                [self.imagePickerView updateImageData:imageModels isScrollLastIndex:YES];
            }
            IMYAssetModel *firstAsset = self.panAssetPicker.selectedAssetArray.firstObject;
            if (firstAsset.assetType != IMYAssetTypeVideo && self.viewModel.video) {
                /// 如果本次之前选择的是视频，但进全屏后取消视频选择了图片，这时要清空视频数据及展示
                [self clearVideo];
                if (self.panAssetPicker.selectedAssetArray.count == 0) {
                    // 如果没有任何数据，清除视频要刷新footerview，重新计算高度
                    self.tableFooterView.frame = CGRectMake(0, 0, SCREEN_WIDTH, 0);
                    self.tableView.tableFooterView = self.tableFooterView;
                    [self.tableView reloadData];
                }
            }
            [self removePickerViewWith:YES];
            /// 重走正常添加图片的回调。
            if ([self.takeImageUtil respondsToSelector:@selector(assetPickerController:didSelectAssets:)]) {
                [self.takeImageUtil assetPickerController:self.panAssetPicker didSelectAssets:self.panAssetPicker.selectedAssetArray];
            }
            [self positionAtIndex:[NSIndexPath indexPathForRow:0 inSection:0]];
        }];
        if (self.viewModel.publishFromType == TTQPublishFromNew && self.viewModel.subjectModel && self.panAssetShowBlock) {
            self.panAssetShowBlock(NO, NO);
        }
    }
}

#pragma mark - TTQPublishTextEditDelegate 文本输入框cell中文本输入的回调方法

- (NSString *)ttq_placeholderForCell:(TTQPublishTextCell *)textCell
                           indexPath:(NSIndexPath *)indexPath
                           textModel:(TTQPublishTextModel *)textModel {
    return [self placeHolderConfigContentText];
}

// 改动的处理（特别是最多字数的提示跟截取）
- (BOOL)ttq_textViewShouldChange:(SZTextView *)textView
                       textModel:(TTQPublishContentModel *)model {
    if (textView.markedTextRange == nil) {
        NSInteger cutOut = [TTQPublishContentUtil
            cutOutTextForOverflow:self.viewModel.dataSource
                          without:model
                           maxNum:self.viewModel.publishConfiguration
                                      .bodyContentMaxNum];
        cutOut += textView.text.ttq_textLength;
        if (cutOut > 0) {
            cutOut = MAX(0, textView.text.ttq_textLength - cutOut);
        }
        if (model.richContentType == TTQPublishContentTypeText) {
            [(TTQPublishTextModel *)model setText:[IMYREmoticonManager encodeEmojiText:textView.attributedText]];
        }
    }
    self.mLimitLabel.text = [self titleForContentCountLabel];
    [self updateRightBtn]; // 标题不进行字数统计
    if (imy_isEmptyString(textView.text)) {
        /// 重置请求标志
        self.viewModel.hasRequestAITitle = NO;
    }
    [self requestAiTitleWithForce:NO];
    [self showPolishTipIfNeeded];
    return YES;
}


- (BOOL)ttq_textViewShouldBeginEditing:(SZTextView *)textView
                             textModel:(TTQPublishContentModel *)model {
    //标题是否是正在输入
    [self.bottomToolView updateAllBtnStatus:YES];
    [self postBIwithPublishInfoType:@"编辑内容区域"];
    [self updateVoteEnable];
    self.bottomToolView.publishToolState = TTQPublishToolStateNormal;
    return YES;
}

- (void)ttq_textViewDidBeginEditing:(SZTextView *)textView
                          textModel:(TTQPublishContentModel *)model {
    [self publishToolDidEdit];
    [self hideAssetPicker];
    [self.bottomToolView setEmotionBtnStatus:(textView.inputView == self.emotionView)?YES:NO];
    [self updateVoteEnable];
}

- (BOOL)textViewShouldEndEditing:(SZTextView *)textView
                       textModel:(TTQPublishContentModel *)model {
    //    [self setBottomToolViewState:TTQPublishToolStateNormal];
    return YES;
}

/**
 判断位置textView当前光标位置是否是可以删除的表情
 */
- (BOOL)ttq_textViewIsEmoji:(SZTextView *)textView {
    if (self.activeTextCell) {
        NSRange range = textView.selectedRange;
        if (range.length == 0) {
            if (range.location > 0) {
                range.location -= 1;
                range.length = 1;
            }
        }

        BOOL isNotEmoji = [IMYREmoticonManager emojiWithTextView:textView
                                         shouldChangeTextInRange:range
                                                 replacementText:nil];
        if (isNotEmoji == NO) {
            if (range.length == 0 &&
                range.location <= 0) { //这一步是为了知道是否在段头了还在点击删除按钮
                [self.activeTextCell textView:self.activeTextCell.textView
                      shouldChangeTextInRange:range
                              replacementText:nil];
            } else {
                [self.activeTextCell textViewDidChange:self.activeTextCell.textView];
            }
        }
        return (NO == isNotEmoji);
    }
    return NO;
}

- (void)textViewDidEndEditing:(SZTextView *)textView
                    textModel:(TTQPublishContentModel *)model {
    [self richDidEndEditing];
    self.associateTopicsView.hidden = YES;
    self.subjectContainerView.hidden = NO;
    self.tableView.contentInset = UIEdgeInsetsZero;
}

- (void)ttq_textViewEditing:(id)textCell
                   textView:(SZTextView *)textView
                  indexPath:(NSIndexPath *)indexPath
                  textModel:(TTQPublishTextModel *)textModel {
}

- (void)ttq_textViewChangeInputView:(SZTextView *)textView
                          inputMode:(NSInteger)inputMode {
    if (inputMode == 0 || (inputMode == 1 && textView.inputView) ||
        (inputMode == 2 && textView.inputView == nil)) {
        [self resetKeyboard:textView];
    }
}

#pragma mark - 定位到某一行

/**
 定位到指定的元素
 */
- (void)positionAtIndex:(NSIndexPath *)indexPath {
    UITableViewCell *cell = [self.tableView cellForRowAtIndexPath:indexPath];
    if ([cell isKindOfClass:[TTQPublishTextCell class]]) {
        [cell becomeFirstResponder];
    }
}

#pragma mark - 各种插入处理

- (void)insertImages:(NSArray<TTQPublishContentModel *> *)images {
    if (images.count == 0) {
        return;
    }
    [self insertImagesIn865Style:images];
}

- (BOOL)deletedImage:(TTQPublishImageModel *)imageModel {
    if (imageModel == nil ||
        ![imageModel isKindOfClass:[TTQPublishImageModel class]]) {
        return NO;
    }
    return [self deletedImageIn865Style:imageModel];
}

- (void)ttq_textViewDidHidePlaceHolder:(NSString *)tags {
    for (int i = 0; i < self.tagsArray.count; i++) {
        TTQPublishTemplateItemModel *item = self.tagsArray[i];
        NSString *tagString = [NSString stringWithFormat:@"%@\n", item.title];
        if ([tags isEqualToString:tagString]) {
            self.placeHolderArray[i] = @(YES);
        }
    }
}

#pragma mark - 865 图片添加，删除

- (void)insertImagesIn865Style:(NSArray<TTQPublishContentModel *> *)images {
    NSMutableArray *dataSource = [self.viewModel.dataSource mutableCopy];
    [dataSource addObjectsFromArray:images];
    self.viewModel.dataSource = [dataSource copy];
    [self addBottomImagePickerIfNeeded:images];
}

- (BOOL)deletedImageIn865Style:(TTQPublishImageModel *)imageModel {
    NSMutableArray *dataSource = [self.viewModel.dataSource mutableCopy];
    [dataSource removeObject:imageModel];
    [self richDidEndEditing];
    self.viewModel.dataSource = [dataSource copy];
    if (self.panAssetPicker) {
        NSMutableArray *assetModels = [NSMutableArray arrayWithCapacity:self.viewModel.dataSource.count];
        for (TTQPublishImageModel *imageModel in self.viewModel.dataSource) {
            if ([imageModel isKindOfClass:TTQPublishImageModel.class]) {
                IMYAssetModel *asset = [[IMYAssetModel alloc] initWithIdentifier:imageModel.publishNewImage.assetUrl];
                if (asset) {
                    [assetModels addObject:asset];
                }
            }
        }
        self.panAssetPicker.selectedAssetArray = assetModels;
        [self.panAssetPicker reloadCollectionView];
    }
    if (imageModel.publishNewImage) {
        if ([self isDraftImage:imageModel.publishNewImage.filePath]) {
            [self.deletedImageFileCaches addObject:imageModel.publishNewImage.filePath];
        } else {
            [[TTQPublishHelper sharedInstance] deleteCacheImageWithPath:imageModel.publishNewImage.filePath];
        }
    }
    return YES;
}

- (void)hideAssetPicker {
    [self.panAssetPicker dimissPanController];
    self.panAssetPicker = nil;
}

- (BOOL)isDraftImage:(NSString *)filePath {
    for (TTQPublishImageModel *content in self.viewModel.originalDraft.topicImagesArray) {
        if ([content isKindOfClass:TTQPublishImageModel.class]) {
            if ([content.publishNewImage.filePath isEqualToString:filePath]) {
                return YES;
            }
        }
    }
    return NO;
}

#pragma mark - UITableViewDelegate

- (CGFloat)tableView:(UITableView *)tableView
    heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    TTQPublishContentModel *content;
    content = self.viewModel.onlyTextContentData[indexPath.row];

    if (content.contentH == 0) {
        if (content.richContentType == TTQPublishContentTypeImage) {
            CGFloat contentH = [TTQPublishContentUtil
                computeHeightForImage:(TTQPublishImageModel *)content];
                content.contentH = contentH;
        } else {
            content.contentH = [TTQPublishContentUtil
                computeHeightForText:[(TTQPublishTextModel *)content text]];
        }
    }

    if (content.richContentType == TTQPublishContentTypeImage) {
        return content.contentH + 2;
    }
    CGFloat minTextHeight = bottomToolViewUpH + 286 + self.safeBottom_Margin; //bottomToolView弹出相册时的高度计算
    minTextHeight += 50; // 距离bottomToolView 50个像素
    minTextHeight += kPublishTopicImagePickerViewHeight; // 到这里算出图片或者视频或者投票插入view的Y值
    minTextHeight = SCREEN_HEIGHT - SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT - minTextHeight; // 计算文本最小高度
    CGFloat contentH = MAX(minTextHeight, content.contentH + 10); //
    return contentH;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    TTQPublishTextCell *cell = tableView.visibleCells.firstObject;
    if ([cell isKindOfClass:TTQPublishTextCell.class]) {
        if (![cell.textView isFirstResponder]) {
            [cell.textView becomeFirstResponder];
        }
        [self resetKeyboard:cell.textView];
    }
}

#pragma mark - UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView
    numberOfRowsInSection:(NSInteger)section {
    return self.viewModel.onlyTextContentData.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView
         cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    TTQPublishContentModel *model;
    model = self.viewModel.onlyTextContentData[indexPath.row];
    TTQPublishTextCell *cell = [tableView
        dequeueReusableCellWithIdentifier:NSStringFromClass(
                                                            [TTQPublishTextCell class]) forIndexPath:indexPath];
    cell.delegate = self;
    cell.maxWordLimit = self.viewModel.publishConfiguration.bodyContentMaxNum;
    if (!self.viewModel.isUpdatingFlow) {
        [cell updateWith:(TTQPublishTextModel *)model indexPath:indexPath];
    }
    [cell updateWithPlaceHolderStatus:self.placeHolderArray]; // 同步目前的tag隐藏状态

    @weakify(self);
    cell.updateLimitLabelBlock = ^(NSString *str) {
        @strongify(self)
            [(TTQPublishTextModel *)model setText:str];
        self.mLimitLabel.text = [self titleForContentCountLabel];
    };
    
    [cell setUpdateTopicsBlock:^{
        @strongify(self);
        [self updateRecommendTopics];
    }];

    [cell setTextViewDidChangeBlock:^{
        @strongify(self);
        if (!self.reportTextViewChange) {
            self.reportTextViewChange = YES;
            [self postBIwithPublishInfoType:@"改动内容"];
        }
    }];
    
    [cell setTopicsInputBlock:^(BOOL showTopicsInput, NSString *keyword, NSString *content) {
        @strongify(self);
        self.associateTopicsView.hidden = !showTopicsInput;
        if (showTopicsInput) {
            [self.associateTopicsView reloadTableWithKeyWord:keyword publishContent:content];
        } else {
            [self.associateTopicsView reloadTableWithNilData];
        }
        self.subjectContainerView.hidden = showTopicsInput;
        [self.view bringSubviewToFront:self.associateTopicsView];
        self.tableView.contentInset = showTopicsInput?UIEdgeInsetsMake(0, 0, self.associateTopicsView.imy_height - self.subjectContainerView.imy_height, 0):UIEdgeInsetsZero;
    }];
    
    if (self.viewModel.draft.templateModel.sub_titles.count > 0) { // 存在模板
        if (self.needAddExperience) {                              // 需要插入模板
            self.needAddExperience = NO;
            self.templateIndexPath = nil;
            self.tagsArray = [NSMutableArray new];
            self.placeHolderArray = [NSMutableArray new];
            NSString *tpl_subject = self.viewModel.draft.templateModel.subject_name;
            BOOL needAppendSubject = NO;
            if (imy_isNotEmptyString(tpl_subject)) {
                if (!self.viewModel.subjectModel) {
                    needAppendSubject = YES;
                } else if (![self.viewModel.subjectModel.name isEqualToString:tpl_subject]) {
                    needAppendSubject = YES;
                }
            }
            if (needAppendSubject) {
                /// 与带入话题不一致时才会插入模板的话题
                self.selectRange = [cell insertContent:[NSString stringWithFormat:@"#%@",self.viewModel.draft.templateModel.subject_name]];
            }
            if (self.selectRange.location == NSNotFound) {
                /// 初始进来的
                self.selectRange = NSMakeRange(cell.textView.text.length, 0);
            }
            [((TTQPublishTextCell *)cell) appendTagsToTextView:self.viewModel.draft.templateModel.sub_titles range:self.selectRange showKeyboard:!self.autoFillTemplate]; // 自动填充模板,不要展示键盘
            self.autoFillTemplate = NO;
            self.tagsArray = self.viewModel.draft.templateModel.sub_titles;
            for (id tag in self.tagsArray) {
                [self.placeHolderArray addObject:@(NO)];
            }
            [((TTQPublishTextCell *)cell) becomeFirstResponder];
        } else {
            @weakify(self);
            imy_asyncMainBlock(0.2, ^{
                @strongify(self);
                [((TTQPublishTextCell *)cell) detactTagsInTextView:self.tagsArray placeHolders:self.placeHolderArray];
                [((TTQPublishTextCell *)cell) rearrangePlaceHolder:self.placeHolderArray]; // 等textview排列正常后重新定位占位
            });
        }
    }
    [cell setInspirationActionBlock:^(NSString *url) {
        @strongify(self);
        if (imy_isNotEmptyString(url)) {
            if ([url isEqualToString:@"chanjian"]) {
                [self chanjianAction];
            } else {
                [[IMYURIManager sharedInstance] runActionWithString:url];
            }
        }
    }];
    
    self.activeTextCell = cell;
    self.activeIndexPath = indexPath;
    return cell;
}

#pragma mark - UIGestureRecognizerDelegate

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer
    shouldRecognizeSimultaneouslyWithGestureRecognizer:
        (UIGestureRecognizer *)otherGestureRecognizer {
    return gestureRecognizer == self.tableTapGesture;
}

- (void)didTapTableView:(UITapGestureRecognizer *)ges {
    CGPoint location = [ges locationInView:self.tableView];
    
    if (location.y > self.tableView.tableFooterView.imy_bottom &&
        (!self.subjectContainerView || location.y < self.subjectContainerView.imy_top + 15)) {
        if (self.viewModel.dataSource.count) {
            NSIndexPath *lastIndexPath = [NSIndexPath indexPathForRow:self.viewModel.onlyTextContentData.count - 1
                                                                   inSection:0];
            UITableViewCell *cell = [self.tableView cellForRowAtIndexPath:lastIndexPath];
            if ([cell isKindOfClass:[TTQPublishTextCell class]]) {
                if (cell.isFirstResponder) {
                    if ([(TTQPublishTextCell *)cell textView].inputView) {
                        [self resetKeyboard:[(TTQPublishTextCell *)
                                                                 cell textView]];
                    }
                } else {
                    [(TTQPublishTextCell *)cell becomeFirstResponder];
                }
            }
        }
    }
    else if (self.tableView.tableFooterView.imy_height > 1) {
        /// 修复选中图片后，点击空白不响应性键盘的bug
        if (location.y < self.tableView.tableFooterView.imy_top) {
            UITableViewCell *cell = [self.tableView cellForRowAtIndexPath:[NSIndexPath indexPathForRow:0 inSection:0]];
            if ([cell isKindOfClass:[TTQPublishTextCell class]]) {
                UIView *textView = ((TTQPublishTextCell *)cell).textView;
                CGRect textViewRect = [textView convertRect:textView.bounds toView:self.tableView];
                if (CGRectContainsPoint(textViewRect, location)) {
                    return;
                }
                if (cell.isFirstResponder) {
                    if ([(TTQPublishTextCell *)cell textView].inputView) {
                        [self resetKeyboard:[(TTQPublishTextCell *)
                                                                 cell textView]];
                    }
                } else {
                    [(TTQPublishTextCell *)cell becomeFirstResponder];
                }
            }
        }
    }
}

- (UIImage *)getDraftImageFromLocalPath:(NSString *)assetURL draftURL:(NSString *)draftURL {
    __block UIImage *image = [[FLAnimatedImage alloc] init]; //流里用来展示的图片(编辑过后的最终图)

    if ([assetURL containsString:@"com.imy.svrrecord/imageEdit"]) {
        //传入的是保存到本地的路径 说明是裁剪后的图片, 覆盖安装会导致路径变动 需要重新组装一下
        NSArray *imageURLStringArr = [assetURL componentsSeparatedByString:@"images/"];
        NSString *tempath = assetURL.copy;
        if (imageURLStringArr.count > 1) {
            tempath = [NSString stringWithFormat:@"%@/%@", [SVRPathManager imageEditImageSavePath:[IMYPublicAppHelper shareAppHelper].userid], imageURLStringArr[1]];
        }
        image = [UIImage imy_imageWithFilePath:tempath];
    } else {
        if ([assetURL containsString:@"imy-default/SDWebImageCache"]) {
            //达人编辑的图片是SDWebimage的缓存图片
            NSArray *imageURLStringArr = [assetURL componentsSeparatedByString:@"markdot"];
            NSString *tempPath = @"";
            if (imageURLStringArr.count > 1) {
                tempPath = imageURLStringArr[0];
            }
            image = [UIImage imy_imageWithFilePath:tempPath]; //编辑帖子,图片是存在SDWebimage的缓存路径下的
        } else {
            //图片在相册里
            NSString *tempPath = @"";
            if (imy_isNotEmptyString(draftURL)) {
                tempPath = draftURL;
            }
            IMYAssetModel *assetModel = [[IMYAssetModel alloc] initWithIdentifier:tempPath];
            PHAsset *asset = [PHAsset fetchAssetsWithLocalIdentifiers:@[tempPath] options:nil].firstObject;
            PHImageRequestOptions *requestOptions = [[PHImageRequestOptions alloc] init];
            requestOptions.synchronous = YES;
            requestOptions.networkAccessAllowed = YES;
            [[[IMYAssetsManager sharedInstance] cachingImageManager] requestImageDataForAsset:asset options:requestOptions resultHandler:^(NSData *_Nullable imageData, NSString *_Nullable dataUTI, UIImageOrientation orientation, NSDictionary *_Nullable info) {
                if (info) {
                    NSMutableDictionary *dic = [[NSMutableDictionary alloc] init];
                    if ([dataUTI containsString:@".gif"]) {
                        image = [UIImage sd_animatedGIFWithData:imageData]; //动图
                    } else {
                        image = [UIImage imageWithData:imageData];
                    }
                }
            }];
        }
    }
    return image;
}

#pragma mark - 工具栏更多按钮

- (void)bottomToolMoreButtonAction {
    TTQPublishSettingViewController *vc = [[TTQPublishSettingViewController alloc] initWithViewModel:self.viewModel];
    vc.publish_entrance = self.publish_entrance;
    @weakify(self);
    [vc setSelectForumBlock:^(TTQForumModel * _Nonnull model) {
        @weakify(self);
        if (self.viewModel.draft.forum_id != model.forum_id) {
            self.viewModel.draft.forum_id = model.forum_id;
            self.viewModel.draft.forum_name = model.name;
        } else {
            self.viewModel.draft.forum_id = 0;
            self.viewModel.draft.forum_name = nil;
        }
    }];
    [vc setSelectVisibleBlock:^(TTQPublishVisibleState state) {
        @weakify(self);
        if (self.viewModel.draft.visibleState != state) {
            if (state == TTQPublishVisibleAnonymous) {
                self.viewModel.draft.isAnonymous = YES;
                self.viewModel.draft.visible = 1;
            } else if (state == TTQPublishVisibleOnlySelf) {
                self.viewModel.draft.visible = 2;
                self.viewModel.draft.isAnonymous = NO;
            } else {
                self.viewModel.draft.visible = 1;
                self.viewModel.draft.isAnonymous = NO;
            }
            if (self.panAssetPicker) {
                BOOL enableVideo = [self.viewModel.draft visibleState] != TTQPublishVisibleAnonymous;
                if (self.panAssetPicker.selectedAssetArray.count) {
                    /// 如果已选择了图片，就无法选择视频
                    IMYAssetModel *first = self.panAssetPicker.selectedAssetArray.firstObject;
                    if (first.assetType != IMYAssetTypeVideo) {
                        enableVideo = NO;
                    }
                }
                [self.panAssetPicker changeVideoAssetEnable:enableVideo];
            }
        }
    }];
    [self imy_push:vc];
}

#pragma mark - 查看大图

- (void)showWithPhotos:(NSMutableArray *)photos atIndex:(NSUInteger)index {
    IMYPhotoBrowser *photoBrowser =
        [IMYPhotoBrowser showWithPhotos:photos atIndex:index];
    photoBrowser.pageControlStyle = IMYBrowserPageControlStyleText;
}

//判断是不是从797之前版本升级上来的 如果是的话 则把标题都置空
- (BOOL)needSetVoteTitleEmpty {
    // flag
    return !self.viewModel.draft.dontNeedEmptyVoteTitle;
}

#pragma mark - Ai

- (void)startRequestAi {
    [self.viewModel setUpdateFlowContentBlock:^(TTQPublishModel *model) {
        TTQPublishTextModel *data = self.viewModel.onlyTextContentData.firstObject;
//        data.text = model.content;
        if (model.flowComplete && model.flowContent.length && [model.flowContent isEqualToString:model.flowPrintContent]) {
            data.aiComplete = YES;
            [self.aiOutputView updateContent:model.flowPrintContent];
            [self endAiOutputWithUpdate:NO];
            [self postAiOutBIEvent:@"生文完成"];
        } else {
            [self.aiOutputView updateContent:model.flowPrintContent];
        }
    }];
    
    [self.viewModel setAiOutputErrorBlock:^(NSError *error) {
        imy_asyncMainBlock(^{
            [self.aiOutputView updateRetryView];
            self.viewModel.draft.flowPrintContent = nil;
            self.viewModel.draft.flowContent = nil;
            self.viewModel.isUpdatingFlow = NO;
            [self.viewModel cancleSSERequest];
            [self postAiOutBIEvent:@"接口报错"];
        });
    }];
    self.viewModel.stream_type = @"content_generate";
    [self.viewModel startSSERequest];
}

- (TTQAiGenerateView *)creatAIViewWithType:(TTQAiGenerateType)type {
    TTQAiGenerateView *aiOutputView = [[TTQAiGenerateView alloc] initWithContentType:type];
    aiOutputView.aiReferer = self.viewModel.aiReferer;
    @weakify(self,aiOutputView);
    [aiOutputView setSuspendActionBlock:^{
        @strongify(self,aiOutputView);
        self.viewModel.isUpdatingFlow = NO;
        [self.viewModel cancleSSERequest];
        [aiOutputView updateContent:self.viewModel.draft.flowContent];
        [self postAiOutBIEvent:@"用户终止"];
    }];
    
    [aiOutputView setRetryActionBlock:^{
        @strongify(self,aiOutputView);
        if (![IMYNetState networkEnable]) {
            [aiOutputView updateRetryView];
            return;
        }
        [self startRequestAi];
        [aiOutputView hideRetryView];
    }];
    
    [aiOutputView setStartRequestBlock:^(NSString * _Nonnull content, NSString * _Nonnull referer) {
        @strongify(self);
        if (!self.viewModel.aiInit) {
            self.viewModel.promise_content = content;
            self.viewModel.aiReferer = referer;
        } else if (referer && ![referer isEqualToString:self.viewModel.aiReferer]) {
            self.viewModel.promise_content = content;
            self.viewModel.aiReferer = referer;
        }
        [self startRequestAi];
    }];
    
    [aiOutputView setUseContentBlock:^(NSString * _Nonnull content) {
        @strongify(self);
        if (imy_isEmptyString(content)) {
            /// 弃用了ai内容，结束输出
            self.viewModel.draft.flowContent = nil;
        } else {
            if (type != TTQAiGeneraterAutoNewLine) {
                /// 非自动换行的要带上ai标识
                self.viewModel.draft.useAi = YES;
            }
            [self.viewModel.draft appendAiRefererToHistory:self.aiOutputView.aiReferer];
        }
        [self endAiOutputWithUpdate:YES];
        [self updateRightBtn];
        self.viewModel.draft.flowContent = nil;
        [self requestAiTitleWithForce:NO];
        if (self.aiImageData) {
            self.takeImageUtil.takeImagesBlock(@[self.aiImageData]);
            self.aiImageData = nil;
        }
    }];
    
    [aiOutputView setSelectImageBlock:^{
        @strongify(self);
        self.takeImageUtil.sceneType = IMYAssetPickerSceneAiChanjian;
        [self.takeImageUtil takeImage:1 canAddVideo:NO];
    }];
    
    [aiOutputView setRemoveBlock:^(BOOL realRemove) {
        @strongify(self);
        if (realRemove) {
            [self endAiOutputWithUpdate:YES];
            if (self.aiImageData) {
                self.takeImageUtil.takeImagesBlock(@[self.aiImageData]);
                self.aiImageData = nil;
            }
        } else {
            [UIView animateWithDuration:0.15 delay:0.5 options:UIViewAnimationOptionCurveEaseInOut animations:^{
                self.aiOutputView.alpha = 0;
                [self setBottomToolViewState:TTQPublishToolStateNormal];
                [self.view layoutIfNeeded];
            } completion:^(BOOL finished) {
                [self.aiOutputView removeFromSuperview];
                self.aiOutputView = nil;
            }];
        }
    }];
    return aiOutputView;
}

/// 手动结束时需要将已获取的flowContent 更新显示到 content里
- (void)endAiOutputWithUpdate:(BOOL)update {
    self.viewModel.isUpdatingFlow = NO;
    [self.viewModel cancleSSERequest];
    if (update) {
        TTQPublishTextModel *data = self.viewModel.onlyTextContentData.firstObject;
        NSString *current = data.text;
        if (imy_isNotEmptyString(current) && imy_isNotEmptyString(self.viewModel.draft.flowContent) && self.aiOutputView.aiType == TTQAiGenerateChanjian) {
            current = [NSString stringWithFormat:@"%@\n%@",current, self.viewModel.draft.flowContent];
        } else if (imy_isNotEmptyString(current) && self.aiOutputView.aiType == TTQAiGenerateChanjian) {
            /// 维持原样
        } else if ([self.aiOutputView isEditContent]) {
            if (imy_isNotEmptyString(self.viewModel.draft.flowContent)) {
                current = self.viewModel.draft.flowContent;
            }
        } else {
             current = self.viewModel.draft.flowContent;
        }
        data.text = current;
        if (imy_isNotEmptyString(self.viewModel.subjectModel.name) && self.aiOutputView.aiType == TTQAiGenerateNormal) {
            if (imy_isNotEmptyString(data.text)) {
                if (![data.text containsString:[NSString stringWithFormat:@"#%@",self.viewModel.subjectModel.name]]) {
                    data.text = [NSString stringWithFormat:@"%@#%@",data.text,self.viewModel.subjectModel.name];
                }
            } else {
                data.text = [NSString stringWithFormat:@"#%@",self.viewModel.subjectModel.name];
            }
        }
        NSString *string = [self cutContentToLimit:data.text];
        data.text = string;
        data.contentH = [TTQPublishContentUtil
                             computeHeightForText:[data text]];;
        [self.tableView reloadData];
        [UIView animateWithDuration:0.15 delay:0.5 options:UIViewAnimationOptionCurveEaseInOut animations:^{
            self.aiOutputView.alpha = 0;
            [self setBottomToolViewState:TTQPublishToolStateNormal];
            [self.view layoutIfNeeded];
        } completion:^(BOOL finished) {
            if (imy_isNotEmptyString(self.viewModel.bubble_text) && [self.viewModel.bubble_position isEqualToString:@"pic"] && !self.hasShowHugTips) {
                IMYHugTipsView *tipView = [IMYHugTipsView tipsView];
                tipView.arrowDirection = IMYDirectionLeft;
                [tipView updateText:self.viewModel.bubble_text];
                [tipView showInView:self.view targetView:self.bottomToolView.picButton];
                self.topicTipsView = tipView;
                self.viewModel.bubble_text = self.viewModel.bubble_position = nil;
                self.hasShowHugTips = YES;
            }
            [self.aiOutputView removeFromSuperview];
            self.aiOutputView = nil;
        }];
    } else {
        [self.aiOutputView updateContentComplete];
    }
    [self updateRightBtn];
}

- (void)postAiOutBIEvent:(NSString *)type {
    [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_twfby_aiswtz",@"action":@2,@"public_type":type,@"public_info":self.aiOutputView.aiReferer} headers:nil completed:nil];
}

- (void)aiPublishBIEvent {
    if (imy_isNotEmptyString(self.viewModel.draft.aiReferHistory)) {
        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"TopicPublish_PublishBotton_AI",@"action":@2,@"public_type":self.viewModel.draft.aiReferHistory
                                                       } headers:nil completed:nil];
    }
}

#pragma mark -

- (TTQPublishExperienceTemplatePickerView *)experienceTemplatePickerView {
    if (!_experienceTemplatePickerView) {
        _experienceTemplatePickerView = [[TTQPublishExperienceTemplatePickerView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 292 +SCREEN_TABBAR_SAFEBOTTOM_MARGIN)];
        @weakify(self);
        [_experienceTemplatePickerView setSelectedTemplateHandler:^(TTQPublishTemplateModel *_Nonnull templateModel, NSInteger index) {
            @strongify(self);
            if (self.viewModel.draft.templateModel) {
                if (self.viewModel.draft.templateModel.templateID == templateModel.templateID) {
                    [UIWindow imy_showTextHUD:IMYString(@"正在使用该模板")];
                } else {
                    UITextView *textView = [self validTextView];
                    if (textView == nil) { // 如果当前没有活跃的文本框，就找到文末的文本框
                        textView = [self nethermostTextView];
                    }
                    textView.inputView = nil;
                    self.isExperienceTemplatePickerShowing = NO;
                    [textView resignFirstResponder];

                    [UIAlertController imy_showAlertViewWithTitle:nil message:@"切换模板后，已输入的内容将会被清空，是否切换模板？" cancelButtonTitle:@"取消" otherButtonTitles:@[@"切换模板"] handler:^(UIAlertController *alertController, NSInteger buttonIndex) {
                        @strongify(self);
                        if (buttonIndex == 1) {
                            // 所有的文本清空
                            self.viewModel.subjectModel = nil;
                            self.selectRange = NSMakeRange(0, 0);
                            [self cleanAllEditingContentWhenChangeTemplate];
                            self.needAddExperience = YES;
                            self.viewModel.draft.templateModel = templateModel;
                            [self.tableView reloadData];
                            [self.bottomToolView setTemplateBtnStatus:NO];
                            self.isExperienceTemplatePickerShowing = NO;
                            [self setBottomToolViewState:TTQPublishToolStateNormal];
                        }
                    }];
                }
            } else {
                self.needAddExperience = YES;
                self.viewModel.draft.templateModel = templateModel;

                self.templateIndexPath = self.activeIndexPath;
                UITextView *textView = [self validTextView];
                self.selectRange = textView.selectedRange; // 保留键盘未消失前的光标位置
                [self.tableView reloadData];
                textView.inputView = nil;
                [textView reloadInputViews];
                [self.bottomToolView setTemplateBtnStatus:NO];
                self.isExperienceTemplatePickerShowing = NO;
                [self setBottomToolViewState:TTQPublishToolStateNormal];
            }
        }];
    }
    return _experienceTemplatePickerView;
}

#pragma mark - 创作助手

- (TTQPublishInspirationView *)inspirationView {
    if (!_inspirationView) {
        _inspirationView = [[TTQPublishInspirationView alloc] initWithFrame:CGRectMake(0, SCREEN_HEIGHT, SCREEN_WIDTH,286 +SCREEN_TABBAR_SAFEBOTTOM_MARGIN)];
        @weakify(self);
        [_inspirationView setItemClickBlock:^(TTQAiToolItemType type) {
            if (type == TTQAiToolRunse) {
                [self runseAction];
                [self postBIwithPublishInfoType:@"一键润色"];
            } else if (type == TTQAiToolChanjian) {
                [self chanjianAction];
                [self postBIwithPublishInfoType:@"产检报告"];
            } else if (type == TTQAiToolNewLine) {
                [self aiNewLineAction];
                [self postBIwithPublishInfoType:@"智能分段"];
            }
            [self setBottomToolViewState:TTQPublishToolStateNormal];
        }];
        [_inspirationView imy_showLineForDirection:IMYDirectionUp];
        [self.view addSubview:_inspirationView];
    }
    return _inspirationView;
}

- (void)runseAction {
    if (![IMYNetState networkEnable]) {
        [UIView imy_showTextHUD:kStatusText_networkDisconnectCache];
        return;
    }
    if ([self.viewModel current_content_word_num] < self.viewModel.polish_up_word_limit) {
        [UIView imy_showTextHUD:[NSString stringWithFormat:@"帖子正文需至少输入%ld个字",self.viewModel.polish_up_word_limit]];
        return;
    }
    self.aiOutputView = [self creatAIViewWithType:TTQAiGeneraterRunse];
    self.aiOutputView.aiReferer = @"polish_content";
    [self.aiOutputView loadRunseContent:[self.viewModel current_content]];
    [self.navigationController.view addSubview:self.aiOutputView];
}

- (void)chanjianAction {
    if (self.viewModel.draft.showVideo) {
        [UIView imy_showTextHUD:@"已选择视频，不能同时上传图片"];
        return;
    }
    if (self.imagePickerView.currentImageCount >= 9) {
        [UIView imy_showTextHUD:@"最多可添加9张图"];
        return;
    }
    self.takeImageUtil.sceneType = IMYAssetPickerSceneAiChanjian;
    [self.takeImageUtil takeImage:1 canAddVideo:NO];
    if (self.panAssetPicker) {
        [self hideAssetPicker];
        [self setBottomToolViewState:TTQPublishToolStateNormal];
    }
    [self publishToolDidEdit];
}

- (NSString *)cutContentToLimit:(NSString *)content {
    /// 转一次，保证美柚表情也算一个字符。
    content = [IMYREmoticonManager decodeEmojiText:content attributes:@{}].string;
    if ([content ttq_textLength] > self.viewModel.publishConfiguration.bodyContentMaxNum) {
        imy_asyncMainBlock(0.25, ^{
            [UIView imy_showTextHUD:[NSString stringWithFormat:@"最多可输入%ld字",self.viewModel.publishConfiguration.bodyContentMaxNum]];
        });
    }
    /// 下面这个方法
    content = [content ttq_subChapterToIndex:self.viewModel.publishConfiguration.bodyContentMaxNum];
    return content;
}

- (void)showPolishTipIfNeeded {
    if ([self.viewModel current_content_word_num] >= self.viewModel.polish_up_word_limit && [TTQPublishTopicViewModel enablePolish]) {
        if (![IMYHugTipsView hasShownGuideWithKey:@"ttq_publish_ai_polish"] && !self.hasShowHugTips) {
            IMYHugTipsView *tipView = [IMYHugTipsView tipsView];
            [tipView updateText:@"试试AI一键润色帖子吧"];
            [tipView showInView:self.view targetView:self.bottomToolView.aiButton];
            tipView.imy_bottom += 16;
            self.topicTipsView = tipView;
            [IMYHugTipsView setShownWithKey:@"ttq_publish_ai_polish"];
            self.hasShowHugTips = YES;
        }
    }
}

#pragma mark - AI 自动换行

- (BOOL)shouldShowNewlineAlert {
    if (self.viewModel.aiInit) {
        return NO;
    }
    NSString *key = @"ttq_publish_newline";
    if (![[IMYUserDefaults standardUserDefaults] boolForKey:key]) {
        if ([self.viewModel current_content_word_num] >= [TTQPublishTopicViewModel newlineLimitCount] && ![[self.viewModel current_content] containsString:@"\n"]) {
            /// 大于字数限制，且不带换行
            NSMutableDictionary *biParams = [@{@"event":@"dsq_bjq_znfdtc",@"action":@1} mutableCopy];
            [IMYGAEventHelper postWithPath:@"event" params:biParams headers:nil completed:nil];
            [IMYCKBannerAlertView imy_showWithTitle:IMYString(@"长文建议智能分段后发布") content:IMYString(@"使用长文一键智能分段，发布省心，阅读更省力") bannerUrl:@" " leftButtonTitle:@"直接发布" rightButtonTitle:@"去智能分段" handler:^(NSInteger buttonIndex) {
                NSMutableDictionary *actionParams = [@{@"event":@"dsq_bjq_znfdtc",@"action":@2} mutableCopy];
                if (buttonIndex == 0) {
                    [self preparationToPost];
                    actionParams[@"public_type"] = @"直接发布";
                } else if (buttonIndex == 1) {
                    [self aiNewLineAction];
                    actionParams[@"public_type"] = @"去智能分";
                } else if (buttonIndex == -1) {
                    actionParams[@"public_type"] = @"关闭按钮";
                }
                [IMYGAEventHelper postWithPath:@"event" params:actionParams headers:nil completed:nil];
            }];
            [[IMYUserDefaults standardUserDefaults] setBool:YES forKey:key];
            [[IMYUserDefaults standardUserDefaults] setBool:YES forKey:@"ttq_publish_showNewLinePop"];
            return YES;
        }
    }
    return NO;
}

- (void)aiNewLineAction {
    if ([self.viewModel current_content_word_num] < [TTQPublishTopicViewModel newlineLimitCount]) {
        [UIView imy_showTextHUD:[NSString stringWithFormat:@"帖子正文需至少输入%ld个字",[TTQPublishTopicViewModel newlineLimitCount]]];
        return;
    }
    self.aiOutputView = [self creatAIViewWithType:TTQAiGeneraterAutoNewLine];
    self.aiOutputView.aiReferer = @"paragraphed";
    [self.aiOutputView loadRunseContent:[self.viewModel current_content]];
    [self.navigationController.view addSubview:self.aiOutputView];
}

#pragma mark - helper

- (void)publishToolDidEdit {
    if (self.topicTipsView) {
        [self.topicTipsView removeFromSuperview];
        self.topicTipsView = nil;
    }
    if(self.didEditWithTemplate){
        self.didEditWithTemplate = NO; // 区分下后续主动点击
        return;
    }
    if (self.didEdit) {
        return;
    }
    self.didEdit = YES;
    if (self.publishViewDidEdit) {
        self.publishViewDidEdit();
    }
}

- (BOOL)canShowPanAssetPickerWhenInit {
    if (IMYAssetAuthorizationStatusNotDetermined == [IMYAssetsManager authorizationStatus]) {
        return NO;
    }
    if (IMYAssetAuthorizationStatusNotAuthorized == [IMYAssetsManager authorizationStatus]) {
        return NO;
    }

    if (self.viewModel.isAiOutput) {
        return NO;
    }
    if (self.viewModel.autoEnterEidt) {
        return NO;
    }
    if (self.viewModel.publishFromType != TTQPublishFromNew) {
        return NO;
    }
    if (self.viewModel.publishConfiguration.isExperiencePublish) { // 展示模板列表
        return NO;
    }
    if (self.viewModel.open_ai_prenatal && [TTQPublishTopicViewModel openAIChanJian]) {
        return NO;
    }
    
    return YES;
}

- (void)postBiWithSuccess:(BOOL)success {
    NSInteger entrance = self.publish_entrance;
    if (self.viewModel.publishFromType == TTQPublishFromEdit && self.publish_entrance == 0) {
        entrance = TTQPublishEntranceEdit;
    }
    NSString *type = @"帖子";
    NSMutableDictionary *params = [@{@"event":@"TopicPublish_PublishBotton",@"action":@2,@"publish_entrance":@(entrance),@"public_type":type,@"public_info":success?@"success":@"fail"} mutableCopy];
    if (self.viewModel.bi_index) {
        params[@"index"] = @(self.viewModel.bi_index);
    }
    [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];

}

-(void)postBIwithPublishInfoType:(NSString *)info  {
    NSMutableDictionary *params = [@{@"event":@"TopicPublish_edit",@"action":@2,@"publish_entrance":@(self.publish_entrance),@"public_type":@"帖子",@"public_info":info} mutableCopy];
    if (self.viewModel.bi_index) {
        params[@"index"] = @(self.viewModel.bi_index);
    }
    [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
}

- (void)pageExposureEvent {
    NSMutableDictionary *params = [@{@"event":@"TopicPublish_edit",@"action":@1,@"publish_entrance":@(self.publish_entrance),@"public_type":@"帖子"} mutableCopy];
    if (self.viewModel.bi_index) {
        params[@"index"] = @(self.viewModel.bi_index);
    }
    [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
}

- (void)postTopicBIAction {
    if (self.viewModel.currentTemplateID < 1 && !self.viewModel.subjectModel) {
        return;
    }
    NSMutableDictionary *params = [NSMutableDictionary dictionaryWithCapacity:1];
    params[@"event"] = @"dsq_bjq_mbhtbg";
    params[@"action"] = @1;
    if (self.viewModel.currentTemplateID) {
        params[@"public_type"] = @(self.viewModel.currentTemplateID);
    }
    if (self.viewModel.subjectModel) {
        params[@"public_info"] = @(self.viewModel.subjectModel.subjectID);
    }
    [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
}

@end
