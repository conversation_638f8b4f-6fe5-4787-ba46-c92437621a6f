//
//  TTQPublishNavigationBarView.m
//  IMYTTQ
//
//  Created by ltj on 2023/7/19.
//

#import "TTQPublishNavigationBarView.h"
#import <IMYBaseKit/IMYViewKit.h>
#import <IMYBaseKit/IMYPublic.h>
#import "TTQCommonHelp.h"
#import "TTQABTestConfig.h"
#import "TTQPublishModel.h"
#import <IMYUGC/IMYUGC.h>
#import <IMYUGC/IMYCKInputPlaceholder.h>

@interface TTQPublishNavigationBarView ()

@property (nonatomic, assign) TTQPublishNavigationBarType currentType;
@property (nonatomic, strong) UIImageView *bgImageView;
@property (nonatomic, strong) IMYButton *cancelBtn;     /// 取消按钮
@property (nonatomic, strong) IMYButton *draftBtn;     /// 草稿箱
@property (nonatomic, strong) UIButton *rightBtn;  /// 发布按钮

// 中间区域
@property (nonatomic, copy) NSArray  *navigationButtonArray;
@property (nonatomic, strong) UIView *selectedLineView;

@property (nonatomic, copy) NSArray  *navigationItemArray;
@property (nonatomic, assign) NSInteger  selectedIndex;

@property (nonatomic, strong) IMYButton *helperButton;
@property (nonatomic, strong) UIView *circleAICoverView;
@end

@implementation TTQPublishNavigationBarView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self initSubviews];
        [self addEvents];
    }
    return self;
}

- (void)initSubviews {
    [self imy_setBackgroundColorForKey:kIMY_BG];
    
    self.bgImageView = [[UIImageView alloc] initWithFrame:self.bounds];
    self.bgImageView.contentMode = UIViewContentModeScaleAspectFill;
    self.bgImageView.clipsToBounds = YES;
    [self addSubview:self.bgImageView];
        
    // 取消按钮
    IMYButton *cancelBtn = [[IMYButton alloc] init];
    [cancelBtn imy_setTitle:IMYString(@"取消")];
    [cancelBtn imy_setTitleColor:kCK_Black_A];
    cancelBtn.titleLabel.font = [UIFont systemFontOfSize:15];
    [cancelBtn sizeToFit];
    cancelBtn.imy_left = 12;
    cancelBtn.imy_height = 44;
    cancelBtn.imy_bottom = self.imy_height;
    [cancelBtn addTarget:self action:@selector(cancelBtnAction:) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:cancelBtn];
    self.cancelBtn = cancelBtn;
    
    // 发布按钮
    [self addSubview:self.rightBtn];
    self.rightBtn.imy_right = self.imy_width - 12;;
    self.rightBtn.imy_bottom = self.imy_height - 8;
    self.rightBtn.hidden = YES;
    
    self.helperButton = [[IMYButton alloc] initWithFrame:CGRectMake(0, 0, 16, 16)];
    self.helperButton.hidden = YES;
    [self.helperButton imy_setImage:@"post_icon_sigh"];
    [self.helperButton setExtendTouchInsets:UIEdgeInsetsMake(10, 10, 10, 10)];
    [[self.helperButton rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
        IMYCConfigsGroup *config = [[IMYConfigsCenter sharedInstance] groupForKeyPath:@"community.publish_tips_config"];
        NSString *uri = [config stringForKey:@"jump_url"];
        if (imy_isNotEmptyString(uri)) {
            if ([uri hasPrefix:@"http"]) {
                [[IMYURIManager sharedInstance] runActionWithPath:@"pagesheet/web" params:@{@"url":uri} info:nil];
            } else {
                [[IMYURIManager sharedInstance] runActionWithString:uri];
            }
            if (self.helpBtnClickBlock) {
                self.helpBtnClickBlock();
            }
        }
    }];
    [self addSubview:self.helperButton];
}

- (void)addEvents {
    @weakify(self);
    [[[NSNotificationCenter defaultCenter] rac_addObserverForName:@"TTQPublishDraftSave" object:nil] subscribeNext:^(NSNotification * _Nullable x) {
        @strongify(self);
        [self.draftBtn imy_setTitle:[self getDraftTitle]];
    }];
}

- (void)updateNavigationBarItems:(NSArray *)items {
    self.navigationItemArray = [items copy];
    if(items.count == 1){
        [self updateNavigationForType:[self barTypeForItem:items.firstObject]];
        return;
    }
    if ([IMYCKFeedsHelper feedsExp889] && items.count > 1) {
        /// 未选中的，命中了889实验组，要移除提问
        if ([items containsObject:@"ask"]) {
            NSMutableArray *array = [NSMutableArray arrayWithArray:items];
            [array removeObject:@"ask"];
            self.navigationItemArray = array;
        }
    }

    NSMutableArray *buttonArray = [[NSMutableArray alloc] init];
    CGFloat insetBetween = 21;
    CGFloat btnWidth = 36;
    CGFloat startX = (SCREEN_WIDTH - self.navigationItemArray.count * btnWidth - (self.navigationItemArray.count - 1) * insetBetween)/2.0;
    NSInteger index = 0;
    for (NSString *itemString in self.navigationItemArray) {
        IMYButton *button = [[IMYButton alloc] initWithFrame:CGRectMake(0, 0, btnWidth, 28)];
        button.tag = 1000 + index;
        [button addTarget:self action:@selector(changeTabAction:) forControlEvents:UIControlEventTouchUpInside];
        [button imy_setTitleColor:[UIColor imy_colorForKey:kCK_Black_M] state:UIControlStateNormal];
        [button imy_setTitleColor:[UIColor imy_colorForKey:kCK_Red_B] state:UIControlStateSelected];
        button.titleLabel.font = [UIFont systemFontOfSize:16];
        button.imy_left = startX;
        button.imy_centerY = SCREEN_STATUSBAR_HEIGHT + SCREEN_NAVIGATIONBAR_HEIGHT / 2;
        [self addSubview:button];
        if ([itemString isEqualToString:@"topic"]) {
            [button imy_setTitle:@"帖子"];
        } else if ([itemString isEqualToString:@"note"]) {
            [button imy_setTitle:@"笔记"];
        } else if ([itemString isEqualToString:@"ask"]) {
            [button imy_setTitle:@"提问"];
        }
     
        [buttonArray addObject:button];
        if (index == 0) { // [itemString isEqualToString:@"topic"]
            button.selected = YES;
            button.titleLabel.font = [UIFont boldSystemFontOfSize:17];
            self.selectedLineView.imy_bottom = self.imy_height;
            self.selectedLineView.imy_centerX = button.imy_centerX;
            self.selectedIndex = index;
        }
        startX = startX + btnWidth + insetBetween;
        index++;
    }
    self.navigationButtonArray = [buttonArray copy];
    
    [self addSubview:self.selectedLineView];
    [self updateRightBtn];
    if (self.navigationItemArray.count == 1) {
        /// 889实验要求，只展示一个的话，不能显示tab样式
        IMYButton *button = self.navigationButtonArray.firstObject;
        button.selected = NO;
        self.selectedLineView.hidden = YES;
    }
}

- (void)updateRightBtn {
    // 右 草稿
    IMYButton *draftBtn = [[IMYButton alloc] init];
    [draftBtn imy_setTitle:[self getDraftTitle]];
    [draftBtn imy_setTitleColor:kCK_Black_A];
    draftBtn.titleLabel.font = [UIFont systemFontOfSize:15];
    [draftBtn sizeToFit];
    draftBtn.imy_right = self.imy_width - 12;
    draftBtn.imy_height = 44;
    draftBtn.imy_bottom = self.imy_height;
    [draftBtn addTarget:self action:@selector(draftBtnAction:) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:draftBtn];
    self.draftBtn = draftBtn;
    
    draftBtn.imyut_eventInfo.eventName = [NSString stringWithFormat:@"publish_draft_%p",self];
    [draftBtn.imyut_eventInfo setExposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_bjy_cgbg",@"action":@1,@"public_info":@([TTQPublishModel draftCount])} headers:nil completed:nil];
    }];
}

- (TTQPublishNavigationBarType)barTypeForItem:(NSString *)item{
    // 转换下
    if ([item isEqualToString:@"topic"]) {
        return TTQPublishNavigationBarTypeTopic;
    } else if ([item isEqualToString:@"note"]) {
        return TTQPublishNavigationBarTypeNote;
    } else if ([item isEqualToString:@"ask"]) {
        return TTQPublishNavigationBarTypeAsk;
    }
    return TTQPublishNavigationBarTypeTopic; // 图文兜底
}

- (void)updateNavigationForType:(TTQPublishNavigationBarType)type{
    self.currentType = type;
    [self handleSpecificTitleBtn:type];
    self.rightBtn.hidden = NO;
}

-(IMYButton *)handleSpecificTitleBtn:(TTQPublishNavigationBarType)type{
    // 提问题 发帖子 发笔记
    IMYButton *button = [[IMYButton alloc] initWithFrame:CGRectMake(0, 0, 34, 24)];
    [button imy_setTitleColor:kCK_Black_A state:UIControlStateNormal];
    button.titleLabel.font = [UIFont boldSystemFontOfSize:17];
    
    switch (type) {
        case TTQPublishNavigationBarTypeNote:
            [button imy_setTitle:[IMYCKFeedsHelper praiseStyleExp]?[IMYCKInputPlaceholder noteEditorVCTitle]:IMYString(@"发笔记")];
            break;
        case TTQPublishNavigationBarTypeTopic:
            [button imy_setTitle:[IMYCKFeedsHelper praiseStyleExp]?[IMYCKInputPlaceholder topicEditorVCTitle]:IMYString(@"发帖子")];
            break;
        case TTQPublishNavigationBarTypeAsk:
            [button imy_setTitle:[IMYCKFeedsHelper praiseStyleExp]?[IMYCKInputPlaceholder questionEditorVCTitle]:IMYString(@"提问题")];
            break;
        default:
            break;
    }
    
    [button sizeToFit];
    button.imy_centerX = self.imy_width/2;
    button.imy_centerY = SCREEN_STATUSBAR_HEIGHT + SCREEN_NAVIGATIONBAR_HEIGHT / 2;
    [self addSubview:button];
    if (type == TTQPublishNavigationBarTypeTopic && [self isPublishTipShow]) {
        button.imy_centerX -= (self.helperButton.imy_width/2 + 2);
        self.helperButton.imy_centerY = button.imy_centerY;
        self.helperButton.imy_left = button.imy_right + 4;
        self.helperButton.hidden = NO;
    }
    return button;
}

#pragma mark - api

- (void)changeUIToPage:(NSInteger)toIndex {
    if (self.navigationButtonArray.count == 1) {
        return;
    }
    IMYButton *toButton = self.navigationButtonArray[toIndex];
    IMYButton *fromButton = self.navigationButtonArray[self.selectedIndex];
    toButton.selected = YES;
    toButton.titleLabel.font = [UIFont imy_MediumFontWith:17];
    if (fromButton != toButton) {
        fromButton.selected = NO;
        fromButton.titleLabel.font = [UIFont imy_FontWith:16];
    }
    [UIView animateWithDuration:0.15 animations:^{
        self.selectedLineView.imy_centerX = toButton.imy_centerX;
    }];
    self.selectedIndex = toIndex;
}

- (void)selectNavibar:(TTQPublishNavigationBarType)type delay:(CGFloat)delay{
    if (self.currentType == type) {
        return;
    }
    self.currentType = type;
    // 这里禁止所有的按钮点击
    self.userInteractionEnabled = NO;
    // 提问题 发帖子 发笔记
    IMYButton *titleBtn = [self handleSpecificTitleBtn:type];
    titleBtn.alpha = 0;
    self.helperButton.alpha = 0;
    // 发布按钮
    self.rightBtn.alpha = 0;
    self.rightBtn.hidden = NO;
    
    // animation
    imy_asyncMainBlock(delay, ^{
        [UIView animateWithDuration:0.3 animations:^{
            for (IMYButton *btn in self.navigationButtonArray) {
                btn.alpha = 0;
            }
            self.draftBtn.alpha = 0;
            self.selectedLineView.alpha = 0;
            titleBtn.alpha = 1;
            self.rightBtn.alpha = 1;
            self.helperButton.alpha = 1;
        } completion:^(BOOL finished) {
            for (IMYButton *btn in self.navigationButtonArray) {
                btn.hidden = YES;
            }
            self.selectedLineView.hidden = YES;
            self.draftBtn.hidden = YES;
            self.userInteractionEnabled = YES;
        }];
    });
}

- (CGRect)askPublishPosition{
    IMYButton *askBtn = (IMYButton *)self.navigationButtonArray.lastObject;
    CGRect position = [self convertRect:askBtn.frame toView:self.superview];
    return position;
}

-(void)changePublishBtnState:(BOOL)enable{
//    self.publishBtn.enabled = enable;
    // 一直都可点，才能弹出吐司
    if(enable){
        [self.rightBtn imy_setBackgroundColorForKey:kCK_Red_B];
    } else {
        [self.rightBtn setBackgroundColor:[UIColor imy_colorWithHexString:@"0xFFB7CF"]];
    }
}

#pragma mark - handler

- (void)cancelBtnAction:(UIButton *)sender{
    if (self.cancelBtnHandler) {
        self.cancelBtnHandler();
    }
}

- (void)draftBtnAction:(UIButton *)sender{
    if(self.draftBtnHandler){
        self.draftBtnHandler();
    }
    [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_bjy_cgbg",@"action":@2,@"public_info":@([TTQPublishModel draftCount])} headers:nil completed:nil];
}

- (void)publishBtnAction:(UIButton *)sender{
    // 吐司提示
    if(self.publishBtnHandler){
        self.publishBtnHandler();
    }
}

- (void)changeTabAction:(UIButton *)button {
    NSInteger index = button.tag - 1000;
    if (index == self.selectedIndex) {
        return;
    }

    if (index < self.navigationItemArray.count) {
        if (self.changeToPageHandler) {
            self.changeToPageHandler(index);
        }
    }
}
/// 这里是AI发文输出过程中，隐藏其它所有按钮。目前用在社区
- (void)hideTopActionButton:(BOOL)hide {
    if (hide) {
        if (!self.circleAICoverView) {
            self.circleAICoverView = [[UIView alloc] initWithFrame:self.bounds];
            [self.circleAICoverView imy_setBackgroundColorForKey:kCK_Black_F];
            IMYButton *titleBtn = [self handleSpecificTitleBtn:TTQPublishNavigationBarTypeTopic];
            titleBtn.imy_centerX = self.circleAICoverView.imy_width/2;
            [self.circleAICoverView addSubview:titleBtn];
        }
        [self addSubview:self.circleAICoverView];
    } else {
        [self.circleAICoverView removeFromSuperview];
    }
}

#pragma mark - getter

- (UIView *)selectedLineView {
    if (!_selectedLineView) {
        _selectedLineView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 20, 3)];
        [_selectedLineView imy_setBackgroundColorForKey:kCK_Red_B];
        _selectedLineView.layer.masksToBounds = YES;
        _selectedLineView.layer.cornerRadius = 1.5;
    }
    return _selectedLineView;
}

- (UIButton *)rightBtn{
    if (!_rightBtn) {
        _rightBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_rightBtn setFrame:CGRectMake(0, 0, 54, 28)];
        [_rightBtn addTarget:self action:@selector(publishBtnAction:) forControlEvents:UIControlEventTouchUpInside];
        [_rightBtn setBackgroundColor:[UIColor imy_colorWithHexString:@"0xFFB7CF"]];
        [_rightBtn imy_setTitle:IMYString(@"发布") state:UIControlStateNormal];
        [_rightBtn imy_setTitleColorForKey:kCK_White_A andState:UIControlStateNormal];
        [_rightBtn.titleLabel setFont:[UIFont systemFontOfSize:13]];
        _rightBtn.layer.cornerRadius = 14;
        _rightBtn.layer.masksToBounds = YES;
    }
    return _rightBtn;
}

#pragma mark - helper

-(NSString *)getDraftTitle{
    //草稿箱
    NSUInteger draftCount = [TTQPublishModel draftCount];
    NSString *draftTitle;
    if (draftCount) {
        if (draftCount > 99) {
            draftTitle = [NSString stringWithFormat:@"草稿(99+)"];
        } else {
            draftTitle = [NSString stringWithFormat:@"草稿(%ld)",draftCount];
        }
    } else {
        draftTitle = [NSString stringWithFormat:@"草稿"];
    }
    return draftTitle;
}

#pragma mark -

- (BOOL)isPublishTipShow {
    IMYCConfigsGroup *config = [[IMYConfigsCenter sharedInstance] groupForKeyPath:@"community.publish_tips_config"];
    if (config && [config boolForKey:@"is_open"]) {
        return YES;
    }
    return NO;
}


@end
