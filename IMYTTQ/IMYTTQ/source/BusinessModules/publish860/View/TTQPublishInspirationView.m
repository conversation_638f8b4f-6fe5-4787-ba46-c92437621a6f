//
//  TTQPublishInspirationView.m
//  IMYTTQ
//
//  Created by 林云峰 on 2024/12/17.
//

#import "TTQPublishInspirationView.h"
#import <IMYBaseKit/IMYViewKit.h>

@interface TTQPublishInspirationView () <UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) NSArray *data;
@end

@implementation TTQPublishInspirationView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self imy_setBackgroundColorForKey:kCK_White_AN];
        [self initViews];
    }
    return self;
}

- (void)initViews {
    UILabel *label = [[UILabel alloc] initWithFrame:CGRectMake(16, 16, 200, 18)];
    label.font = [UIFont systemFontOfSize:13];
    [label imy_setTextColorForKey:kCK_Black_B];
    IMYCConfigsGroup *group = [[IMYConfigsCenter sharedInstance] groupForKeyPath:@"community.publish_tool"];
    NSString *text = [group stringForKey:@"ai_tool_title_text"];
    if (imy_isEmptyString(text)) {
        text = IMYString(@"AI帮写助手·测试版");
    }
    label.text = text;
    [self addSubview:label];
    self.data = [self createData];
    CGFloat left = 16;
    CGFloat width = (self.imy_width - 2*left - 12)/2;
    for (int i = 0; i < self.data.count; i++) {
        UIControl *control = [[UIControl alloc] initWithFrame:CGRectMake(left + (width + 12)*i, 50, width, 105)];
        [control imy_setBackgroundColorForKey:kCK_Black_H];
        control.layer.cornerRadius = 8;
        [control addTarget:self action:@selector(controlClick:) forControlEvents:UIControlEventTouchUpInside];
        [self addSubview:control];
        NSDictionary *item = self.data[i];
        control.tag = [item[@"type"] integerValue];

        UIImageView *imageV = [[UIImageView alloc] initWithFrame:CGRectMake(16, 16, 22, 22)];
        [imageV imy_setImage:item[@"icon"]];
        [control addSubview:imageV];
        
        UILabel *label = [[UILabel alloc] initWithFrame:CGRectMake(16, imageV.imy_bottom + 8, control.imy_width - 2*16, 21)];
        label.font = [UIFont boldSystemFontOfSize:15];
        [label imy_setTextColorForKey:kCK_Black_A];
        label.text = item[@"title"];
        [control addSubview:label];
        
        label = [[UILabel alloc] initWithFrame:CGRectMake(16, 71, control.imy_width - 2*16, 18)];
        label.font = [UIFont systemFontOfSize:13];
        [label imy_setTextColorForKey:kCK_Black_B];
        label.text = item[@"content"];
        [control addSubview:label];
    }
}

- (void)controlClick:(UIControl *)sender {
    if (self.itemClickBlock) {
        self.itemClickBlock(sender.tag);
    }
}

- (NSArray *)createData {
    return [TTQPublishInspirationView items];
}

- (BOOL)haveItem {
    return self.data.count;
}

+ (NSArray *)items {
    IMYCConfigsGroup *group = [[IMYConfigsCenter sharedInstance] groupForKeyPath:@"community.publish_tool"];
    NSMutableArray *data = [NSMutableArray arrayWithCapacity:1];
    if ([group boolForKey:@"is_open_Intelligent_segmentation"]) {
        /// 自动换行
        NSMutableDictionary *item = [NSMutableDictionary dictionary];
        item[@"icon"] = @"post_actionbar_icon_ai_newline";
        item[@"title"] = imy_isNotEmptyString([group stringForKey:@"Intelligent_segmentationpolish_button_text"])?[group stringForKey:@"Intelligent_segmentationpolish_button_text"]:@"智能分段";
        item[@"content"] = imy_isNotEmptyString([group stringForKey:@"Intelligent_segmentationpolish_guide_text"])?[group stringForKey:@"Intelligent_segmentationpolish_guide_text"]:@"长内容分段展示更美观";
        item[@"type"] = @(TTQAiToolNewLine);
        [data addObject:item];
    }
    if ([group boolForKey:@"is_open_polish_up"]) {
        /// 润色
        NSMutableDictionary *item = [NSMutableDictionary dictionary];
        item[@"icon"] = @"post_actionbar_icon_ai_polish";
        item[@"title"] = imy_isNotEmptyString([group stringForKey:@"polish_up_button_text"])?[group stringForKey:@"polish_up_button_text"]:@"一键润色";
        item[@"content"] = imy_isNotEmptyString([group stringForKey:@"polish_up_guide_text"])?[group stringForKey:@"polish_up_guide_text"]:@"点击润色当前帖子内容";
        item[@"type"] = @(TTQAiToolRunse);
        [data addObject:item];
    }
    if ([group boolForKey:@"is_open_prenatal_checkup_ai_new"] && [IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModePregnancy) {
        /// 产检
        NSMutableDictionary *item = [NSMutableDictionary dictionary];
        item[@"icon"] = @"post_actionbar_icon_ai_image";
        item[@"title"] = imy_isNotEmptyString([group stringForKey:@"prenatal_checkup_button_text_new"])?[group stringForKey:@"prenatal_checkup_button_text_new"]:@"产检报告智能发文";
        item[@"content"] = imy_isNotEmptyString([group stringForKey:@"prenatal_checkup_guide_text_new"])?[group stringForKey:@"prenatal_checkup_guide_text_new"]:@"上传产检图片自动配文";
        item[@"type"] = @(TTQAiToolChanjian);
        [data addObject:item];
    }
    return data;
}

@end

