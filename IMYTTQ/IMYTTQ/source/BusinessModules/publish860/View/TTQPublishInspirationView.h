//
//  TTQPublishInspirationView.h
//  IMYTTQ
//
//  Created by 林云峰 on 2024/12/17.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
@class IMYTouchEXButton;

typedef NS_ENUM(NSUInteger, TTQAiToolItemType) {
    TTQAiToolRunse, /// 润色
    TTQ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    TTQAiToolNewLine,

};


@interface TTQPublishInspirationView : UIView

@property (nonatomic, copy) void (^itemClickBlock)(TTQAiToolItemType type);
/// 是否有内容
- (BOOL)haveItem;

+ (NSArray *)items;
@end

NS_ASSUME_NONNULL_END
