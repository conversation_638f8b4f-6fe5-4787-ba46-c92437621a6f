//
//  TTQSubCommentAdapter.m
//  AFNetworking
//
//  Created by 林云峰 on 2025/4/7.
//

#import "TTQSubCommentAdapter.h"
#import "IMYUGCCommentCell.h"
#import <IMYBaseKit/IMYViewKit.h>
#import "TTQTopicCommentEmptyCell.h"
#import "TTQTopicViewModel.h"
#import <IMYRM80AttributedLabelURL.h>
#import "NSString+IMYR.h"
#import "TTQDetailHelper.h"
#import <IMYUGC/IMYUGCEventHelper.h>

@interface TTQSubCommentAdapter ()<IMYAdapterModuleTableViewDelegate, IMYRM80AttributedLabelDelegate>

@property (nonatomic, weak) IMYTableViewAdapterModule *module;
@property (nonatomic, strong) NSArray <TTQCommentModel *>*items;
@property (nonatomic, strong) TTQTopicReferenceViewModel *viewModel;
@property (nonatomic, strong) NSIndexPath *longPressIndexPath;
@property (nonatomic, strong) TTQCommentModel *longPressCommentModel;
@property (nonatomic, strong) UIView *handleUIMenuItemView;           //用于处理UIMenuitem的消失事件
@property (nonatomic, strong) IMYUGCCommentCell *heightCell;
@property (nonatomic, strong) IMYPhotoBrowser *photoBrowser;
@end

@implementation TTQSubCommentAdapter

- (void)setupWithAdapterModule:(IMYTableViewAdapterModule *)module {
    self.module = module;
    self.viewModel = [TTQTopicReferenceViewModel new];
    self.viewModel.is_ask = YES;
    [module registerClass:IMYUGCCommentCell.class forCellReuseIdentifier:@"IMYUGCCommentCell"];
    [module registerClass:TTQTopicCommentEmptyCell.class forCellReuseIdentifier:@"TTQTopicCommentEmptyCell"];
}

- (NSInteger)sortIndexOfAdapterModule:(IMYTableViewAdapterModule *)module {
    return 33;
}

- (void)updateViewModel:(TTQTopicReferenceViewModel *)viewModel {
    self.viewModel = viewModel;
}

- (void)loadItems:(NSArray <TTQCommentModel *>*)items {
    self.items = items;
    [self.module reloadData];
    [self hideHighlightCellIfNeeded];
}

- (void)hideHighlightCellIfNeeded {
    imy_asyncMainBlock(1, ^{
        /// 设置数据已展示高亮
        BOOL findHighlight = NO;
        for (TTQCommentModel *subModel in self.viewModel.dataSource) {
            if ([subModel isKindOfClass:TTQCommentModel.class] && subModel.needHighlightWhenAppear) {
                subModel.hasShowHighlight = YES;
                subModel.needHighlightWhenAppear = NO;
                findHighlight = YES;
            }
        }
        if (findHighlight) {
            NSArray *cells = [self.tableView visibleCells];
            [cells bk_each:^(UITableViewCell *obj) {
                if ([obj respondsToSelector:@selector(hideWarmreviewHighlighted)]) {
                    [obj performSelector:@selector(hideWarmreviewHighlighted)];
                }
            }];
        }
    });
}
#pragma mark - ShowLargePhotosDelegate methods
- (void)showWithPhotos:(NSMutableArray *)photos atIndex:(NSUInteger)index {
    
    self.photoBrowser = [IMYPhotoBrowser showWithPhotos:photos atIndex:index];
    self.photoBrowser.pageControlStyle = IMYBrowserPageControlStyleText;
    self.photoBrowser.delegate = self;
    
}
#pragma mark - tablView delegate

- (nonnull UITableViewCell *)tableView:(nonnull IMYTableViewAdapterModule *)module cellForRowAtIndexPath:(nonnull NSIndexPath *)indexPath {
    if (self.items.count == 0) {
        TTQTopicCommentEmptyCell *cell = [module dequeueReusableCellWithIdentifier:@"TTQTopicCommentEmptyCell" forIndexPath:indexPath];
        [cell bindModel:nil cellForRowAtIndexPath:indexPath viewModel:[TTQTopicViewModel new]];
        @weakify(self);
        [cell setTapActionBlock:^{
            @strongify(self);
            if (self.replyActionBlock) {
                self.replyActionBlock(nil);
            }
        }];
        return cell;
    } else {
        //        IMYUGCCommentCell *cell = [module dequeueReusableCellWithIdentifier:@"IMYUGCCommentCell" forIndexPath:indexPath];
        IMYUGCCommentCell *cell = [self.tableView dequeueReusableCellWithIdentifier:@"IMYUGCCommentCell"];
        if (!cell) {
            cell = [[IMYUGCCommentCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"IMYUGCCommentCell"];
        }
        TTQCommentModel *comment = self.items[indexPath.section];
        comment.privilege = 0;
        [cell updateWithData:comment viewModel:self.viewModel];
        @weakify(self);
        [cell setReloadCellBlock:^{
            @strongify(self);
            [self.module reloadData];
        }];
        
        [cell setCommentButtonBlock:^{
            @strongify(self);
            [self tableView:self.module didSelectRowAtIndexPath:indexPath];
        }];
        cell.contentClickedOnLinkBlk = ^(IMYRM80AttributedLabel * _Nonnull label, IMYRM80AttributedLabelURL * _Nonnull linkData) {
            @strongify(self);
            [self m80AttributedLabel:label clickedOnLink:linkData];
        };
        
        @weakify(comment, indexPath);
        cell.imageDidClickBlock = ^(NSMutableArray * _Nonnull photos, NSUInteger index) {
            @strongify(self,comment, indexPath);
            [self showWithPhotos:photos atIndex:index];
            [self postCommentBiFeedsView:2 commentId:comment.commentID floor:indexPath.section + 1 clickpos:18];
            // 埋点:帖子详情页_评论图片
            [TTQCommonHelp GAEventForEventWith:@{@"action": @(2),
                                                 @"topic_id": @(self.viewModel.topic_id),
                                                 @"comment_id":@(comment.commentID),
                                                 @"event": @"tzxqy_pltp"}];
            
        };
        cell.longPressActionBlk = ^(UIGestureRecognizer * _Nonnull sender) {
            @strongify(self);
            [self longPressAction:sender];
        };
        
        [cell setUserInfoTapBlock:^{
            @strongify(self,comment);
            NSMutableDictionary *params = [NSMutableDictionary dictionary];
            params[@"event"] = @"dsq_nrxqy_tx";
            params[@"action"] = @2;
            params[@"info_type"] = @12;
            params[@"info_id"] = @(self.viewModel.topic_id);
            params[@"fuid"] =@(self.viewModel.referenced.publisher.userID);
            params[@"public_type"] = @2;
            params[@"comment_id"] = @(self.viewModel.referenced_id);
            [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
        }];
        
        [cell setPraiseResultBlock:^(BOOL isPraise,NSUInteger num) {
            @strongify(self,comment);
            NSMutableDictionary *params = @{
                @"action":@2,
                @"info_type":@12,
                @"info_id":@(self.viewModel.topic_id),
                @"fuid":@(self.viewModel.referenced.publisher.userID),
                @"public_type":(isPraise?@21:@22),
                @"comment_uid":@(comment.publisher.userID),
                @"comment_id":@(comment.commentID),
                @"event":@"dsq_nrxqy_dz",@"public_info":@"评论帖的评论",
                @"interact_num":@(num),
                @"is_on_period":@([IMYUGCEventHelper userIsInPhysiological]),@"redirect_url":[IMYUGCEventHelper currentPageRedirectUrl]
            };
            [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
        }];
        
        cell.biFeedsView.imyut_eventInfo.eventName = [NSString stringWithFormat:@"answer_comment_feeds_%ld",comment.commentID];
        cell.biFeedsView.imyut_eventInfo.edgeOffset = UIEdgeInsetsMake(0, 0, 52+SCREEN_TABBAR_SAFEBOTTOM_MARGIN, 0);
        NSInteger commentId = comment.commentID;
        [cell.biFeedsView.imyut_eventInfo setExposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
            @strongify(self);
            TTQCommentModel *model = [self.viewModel tableCellModelAtIndexPath:indexPath];
            IMYMixHomeUserBadge *badge = [model.publisher.badges bk_match:^BOOL(IMYMixHomeUserBadge * _Nonnull element) {
                return [element.type isEqualToString:@"badge"];
            }];
            [self postCommentBiFeedsView:1 commentId:commentId floor:indexPath.section + 1 clickpos:32 publicType:badge.badgeID];
        }];
        
        cell.biBadgeOnClickedBlk = ^(NSString *badgeID, BOOL result) {
            @strongify(self);
            [self postCommentBiFeedsView:2 commentId:commentId floor:indexPath.section + 1 clickpos:61 publicType:badgeID];
        };
        return cell;
    }
}

- (void)tableView:(nonnull IMYTableViewAdapterModule *)module didSelectRowAtIndexPath:(nonnull NSIndexPath *)indexPath {
    [module deselectRowAtIndexPath:indexPath animated:YES];
    if (self.items.count) {
        if (self.replyActionBlock) {
            self.replyActionBlock(indexPath);
        }
        TTQCommentModel *comment = [self.items imy_objectAtIndex:indexPath.section];
        [self postCommentBiFeedsView:3 commentId:comment.commentID floor:indexPath.section + 1 clickpos:32];
    }
}

- (CGFloat)tableView:(nonnull IMYTableViewAdapterModule *)module heightForRowAtIndexPath:(nonnull NSIndexPath *)indexPath {
    if (self.items.count == 0) {
        return [TTQTopicCommentEmptyCell cellHeight];
    } else {
        TTQCommentModel *model = [self.items imy_objectAtIndex:indexPath.section];
        if (model.cellHeight > 1) {
            return model.cellHeight;
        }
        IMYUGCCommentCell<ReactiveTableViewCell> *cell = [self heightCell];
        model.privilege = 0;
        [cell updateWithData:model viewModel:self.viewModel];
        return model.cellHeight;
    }
    return 44 + 8;
}

- (NSInteger)tableView:(nonnull IMYTableViewAdapterModule *)module numberOfRowsInSection:(NSInteger)section {
    if (self.items.count == 0) {
        return 1;
    } else {
        return 1;
    }
}

- (NSInteger)numberOfSectionsInTableView:(IMYTableViewAdapterModule *)module {
    return MAX(1, self.items.count);
}

- (IMYUGCCommentCell *)heightCell {
    if (!_heightCell) {
        _heightCell = [self.tableView dequeueReusableCellWithIdentifier:@"IMYUGCCommentCell"];
    }
    return _heightCell;
}

- (void)tableView:(IMYTableViewAdapterModule *)module willDisplayCell:(UITableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath {
    if (self.autoLoadMoreBlock) {
        self.autoLoadMoreBlock(indexPath);
    }
}

#pragma mark - IMYUGCCommentCellDelegate
- (void)detailCell:(IMYUGCCommentCell *)cell commentDidSelectedAtIndexPath:(NSIndexPath *)indexPath {
    [self tableView:self.module didSelectRowAtIndexPath:indexPath];
}

- (void)detailCell:(IMYUGCCommentCell *)cell praiseDidClickAtIndexPath:(NSIndexPath *)indexPath praise:(BOOL)praise {
    
}

- (void)detailCell:(TTQCommentModel *)commentData imageDidClickWithPhotos:(NSMutableArray *)photos atIndex:(NSUInteger)index {
}

- (void)detailCell:(IMYUGCCommentCell *)cell unfoldDetail:(BOOL)fold atIndex:(NSIndexPath *)index {
    [self.module reloadData];
}

#pragma mark - m80 delegate

- (void)m80AttributedLabel:(IMYRM80AttributedLabel *)label clickedOnLink:(IMYRM80AttributedLabelURL *)linkURL {
    NSString *copyString = linkURL.linkData;
    IMYRM80AttributedLabelURL *tmpLinkURL = linkURL;
    // 这里为了统计，要不然都可以直接在解析时的linkData直接为url就好了。。。都是为了统计啊呀
    if ([copyString isEqualToString:@"to"] || [copyString isEqualToString:@"to-reply"]) {
        UITableViewCell *cell = [label imy_findParentViewWithClass:[UITableViewCell class]];
        TTQCommentModel *model = [self.items imy_objectAtIndex:[self.module indexPathForCell:cell].section];
        TTQPublisherModel *publisher = model.replygoal;
        if (publisher.userID > 0 && publisher.error == 0) {
            [[IMYURIManager shareURIManager] runActionWithPath:@"dynamic/homePage"
                                                        params:@{
                                                                 @"userId": @(publisher.userID),
                                                                 @"locationType": @(12),
                                                                 }
                                                          info:nil];
        } else if (publisher.error == 1) {
            [UIView imy_showTextHUD:kStatusText_UserAnonymous];
        } else if (publisher.error == 2) {
            [UIView imy_showTextHUD:@"该用户主页暂未开放"];
        } else if (publisher.error == 3){
            [UIView imy_showTextHUD:IMYString(kStatusText_homePageNotOpen)];
        }
        [self postCommentBiFeedsView:2 commentId:model.commentID floor:[self.module indexPathForCell:cell].section+1 clickpos:1];
        return;
    }
    
    [self clickWithLink:copyString];
}

- (void)clickWithLink:(NSString *)linkUrl {
    NSURL *url = [NSURL URLWithString:linkUrl];
    if ([url.absoluteString containsString:@"itunes.apple.com"]) {
        [[UIApplication sharedApplication] openURL:url options:nil completionHandler:nil];
    } else {
        IMYURI *uri = nil;
        if (linkUrl) {
            uri = [IMYURI uriWithURIString:linkUrl];
            if ([[IMYURIManager shareURIManager] containScheme:uri.scheme] &&
                [[IMYURIManager shareURIManager] runActionWithURI:uri]) {
                return;
            }
        }
        
        NSDictionary *dic = [linkUrl imy_queryDictionary];
        
        NSString *uriString = dic[@"uri"];
        if (uriString) {
            uri =
            [IMYURI uriWithURIString:[uriString imy_base64DecodedSafeURLString]];
            if ([[IMYURIManager shareURIManager] containScheme:uri.scheme] &&
                [[IMYURIManager shareURIManager] runActionWithURI:uri]) {
                return;
            }
        }
        NSString *topic_id = dic[@"topic_id"];
        //旧的跳转方式.只提供topic_id.没有提供__type
        if (topic_id) {
            [[IMYURIManager shareURIManager]
             runActionWithURI:[IMYURI uriWithPath:@"circles/group/topic"
                                           params:@{@"topicID" : topic_id}
                                             info:nil]];
            return;
        }
        NSString *type = dic[@"__type"];
        if (type) {
            [IMYEventHelper event:[NSString stringWithFormat:@"ttq-%@", type]];
            if ([type isEqualToString:@"1"]) {
                [[UIApplication sharedApplication] openURL:url options:nil completionHandler:nil];
            } else if ([type isEqualToString:@"2"]) {
                NSDictionary *params = @{
                    @"url" : url.absoluteString,
                    @"usingWK" : @(YES),
                };
                [[IMYURIManager shareURIManager] runActionWithPath:@"web"
                                                            params:params
                                                              info:nil];
            }
        }
    }
}

#pragma mark-- 新的长按菜单

- (BOOL)loginActicon {
    if (![IMYPublicAppHelper shareAppHelper].hasLogin) {
        [UIWindow imy_showTextHUD:kStatusText_unLogin];
        [[IMYURIManager shareURIManager] runActionWithString:@"login"];
        return NO;
    } else {
        return YES;
    }
}

- (void)longPressAction:(UILongPressGestureRecognizer *)longPressGestureRecognizer {
    if (longPressGestureRecognizer.state != UIGestureRecognizerStateBegan) {
        return;
    }
    if (![self loginActicon]) {
        return;
    }
    [self createUIMenuInCell:longPressGestureRecognizer];
}


- (void)createUIMenuInCell:(UILongPressGestureRecognizer *)longPressGestureRecognizer {
    if (![longPressGestureRecognizer.view isKindOfClass:UITableViewCell.class]) {
        return;
    }
    UITableViewCell *tableViewCell = (UITableViewCell *)longPressGestureRecognizer.view;
    CGRect tempRect = CGRectMake(0, 0, 0, 0);
    UIView *tempView = [UIView new];
    [self.tableView endEditing:YES];
    NSIndexPath *indexPath = [self.module indexPathForCell:tableViewCell];
    TTQCommentModel *comment = [self.items imy_objectAtIndex:indexPath.section];
    NSArray *titles = @[IMYString(@"复制"), IMYString(@"举报")];
    if ([TTQCommonHelp deleteReview]) {
        if (comment.publisher.is_owner) {
            titles = @[IMYString(@"复制"), IMYString(@"删除")];
        }
        else if (comment.publisher.userID > 0) {
            if ([[IMYPublicAppHelper shareAppHelper].userid
                    isEqualToString:@(comment.publisher.userID).stringValue]) {
                titles = @[IMYString(@"复制"), IMYString(@"删除")];
            }
        }
    }
    
    self.longPressIndexPath = indexPath;
    self.longPressCommentModel = comment;

    @weakify(self);
    NSString *string = [self.longPressCommentModel.content imyr_replaceEmotionStringByString:@"" withType:IMYRReplaceEmotionStringTypeDynamic];
    NSString *content = [NSString ttq_filterHtmlTag:string];
    if (imy_isEmptyString(content)) {
        content = nil;
    }
    IMYActionSheet *sheet = [[IMYActionSheet alloc] initWithWithCancelTitle:@"取消" destructiveTitle:nil otherTitles:titles summary:content style:YES showInView:nil];
    sheet.onActionBlock = ^(NSInteger index) {
        @strongify(self);
        if (index == 0) {
            [self highlightCell];
            return;
        }
        NSString *title = titles[index - 1];
        if ([title isEqualToString:@"复制"]) {
            [self copyActionLongPress];
        } else if ([title isEqualToString:@"举报"]) {
            [self reportActionLongPress];
        } else if ([title isEqualToString:@"删除"]) {
            [self deleteActionReplyLongPress];
        }
    };
    [sheet show];
}

- (void)addHandleUIMenuItemView {
    UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
    if ([keyWindow.subviews containsObject:self.handleUIMenuItemView]) {
        return;
    }
    [keyWindow addSubview:self.handleUIMenuItemView];
}

- (void)removeHandleUIMenuItemView {
    [self removeHandleUIMenuItemViewFromWindow];
    [self hideUIMenuController];
}

- (void)removeHandleUIMenuItemViewFromWindow {
    UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
    if ([keyWindow.subviews containsObject:self.handleUIMenuItemView]) {
        [self.handleUIMenuItemView removeFromSuperview];
    }
}

- (void)changeCellLongPressBackground {
    [self removeHandleUIMenuItemViewFromWindow];
    [self highlightCell];
}

- (void)highlightCell {
    UITableViewCell *cell = [self.tableView cellForRowAtIndexPath:self.longPressIndexPath];
    if ([cell isKindOfClass:IMYUGCCommentCell.class]) {
        IMYUGCCommentCell *cellTemp = (IMYUGCCommentCell *)cell;
        [cellTemp setTextBackgroundHilighted:NO];
    }
}

#pragma mark-- 长按菜单
- (void)deleteActionReplyLongPress {
    [self removeHandleUIMenuItemView];
    @weakify(self);
    NSString *message = @"要删除该回复吗？";
    [IMYEventHelper event:@"plxq-sc"];
    [UIAlertController
        imy_showAlertViewWithTitle:nil
                           message:message
                 cancelButtonTitle:IMYString(@"取消")
                 otherButtonTitles:@[IMYString(@"删除")]
                           handler:^(UIAlertController *alertController, NSInteger buttonIndex) {
                               @strongify(self);
                               if (buttonIndex == 1) {
                                   if (![IMYNetState networkEnable]) {
                                       [UIWindow imy_showTextHUD:IMYString(@"咦？网络不见了，请检查网络连接")];
                                       return;
                                   }
                                   [[[self.viewModel deleteCommentWithID:self.longPressCommentModel.commentID] deliverOnMainThread] subscribeNext:^(id x) {
                                       @strongify(self);
                                       self.viewModel.referenced.referenced_num -= 1;
                                       if (self.reloadDataSourceBlock) {
                                           self.reloadDataSourceBlock();
                                       }
                                   }];
                               }
                           }];
}

- (void)reportActionLongPress {
    [self removeHandleUIMenuItemView];
    [IMYEventHelper event:@"plxq-jb"];
    [self.tableView endEditing:YES];
    [TTQDetailHelper reportTopicAction:self.longPressCommentModel.commentID postType:2 topicId:self.viewModel.topic_id callback:nil];
}

- (void)copyActionLongPress {
    [self removeHandleUIMenuItemView];
    NSMutableString *string = [NSMutableString new];
    NSString *content = [self.longPressCommentModel.content imyr_replaceEmotionStringByString:@"" withType:IMYRReplaceEmotionStringTypeDynamic];
    if (content) {
        [string appendString:content];
    }
    IMYSwitchModel *door = [[IMYDoorManager sharedManager] switchForType:@"copy_copyright"];
    NSString *words = door.dataDictionary[@"words"];
    if (words != nil) {
        [string appendString:words];
    }
    
    [UIPasteboard generalPasteboard].string = string;
    
    [UIMenuController sharedMenuController].menuItems = nil;
    [UIView imy_showTextHUD:IMYString(@"复制成功")];
}

- (UIView *)handleUIMenuItemView {
    if (!_handleUIMenuItemView) {
        _handleUIMenuItemView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
        @weakify(self);
        [_handleUIMenuItemView bk_whenTapped:^{
            @strongify(self);
            [self removeHandleUIMenuItemView];
        }];
    }
    return _handleUIMenuItemView;
}

- (void)hideUIMenuController {
    UIMenuController *menu = [UIMenuController sharedMenuController];
    if (menu.isMenuVisible) {
        [menu setMenuVisible:NO animated:YES];
    }
    [self highlightCell];
}

#pragma mark - BI
- (void)postCommentBiFeedsView:(NSInteger)action commentId:(NSInteger)commentId floor:(NSInteger)floor clickpos:(NSInteger)clickpos {
    
}

- (void)postCommentBiFeedsView:(NSInteger)action commentId:(NSInteger)commentId floor:(NSInteger)floor clickpos:(NSInteger)clickpos publicType:(nullable NSString *)publicType {
    NSInteger entrance = [self biEntrance];
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"action"] = @(action);
    params[@"entrance"] = @(entrance);
    params[@"position"] = @141;
    params[@"channel_id"] = @2;
    params[@"clickpos_feedscard"] = @(clickpos);
    params[@"info_type"] = @63;
    params[@"info_id"] = @(commentId);
    params[@"floor"] = @(floor);
    params[@"data_id"] = @(self.viewModel.topic_id);
    if (imy_isNotEmptyString(publicType)) {
        params[@"public_type"] = publicType;
    }
    [IMYGAEventHelper postWithPath:@"bi_feeds_view" params:params headers:nil completed:nil];
}


- (NSInteger)floorForCommentId:(NSInteger)commentId {
    TTQCommentModel *model = [self.viewModel.dataSource match:^BOOL(id  _Nonnull element) {
        if ([element isKindOfClass:TTQCommentModel.class] && [element commentID] == commentId) {
            return YES;
        }
        return NO;
    }];
    return model?[self.viewModel.dataSource indexOfObject:model]:-1;
}

@end
