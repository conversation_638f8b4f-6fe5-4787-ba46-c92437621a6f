//
//  TTQTopicCommentSectionHeader.m
//  IMYTTQ
//
//  Created by 连晓彬 on 2025/6/4.
//

#import "TTQTopicCommentSectionHeader.h"
#import "TTQContentFollowView.h"
#import "TTQTopicDetailScreenTipToolView.h"
#import <IMYUGC/IMYCKFeedsHelper.h>

@interface TTQTopicCommentSectionHeader()
@property (nonatomic, assign) NSInteger biFeedsEntrance;///< 入口标识
@property (nonatomic, strong) TTQTopicViewModel *viewModel;///< 话题详情viewModel

@property (nonatomic, strong) TTQTopicDetailScreenTipToolView *screenTipToolView;  //筛选tool, 放在 sectionHeaderView 上
@property (nonatomic, strong) TTQContentFollowView *followView;///< 放在 sectionHeaderView 上
@end

@implementation TTQTopicCommentSectionHeader

- (instancetype)initWithFrame:(CGRect)frame viewModel:(TTQTopicViewModel *)viewModel biFeedsEntrance:(NSInteger)biFeedsEntrance {
    self = [super initWithFrame:frame];
    if (self) {
        self.viewModel = viewModel;
        self.biFeedsEntrance = biFeedsEntrance;
        self.backgroundColor = [UIColor clearColor];
        
        if (viewModel.isUGCUIStlye) {
            self.backgroundColor = [UIColor imy_colorForKey:kCK_White_AN];
            UIView *lineView = [[UIView alloc] initWithFrame:CGRectMake(12, 0, SCREEN_WIDTH - 24, 0.5)];
            lineView.backgroundColor = [UIColor imy_colorWithHexString:@"#E8E8E8"];
            [self addSubview:lineView];
        } else {
            self.backgroundColor = [UIColor imy_colorForKey:kCK_White_ANQ];
        }
        [self clipsToBounds];
        /// 8.0.6评论切换栏不吸顶了，所以直接加载sectionHeader上，固定位置展示
        [self addSubview:self.screenTipToolView];
        self.imy_top = [self screenTipViewStartY];
    }
    return self;
}

/// 更新
- (void)updateHeaderView{
    if (![self.viewModel isShowCommentsSwitch] ) {// 没有评论的时候，隐藏“最新/最热”切换按钮
        self.screenTipToolView.sortBtn.hidden = YES;
    } else {
        self.screenTipToolView.sortBtn.hidden = NO;
    }
    self.screenTipToolView.hidden = NO;
    self.screenTipToolView.imy_top = [self screenTipViewStartY];
    
    if (!self.viewModel.subsequentModel || ![IMYCKFeedsHelper isOpenSubsequent]) {
        self.needShowSubsequent = NO;
        self.followView.hidden = YES;
    } else {
        self.needShowSubsequent = YES;
        self.followView.hidden = NO;
        BOOL isAuthor = self.viewModel.topicUserID == [[IMYPublicAppHelper shareAppHelper].userid integerValue];
        [self.followView updateData:self.viewModel.subsequentModel isAuthor:isAuthor];
        self.followView.topicId = self.viewModel.topic_id;
        self.followView.forum_id = self.viewModel.topic.forum_id;
        self.followView.topicUserID = self.viewModel.topicUserID;
    }
    
    if (self.needShowSubsequent && !self.followView.superview) {
        [self addSubview:self.followView];
        self.followView.imy_top = self.screenTipToolView.imy_bottom;
    }
}

- (CGFloat)viewHeight:(BOOL)hasTableHeaderView{
    if (hasTableHeaderView) {
        return [self screenTipViewStartY] + self.screenTipToolView.imy_height - 1 + (self.needShowSubsequent? [TTQContentFollowView viewHeight]:0);
    }
    return self.screenTipToolView.imy_height - 1 + (self.needShowSubsequent?[TTQContentFollowView viewHeight]:0);
}


/// 更新评论条数
/// - Parameter count: 评论条数
- (void)updateCommentCount:(NSUInteger)count {
    if (count > 0) {
        self.screenTipToolView.title.text = [NSString stringWithFormat:IMYString(@"评论 %ld条"),count];
    } else {
        self.screenTipToolView.title.text = IMYString(@"评论");
    }
}


- (void)updateSort:(BOOL)isHot {
    [self.screenTipToolView setOrderAppearbyIndex:isHot?0:1];
}

//MARK: - private methods

- (CGFloat)screenTipViewStartY {
    return self.viewModel.isUGCUIStlye ? 1 : 10;
}

- (NSArray *)orderTitles {
    return @[[TTQTopicOrderByModel new].orderbyDescriptionWith(IMYString(@"最热")).orderbyFilterWith(TTQOrderByFilterHot),
             [TTQTopicOrderByModel new].orderbyDescriptionWith(IMYString(@"最新")).orderbyFilterWith(TTQOrderByFilterLastest)];
}

- (TTQTopicDetailScreenTipToolView *)screenTipToolView{
    if (!_screenTipToolView) {
        @weakify(self);
        TTQTopicDetailScreenTipToolView *view = [TTQTopicDetailScreenTipToolView topicDetailScreenTipToolView:nil andMenuAction:nil orderbyTitles:[self orderTitles] andOrderbyAction:^BOOL(NSInteger index, TTQTopicOrderByModel *orderbyModel, IMYButton *button) {
            @strongify(self);
            if (orderbyModel.orderbyFilter != self.viewModel.orderByFilter) {
                self.viewModel.datasourceOrderFilter = self.viewModel.orderByFilter;
                !self.changeOrderFilterBlk?:self.changeOrderFilterBlk(orderbyModel.orderbyFilter, YES);
            }
            switch (orderbyModel.orderbyFilter) {
                case TTQOrderByFilterHot: {
                    [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqypl_zxzr",@"public_type":@2,@"action":@2} headers:nil completed:nil];
                    break;
                }
                case TTQOrderByFilterLastest: {
                    [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqypl_zxzr",@"public_type":@1,@"action":@2} headers:nil completed:nil];
                    break;
                }
                default:
                    break;
            }
            button.tag = 1;
            return YES;
        }];
        view.imyut_eventInfo.eventName = [NSString stringWithFormat:@"screenTipToolView_%@",[NSDate date]];
        view.orderbyAppearActionBlock = ^(NSInteger index, TTQTopicOrderByModel *orderbyModel, IMYButton *orderbyButton) {
            if (orderbyButton.tag == 0 && orderbyModel.orderbyFilter == TTQOrderByFilterNone) {
                [orderbyButton imy_setTitle:IMYString(@"默认")];
            }
        };
        view.orderbyEnableBlock = ^BOOL(IMYButton *button) {
            @strongify(self);
            !self.hideKeyboardBlk?:self.hideKeyboardBlk();
            if (![IMYNetState networkEnable]) {
                [UIWindow imy_showTextHUD:IMYString(kStatusText_networkDisconnectCache)];
                return NO;
            }
            return YES;
        };
        _screenTipToolView = view;
    }
    return _screenTipToolView;
}

- (TTQContentFollowView *)followView{
    if (!_followView) {
        _followView = [[TTQContentFollowView alloc] initWithFrame:CGRectMake(0, self.screenTipToolView.imy_bottom, SCREEN_WIDTH, 68)];
        _followView.biEntrance = self.biFeedsEntrance;
        @weakify(self);
        [_followView setUpdateSubsequentData:^(IMYSubsequentModel * _Nonnull data) {
            @strongify(self);
            self.viewModel.subsequentModel = data;
        }];
    }
    return _followView;
}
@end
