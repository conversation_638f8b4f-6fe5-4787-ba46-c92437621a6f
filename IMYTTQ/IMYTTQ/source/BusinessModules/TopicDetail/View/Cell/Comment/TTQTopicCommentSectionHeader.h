//
//  TTQTopicCommentSectionHeader.h
//  IMYTTQ
//
//  Created by 连晓彬 on 2025/6/4.
//

#import <UIKit/UIKit.h>
#import <IMYBaseKit/IMYBaseKit.h>
#import "TTQTopicViewModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface TTQTopicCommentSectionHeader : UIView

@property (nonatomic, copy) void (^hideKeyboardBlk)(void);///< 隐藏键盘
@property (nonatomic, copy) void (^changeOrderFilterBlk)(TTQTopicFilter topicFilter, BOOL shouldLocateToComment);///< 切换栏目

@property (nonatomic, assign) BOOL needShowSubsequent;///< 是否需要展示蹲蹲

- (instancetype)initWithFrame:(CGRect)frame viewModel:(TTQTopicViewModel *)viewModel biFeedsEntrance:(NSInteger)biFeedsEntrance;
/// 更新
- (void)updateHeaderView;

- (CGFloat)viewHeight:(BOOL)hasTableHeaderView;

/// 更新评论条数
/// - Parameter count: 评论条数
- (void)updateCommentCount:(NSUInteger)count;

- (void)updateSort:(BOOL)isHot;
@end

NS_ASSUME_NONNULL_END
