//
//  TTQCommentCellRichMainView.m
//  IMYTTQ
//
//  Created by 连晓彬 on 2025/5/15.
//

#import "TTQCommentCellRichMainView.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import <IMYBaseKit/IMYRM80AttributedLabelURL.h>
#import <IMYBaseKit/NSRegularExpression+IMYR.h>
#import <Myhpple/TFHpple.h>
#import "NSAttributedString+TTQ.h"
#import "IMYRM80AttributedLabel+IMYQA.h"
#import "IMYRM80AttributedLabel+TTQ.h"
#import "NSRegularExpression+TTQ.h"
#import "TTQTagIcon.h"
#import "TTQCommonHelp.h"
#import "UIImageView+TTQ.h"
#import "NSString+TTQ.h"
#import "TTQTopicBaseViewModel.h"

//MARK: - TTQCommentImageItemView
@interface TTQCommentImageItemView : UIView
@property (nonatomic, strong) YYAnimatedImageView *imageView;
@property (nonatomic, strong) UIView *blurMaskView;

@property (nonatomic, copy) void (^imageViewTapBlk)(void);///< 图片点击回调
@property (nonatomic, copy) void (^maskViewTapBlk)(void);///< 点击蒙层回调
@end

@implementation TTQCommentImageItemView
- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self addSubview:self.imageView];
        [self.imageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(self);
        }];
    }
    return self;
}

- (void)showBlurView:(BOOL)show{
    if (!show) {
        if (self.blurMaskView.superview) {
            [self.blurMaskView removeFromSuperview];
        }
        return ;
    }
    self.blurMaskView.alpha = 1;
    if (!self.blurMaskView.superview) {
        [self addSubview:self.blurMaskView];
        [self.blurMaskView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(self);
        }];
    }
    [self layoutIfNeeded];
}


//MARK: - getter
- (YYAnimatedImageView *)imageView{
    if (!_imageView) {
        YYAnimatedImageView *imageView = [[YYAnimatedImageView alloc] init];
        imageView.clipsToBounds = YES;
        imageView.userInteractionEnabled = YES;
        imageView.contentMode = UIViewContentModeScaleAspectFill;
        [imageView updateWithImageType:TTQImageTypePic];
        @weakify(self);
        [imageView bk_whenTapped:^{
            @strongify(self);
            !self.imageViewTapBlk?:self.imageViewTapBlk();
        }];
        
        _imageView = imageView;
    }
    return _imageView;
}

- (UIView *)blurMaskView{
    if (!_blurMaskView) {
        UIView *view = [[UIView alloc] init];
        
        UIVisualEffectView *blurView = [[UIVisualEffectView alloc] init];
        blurView = [[UIVisualEffectView alloc] initWithEffect:[UIBlurEffect effectWithStyle:UIBlurEffectStyleLight]];
        blurView.contentView.backgroundColor = [[UIColor imy_colorWithHexString:kCK_Black_B] colorWithAlphaComponent:0.3];
        [view addSubview:blurView];
        [blurView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(view);
        }];
        
        UILabel *label1 = [[UILabel alloc] init];
        [label1 imy_setTextColorForKey:kCK_White_A];
        label1.font = [UIFont systemFontOfSize:12];
        [label1 imy_setText:@"图片可能引起不适"];
        [view addSubview:label1];
        
        UILabel *label2 = [[UILabel alloc] init];
        [label2 imy_setTextColorForKey:kCK_White_A];
        label2.font = [UIFont systemFontOfSize:12];
        [label2 imy_setText:@"点击查看"];
        [view addSubview:label2];
        
        [label1 mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(view);
            make.bottom.equalTo(view.mas_centerY).offset(-2);
            make.height.mas_equalTo(17);
        }];
        
        [label2 mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(view);
            make.top.equalTo(view.mas_centerY).offset(2);
            make.height.mas_equalTo(17);
        }];
        
        @weakify(self);
        [view bk_whenTapped:^{
            @strongify(self);
            [UIView animateWithDuration:0.3 animations:^{
                self.blurMaskView.alpha = 0;
            } completion:^(BOOL finished) {
                [self.blurMaskView removeFromSuperview];
            }];
            !self.maskViewTapBlk?:self.maskViewTapBlk();
        }];
        
        _blurMaskView = view;
    }
    return _blurMaskView;
}

@end

//MARK: - TTQCommentCellRichMainView
@interface TTQCommentCellRichMainView()<IMYRM80AttributedLabelDelegate>
@property (nonatomic, strong) IMYRM80AttributedLabel *contentLabel;
/// 作为U+插件的曝光view
@property (nonatomic, strong) UIView *youPlusExposureView;
@property (nonatomic, strong) id uPlusData;

@property (nonatomic, strong) UIView *imageContainerView;///< 图片容器视图
@property (nonatomic, strong) NSArray<TTQCommentImageItemView *> *imageViews;
@property (nonatomic, strong) NSMutableArray *photos;

@property (nonatomic, strong) TTQCommentModel *model;
@property (nonatomic, strong) TTQCommentCellUIConfig *config;
@end

@implementation TTQCommentCellRichMainView

- (instancetype)initWithFrame:(CGRect)frame style:(TTQCommentCellUIConfigStyle)style{
    TTQCommentCellUIConfig *config = [[TTQCommentCellUIConfig alloc] initWithStyle:style];
    return [self initWithFrame:frame config:config];
}


- (instancetype)initWithFrame:(CGRect)frame config:(TTQCommentCellUIConfig *)config{
    self = [super initWithFrame:frame];
    if (self) {
        self.config = config;
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    
    [self addSubview:self.contentLabel];
    [self.contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).mas_offset(self.config.leftMargin);
        make.top.bottom.equalTo(self);
        make.width.mas_equalTo(self.config.contentWidth);
        make.right.equalTo(self).mas_offset(-12);
        make.height.mas_equalTo(20);
    }];
    
    [self addSubview:self.imageContainerView];
    [self.imageContainerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).mas_offset(self.config.leftMargin);
        make.top.bottom.equalTo(self.contentLabel).mas_offset(8);
        make.right.lessThanOrEqualTo(self).mas_offset(-12);
    }];
    
    if (self.config.style == TTQCommentCellUIConfigStyleTopic) {
        self.youPlusExposureView = [UIView new];
        [self addSubview:self.youPlusExposureView];
        [self.youPlusExposureView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(self.contentLabel);
        }];
        self.youPlusExposureView.hidden = YES;
        self.youPlusExposureView.userInteractionEnabled = NO;
    }
    
}
//MARK: - public methods

/// 更新数据
/// - Parameters:
///   - model: 评论内容
///   - referencedID: 特殊处理， -1 时会展示当前评论者的昵称
///   - cacheKey: 缓存 key
///   - Returns: 文本的高度
- (CGFloat)updateWithModel:(TTQCommentModel *)model referencedID:(NSInteger)referencedID cacheKey:(nullable NSString *)cacheKey {
    self.model = model;
    
    CGFloat viewHeight = 0;
    
    CGFloat contentHeight = [self updateContentWithModel:model referencedID:referencedID cacheKey:cacheKey];
    [self.contentLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).mas_offset(self.config.leftMargin);
        make.top.equalTo(self);
        make.bottom.lessThanOrEqualTo(self);
        make.right.lessThanOrEqualTo(self);
        make.size.mas_equalTo(CGSizeMake(self.config.contentWidth, contentHeight));
    }];
    
    if (contentHeight > 0) {
        viewHeight = contentHeight;
    }
    
    CGFloat imageHeight = [self updateImagesWithModel:model];
    [self.imageContainerView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).mas_offset(self.config.leftMargin);
        if (contentHeight > 0) {
            make.top.equalTo(self).mas_offset(viewHeight + 4);
        }else{
            make.top.equalTo(self).mas_offset(viewHeight);
        }
        make.bottom.equalTo(self);
        make.right.lessThanOrEqualTo(self).mas_offset(-12);
    }];
    if (imageHeight > 0) {
        if (contentHeight > 0) {
            viewHeight += 4; // 内容和图片之间的间距
        }
        viewHeight += imageHeight;
    }
    
    return viewHeight;
}

- (CGFloat)updateContentWithModel:(TTQCommentModel *)model referencedID:(NSInteger)referencedID cacheKey:(nullable NSString *)cacheKey{
    if (self.config.needFoldComment && !model.isOpen) {
        self.contentLabel.numberOfLines = self.config.commentMaxRows;
    }else{
        self.contentLabel.numberOfLines = 0;
    }
    self.contentLabel.imy_width = self.config.contentWidth;
    
    [self.contentLabel setAttributedText:nil];
    self.contentLabel.showAttributedString = nil;
    switch (self.config.style) {
        case TTQCommentCellUIConfigStyleTopic:
            self.youPlusExposureView.hidden = YES;
            self.uPlusData = nil;
            [self updateTopicContentWithModel:model];
            break;
        case TTQCommentCellUIConfigStyleTopicSub:
        case TTQCommentCellUIConfigStyleComment:
        case TTQCommentCellUIConfigStyleAnswerDetail:
            [self updateCommonWithModel:model referencedID:referencedID cacheKey:cacheKey];
            break;
        default:
            NSAssert(NO, @"不支持的评论cell类型");
            break;
    }

    [self checkAddFoldTailLink];//添加“收起”尾巴
    [self.contentLabel imyr_autoAdjustHeight];
    [self.contentLabel setNeedsDisplay];
    
    if (self.config.style == TTQCommentCellUIConfigStyleTopic) {
        //柚+
        self.youPlusExposureView.imyut_eventInfo.eventName = [NSString stringWithFormat:@"youplus_%ld",model.commentID];
        self.youPlusExposureView.imyut_eventInfo.showRadius = 1;
        /// 过滤底部评论输入框高度
        self.youPlusExposureView.imyut_eventInfo.edgeOffset = UIEdgeInsetsMake(0, 0, 52+SCREEN_TABBAR_SAFEBOTTOM_MARGIN, 0);
        
        @weakify(self);
        [self.youPlusExposureView.imyut_eventInfo setExposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
            @strongify(self);
            if (self.contentExposuredBlk && self.uPlusData) {
                self.contentExposuredBlk(self.uPlusData);
            }
        }];
    }
    return ceilf(self.contentLabel.imy_height);
}

//MARK: - private methods (通用评论内容)
- (void)updateCommonWithModel:(TTQCommentModel *)model referencedID:(NSInteger)referencedID cacheKey:(nullable NSString *)cacheKey{
    
    if (imy_isNotEmptyString(cacheKey) && [self.contentLabel imyr_readCacheWithKey:cacheKey]) {
        //有缓存
        return ;
    }
    
    CGFloat length = 0;
    if (referencedID == -1) {
        [self.contentLabel appendText:model.publisher.screen_name];
        length = model.publisher.screen_name.length;
        [self.contentLabel addCustomLink:@"from" forRange:NSMakeRange(0, length)];
    }
    
    if (model.replygoal && imy_isNotEmptyString(model.replygoal.screen_name)) {
        NSString *replyString = IMYString(@"回复 ");
        if (referencedID == -1) {
            replyString = IMYString(@" 回复 ");
        }
        NSMutableAttributedString *replyAttStr = [[NSMutableAttributedString alloc] initWithString:replyString];
        [replyAttStr setFont:self.contentLabel.font];
        [replyAttStr setTextColor:[UIColor imy_colorForKey:kCK_Black_A]];
        [self.contentLabel appendAttributedText:replyAttStr];
        length += replyString.length;
        
        NSString *replygoalScreenName = model.replygoal.screen_name;
        [self.contentLabel appendText:replygoalScreenName];
        
        if (referencedID != -1) {
            [self.contentLabel addCustomLink:@"to" forRange:NSMakeRange(length, replygoalScreenName.length)];
        } else {
            [self.contentLabel addCustomLink:@"to-reply" forRange:NSMakeRange(length, replygoalScreenName.length)];
        }
    }
    
    IMYRTextParseType parseType = IMYRTextParseTypeEmoji;
    if (model.privilege == 1) {
        parseType = IMYRTextParseTypeEmoji | IMYRTextParseTypeForceURL;
    }
    
    if (length > 0) {
        NSString *text = @"：";
        [self.contentLabel imyr_appendText:@"：" parseType:parseType];
    }
    if (referencedID == -1) { //特殊需求，叠楼透出换行改为空格
        NSString *content = [model.content stringByReplacingOccurrencesOfString:@"\n" withString:@" "];
        content = [content stringByReplacingOccurrencesOfString:@"\r" withString:@" "];
        length += content.length;
    } else {
        [self.contentLabel imyr_appendText:model.content parseType:parseType];
    }

    if (model.subject_struct.count) {
        NSAttributedString *attriString = [self.contentLabel getBuildAttributedString];
        NSArray *results = [TTQCommonHelp matchsTopicsInString:attriString.string];
        NSMutableArray<IMYRM80AttributedLabelURL *> *links = @[].mutableCopy;
        for (int i = 0; i < results.count; i++) {
            NSTextCheckingResult *result = results[i];
            if (i >= model.topicsLinks.count) {
                break;
            }
            IMYRM80AttributedLabelURL *tempLink = model.topicsLinks[i];
            IMYRM80AttributedLabelURL *link = [IMYRM80AttributedLabelURL urlWithLinkData:tempLink.linkData range:result.range color:tempLink.color];
            [links addObject:link];
        }
        [self.contentLabel.linkLocations addObjectsFromArray:links];
    }
}


//MARK: - private methods (话题详情一级评论)
- (void)updateTopicContentWithModel:(TTQCommentModel *)model {
    
    BOOL isWarm = model.is_quality_reviews && !model.is_ad_logo;
    if (isWarm) {
        [self.contentLabel imyr_appendTagsWith:@[[TTQTagIconModel forumTagIconWith:ForumTagWarm]]];
    }
    IMYRTextParseType parseType = IMYRTextParseTypeEmoji;
    if (model.privilege == 1) {//超链接标签带有样式显示文案
        self.contentLabel.numberOfLines = 0;/// 不折叠， 配置支持折叠线上配置会特定条件下会展示异常，
        parseType = IMYRTextParseTypeEmoji | IMYRTextParseTypeForceURL;
        [self.contentLabel imyr_appendText:model.content parseType:parseType];
        //IMYRTextParseTypeForceURL：R80内部对超链接有解析，所以这边选择渲染之后取得attributeString遍历热议话题，以防点击区域发生偏移
        NSAttributedString *attriString = [self.contentLabel getBuildAttributedString];
        NSArray *results = [TTQCommonHelp matchsTopicsInString:attriString.string];
        NSMutableArray<IMYRM80AttributedLabelURL *> *links = @[].mutableCopy;
        for (int i = 0; i < results.count; i++) {
            NSTextCheckingResult *result = results[i];
            if (i >= model.topicsLinks.count) {
                break;
            }
            IMYRM80AttributedLabelURL *tempLink = model.topicsLinks[i];
            IMYRM80AttributedLabelURL *link = [IMYRM80AttributedLabelURL urlWithLinkData:tempLink.linkData range:result.range color:tempLink.color];
            [links addObject:link];
        }
        if (links.count) {
            [self.contentLabel.linkLocations addObjectsFromArray:links];
        }
    } else {//超链接不带样式，只是高亮显示
 
        parseType = IMYRTextParseTypeEmoji;
        NSMutableArray *links = @[].mutableCopy;
        NSMutableString *contentMutable = @"".mutableCopy;
        
        //超链接标签高亮显示
        NSRegularExpression *regex = [NSRegularExpression imyr_A_tagExpression];
        NSArray *matches = [regex matchesInString:model.content options:0 range:NSMakeRange(0, model.content.length)];
        NSUInteger offset = 0;
        NSUInteger i = 0;
        NSTextCheckingResult *lastMatch;
        for (NSTextCheckingResult *match in matches) {
            lastMatch = match;
            if (match.range.location > offset) {
                [contentMutable appendString:[model.content substringWithRange:NSMakeRange(offset, match.range.location - offset)]];
            }
            offset = NSMaxRange(match.range);
            NSString *mathText = [model.content substringWithRange:match.range];
            //解析A标签 href内容
            NSTextCheckingResult *hrefMatch = [[NSRegularExpression imyr_A_hrefExpression] firstMatchInString:mathText options:0 range:NSMakeRange(0, mathText.length)];
            NSString *ahref = nil;
            if (hrefMatch.range.length > 0) {
                NSRange hrefRange = hrefMatch.range;
                hrefRange.length -= 1;
                ahref = [mathText substringWithRange:hrefRange];
                NSRange hrefStartRange = [ahref rangeOfCharacterFromSet:[NSCharacterSet characterSetWithCharactersInString:@"'\""] options:NSBackwardsSearch];
                if (hrefStartRange.length > 0) {
                    ahref = [ahref substringFromIndex:hrefStartRange.location + 1];
                }
            }
            
            NSInteger location = [mathText rangeOfString:@">"].location + 1;
            NSInteger length = (mathText.length - location - 4);
            NSString *showText = [mathText substringWithRange:NSMakeRange(location, length)];
            IMYRM80AttributedLabelURL *link = [IMYRM80AttributedLabelURL urlWithLinkData:ahref range:NSMakeRange(contentMutable.length, showText.length) color:nil];
            [links addObject:link];
            [contentMutable appendString:showText];
        }
        
        if (model.content.length > NSMaxRange(lastMatch.range)) {
            [contentMutable appendString:[model.content substringFromIndex:lastMatch.range.location + lastMatch.range.length]];
        }
        contentMutable = [self uplusCommentWithModel:model content:contentMutable links:links];
        
        [self.contentLabel imyr_appendText:contentMutable parseType:parseType];
        NSMutableAttributedString *attriString = (NSMutableAttributedString *)[self.contentLabel getBuildAttributedString];
        //遍历热议话题，高亮显示
        NSArray *results = [TTQCommonHelp matchsTopicsInString:attriString.string];
        NSMutableArray<IMYRM80AttributedLabelURL *> *topicLinks = @[].mutableCopy;
        for (int i = 0; i < results.count; i++) {
            NSTextCheckingResult *result = results[i];
            if (i >= model.topicsLinks.count) {
                break;
            }
            IMYRM80AttributedLabelURL *tempLink = model.topicsLinks[i];
            if (tempLink.linkData) {
                /// 有效的话题才展示和跳转
                IMYRM80AttributedLabelURL *link = [IMYRM80AttributedLabelURL urlWithLinkData:tempLink.linkData range:result.range color:tempLink.color];
                [topicLinks addObject:link];
            }
        }
        if (links.count) {
            [self.contentLabel.linkLocations addObjectsFromArray:links];
        }
        if (topicLinks.count) {
            [self.contentLabel.linkLocations addObjectsFromArray:topicLinks];
        }
    }
}

- (NSString *)uplusCommentWithModel:(TTQCommentModel *)model content:(NSString *)content links:(NSMutableArray *)links {
    //超链接标签高亮显示
    NSRegularExpression *regex = [NSRegularExpression ttq_youplus_TagExpression];
    NSArray *matches = [regex matchesInString:model.content options:0 range:NSMakeRange(0, model.content.length)];
    NSUInteger offset = 0;
    NSUInteger i = 0;
    NSTextCheckingResult *lastMatch;
    NSMutableString *contentMutable = @"".mutableCopy;
    for (NSTextCheckingResult *match in matches) {
        lastMatch = match;
        if (match.range.location > offset) {
            /// 添加匹配前的文字
            [contentMutable appendString:[content substringWithRange:NSMakeRange(offset, match.range.location - offset)]];
        }
        offset = NSMaxRange(match.range);
        NSString *nodeString = [content substringWithRange:match.range];
        TFHpple *doc = [[TFHpple alloc] initWithXMLData:[nodeString dataUsingEncoding:NSUTF8StringEncoding]];
        NSArray *elements = [doc searchWithXPathQuery:@"//youplus"];
        for (TFHppleElement *element in elements) {
            if ([element.tagName isEqualToString:@"youplus"]) {
                /// 匹配出柚加的数据
                TTQTopicRichParserYouPlusModel *youplus = [element.attributes toModel:TTQTopicRichParserYouPlusModel.class];
                if (imy_isNotEmptyString(youplus.btn_txt)) {
                    IMYRM80AttributedLabelURL *link = [IMYRM80AttributedLabelURL urlWithLinkData:youplus range:NSMakeRange(contentMutable.length, youplus.btn_txt.length) color:[UIColor imy_colorForKey:@"4F7CB0"]];
                    [links addObject:link];
                    [contentMutable appendString:youplus.btn_txt];
                    self.uPlusData = youplus;
                    self.youPlusExposureView.hidden = NO;
                }
            }
        }
    }
    if (content.length > NSMaxRange(lastMatch.range)) {
        /// 添加match之后的文字
        [contentMutable appendString:[content substringFromIndex:lastMatch.range.location + lastMatch.range.length]];
    }
    return contentMutable;
}

- (void)checkAddFoldTailLink{
    if (self.config.needFoldComment && self.model.isOpen) {
        NSInteger  length = [self.contentLabel getBuildAttributedString].string.length;
        NSString *text = @" 收起";
        NSMutableAttributedString *foldStr = [[NSMutableAttributedString alloc] initWithString:text];
        [foldStr setFont: self.contentLabel.font];
        [foldStr setTextColor:[UIColor imy_colorForKey:kCK_Colour_A]];
        [self.contentLabel appendAttributedText:foldStr];
        
        [self.contentLabel addCustomLink:@"收起" forRange:NSMakeRange(length, text.length) linkColor:[UIColor imy_colorForKey:kCK_Colour_A]];
    }
}
//MARK: - IMYRM80AttributedLabelDelegate
- (void)m80AttributedLabel:(IMYRM80AttributedLabel *)label longedOnLink:(IMYRM80AttributedLabelURL *)linkURL {
    if (linkURL.linkData && [linkURL.linkData isKindOfClass:NSString.class]) {
        NSString *action = (NSString *)linkURL.linkData;
        if ([action isEqualToString:@"展开"] || [action isEqualToString:@"收起"]) {
            //长按过滤 展开和收起
            return ;
        }
    }
    !self.contentLongedOnLinkBlk?:self.contentLongedOnLinkBlk(label, linkURL);
}

- (void)m80AttributedLabel:(IMYRM80AttributedLabel *)label clickedOnLink:(IMYRM80AttributedLabelURL *)linkURL {
    if (linkURL.linkData && [linkURL.linkData isKindOfClass:NSString.class]) {
        NSString *action = (NSString *)linkURL.linkData;
        if ([action isEqualToString:@"展开"] || [action isEqualToString:@"收起"]) {
            self.model.isOpen = !self.model.isOpen;
            !self.reloadContentBlk?:self.reloadContentBlk();
            return ;
        }
    }
    !self.contentClickedOnLinkBlk?:self.contentClickedOnLinkBlk(label, linkURL);
}

//MARK: - update ImagesView
- (CGFloat)updateImagesWithModel:(TTQCommentModel *) model{
    
    [self.imageViews bk_each:^(TTQCommentImageItemView *obj) {
        [obj.imageView sd_cancelCurrentImageLoad];
    }];
    [self.imageContainerView imy_removeAllSubviews];
    if (model.images_list.count == 0) {
        self.imageContainerView.hidden = YES;
        return 0;
    }
    self.imageContainerView.hidden = NO;
    //最大截取 3 个
    NSRange range = NSMakeRange(0, MIN(model.images_list.count, self.config.maxImageCount));
    NSArray<NSDictionary *> *imageList = [model.images_list subarrayWithRange:range];
    NSMutableArray *photos = [[NSMutableArray alloc] init];
    
    CGSize imageSize = CGSizeZero;
    UIView *lastView = nil;
    for (NSInteger idx = 0; idx < imageList.count; idx ++) {
        NSDictionary *imageObj = imageList[idx];
        NSString *imageUrl = imageObj[@"image_url"];
        CGSize imageItemSize = [imageUrl ttq_communityImageSizeWithMaxWidth:self.config.imageMaxWidth];// 图片宽高在url里面
        TTQCommentImageItemView *itemView = [self.imageViews objectAtIndex:idx];
        BOOL isEmotion = [imageObj[@"is_expression"] intValue] == 1;
        imageSize = [self.class getImageSizeWithOriginalSize:imageItemSize imageCount:imageList.count isEmotion:isEmotion];
        [self.imageContainerView addSubview:itemView];
        [itemView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.top.bottom.equalTo(self.imageContainerView);
            make.size.mas_equalTo(imageSize);
            if (lastView) {
                make.left.equalTo(lastView.mas_right).offset(4);
            }else{
                make.left.equalTo(self.imageContainerView);
            }
        }];
        
        if (isEmotion) {
            /// 表情就使用原图就行
            [itemView.imageView imy_setOriginalImageURL:imageUrl];
        } else {
            [itemView.imageView imy_setImageURL:imageUrl];
        }
        [itemView.imageView imy_setDownloadFinishedBlock:^(UIImageView *tImageView, BOOL success) {
            [tImageView imy_setBackgroundColorForKey:success?kCK_White_AN:kCK_Black_F];
        }];

        if (imy_isNotEmptyString(imageUrl) && !isEmotion) {
            IMYPhoto *photo = [[IMYPhoto alloc] init];
            photo.url = [NSURL URLWithString:imageUrl];
            photo.srcImageView = itemView.imageView;
            [photos addObject:photo];
        }
        itemView.imageView.animationWhenFirstTimeDownload = true;
        [itemView showBlurView:[model needShowBlurMaskAtIndex:idx]];
        lastView = itemView;
    }
    
    self.photos = photos;
    
    if (lastView) {
        [lastView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.lessThanOrEqualTo(self.imageContainerView);
        }];
    }
    return ceilf(imageSize.height);
}


/// 获取图片容器的大小
/// - Parameters:
///   - originalSize: 原始图片大小
///   - imageCount: 图片数量， 最大按 3 个
///   - isEmotion: 是否是表情
+ (CGSize)getImageSizeWithOriginalSize:(CGSize)originalSize imageCount:(NSInteger)imageCount isEmotion:(BOOL)isEmotion {

    CGFloat leftMargin = 56;
    if (imageCount > 1) {//多图统一按 3 图展示
        CGFloat contentWidth = SCREEN_WIDTH - leftMargin - 12;
        CGFloat width = floor(contentWidth / 3.f) - 4 - 4; /// 4 是间距
        return CGSizeMake(width, width);
    }
    
    
    if (isEmotion || originalSize.width == originalSize.height) {//表情 1:1, 或者正方形图
        CGFloat width = SCREEN_WIDTH - leftMargin - 214;
        return CGSizeMake(width, width);
    }
    
    if (originalSize.width > originalSize.height) {
        //横图
        CGFloat width = SCREEN_WIDTH - leftMargin - 179;
        CGFloat height = ceilf(3.0/4.0 * width);
        return CGSizeMake(width, height);
    }else{
        //竖图
        CGFloat width = SCREEN_WIDTH - leftMargin - 214;
        CGFloat height = ceilf(4.0/3.0 * width);
        return CGSizeMake(width, height);
    }
}


//MARK: - getter
- (IMYRM80AttributedLabel *)contentLabel {
    if (_contentLabel == nil) {
        IMYRM80AttributedLabel *label = [[IMYRM80AttributedLabel alloc] initWithFrame:CGRectMake(0, 0, self.config.contentWidth, 20)];
        label.numberOfLines = 0;

        label.font = [UIFont systemFontOfSize:15];
        label.backgroundColor = [UIColor clearColor];
        [label imy_setTextColorForKey:kCK_Black_A];
        label.linkColor = IMY_COLOR_KEY(kCK_Black_M);
        label.imyr_emoticonSize = CGSizeMake(20, 20);
        label.imyr_dynamicEmoticonSize = CGSizeMake(80, 80);
        label.minLineHeight = 20;
        label.singleLineCenter = YES;
        label.delegate = self;
        label.imyr_analysisDynamicEmoticon = NO;
        
        label.textAlignment = kCTTextAlignmentNatural;
        label.lineBreakMode = kCTLineBreakByTruncatingTail;
        
        if (self.config.style == TTQCommentCellUIConfigStyleTopic) {
            label.newStyle = YES;
            label.shouldLongPress = YES;
        }
        
        // 尾巴
        NSMutableAttributedString *truncationAttributedString = [[NSMutableAttributedString alloc] initWithString:@"… 展开"];
        [truncationAttributedString setFont:[UIFont systemFontOfSize:15]];
        [truncationAttributedString setTextColor:[UIColor imy_colorForKey:kCK_Colour_A] range:NSMakeRange(truncationAttributedString.string.length - 2, 2)];
        [truncationAttributedString setTextColor:[UIColor  imy_colorForKey:kCK_Black_A] range:NSMakeRange(0, truncationAttributedString.string.length - 2)];
        label.truncationAttributedString = truncationAttributedString.copy;
        label.truncationLinkData = @"展开";
        
        _contentLabel = label;
    }
    return _contentLabel;
}

- (UIView *)imageContainerView{
    if (!_imageContainerView) {
        _imageContainerView = [UIView new];
        _imageContainerView.layer.cornerRadius = 8;
        _imageContainerView.layer.masksToBounds = YES;
    }
    return _imageContainerView;
}

- (NSArray<TTQCommentImageItemView *> *)imageViews{
    if (!_imageViews) {
        NSMutableArray *array = [NSMutableArray new];
        for (NSInteger i = 0; i < 3; i++) {
        
            TTQCommentImageItemView *itemView = [TTQCommentImageItemView new];
            @weakify(self, itemView);
            itemView.maskViewTapBlk = ^{
                @strongify(self, itemView);
                NSInteger index = [self.imageViews indexOfObject:itemView];
                [self.model removeBlurMaskAtIndex:index];
            };
            itemView.imageViewTapBlk = ^{
                @strongify(self, itemView);
                NSInteger index = [self.imageViews indexOfObject:itemView];
                if (self.photos.count > index) {
                    !self.imageViewTapBlk?:self.imageViewTapBlk(self.photos, index);
                }
            };
            [array addObject:itemView];
        }
        _imageViews = [array copy];
    }
    return _imageViews;
}

@end
