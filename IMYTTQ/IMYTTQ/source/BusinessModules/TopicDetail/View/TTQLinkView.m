//
//  TTQLinkView.m
//  IMYTTQ
//
//  Created by king on 2017/1/18.
//  Copyright © 2017年 MeiYou. All rights reserved.
//

#import "TTQLinkView.h"
#import "GCDQueue.h"
#import <IMYBaseKit.h>
#import <Masonry.h>
#import "TTQCommonHelp.h"

@interface TTQLinkView ()
@property (nonatomic, strong) UILabel *label;
@property (nonatomic, strong) UIImageView *iconImgView;
@property (nonatomic, strong) UILabel *subTitleLabel;
@property (nonatomic, strong) UIButton *adButton;//广告配置的查看按钮778
@property (nonatomic, strong) UIButton *adIcon; // 带有“广告”字段的标志
@end
@implementation TTQLinkView
- (instancetype)initWithCoder:(NSCoder *)coder {
    self = [super initWithCoder:coder];
    if (self) {
        [self commonInit];
    }
    return self;
}
- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self commonInit];
    }
    return self;
}
- (void)commonInit {
    self.layer.cornerRadius = 8;
    self.layer.masksToBounds = YES;
    [self imy_setBackgroundColorForKey:kCK_Black_H];
    self.iconImgView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, 64, 64)];
    self.iconImgView.contentMode = UIViewContentModeScaleAspectFill;
    self.iconImgView.clipsToBounds = YES;
    [self.iconImgView setPlaceholderImageName:@"tata_img_link"];
    
    self.adIcon = [[UIButton alloc] initWithFrame:CGRectMake(4, 49, 18, 11)];
    [self.adIcon imy_setTitleColor:[UIColor imy_colorForKey:kCK_White_A]];
    self.adIcon.titleLabel.font = [UIFont systemFontOfSize:7];
    [self.adIcon setBackgroundColor:[UIColor colorWithWhite:0 alpha:0.16]];
    [self.adIcon imy_setTitle:@"广告"];
    self.adIcon.titleLabel.textAlignment = NSTextAlignmentCenter;
    self.adIcon.layer.cornerRadius = 2;
    [self.iconImgView addSubview:self.adIcon];
    self.adIcon.hidden = YES;
    
    [self addSubview:self.iconImgView];
    self.label = [[UILabel alloc] initWithFrame:CGRectMake(self.iconImgView.imy_right + 8, 0, self.imy_width - self.iconImgView.imy_right - 16, self.imy_height)];
    self.label.numberOfLines = 2;
    self.label.font = [UIFont systemFontOfSize:17];
    [self.label imy_setTextColorForKey:kCK_Black_A];
    [self addSubview:self.label];

    self.subTitleLabel = [[UILabel alloc] initWithFrame:self.label.frame];
    self.subTitleLabel.numberOfLines = 1;
    self.subTitleLabel.font = [UIFont systemFontOfSize:14];
    [self.subTitleLabel imy_setTextColorForKey:kCK_Black_C];
    [self addSubview:self.subTitleLabel];
    
    [self addSubview:self.adButton];

    UILongPressGestureRecognizer *longPressGestureRecognizer = [UILongPressGestureRecognizer new];
    UITapGestureRecognizer *tapGestureRecognizer = [UITapGestureRecognizer new];
    [tapGestureRecognizer requireGestureRecognizerToFail:longPressGestureRecognizer];
    [self addGestureRecognizer:longPressGestureRecognizer];
    [self addGestureRecognizer:tapGestureRecognizer];
    @weakify(self);
    [[tapGestureRecognizer rac_gestureSignal] subscribeNext:^(id x) {
        @strongify(self);
        [self imy_setBackgroundColorForKey:kCK_Black_I];
        [[GCDQueue mainQueue] queueBlock:^{
            @strongify(self);
            [self imy_setBackgroundColorForKey:kCK_Black_H];
        }
                              afterDelay:0.35];
        if (self.clickedBlock) {
            self.clickedBlock();
        }
    }];
    [[longPressGestureRecognizer rac_gestureSignal] subscribeNext:^(UILongPressGestureRecognizer *longPressGestureRecognizer) {
        @strongify(self);
        if (longPressGestureRecognizer.state != UIGestureRecognizerStateBegan) {
            return;
        }
        [self imy_setBackgroundColorForKey:kCK_Black_I];
        [[GCDQueue mainQueue] queueBlock:^{
            @strongify(self);
            [self imy_setBackgroundColorForKey:kCK_Black_H];
        }
                              afterDelay:0.35];

        if (self.longedBlock) {
            self.longedBlock();
        }
    }];
}

- (UIButton *)adButton {
    if (!_adButton) {
        _adButton = [UIButton buttonWithType:UIButtonTypeCustom];
        _adButton.frame = CGRectMake(0, 18, 80, 28);
        [_adButton setBackgroundColor:[UIColor imy_colorForKey:kCK_Red_B]];
        _adButton.titleLabel.font = [UIFont systemFontOfSize:14];
        _adButton.userInteractionEnabled = NO;
        _adButton.layer.cornerRadius = 14;
        _adButton.layer.masksToBounds = YES;
    }
    return _adButton;
}

- (void)setText:(NSString *)text subTitle:(NSString *)subTitle {
    if (subTitle.length > 0) {
        self.label.attributedText = nil;
        self.label.numberOfLines = 1;
        self.label.text = nil;
        self.label.imy_height = self.imy_height / 2.0 - 6;
        self.label.imy_top = 6;
        self.subTitleLabel.imy_height = self.label.imy_height;
        self.subTitleLabel.imy_top = self.label.imy_bottom;
        self.label.text = text;
        self.subTitleLabel.text = subTitle;
        self.subTitleLabel.hidden = NO;
    } else {
        self.subTitleLabel.hidden = YES;
        self.label.text = nil;
        self.label.numberOfLines = 2;
        self.label.imy_height = self.imy_height;
        self.label.imy_top = 0;
        NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
        paragraphStyle.lineSpacing = self.lineSpacing;
        paragraphStyle.paragraphSpacing = self.paragraphSpacing;
        paragraphStyle.lineBreakMode = NSLineBreakByTruncatingTail;
        paragraphStyle.alignment = NSTextAlignmentLeft;
        self.label.attributedText = [[NSAttributedString alloc] initWithString:text attributes:@{NSParagraphStyleAttributeName: paragraphStyle}];
    }
}

- (void)setTextFont:(UIFont *)textFont subTitleFont:(UIFont *)subTitleFont {
    if (textFont) {
        self.label.font = textFont;
    }
    if (subTitleFont) {
        self.subTitleLabel.font = subTitleFont;
    }
}

- (void)setIconUrl:(NSString *)icon {
    if (icon.length > 0) {
        [self.iconImgView imy_setImageURL:icon resize:[TTQCommonHelp optimizedImageSizeWithViewSize:CGSizeMake(64, 64)]];
    } else {
        [self.iconImgView imy_setImage:@"tata_img_link"];
    }
}

- (void)showAdTag{
    self.adIcon.hidden = NO;
}

- (void)setADCheckButton:(NSInteger)isShowBtn andTitle:(NSString*)title {
    self.adButton.hidden = !isShowBtn;
    CGFloat buttonWidth = [TTQCommonHelp getWidthWithText:title height:20 font:[UIFont systemFontOfSize:14]];
    buttonWidth = buttonWidth + 24;
    CGFloat marginOfLabelAndButton = 8;//label与按钮间距
    if (isShowBtn) {
        self.adButton.imy_width = buttonWidth;
        self.adButton.imy_left = self.imy_width - buttonWidth - 12;
        [self.adButton setTitle:title forState:UIControlStateNormal];
        self.label.imy_width = self.imy_width - self.iconImgView.imy_right - 8 - marginOfLabelAndButton - buttonWidth - 12;
        self.subTitleLabel.imy_width = self.imy_width - self.iconImgView.imy_right - 8 - marginOfLabelAndButton - buttonWidth - 12;
    } else {
        self.label.imy_width = self.imy_width - self.iconImgView.imy_right - 16;
        self.subTitleLabel.imy_width = self.imy_width - self.iconImgView.imy_right - 16;
    }
}

@end


@interface TTQButtonLinkView ()
@property (nonatomic, strong) UIButton *button;
@end

@implementation TTQButtonLinkView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self addSubview:self.button];
        [self.button mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(100);
            make.height.mas_equalTo(34);
            make.center.equalTo(self);
        }];
    }
    return self;
}

- (void)setText:(NSString *)text {
    [self.button setTitle:text forState:UIControlStateNormal];
    CGFloat width = [text sizeWithAttributes:@{NSFontAttributeName:self.button.titleLabel.font}].width;
    [self.button mas_updateConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(MAX(100, MIN(SCREEN_WIDTH - 24, ceilf(width + 32))));
    }];
    
}

- (UIButton *)button {
    if (!_button) {
        _button = [UIButton buttonWithType:UIButtonTypeCustom];
        _button.titleLabel.font = [UIFont systemFontOfSize:14];
        [_button setTitleColor:[UIColor imy_colorForKey:kCK_Red_B] forState:UIControlStateNormal];
        [_button setTitleColor:[[UIColor imy_colorForKey:kCK_Red_B] colorWithAlphaComponent:0.7] forState:UIControlStateHighlighted];
        _button.layer.cornerRadius = 17;
        _button.layer.borderColor = [UIColor imy_colorForKey:kCK_Red_B].CGColor;
        _button.layer.borderWidth = 1;
        _button.layer.masksToBounds = YES;
        @weakify(self);
        [_button bk_addEventHandler:^(id sender) {
            @strongify(self);
            !self.clickedBlock ?: self.clickedBlock();
        } forControlEvents:UIControlEventTouchUpInside];
    }
    return _button;
}

@end
