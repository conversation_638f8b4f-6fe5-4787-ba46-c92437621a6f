//
//  TTQCommentDetailHeaderView.m
//  AFNetworking
//
//  Created by 林云峰 on 2025/4/7.
//

#import "TTQCommentDetailHeaderView.h"
#import <IMYBaseKit/IMYViewKit.h>
#import <IMYCommonKit/IMYCKFollowButton.h>
#import "TTQRelateContentView.h"
#import "IMYCKFeedsAuthenticationView.h"
#import "UIImageView+TTQ.h"
#import "NSString+TTQ.h"
#import "TTQTopicRichWithParserView.h"
#import "TTQABTestConfig.h"
#import "TTQTagIcon.h"
#import "UIView+TTQ.h"
#import "TTQPublisherModel+TTQCommentCellTag.h"
#import "IMYUGCBadgesTagView.h"
#import <IMYUGC/IMYUGCEventHelper.h>
#import <IMYBaseKit/IMYRM80AttributedLabelURL.h>

@interface TTQCommentDetailHeaderView () <IMYRM80AttributedLabelDelegate>

@property (nonatomic, strong) TTQCommentModel *data;
@property (nonatomic, strong) IMYAvatarImageView *avatarButton;
@property (nonatomic, strong) IMYRM80AttributedLabel *contentLabel;
@property (nonatomic, strong) UIControl *userInfoControl;

@property (nonatomic, strong) UILabel *nicknameLabel;
@property (nonatomic, strong) UILabel *ipInfoLabel;// ip
@property (nonatomic, strong) UILabel *publishTimeLabel;// 时间
@property (nonatomic, strong) UILabel *authorInfoLabel;   /// 作者信息
@property (nonatomic, strong) IMYCKFollowButton *followButton;
@property (nonatomic, strong) TTQRelateContentView *articleView;   /// 原问题内容
@property (nonatomic, assign) CGFloat contentStartY;
@property (strong, nonatomic) NSMutableArray *commentImageViews;
@property (nonatomic, strong) UILabel *copywriteLabel;
@property (nonatomic, strong) IMYUGCBadgesTagView *authView;   /// 创作者标识

@end

@implementation TTQCommentDetailHeaderView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.commentImageViews = [NSMutableArray array];
        [self imy_setBackgroundColorForKey:kCK_White_AN];
        [self initViews];
    }
    return self;
}

- (void)initViews {
    [self addSubview:self.avatarButton];
    [self addSubview:self.nicknameLabel];
    [self addSubview:self.authorInfoLabel];
    [self addSubview:self.publishTimeLabel];
    [self addSubview:self.ipInfoLabel];
    [self addSubview:self.followButton];
    [self addSubview:self.contentLabel];
    [self addSubview:self.articleView];
    [self addSubview:self.authView];
    [self addSubview:self.copywriteLabel];

    self.userInfoControl = [[UIControl alloc] initWithFrame:CGRectMake(12, 16, 0, 36)];
    [self addSubview:self.userInfoControl];
    @weakify(self);
    [[self.userInfoControl rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
        @strongify(self);
        TTQPublisherModel *user = self.data.publisher;
        [self gotoUserIntroduce:user];
    }];
}

- (void)updateWithData:(TTQCommentModel *)data {
    self.data = data;
    [self updateUser];
    [self updateContent];
}

#pragma mark - 作者区
- (void)updateUser {
    // 封号也要显示关注
    BOOL isShow = (self.data.publisher.error == 0 || self.data.publisher.error == 2 || self.data.publisher.error == 3) && self.data.publisher.userID != [[IMYPublicAppHelper shareAppHelper].userid integerValue];
    [self.avatarButton setAvatarWithURLString:self.data.publisher.user_avatar.large placeholder:[UIImage imy_imageForKey:@"all_usericon"]];
    [self.avatarButton ttq_showCrownLevel:self.data.publisher.rank];
    [self.avatarButton imy_showPendant:[self.data.publisher hasRankPendant]? nil :self.data.publisher.pendant_url];
    [self updatefollowButtonShow:isShow status:self.data.publisher.is_followed];
    // 用户身份信息
    NSString *babyInfoStr = self.data.publisher.baby_info;
    if (self.data.publisher.title) {
        babyInfoStr = self.data.publisher.title;
    }
    if (imy_isNotEmptyString(babyInfoStr)) {
        self.authorInfoLabel.hidden = NO;
        self.authorInfoLabel.text = babyInfoStr;
        CGFloat maxRight = self.followButton.hidden?self.imy_width : self.followButton.imy_left;
        self.authorInfoLabel.imy_width = maxRight - 12 - 36;
        self.publishTimeLabel.imy_top = self.authorInfoLabel.imy_bottom;
        self.followButton.imy_top = 30;
    } else {
        self.authorInfoLabel.hidden = YES;
        self.publishTimeLabel.imy_top = self.authorInfoLabel.imy_top;
        self.followButton.imy_centerY = self.avatarButton.imy_centerY;
    }
    self.authorInfoLabel.imy_left = 56;
    self.publishTimeLabel.text = [[self.data.updated_date imy_getDateTime] imy_getDisplayTimeStringYear];
    [self.publishTimeLabel sizeToFit];
    self.publishTimeLabel.imy_left = 56;
    self.publishTimeLabel.imy_height = 16;
    if (imy_isNotEmptyString(self.data.ip_region)) {
        self.ipInfoLabel.hidden = NO;
        self.ipInfoLabel.text = [NSString stringWithFormat:@"发布于 %@",self.data.ip_region];
        [self.ipInfoLabel sizeToFit];
        self.ipInfoLabel.imy_height = 16;
        self.ipInfoLabel.imy_centerY = self.publishTimeLabel.imy_centerY;
        self.ipInfoLabel.imy_left = self.publishTimeLabel.imy_right + 8;
    } else {
        self.ipInfoLabel.hidden = YES;
    }
    self.contentStartY = self.publishTimeLabel.imy_bottom + 12;
    self.userInfoControl.imy_width = self.nicknameLabel.imy_right - 12;
}

- (void)updatefollowButtonShow:(BOOL)show status:(NSInteger)followStatus {
    if (!show) {
        self.followButton.hidden = YES;
        [self updateBadges];
        return;
    }
    self.followButton.hidden = NO;
    [self.followButton setRelation:followStatus fullColorStyle:YES];
    [self layoutIfNeeded];
    [self updateBadges];
}

-(void)updateBadges {
    self.nicknameLabel.text = self.data.publisher.screen_name;
    if (self.data.publisher.badges.count) {
        self.authView.targetUserId = self.data.publisher.userID;
        [self.authView updateBadges:self.data.publisher.badges];
        [self.authView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.right.lessThanOrEqualTo(self.followButton.hidden? self : self.followButton.mas_left).offset(-12);
            make.centerY.mas_equalTo(self.nicknameLabel);
            make.left.equalTo(self.nicknameLabel.mas_right).offset(4);
            make.size.mas_equalTo(self.authView.imy_size);
        }];
        self.authView.hidden = NO;
    } else {
        self.authView.hidden = YES;
    }
    
    [self.nicknameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        if(self.authView.hidden == NO){
            make.right.mas_equalTo(self.authView.mas_left).offset(-4).priorityHigh;
        } else {
            make.right.mas_lessThanOrEqualTo(self.followButton.hidden?self:self.followButton.mas_left).offset(-12).priorityHigh;
        }
        make.left.equalTo(self.avatarButton.mas_right).offset(8);
        make.top.equalTo(self.avatarButton.mas_top);
        make.height.equalTo(@20);
    }];
    [self layoutIfNeeded];
}

#pragma mark -

- (void)updateContent {
    self.contentLabel.imy_top = self.contentStartY;
    [self.contentLabel imyr_setText:nil];
    [self.contentLabel imyr_setText:[self.data.content imy_trimString]];
    [self.contentLabel imyr_autoAdjustHeight];
    
    CGFloat bottom = [self updateImages];
    self.articleView.imy_top = bottom;
    [self.articleView updateWithData:self.data.topic];
    if (self.data.is_ai_reply) {
        self.copywriteLabel.hidden = NO;
        self.copywriteLabel.imy_top = self.articleView.imy_bottom + 24;
        self.imy_height = self.copywriteLabel.imy_bottom + 24;
    } else {
        self.copywriteLabel.hidden = YES;
        self.imy_height = self.articleView.imy_bottom + 24;
    }
}

- (CGFloat)updateImages {
    __block CGFloat imageTop = self.contentLabel.imy_bottom + 12;
    [self.commentImageViews bk_each:^(TTQTopicRichWithImgView *obj) {
        [obj removeFromSuperview];
        obj.imgView.image = nil;
        obj.imy_height = 0.0;
        [obj.imgView sd_cancelCurrentImageLoad];
        [obj.gestureRecognizers enumerateObjectsWithOptions:NSEnumerationReverse
                                                 usingBlock:^(__kindof UIGestureRecognizer *_Nonnull ges, NSUInteger idx, BOOL *_Nonnull stop) {
                                                     [obj removeGestureRecognizer:ges];
                                                 }];
    }];
    
    if (self.data.images.count) {
        NSMutableArray *photos = [NSMutableArray array];
        if (self.data.images.count > self.commentImageViews.count) {
            NSInteger leftNum = self.data.images_list.count - self.commentImageViews.count;
            for (NSInteger index = 0; index < leftNum; index++) {
                [self.commentImageViews addObject:[self createCommentImageView]];
            }
        }
        CGFloat imageWidth = SCREEN_WIDTH - 24;
        [self.data.images_list enumerateObjectsUsingBlock:^(NSDictionary *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
            TTQTopicRichParserImgModel *imgModel = [TTQTopicRichParserImgModel new];
            imgModel.src = obj[@"image_url"];
            imgModel.isLow = [obj[@"is_low"] boolValue];
            TTQTopicRichWithImgView *imageView = self.commentImageViews[idx];
            imageView.imgView.tag = idx;

            [imageView updateImageModel:imgModel addition:nil width:self.imy_width - 24 isStart:NO coverHeight:0];
            imageView.imy_top = imageTop;
            imageTop += (imageView.imy_height + 12);
            [self addSubview:imageView];
            
            if (imgModel.isLow) {
                [imageView hideImageForLowQuality];
            }
            @weakify(self);
            imageView.clickImgBlock = ^(UIImageView *imgView) {
                @strongify(self);
                [self showWithPhotos:photos atIndex:imgView.tag];
            };
            IMYPhoto *photo = [[IMYPhoto alloc] init];
            photo.url = [NSURL URLWithString:imgModel.src];
            photo.srcImageView = imageView.imgView;
            [photos addObject:photo];

        }];
    }
    return imageTop;
}

- (TTQTopicRichWithImgView *)createCommentImageView {
    TTQTopicRichWithImgView *commentImageView = [[TTQTopicRichWithImgView alloc] initWithFrame:CGRectMake(12, 0, self.imy_width - 24, 0)];
    commentImageView.imgView.imy_failureShowText = @"图片加载失败，点击重试";
    commentImageView.imgView.imy_placeholderImage = nil;
    return commentImageView;
}


- (BOOL)isAppUser:(NSUInteger)userId {
    if ([[IMYPublicAppHelper shareAppHelper].userid integerValue] == userId) {
        return YES;
    }
    return NO;
}

- (void)showWithPhotos:(NSMutableArray *)photos atIndex:(NSUInteger)index {
    [IMYPhotoBrowser showWithPhotos:photos atIndex:index];
}

#pragma mark - 个人主页跳转
- (void)gotoUserIntroduce:(TTQPublisherModel *)publisher {
    if ([TTQABTestConfig disableInteractByType:TTQ_Interact_type_userPage]) {
        return;
    }
    if (publisher.userID > 0 && publisher.error == 0) {
        IMYURI *uri = [IMYURI uriWithPath:@"user/dynamic"
                                               params:@{ @"userID": @(publisher.userID),
                                                         @"source": @"话题详情" }
                                                 info:nil];
        [[IMYURIManager shareURIManager] runActionWithURI:uri];
        
    } else if (publisher.error == 1) {
        [UIView imy_showTextHUD:kStatusText_UserAnonymous];
    } else if (publisher.error == 2 || publisher.error == 3) {
        [UIView imy_showTextHUD:kStatusText_homePageNotOpen];
    }
    [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_tx",@"action":@2,@"comment_id":@(self.data.commentID),@"info_type":@12,@"info_id":@(self.data.topic_id),@"fuid":@(self.data.publisher.userID),@"public_type":@1} headers:nil completed:nil];
}

#pragma mark - m80 delegate

- (void)m80AttributedLabel:(IMYRM80AttributedLabel *)label clickedOnLink:(IMYRM80AttributedLabelURL *)linkURL {
    if ([linkURL.linkData isKindOfClass:NSString.class]) {
        [self clickWithLink:linkURL.linkData];
    }
}

- (void)clickWithLink:(NSString *)linkUrl {
    NSURL *url = [NSURL URLWithString:linkUrl];
    if ([url.absoluteString containsString:@"itunes.apple.com"]) {
        [[UIApplication sharedApplication] openURL:url options:nil completionHandler:nil];
    } else {
        if (linkUrl) {
            IMYURI *uri = [IMYURI uriWithURIString:linkUrl];
            /// 发布入口埋点
            [uri appendingParams:@{@"publish_entrance":@7}];
            if ([[IMYURIManager shareURIManager] containScheme:uri.scheme] && [self injectSaleUri:uri]) {
                return;
            }
        }
        NSDictionary *dic = [linkUrl imy_queryDictionary];
        NSString *uriString = dic[@"uri"];
        if (uriString) {
            IMYURI *uri = [IMYURI uriWithURIString:[uriString imy_base64DecodedSafeURLString]];
            if ([[IMYURIManager shareURIManager] containScheme:uri.scheme] && [self injectSaleUri:uri]) {
                return;
            }
        }
        [[UIViewController imy_currentViewControlloer] imy_push:[IMYVKWebViewController webWithURLString:url.absoluteString]];
    }
}

/// 835电商有个埋点需求，前后版本电商需要的协议不一样，但服务端只会下发旧协议，会添加一个特定的协议参数下来，这里需要调用下对方的协议
- (BOOL)injectSaleUri:(IMYURI *)uri {
    if ([[IMYURIManager shareURIManager] containActionBlockForPath:uri.path]) {
        /// 有相应电商的协议才生效，防止电商版本不一致，过滤下热议话题业务
        if (![uri.params objectForKey:@"topic_id"] && ![uri.path hasSuffix:@"circles/topic/subject"]) {
            [uri appendingParams:@{@"topic_id":@(self.data.topic_id)}];
        }
        IMYURIActionBlockObject *objc = [IMYURIActionBlockObject actionBlockWithURI:[IMYURI uriWithPath:@"sale/interceptor" params:@{@"redirect_url":[uri uri]} info:@{}]];
        __block BOOL uriComplete = NO;
        /// 这里是电商替换完redirect_url后最终执行的地方
        objc.implCallbackBlock = ^(id result, NSError *error, NSString *eventName) {
            NSString *redirect_url = result[@"redirect_url"];
            uriComplete = [[IMYURIManager shareURIManager] runActionWithString:redirect_url];
        };
        [[IMYURIManager shareURIManager] runActionWithActionObject:objc completed:nil];
        return uriComplete;
    } else {
        return [[IMYURIManager shareURIManager] runActionWithURI:uri];
    }
}


#pragma mark - UI

- (IMYAvatarImageView *)avatarButton {
    if (!_avatarButton) {
        _avatarButton = [[IMYAvatarImageView alloc] initWithFrame:CGRectMake(12, 16, 36, 36)];
        _avatarButton.layer.cornerRadius = 18;
        _avatarButton.layer.borderColor = [UIColor imy_colorForKey:kCK_Black_L].CGColor;
        _avatarButton.layer.borderWidth = 0.5f;
    }
    return _avatarButton;
}

- (UILabel *)nicknameLabel {
    if (!_nicknameLabel) {
        _nicknameLabel = [[UILabel alloc] initWithFrame:CGRectMake(56, 16, 100, 20)];
        _nicknameLabel.font = [UIFont systemFontOfSize:14];
        _nicknameLabel.lineBreakMode = NSLineBreakByTruncatingMiddle;
        [_nicknameLabel imy_setTextColorForKey:kCK_Black_A];
    }
    return _nicknameLabel;
}

- (UILabel *)ipInfoLabel {
    if (!_ipInfoLabel) {
        _ipInfoLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 0, 16)];
        _ipInfoLabel.font = [UIFont systemFontOfSize:11];
        [_ipInfoLabel imy_setTextColorForKey:kCK_Black_B];
    }
    return _ipInfoLabel;
}

- (UILabel *)publishTimeLabel {
    if (!_publishTimeLabel) {
        _publishTimeLabel = [[UILabel alloc] initWithFrame:CGRectMake(56, 0, 0, 16)];
        _publishTimeLabel.font = [UIFont systemFontOfSize:11];
        
        [_publishTimeLabel imy_setTextColorForKey:kCK_Black_B];
    }
    return _publishTimeLabel;
}

- (UILabel *)authorInfoLabel {
    if (!_authorInfoLabel) {
        _authorInfoLabel = [[UILabel alloc] initWithFrame:CGRectMake(56, 36, 200, 16)];
        _authorInfoLabel.font = [UIFont systemFontOfSize:11];
        [_authorInfoLabel imy_setTextColorForKey:kCK_Black_B];
        _authorInfoLabel.lineBreakMode = NSLineBreakByTruncatingMiddle;
    }
    return _authorInfoLabel;
}

- (IMYCKFollowButton *)followButton {
    if (!_followButton) {
        _followButton = [[IMYCKFollowButton alloc] initWithFrame:CGRectMake(0, 0, 60, 26)];
        _followButton.imy_right = self.imy_width - 12;
        [_followButton.titleLabel setFont:[UIFont systemFontOfSize:12]];
        _followButton.imy_size = CGSizeMake(60, 26);
        _followButton.imy_centerY = self.avatarButton.imy_centerY;
        @weakify(self);
        [[[[_followButton rac_signalForControlEvents:UIControlEventTouchUpInside] deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(id x) {
            @strongify(self);
            if (self.tapFollowAction) {
                self.tapFollowAction(x);
            }
        }];

    }
    return _followButton;
}

- (IMYUGCBadgesTagView *)authView {
    if (!_authView) {
        _authView = [[IMYUGCBadgesTagView alloc] initWithFrame:CGRectMake(0, 0, 0, 16)];
        @weakify(self);
        _authView.biBadgeOnClickedBlk = ^(id<IMYCKPublisherBadge>  _Nonnull badge, BOOL result) {
            @strongify(self);
            NSDictionary *params = @{
                @"event":@"dsq_nrxqy_zzhz",
                @"action":@2,
                @"comment_id": @(self.data.commentID),
                @"info_type":@12,
                @"info_id":@(self.data.topic_id),
                @"fuid":@(self.data.publisher.userID),
                @"public_type":badge.badgeID
            };
            [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
        };
        _authView.biBadgeOnExposuredBlk = ^(id<IMYCKPublisherBadge>  _Nonnull badge) {
            @strongify(self);
            NSDictionary *params = @{
                @"event":@"dsq_nrxqy_zzhz",
                @"action":@1,
                @"comment_id": @(self.data.commentID),
                @"info_type":@12,
                @"info_id":@(self.data.topic_id),
                @"fuid":@(self.data.publisher.userID),
                @"public_type":badge.badgeID
            };
            [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
        };
    }
    return _authView;
}


- (IMYRM80AttributedLabel *)contentLabel {
    if (!_contentLabel) {
        _contentLabel = [[IMYRM80AttributedLabel alloc] initWithFrame:CGRectMake(12, 0, SCREEN_WIDTH - 24, 0)];
        [_contentLabel imy_setTextColorForKey:kCK_Black_A];
        _contentLabel.font = [UIFont systemFontOfSize:16];
        _contentLabel.newStyle = YES;
        _contentLabel.minLineHeight = 26;
        _contentLabel.imyr_emoticonSize = CGSizeMake(22, 22);
        _contentLabel.imyr_dynamicEmoticonSize = CGSizeMake(80, 80);
        _contentLabel.imyr_analysisDynamicEmoticon = NO;
        _contentLabel.textAlignment = kCTTextAlignmentLeft;
        _contentLabel.delegate = self;
    }
    return _contentLabel;
}

- (TTQRelateContentView *)articleView {
    if (!_articleView) {
        _articleView = [[TTQRelateContentView alloc] initWithFrame:CGRectMake(12, 0, SCREEN_WIDTH - 24, 24)];
        _articleView.layer.cornerRadius = 8;
        _articleView.layer.masksToBounds = YES;
        @weakify(self);
        [[_articleView rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
            @strongify(self);
            if (self.articleTapAction) {
                self.articleTapAction();
            }
            [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_ytlj",@"action":@2,@"comment_id":@(self.data.commentID),@"info_type":@12,@"info_id":@(self.data.topic_id),@"fuid":@(self.data.publisher.userID)} headers:nil completed:nil];
        }];
//        
        _articleView.imyut_eventInfo.eventName = [NSString stringWithFormat:@"answer_qusetion_%p",_articleView];
        [_articleView.imyut_eventInfo setExposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
            @strongify(self);
            [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_ytlj",@"action":@1,@"comment_id":@(self.data.commentID),@"info_type":@12,@"info_id":@(self.data.topic_id),@"fuid":@(self.data.publisher.userID)} headers:nil completed:nil];
        }];
    }
    return _articleView;
}

- (UILabel *)copywriteLabel {
    if (!_copywriteLabel) {
        _copywriteLabel = [[UILabel alloc] initWithFrame:CGRectMake(12, 0, 300, 18)];
        _copywriteLabel.font = [UIFont systemFontOfSize:15];
        [_copywriteLabel imy_setTextColorForKey:kCK_Black_B];
        _copywriteLabel.text = @"以上评论包含AI生成内容";
        _copywriteLabel.hidden = YES;
    }
    return _copywriteLabel;
}

@end
