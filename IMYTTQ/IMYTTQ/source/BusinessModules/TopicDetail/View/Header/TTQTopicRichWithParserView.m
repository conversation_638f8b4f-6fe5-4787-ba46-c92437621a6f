//
//  TTQTopicRichWithParserView.m
//  IMYTTQ
//
//  Created by <PERSON><PERSON> on 2017/10/10.
//  Copyright © 2017年 MeiYou. All rights reserved.
//

#import "TTQTopicDetailYouPinView.h"
#import "TTQTopicRichWithParserView.h"
#import "IMYRM80AttributedLabel+TTQ.h"
#import "IMYSelectableAttributedLabel.h"
#import "NSString+TTQ.h"
#import "TTQLinkView.h"
#import "TTQPortraitVideoPlayerView.h"
#import "TTQTopicRichParserModel.h"
#import "UIFont+TTQ.h"
#import "UIImageView+TTQ.h"
#import "UILabel+TTQ.h"
#import <IMYViewKit.h>
#import <IMYVideoPlayer/IMYVideoPlayer.h>
#import "TTQDetailVideoWraperView.h"
#import <NSString+IMYR.h>
#import <NSRegularExpression+IMYR.h>
#import <IMYRM80AttributedLabelURL.h>
#import "TTQTopicDetailYouPlusView.h"
#import "TTQABTestConfig.h"
#import "UIImage+TTQ.h"
#import "NSAttributedString+TTQ.h"
#import "NSString+TTQ.h"
#import "NSRegularExpression+TTQ.h"
#import "TTQTopicSubjectModel.h"
#import "TTQTopicModel.h"

#define kContainerInsetsDx -40
#define kContainerInsetsDy -20


static __strong NSCache *topicRichWithParserCache;

@interface TTQTopicRichWithParserView () <IMYSelectableAttributedLabelDelegate, IMYRM80AttributedLabelDelegate>
@property (nonatomic, strong) NSArray *parserModels;
@property (nonatomic, copy) NSString *lastRenderedText;
@property (nonatomic, strong) UITapGestureRecognizer *tapRecognizer;
// U+ 曝光滤重
@property (nonatomic, strong) NSMutableSet<NSString *> *youplusExposuredIds;
@property (nonatomic, strong) NSMutableArray *imageViewCache;

@end

@implementation TTQTopicRichWithParserView

+ (void)ttq_clearTopicRichWithParserCache {
    [topicRichWithParserCache removeAllObjects];
}

IMY_KYLIN_FUNC_MAINTAB {
    topicRichWithParserCache = [[NSCache alloc] init];
}

// MARK: - LifeCircle

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setup];
    }
    return self;
}

- (instancetype)initWithCoder:(NSCoder *)aDecoder {
    self = [super initWithCoder:aDecoder];
    if (self) {
        [self setup];
    }
    return self;
}

// MARK: - setup

- (void)setup {
//    UITapGestureRecognizer *tapRecognizer = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tapAction:)];
//    tapRecognizer.enabled = YES;
//    [self addGestureRecognizer:tapRecognizer];
    
    self.youplusExposuredIds = [NSMutableSet set];
}

- (void)tapAction:(UITapGestureRecognizer *)doubleTap{
    // 发送消息给所有的attributedLabel，取消内容选择
    [self cleanTextSelector];
    [self setCancleContinueTriggereClick];
}

/**
 发送消息给所有的attributedLabel，取消内容选择
 */
- (void)cleanTextSelector {
    for (IMYSelectableAttributedLabel *contentLabel in self.attributedLabels) {
        if ([contentLabel isKindOfClass:IMYSelectableAttributedLabel.class]) {
            [contentLabel cancelSelector];
        }
    }
}

/**
 外部的点击手势会导致touch时间失效，走到touchesCancelled方法，使用这个标记位处理这种情况，正常处理点击高亮内容
 */
- (void)setCancleContinueTriggereClick {
    for (IMYSelectableAttributedLabel *contentLabel in self.attributedLabels) {
        if ([contentLabel isKindOfClass:IMYSelectableAttributedLabel.class]) {
            contentLabel.shouldCancleContinueTriggereClick = YES;
        }
    }
}

// MARK: - Override

// 重写pointInside扩展点击区域是为了IMYSelectableAttributedLabel选择复制文字的把手处于边界的情况能够响应触摸事件
- (BOOL)pointInside:(CGPoint)point withEvent:(UIEvent *)event {
    if (CGRectContainsPoint(CGRectInset(self.bounds, kContainerInsetsDx, kContainerInsetsDy), point)) {
        return YES;
    }
    return [super pointInside:point withEvent:event];
}

#pragma mark - 排版开始
- (void)ttq_setText:(NSString *)text {
    [self ttq_setText:text parseType:IMYRTextParseTypeNormal];
}

- (void)ttq_setText:(NSString *)text parseType:(NSInteger)parseType {
    [self ttq_setText:text parseType:parseType otherKey:@""];
}

- (void)ttq_setText:(NSString *)text parseType:(NSInteger)parseType otherKey:(NSString *)key {
    [self ttq_setText:text parseType:parseType otherKey:key prefixText:nil prefixLinkData:nil];
}

- (void)ttq_setText:(NSString *)text parseType:(NSInteger)parseType prefixText:(NSString *)prefixText prefixLinkData:(id)prefixLinkData {
    [self ttq_setText:text parseType:parseType otherKey:@"" prefixText:prefixText prefixLinkData:prefixLinkData];
}

- (void)ttq_setText:(NSString *)text parseType:(NSInteger)parseType prefixTexts:(NSArray *)prefixTexts prefixLinkDatas:(NSArray *)prefixLinkDatas unFold:(BOOL)unfold{
    NSString *cacheKey = [NSString stringWithFormat:@"text_7_2_%@_%ld_%@", text.imy_sha1, parseType, @""];
    if (![self ttq_readCacheWithKey:cacheKey]) {
        self.parserModels = [TTQTopicRichParserModel topicRichParser:text parseType:parseType];
        [self ttq_saveCacheToKey:cacheKey];
    }
    [self ttq_richAutoAdjustHeight:prefixTexts prefixLinkDatas:prefixLinkDatas fold:unfold];
}

- (void)ttq_setText:(NSString *)text parseType:(NSInteger)parseType otherKey:(NSString *)key prefixText:(NSString *)prefixText prefixLinkData:(id)prefixLinkData {
    NSString *cacheKey = [NSString stringWithFormat:@"text_7_1_%@_%ld_%@", text.imy_sha1, parseType, key];
    if (![self ttq_readCacheWithKey:cacheKey]) {
        self.parserModels = [TTQTopicRichParserModel topicRichParser:text parseType:parseType];
        [self ttq_saveCacheToKey:cacheKey];
    }
    [self ttq_richAutoAdjustHeight:prefixText prefixLinkData:prefixLinkData];
}

// MARK: - Helper

- (void)ttq_richAutoAdjustHeight:(NSArray *)prefixTexts prefixLinkDatas:(NSArray *)prefixLinkDatas fold:(BOOL)fold{
    // 清除旧的attributedLabel，在m80LabelWith方法中添加新的attributedLabel
    [self.attributedLabels removeAllObjects];
    @weakify(self);
    [self bk_eachSubview:^(UIView *subview) {
        @strongify(self);
        if (subview != self) {
            [subview removeFromSuperview];
        }
    }];
    CGFloat parserHeight = 0;
    UIView *lastView = nil;
    NSMutableArray *photos = [NSMutableArray array];
    NSMutableArray *parserModels = self.parserModels.mutableCopy;
    if (parserModels == nil) {
        parserModels = [NSMutableArray array];
    }
//    BOOL containImage = [parserModels match:^BOOL(id  _Nonnull element) {
//        return [element isKindOfClass:TTQTopicRichParserImgModel.class];
//    }];
//    if (!containImage && self.image_list) {
//        /// 古老版本的帖子，content没有带图片标签，需要兼容  /// 服务端说他们自己洗，不需要了
//        for (NSDictionary *image in self.image_list) {
//            if (imy_isNotEmptyString(image[@"image_url"])) {
//                TTQTopicRichParserImgModel *img = [TTQTopicRichParserImgModel new];
//                img.src = image[@"image_url"];
//                img.isLow = [image[@"is_low"] boolValue];
//                [parserModels addObject:img];
//            }
//        }
//    }
    NSString *prefixText = prefixTexts.firstObject;
    if (prefixText && self.canAppendSubjectIntoContent) {
        TTQTopicRichParserTextModel *textModel = nil;
        for (TTQTopicRichParserTextModel *model in parserModels) {
            /// 找出最后的一个textModel
            if ([model isKindOfClass:TTQTopicRichParserTextModel.class] && imy_isNotEmptyString(model.content)) {
                textModel = model;
            }
        }
        if (textModel && !textModel.isFixedTopic) {
            /// 拼接上话题，兼容服务端数据
            textModel.content = [NSString stringWithFormat:@"%@ #%@",textModel.content,prefixText];
            /// textModel会被缓存到内容里，这里标志下已经处理了话题兼容，下次不再修改。
            textModel.isFixedTopic = YES;
        }
    }
    if (fold) {
        [self parseSequalPicturesWayTwo:parserModels hasHotTopic:NO];
    }
    
    CGFloat foldHeight = parserHeight;
    NSInteger index = 0;
    BOOL isFirstImage = NO;
    for (id parserModel in parserModels) {
        if ([parserModel isKindOfClass:[TTQTopicRichParserTextModel class]]) {
            TTQTopicRichParserTextModel *textModel = (TTQTopicRichParserTextModel *)parserModel;
            NSUInteger betweenHeight = 12;
            if ([lastView isKindOfClass:[TTQTopicRichWithImgView class]]) {
                parserHeight += 24;
                foldHeight += 24;
                betweenHeight = 24;
            } else if ([lastView isKindOfClass:[TTQDetailVideoWraperView class]]) {
                parserHeight += 24;
                foldHeight += 24;
                betweenHeight = 24;
            } else if ([lastView isKindOfClass:[TTQLinkView class]]
                       || [lastView isKindOfClass:[TTQButtonLinkView class]] || [lastView isKindOfClass:[TTQTopicDetailYouPlusView class]]) {
                parserHeight += 12;
                foldHeight += 12;
            }
            if (textModel.isEmotion) {
                IMYRM80AttributedLabel *label = nil;
                label = [self m80LabelWith:textModel.content];
                label.delegate = (id<IMYRM80AttributedLabelDelegate>)self.delegate;
                [self addSubview:label];
                label.imy_top = parserHeight;
                parserHeight += label.imy_height;
                foldHeight += label.imy_height;
                lastView = label;
            } else {
                if (!textModel.isFold) {
                    IMYRM80AttributedLabel *label = [self m80LabelWith:textModel.content];
                    [self addSubview:label];
                    label.imy_top = parserHeight;
                    parserHeight += label.imy_height;
                    foldHeight += label.imy_height;
                    lastView = label;
                } else {
                    parserHeight -= betweenHeight;
                }
            }
        } else if ([parserModel isKindOfClass:[TTQTopicRichParserAModel class]]) {
            TTQTopicRichParserAModel *textModel = (TTQTopicRichParserAModel *)parserModel;
            if ([lastView isKindOfClass:[TTQTopicRichWithImgView class]]) {
                parserHeight += 12;
            } else if (lastView) {
                parserHeight += 12;
            }
            if (textModel.ltype == 20) {
                TTQButtonLinkView *buttonLinkView = [[TTQButtonLinkView alloc] initWithFrame:CGRectMake(0, 0, self.imy_width, 34)];
                buttonLinkView.imy_top = parserHeight;
                [buttonLinkView setText: textModel.content ? textModel.content : @"我要参加"];
                @weakify(self);
                buttonLinkView.clickedBlock = ^{
                    @strongify(self);
                    if ([self.delegate respondsToSelector:@selector(clickWithLink:)]) {
                        [self.delegate clickWithLink:textModel.href];
                    }
                    // 发送消息给所有的attributedLabel，取消内容选择
                    [self cleanTextSelector];
                    [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"TopicPublish_entrance",@"publish_entrance":@7,@"action":@(2)} headers:nil completed:nil];
                };
                buttonLinkView.imyut_eventInfo.eventName = [NSString stringWithFormat:@"topicdetail_publish_%ld",[parserModels indexOfObject:parserModel]];
                [buttonLinkView.imyut_eventInfo setExposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
                    [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"TopicPublish_entrance",@"publish_entrance":@7,@"action":@(1)} headers:nil completed:nil];
                }];
                [self addSubview:buttonLinkView];
                parserHeight += buttonLinkView.imy_height;
                lastView = buttonLinkView;
            } else {
                TTQLinkView *linkView = [[TTQLinkView alloc] initWithFrame:(CGRect){0, 0, self.imy_width, 64}];
                linkView.imy_top = parserHeight;
                linkView.lineSpacing = 2;
                linkView.paragraphSpacing = 1;
                [linkView setTextFont:[UIFont ttqFontWith:15] subTitleFont:nil];
                [linkView setText:textModel.content subTitle:nil];
                [linkView setIconUrl:textModel.image];
                [linkView setADCheckButton:textModel.is_show_btn andTitle:textModel.btn_title];
                if (textModel.is_ad) {
                    [linkView showAdTag];
                }
                @weakify(textModel);
                linkView.clickedBlock = ^() {
                    @strongify(self, textModel);
                    if ([self.delegate respondsToSelector:@selector(clickWithLink:)]) {
                        [self.delegate clickWithLink:textModel.href];
                    }
                    // 发送消息给所有的attributedLabel，取消内容选择
                    [self cleanTextSelector];
                };
                linkView.longedBlock = ^() {
                    @strongify(self, textModel);
                    if ([self.delegate respondsToSelector:@selector(longPressWithLink:)]) {
                        [self.delegate longPressWithLink:textModel.href];
                    }
                };
                [self addSubview:linkView];
                parserHeight += linkView.imy_height;
                lastView = linkView;
            }
        } else if ([parserModel isKindOfClass:[TTQTopicRichParserImgModel class]]) {
            if ([lastView isKindOfClass:[TTQLinkView class]]
                || [lastView isKindOfClass:[TTQButtonLinkView class]]) {
                parserHeight += 12;
            } else if (lastView) {
                parserHeight += 12;
            }
            TTQTopicRichParserImgModel *imgModel = (TTQTopicRichParserImgModel *)parserModel;
            TTQTopicRichWithImgView *imgView = [self.imageViewCache match:^BOOL(TTQTopicRichWithImgView *element) {
                return element.imgModel == imgModel;
            }];
            if (!imgView) {
                imgView = [[TTQTopicRichWithImgView alloc] initWithFrame:(CGRect){0, 0, self.imy_width, 0}];
            }
            imgView.imgView.imy_failureShowText = @"图片加载失败，点击重试";
            imgView.imgView.tag = [photos count];
            imgView.imgView.imy_placeholderImage = nil;

            if (!isFirstImage) {
                isFirstImage = YES;
                if (self.firstImagePlaceholder.length) {
                    NSURL *imageUrl = [NSURL URLWithString:imgModel.src];
                    NSURL *placeHold = [NSURL URLWithString:self.firstImagePlaceholder];
                    if ([imageUrl.path isEqualToString:placeHold.path]) {
                        imgView.imgView.imy_placeholderImage = [[SDImageCache sharedImageCache] imageFromMemoryCacheForKey:self.firstImagePlaceholder];
                    }
                }
            }

             [imgView updateImageModel:imgModel addition:imgModel.desc width:self.imy_width isStart:imgModel.isStart coverHeight:imgModel.coverHeight];
            

            if (imgView.longImgLabel != nil && imgView.longImgLabel.superview != nil) {
                // BI:长图角标曝光时上报埋点
                imgView.longImgLabel.imyut_eventInfo.eventName = [NSString stringWithFormat:@"ttq_nrxqy_ct%@%ld",imgModel.src,index];
                [imgView.longImgLabel.imyut_eventInfo setExposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
                    @strongify(self);
                    if ([self.delegate respondsToSelector:@selector(postLongImgBIData:)]) {
                        [self.delegate postLongImgBIData:@{@"action":@(1)}];
                    }
                }];
            }
            
            if (imgModel.isLow) {
                [imgView hideImageForLowQuality];
            }
            
            @weakify(imgView);
            imgView.clickImgBlock = ^(UIImageView *imagView) {
                @strongify(self,imgView);
                if ([self.delegate respondsToSelector:@selector(showWithPhotos:atIndex:)]) {
                    [self.delegate showWithPhotos:photos atIndex:imagView.tag];
                }
                if (imgView.longImgLabel != nil && imgView.longImgLabel.superview != nil) {
                    if ([self.delegate respondsToSelector:@selector(postLongImgBIData:)]) {
                        [self.delegate postLongImgBIData:@{@"action":@(2)}];
                    }
                }
                // 发送消息给所有的attributedLabel，取消内容选择
                [self cleanTextSelector];
            };
            
            // 图片四周圆角
            UIBezierPath *maskPath = [UIBezierPath bezierPathWithRoundedRect:imgView.imgView.bounds
                                                                       byRoundingCorners:UIRectCornerAllCorners
                                                                             cornerRadii:CGSizeMake(8, 8)];
            CAShapeLayer *maskLayer = [CAShapeLayer layer];
            maskLayer.frame = CGRectMake(0, 0, imgView.imgView.imy_width, imgView.imgView.imy_height);
            maskLayer.path = maskPath.CGPath;
            imgView.imgView.layer.mask = maskLayer;
            
            BOOL fold = NO;
            if (imgModel.isStart) {
                [imgView resetGradientViewHeight:imgModel.coverHeight];
                [imgView.unfoldButton setTitle:[NSString stringWithFormat:@"展开剩余%ld张图片",imgModel.foldNum] forState:UIControlStateNormal];
                [imgView.unfoldButton setTitleEdgeInsets:UIEdgeInsetsMake(0, -imgView.unfoldButton.imageView.image.size.width, 0, imgView.unfoldButton.imageView.image.size.width)];
                [imgView.unfoldButton setImageEdgeInsets:UIEdgeInsetsMake(0, imgView.unfoldButton.titleLabel.bounds.size.width + 1, 0, -imgView.unfoldButton.titleLabel.bounds.size.width)];
                // BI:折叠按钮曝光时上报埋点
                imgView.unfoldButton.imyut_eventInfo.eventName = [NSString stringWithFormat:@"ttq_nrxqy_zksytp%@%ld",imgModel.src,index];
                [imgView.unfoldButton.imyut_eventInfo setExposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
                    @strongify(self);
                    if ([self.delegate respondsToSelector:@selector(postUnfoldImgBIData:)]) {
                        [self.delegate postUnfoldImgBIData:@{@"action":@(1)}];
                    }
                }];
                @weakify(imgModel,self,imgView);
                [imgView.unfoldButton bk_whenTapped:^{
                    // 从index开始展开图片
                    @strongify(imgModel,self,imgView);
                    [self unfoldImagesFrom:imgModel];
                    if ([self.delegate respondsToSelector:@selector(postUnfoldImgBIData:)]) {
                        [self.delegate postUnfoldImgBIData:@{@"action":@(2)}];
                    }
                    [UIView animateWithDuration:0.15 animations:^{
                        @strongify(imgView);
                        [imgView.whiteCover removeFromSuperview];
                        [imgView.gradientView removeFromSuperview];
                    } completion:^(BOOL finished) {
                        @strongify(self);
                        if (self.unfoldImagesBlock) {
                            self.unfoldImagesBlock();
                        }
                    }];
                }];
            } else {
                if (imgModel.isFold) {
                    fold = YES;
                    parserHeight -= 12;
                }
            }
            
            
            if (!fold) {
                [self addSubview:imgView];
                imgView.imy_top = parserHeight;
                if (imgModel.isStart) {
                    [imgView resetGradientViewHeight:imgModel.coverHeight];
                    [self addSubview:imgView.gradientView];
                    imgView.gradientView.imy_bottom = imgView.imy_bottom;
                }
                if (imgModel.isStart) {
                    parserHeight += 94;
                }else
                    parserHeight += imgView.imy_height;
                lastView = imgView;
            }

            IMYPhoto *photo = [[IMYPhoto alloc] init];
            photo.url = [NSURL URLWithString:imgModel.src];
            photo.srcImageView = imgView.imgView;
            [photos addObject:photo];
            
            if (!self.imageViewCache) {
                self.imageViewCache = [NSMutableArray arrayWithCapacity:1];
            }
            if (![self.imageViewCache containsObject:imgView]) {
                [self.imageViewCache addObject:imgView];
            }
        } else if ([parserModel isKindOfClass:[TTQTopicRichParserVideoModel class]]) {
            if ([lastView isKindOfClass:[TTQLinkView class]]
                || [lastView isKindOfClass:[TTQButtonLinkView class]]) {
                parserHeight += 12;
            } else if (lastView) {
                parserHeight += 12;
            }
            TTQTopicRichParserVideoModel *videoModel = (TTQTopicRichParserVideoModel *)parserModel;
            TTQDetailVideoWraperView *videoView = [[TTQDetailVideoWraperView alloc] initWithVideoModel:videoModel width:self.imy_width];
            // 视频四周圆角
            CGSize size = [videoView systemLayoutSizeFittingSize:UILayoutFittingCompressedSize];
            UIBezierPath *maskPath = [UIBezierPath bezierPathWithRoundedRect:videoView.bounds
                                                                       byRoundingCorners:UIRectCornerAllCorners
                                                                             cornerRadii:CGSizeMake(8, 8)];
            CAShapeLayer *maskLayer = [CAShapeLayer layer];
            maskLayer.frame = CGRectMake(0, 0, size.width, size.height);
            maskLayer.path = maskPath.CGPath;
            videoView.layer.mask = maskLayer;
            
            if ([self.delegate respondsToSelector:@selector(didAddVideoView:)]) {
                [self.delegate didAddVideoView:videoView];
            }

            [self addSubview:videoView];
            videoView.imy_top = parserHeight;
            parserHeight += videoView.imy_height;
            lastView = videoView;
        } else if ([parserModel isKindOfClass:TTQTopicRichParserYouPinModel.class]) {
            TTQTopicDetailYouPinView *view = [[TTQTopicDetailYouPinView alloc] initWithModel:parserModel andWidth:self.imy_width];
            @weakify(self);
            NSString *eventName = [NSString stringWithFormat:@"TTQTopicDetailUPin-%@", ((TTQTopicRichParserYouPinModel *)parserModel).youpinID];
            view.imyut_eventInfo.eventName = eventName;
            view.imyut_eventInfo.showRadius = 1;
            view.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
                @strongify(self);
                NSDictionary *dic = @{
                    @"goods_id":((TTQTopicRichParserYouPinModel *)parserModel).youpinID?:@"",
                    @"action":@"exposure"
                };
                if ([self.delegate respondsToSelector:@selector(postYouPinBIData:)]) {
                    [self.delegate postYouPinBIData:dic];
                }
            };
            view.clickBlock = ^{
                @strongify(self);
                NSDictionary *dic = @{
                    @"goods_id":((TTQTopicRichParserYouPinModel *)parserModel).youpinID?:@"",
                    @"action":@"click"
                };
                if ([self.delegate respondsToSelector:@selector(postYouPinBIData:)]) {
                    [self.delegate postYouPinBIData:dic];
                }
            };
            [self addSubview:view];
            view.imy_top = parserHeight;
            parserHeight += view.imy_height;
            lastView = view;
        }
        else if ([parserModel isKindOfClass:TTQTopicRichParserYouPlusModel.class]) {
            parserHeight += 12;
            TTQTopicDetailYouPlusView *view = [[TTQTopicDetailYouPlusView alloc] initWithModel:parserModel andWidth:self.imy_width];
            @weakify(self);
            // 使用view本身的地址做key，保证每个view 曝光时 都会进回调，然后使用 u+ id 做滤重
            NSString *eventName = [NSString stringWithFormat:@"TTQTopicDetailUPlus-%p", view];
            view.imyut_eventInfo.eventName = eventName;
            view.imyut_eventInfo.showRadius = 1;
            /// 过滤底部评论输入框高度
            view.imyut_eventInfo.edgeOffset = UIEdgeInsetsMake(0, 0, 52+SCREEN_TABBAR_SAFEBOTTOM_MARGIN, 0);
            view.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
                @strongify(self);
                NSString *youplus_id = ((TTQTopicRichParserYouPlusModel *)parserModel).youplus_id ?: @"";
                // 已曝光
                if ([self.youplusExposuredIds containsObject:youplus_id]) {
                    return;
                }
                [self.youplusExposuredIds addObject:youplus_id];
                NSDictionary *dic = @{
                    @"addin_id":((TTQTopicRichParserYouPlusModel *)parserModel).youplus_id?:@"",
                    @"public_type":@(((TTQTopicRichParserYouPlusModel *)parserModel).type),
                    @"e_user_id":((TTQTopicRichParserYouPlusModel *)parserModel).e_user_id?:@"",
                    @"action":@(1),
                    @"public_info":@1,
                    @"isAdPlugin":@(((TTQTopicRichParserYouPlusModel *)parserModel).isAdPlugin)
                };
                NSInteger type = ((TTQTopicRichParserYouPlusModel *)parserModel).type;
                if (type == 1) {
                    NSMutableDictionary *mutDic = [[NSMutableDictionary alloc] initWithDictionary:dic];
                    mutDic[@"wx_account_type"] = @(((TTQTopicRichParserYouPlusModel *)parserModel).wx_account_type);
                    mutDic[@"wx_account_wechat"] = ((TTQTopicRichParserYouPlusModel *)parserModel).wx_account_wechat;
                    dic = [mutDic copy];
                }
                if ([self.delegate respondsToSelector:@selector(postYouPlusBIData:)]) {
                    [self.delegate postYouPlusBIData:dic];
                }
            };
            view.clickBlock = ^{
                @strongify(self);
                NSDictionary *dic = @{
                    @"addin_id":((TTQTopicRichParserYouPlusModel *)parserModel).youplus_id?:@"",
                    @"public_type":@(((TTQTopicRichParserYouPlusModel *)parserModel).type),
                    @"e_user_id":((TTQTopicRichParserYouPlusModel *)parserModel).e_user_id?:@"",
                    @"action":@(2),
                    @"public_info":@1,
                    @"isAdPlugin":@(((TTQTopicRichParserYouPlusModel *)parserModel).isAdPlugin)
                };
                NSInteger type = ((TTQTopicRichParserYouPlusModel *)parserModel).type;
                if (type == 1) {
                    NSMutableDictionary *mutDic = [[NSMutableDictionary alloc] initWithDictionary:dic];
                    mutDic[@"wx_account_type"] = @(((TTQTopicRichParserYouPlusModel *)parserModel).wx_account_type);
                    mutDic[@"wx_account_wechat"] = ((TTQTopicRichParserYouPlusModel *)parserModel).wx_account_wechat;
                    dic = [mutDic copy];
                }
                if ([self.delegate respondsToSelector:@selector(postYouPlusBIData:)]) {
                    [self.delegate postYouPlusBIData:dic];
                }
            };
            view.hideBottomBarBlock = ^{
                @strongify(self);
                if ([self.delegate respondsToSelector:@selector(hideYouPlusBottomBar)]) {
                    [self.delegate hideYouPlusBottomBar];
                }
            };
            [self addSubview:view];
            view.imy_top = parserHeight;
            parserHeight += view.imy_height;
            lastView = view;
        }
        index++;
    }
    self.imy_height = parserHeight;
}

- (void)unfoldImagesFrom:(TTQTopicRichParserImgModel *)model {
    NSMutableArray *parserModels = self.parserModels.mutableCopy;
    if (parserModels == nil) {
        parserModels = [NSMutableArray array];
    }
    int i = model.startIndex;
    int j = model.endIndex;
    for (;i < j; i++) {
        id parserModel = parserModels[i];
        if ([parserModel isKindOfClass:[TTQTopicRichParserTextModel class]]) {
            TTQTopicRichParserTextModel *textModel = (TTQTopicRichParserTextModel *)parserModel;
            textModel.isFold = NO;
        } else if ([parserModel isKindOfClass:[TTQTopicRichParserImgModel class]]) {
            TTQTopicRichParserImgModel *imgModel = (TTQTopicRichParserImgModel *)parserModel;
            imgModel.isFold = NO;
            
            if (imgModel.isStart) {
                imgModel.isStart = NO;
                imgModel.startIndex = 0;
                imgModel.endIndex = 0;
            }
        }
    }
}

- (void)parseSequalPicturesWayTwo:(NSMutableArray *)parserModels hasHotTopic:(BOOL)hasHot{
    BOOL isSequal = NO;
    NSUInteger sequelNum = 1;
    NSUInteger foldNum = 0;
    CGFloat picHeight = 0.0;// 连续3张图的高度>一屏手机高度，去除掉导航栏和底部工具栏高度
    CGFloat screenHeight = SCREEN_HEIGHT - 52 - SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT;
    TTQTopicRichParserImgModel *startModel = nil;
    TTQTopicRichParserImgModel *firstModel = nil;
    for (int i = 0; i < parserModels.count; i++) {
        id parserModel = parserModels[i];
        if ([parserModel isKindOfClass:[TTQTopicRichParserImgModel class]]) {
            TTQTopicRichParserImgModel *imgModel = (TTQTopicRichParserImgModel *)parserModel;
            CGSize imageSize = imgModel.imageSize;
            if (imgModel.imageSize.height != 0 && imgModel.imageSize.width != 0) {
                NSLog(@"优化计算图片宽高比次数");
            } else {
                imageSize = [imgModel.src ttq_communityImageSizeWithMaxWidth:self.imy_width];
                imgModel.imageSize = imageSize;
            }
            if ((imageSize.height / imageSize.width) >= 3.0) {
                // 长图不折叠
                isSequal = NO;
            } else {
                if (isSequal) {
                    sequelNum += 1;
                    foldNum += 1;
                }
                isSequal = YES;
                if (sequelNum == 2) {
                    startModel = parserModel;
                }else if (sequelNum == 1){
                    firstModel = parserModel;
                }
                picHeight += imageSize.height;
            }
        } else if ([parserModel isKindOfClass:[TTQTopicRichParserTextModel class]]) {
            TTQTopicRichParserTextModel *textModel = (TTQTopicRichParserTextModel *)parserModel;
            NSString *finalStr = [textModel.content stringByReplacingOccurrencesOfString:@" " withString:@""];
            finalStr = [finalStr stringByReplacingOccurrencesOfString:@"\n" withString:@""];
            finalStr = [finalStr stringByReplacingOccurrencesOfString:@"\r" withString:@""];
            if (isSequal && ![finalStr isEqualToString:@""]) {
                isSequal = NO;
            } else if(!isSequal){
                isSequal = NO;
            } else
            {
                isSequal = YES;
                foldNum += 1;
            }
        } else {
            isSequal = NO;
        }
        
        BOOL shouldFold = NO;
        if (!isSequal) {
            if (sequelNum >= 3 && picHeight > screenHeight) {
                // 记录结束位置
                startModel.isStart = YES;
                startModel.startIndex = i - foldNum;
                startModel.endIndex = i;
                startModel.foldNum = sequelNum - 1;
                shouldFold = YES;
            } else {
                firstModel = nil;
                startModel = nil;
                sequelNum = 1;
                foldNum = 0;
                picHeight = 0.0;
            }
        } else if (i == parserModels.count - 1 && isSequal){
            if (sequelNum >= 3 && picHeight > screenHeight) {
                // 记录结束位置
                startModel.isStart = YES;
                startModel.startIndex = i - foldNum + 1;
                startModel.endIndex = i+1;
                startModel.foldNum = sequelNum - 1;
                shouldFold = YES;
            }
        }
        
        if (shouldFold) {
            CGSize imageSize = [firstModel.src ttq_communityImageSizeWithMaxWidth:self.imy_width];
            if (imageSize.height <= 94) {
                startModel.coverHeight = imageSize.height/2.0 + 12;
            } else {
                startModel.coverHeight = 94 + 12;
            }
            for (int x = startModel.startIndex; x < startModel.endIndex; x++) {
                id followModel = parserModels[x];
                if ([followModel isKindOfClass:[TTQTopicRichParserTextModel class]]) {
                    TTQTopicRichParserTextModel *textModel = (TTQTopicRichParserTextModel *)followModel;
                    textModel.isFold = YES;
                }else if ([followModel isKindOfClass:[TTQTopicRichParserImgModel class]]) {
                    TTQTopicRichParserImgModel *textModel = (TTQTopicRichParserImgModel *)followModel;
                    textModel.isFold = YES;
                }
            }
            
            firstModel = nil;
            startModel = nil;
            sequelNum = 1;
            foldNum = 0;
            picHeight = 0.0;
        }
        
        
    }
    
    
}

- (void)ttq_richAutoAdjustHeight:(NSString *)prefixText prefixLinkData:(id)prefixLinkData {
    // 清除旧的attributedLabel，在m80LabelWith方法中添加新的attributedLabel
    [self.attributedLabels removeAllObjects];
    @weakify(self);
    [self bk_eachSubview:^(UIView *subview) {
        @strongify(self);
        if (subview != self) {
            [subview removeFromSuperview];
        }
    }];
    CGFloat parserHeight = 0;
    UIView *lastView = nil;
    NSMutableArray *photos = [NSMutableArray array];
    NSMutableArray *parserModels = self.parserModels.mutableCopy;
    if (parserModels == nil) {
        parserModels = [NSMutableArray array];
    }
    if (prefixText) {
        TTQTopicRichParserTextModel *textModel = nil;
        if (self.parserModels.count > 0) {
            textModel = self.parserModels[0];
        }
        if (textModel == nil || ![textModel isKindOfClass:[TTQTopicRichParserTextModel class]]) {
            if (prefixLinkData) {
                IMYRM80AttributedLabel *label = [self m80LabelWith:nil];
                [label imy_setTextColorForKey:kCK_Colour_A];
                [label imyr_setText:prefixText];
                label.delegate = (id<IMYRM80AttributedLabelDelegate>)self.delegate;
                [label addCustomLink:prefixLinkData forRange:NSMakeRange(0, prefixText.length) linkColor:[UIColor imy_colorForKey:kCK_Colour_A]];
                [self addSubview:label];
                label.imy_top = parserHeight;
                parserHeight += label.imy_height;
                lastView = label;
            } else {
                textModel = [[TTQTopicRichParserTextModel alloc] init];
                textModel.content = prefixText;
                [parserModels insertObject:textModel atIndex:0];
            }
        }
    }
    NSInteger index = 0;
    for (id parserModel in parserModels) {
        if ([parserModel isKindOfClass:[TTQTopicRichParserTextModel class]]) {
            TTQTopicRichParserTextModel *textModel = (TTQTopicRichParserTextModel *)parserModel;
            if ([lastView isKindOfClass:[TTQTopicRichWithImgView class]]) {
                parserHeight += 12;
            } else if ([lastView isKindOfClass:[TTQLinkView class]]
                       || [lastView isKindOfClass:[TTQButtonLinkView class]] || [lastView isKindOfClass:[TTQTopicDetailYouPlusView class]]) {
                parserHeight += 15;
            }
            BOOL isPreFix = (index == 0 && prefixText);
            if (textModel.isEmotion || isPreFix) {
                IMYRM80AttributedLabel *label = nil;
                if (isPreFix) {
                    label = [self m80LabelWith:nil];
                    NSMutableAttributedString *prefixAttStr = [[NSMutableAttributedString alloc] initWithString:prefixText];
                    [prefixAttStr setFont:label.font];
                    [prefixAttStr setTextColor:[UIColor imy_colorForKey:kCK_Colour_A]];
                    [label appendAttributedText:prefixAttStr];
                    if (prefixLinkData) {
                        [label addCustomLink:prefixLinkData forRange:NSMakeRange(0, prefixText.length) linkColor:[UIColor imy_colorForKey:kCK_Colour_A]];
                    }
                    //                    [label appendText:@" "];//不加空格
                    [label imyr_appendText:textModel.content parseType:IMYRTextParseTypeEmoji];
                    [label imyr_autoAdjustHeight];
                    [label setNeedsDisplay];

                } else {
                    label = [self m80LabelWith:textModel.content];
                }
                label.delegate = (id<IMYRM80AttributedLabelDelegate>)self.delegate;
                [self addSubview:label];
                label.imy_top = parserHeight;
                parserHeight += label.imy_height;
                lastView = label;
            } else {
                IMYRM80AttributedLabel *label = [self m80LabelWith:textModel.content];
                [self addSubview:label];
                label.imy_top = parserHeight;
                parserHeight += label.imy_height;
                lastView = label;
            }
        } else if ([parserModel isKindOfClass:[TTQTopicRichParserAModel class]]) {
            TTQTopicRichParserAModel *textModel = (TTQTopicRichParserAModel *)parserModel;
            if ([lastView isKindOfClass:[TTQTopicRichWithImgView class]]) {
                parserHeight += 15;
            } else if (lastView) {
                parserHeight += 15;
            }
            if (textModel.ltype == 20) {
                TTQButtonLinkView *buttonLinkView = [[TTQButtonLinkView alloc] initWithFrame:CGRectMake(0, 0, self.imy_width, 34)];
                buttonLinkView.imy_top = parserHeight;
                [buttonLinkView setText: textModel.content ? textModel.content : @"我要参加"];
                @weakify(self);
                buttonLinkView.clickedBlock = ^{
                    @strongify(self);
                    if ([self.delegate respondsToSelector:@selector(clickWithLink:)]) {
                        [self.delegate clickWithLink:textModel.href];
                    }
                    // 发送消息给所有的attributedLabel，取消内容选择
                    [self cleanTextSelector];
                };
                [self addSubview:buttonLinkView];
                parserHeight += buttonLinkView.imy_height;
                lastView = buttonLinkView;
            } else {
                TTQLinkView *linkView = [[TTQLinkView alloc] initWithFrame:(CGRect){0, 0, self.imy_width, 64}];
                linkView.imy_top = parserHeight;
                linkView.lineSpacing = 2;
                linkView.paragraphSpacing = 1;
                [linkView setTextFont:[UIFont ttqFontWith:15] subTitleFont:nil];
                [linkView setText:textModel.content subTitle:nil];
                [linkView setIconUrl:textModel.image];
                [linkView setADCheckButton:textModel.is_show_btn andTitle:textModel.btn_title];
                @weakify(textModel);
                linkView.clickedBlock = ^() {
                    @strongify(self, textModel);
                    if ([self.delegate respondsToSelector:@selector(clickWithLink:)]) {
                        [self.delegate clickWithLink:textModel.href];
                    }
                    // 发送消息给所有的attributedLabel，取消内容选择
                    [self cleanTextSelector];
                };
                linkView.longedBlock = ^() {
                    @strongify(self, textModel);
                    if ([self.delegate respondsToSelector:@selector(longPressWithLink:)]) {
                        [self.delegate longPressWithLink:textModel.href];
                    }
                };
                [self addSubview:linkView];
                parserHeight += linkView.imy_height;
                lastView = linkView;
            }
        } else if ([parserModel isKindOfClass:[TTQTopicRichParserImgModel class]]) {
            if ([lastView isKindOfClass:[TTQLinkView class]]
                || [lastView isKindOfClass:[TTQButtonLinkView class]]) {
                parserHeight += 15;
            } else if (lastView) {
                parserHeight += 15;
            }
            TTQTopicRichParserImgModel *imgModel = (TTQTopicRichParserImgModel *)parserModel;
            TTQTopicRichWithImgView *imgView = [[TTQTopicRichWithImgView alloc] initWithFrame:(CGRect){0, 0, self.imy_width, 0}];
            imgView.imgView.tag = [photos count];
            [imgView updateImageModel:imgModel addition:imgModel.desc width:self.imy_width isStart:imgModel.isStart coverHeight:imgModel.coverHeight];
            if (imgModel.isLow) {
                [imgView hideImageForLowQuality];
            }

            imgView.clickImgBlock = ^(UIImageView *imgView) {
                @strongify(self);
                if ([self.delegate respondsToSelector:@selector(showWithPhotos:atIndex:)]) {
                    [self.delegate showWithPhotos:photos atIndex:imgView.tag];
                }
                // 发送消息给所有的attributedLabel，取消内容选择
                [self cleanTextSelector];
            };
            [self addSubview:imgView];
            imgView.imy_top = parserHeight;
            parserHeight += imgView.imy_height;
            lastView = imgView;

            IMYPhoto *photo = [[IMYPhoto alloc] init];
            photo.url = [NSURL URLWithString:imgModel.src];
            photo.srcImageView = imgView.imgView;
            [photos addObject:photo];
        } else if ([parserModel isKindOfClass:[TTQTopicRichParserVideoModel class]]) {
            if ([lastView isKindOfClass:[TTQLinkView class]]
                || [lastView isKindOfClass:[TTQButtonLinkView class]]) {
                parserHeight += 15;
            } else if (lastView) {
                parserHeight += 15;
            }
            TTQTopicRichParserVideoModel *videoModel = (TTQTopicRichParserVideoModel *)parserModel;
            TTQDetailVideoWraperView *videoView = [[TTQDetailVideoWraperView alloc] initWithVideoModel:videoModel width:self.imy_width];
            if ([self.delegate respondsToSelector:@selector(didAddVideoView:)]) {
                [self.delegate didAddVideoView:videoView];
            }

            [self addSubview:videoView];
            videoView.imy_top = parserHeight;
            parserHeight += videoView.imy_height;
            lastView = videoView;
        } else if ([parserModel isKindOfClass:TTQTopicRichParserYouPinModel.class]) {
            TTQTopicDetailYouPinView *view = [[TTQTopicDetailYouPinView alloc] initWithModel:parserModel andWidth:self.imy_width];
            @weakify(self);
            NSString *eventName = [NSString stringWithFormat:@"TTQTopicDetailUPin-%@", ((TTQTopicRichParserYouPinModel *)parserModel).youpinID];
            view.imyut_eventInfo.eventName = eventName;
            view.imyut_eventInfo.showRadius = 1;
            view.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
                @strongify(self);
                NSDictionary *dic = @{
                    @"goods_id":((TTQTopicRichParserYouPinModel *)parserModel).youpinID?:@"",
                    @"action":@"exposure"
                };
                if ([self.delegate respondsToSelector:@selector(postYouPinBIData:)]) {
                    [self.delegate postYouPinBIData:dic];
                }
            };
            view.clickBlock = ^{
                @strongify(self);
                NSDictionary *dic = @{
                    @"goods_id":((TTQTopicRichParserYouPinModel *)parserModel).youpinID?:@"",
                    @"action":@"click"
                };
                if ([self.delegate respondsToSelector:@selector(postYouPinBIData:)]) {
                    [self.delegate postYouPinBIData:dic];
                }
            };
            [self addSubview:view];
            view.imy_top = parserHeight;
            parserHeight += view.imy_height;
            lastView = view;
        } else if ([parserModel isKindOfClass:TTQTopicRichParserYouPlusModel.class]) {
            parserHeight += 12;
            TTQTopicDetailYouPlusView *view = [[TTQTopicDetailYouPlusView alloc] initWithModel:parserModel andWidth:self.imy_width];
            @weakify(self);
            // 使用view本身的地址做key，保证每个view 曝光时 都会进回调，然后使用 u+ id 做滤重
            NSString *eventName = [NSString stringWithFormat:@"TTQTopicDetailUPlus-%p", view];
            view.imyut_eventInfo.eventName = eventName;
            view.imyut_eventInfo.showRadius = 1;
            view.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
                @strongify(self);
                NSString *youplus_id = ((TTQTopicRichParserYouPlusModel *)parserModel).youplus_id ?: @"";
                // 已曝光
                if ([self.youplusExposuredIds containsObject:youplus_id]) {
                    return;
                }
                [self.youplusExposuredIds addObject:youplus_id];
                NSDictionary *dic = @{
                    @"addin_id":((TTQTopicRichParserYouPlusModel *)parserModel).youplus_id?:@"",
                    @"public_type":@(((TTQTopicRichParserYouPlusModel *)parserModel).type),
                    @"e_user_id":((TTQTopicRichParserYouPlusModel *)parserModel).e_user_id?:@"",
                    @"action":@(1),
                    @"public_info":@1,
                    @"isAdPlugin":@(((TTQTopicRichParserYouPlusModel *)parserModel).isAdPlugin)
                };
                NSInteger type = ((TTQTopicRichParserYouPlusModel *)parserModel).type;
                if (type == 1) {
                    NSMutableDictionary *mutDic = [[NSMutableDictionary alloc] initWithDictionary:dic];
                    mutDic[@"wx_account_type"] = @(((TTQTopicRichParserYouPlusModel *)parserModel).wx_account_type);
                    mutDic[@"wx_account_wechat"] = ((TTQTopicRichParserYouPlusModel *)parserModel).wx_account_wechat;
                    dic = [mutDic copy];
                }
                if ([self.delegate respondsToSelector:@selector(postYouPlusBIData:)]) {
                    [self.delegate postYouPlusBIData:dic];
                }
            };
            view.clickBlock = ^{
                @strongify(self);
                NSDictionary *dic = @{
                    @"addin_id":((TTQTopicRichParserYouPlusModel *)parserModel).youplus_id?:@"",
                    @"public_type":@(((TTQTopicRichParserYouPlusModel *)parserModel).type),
                    @"e_user_id":((TTQTopicRichParserYouPlusModel *)parserModel).e_user_id?:@"",
                    @"action":@(2),
                    @"public_info":@1,
                    @"isAdPlugin":@(((TTQTopicRichParserYouPlusModel *)parserModel).isAdPlugin)
                };
                NSInteger type = ((TTQTopicRichParserYouPlusModel *)parserModel).type;
                if (type == 1) {
                    NSMutableDictionary *mutDic = [[NSMutableDictionary alloc] initWithDictionary:dic];
                    mutDic[@"wx_account_type"] = @(((TTQTopicRichParserYouPlusModel *)parserModel).wx_account_type);
                    mutDic[@"wx_account_wechat"] = ((TTQTopicRichParserYouPlusModel *)parserModel).wx_account_wechat;
                    dic = [mutDic copy];
                }
                if ([self.delegate respondsToSelector:@selector(postYouPlusBIData:)]) {
                    [self.delegate postYouPlusBIData:dic];
                }
            };
            [self addSubview:view];
            view.imy_top = parserHeight;
            parserHeight += view.imy_height;
            lastView = view;
        }
        index++;
    }
    self.imy_height = parserHeight;
}

- (IMYRM80AttributedLabel *)m80LabelWith:(NSString *)content {
    //- (void)imyr_appendText:(NSString *)text parseType:(IMYRTextParseType)parseType 内部会对\r\n进行替换，这里要提前替换掉，避免点击区域有偏移
    content = [content stringByReplacingOccurrencesOfString:@"\r\n" withString:@"\n"];
    
    if ([content hasPrefix:@"\n"]) {
        /// 过滤第一个换行显示
        content = [content substringFromIndex:@"\n".length];
    }
    NSMutableArray *links = @[].mutableCopy;
    if (content.imyr_containsLinkURL_ForceConvert) {
        // email匹配
        NSRegularExpression *emailRegex = [NSRegularExpression imyr_hasEmailExpression];
        NSArray *emailMatches = [emailRegex matchesInString:content options:NSMatchingReportCompletion range:NSMakeRange(0, content.length)];
        
        ///url正则匹配
        NSRegularExpression *urlRegex = [self.class imyr_URLExpression];
        NSArray *urlMatches = [urlRegex matchesInString:content options:NSMatchingReportCompletion range:NSMakeRange(0, content.length)];
        NSMutableArray *pureUrls = [NSMutableArray new];
        NSInteger locationOffset = 0;
        for (NSTextCheckingResult *urlMatch in urlMatches) {
            // 先过滤掉email的再处理url
            NSRange urlRange = NSMakeRange(urlMatch.range.location, urlMatch.range.length);
            BOOL intersect = NO;
            for (NSTextCheckingResult *emailMatch in emailMatches) {
                NSRange emailRange = NSMakeRange(emailMatch.range.location, emailMatch.range.length);
                NSRange intersectionRange = NSIntersectionRange(urlRange, emailRange);
                if (intersectionRange.length > 0) {
                    if (intersectionRange.location == urlRange.location && intersectionRange.length == urlRange.length) {
                        intersect = YES;
                    }
                }
            }
            if (!intersect) {
                [pureUrls addObject:urlMatch];
            }
        }
        for (NSTextCheckingResult *urlMatch in pureUrls) {
            NSRange range = NSMakeRange(urlMatch.range.location - locationOffset, urlMatch.range.length);
            NSString *text = [content substringWithRange:range];
            content = [content stringByReplacingCharactersInRange:range withString:@"网页链接"];
            locationOffset += urlMatch.range.length - 4;
            IMYRM80AttributedLabelURL *link = [IMYRM80AttributedLabelURL urlWithLinkData:text range:NSMakeRange(range.location, 4) color:nil];
            [links addObject:link];
        }
    }
    IMYSelectableAttributedLabel *dtContentLabel = [[IMYSelectableAttributedLabel alloc] init];
    dtContentLabel.needDrawLinkExpourseView = YES;
    dtContentLabel.imy_width = self.imy_width;
    dtContentLabel.backgroundColor = [UIColor clearColor];
    [dtContentLabel imy_setTextColorForKey:kCK_Black_A];
    dtContentLabel.font = [UIFont systemFontOfSize:16];
    dtContentLabel.newStyle = YES;
    dtContentLabel.lineSpacing = 7;
    dtContentLabel.wordSpacing = 0.4;
    dtContentLabel.imyr_emoticonSize = CGSizeMake(22, 22);
    dtContentLabel.imyr_dynamicEmoticonSize = CGSizeMake(80, 80);
    dtContentLabel.imyr_analysisDynamicEmoticon = NO;
    dtContentLabel.textAlignment = kCTTextAlignmentLeft;
    [dtContentLabel setSelectable:self.canSelectContent];
    dtContentLabel.selectableDelegate = self;
    dtContentLabel.delegate = (id<IMYRM80AttributedLabelDelegate>)self.delegate;
    [dtContentLabel imyr_appendText:content parseType:IMYRTextParseTypeEmoji];
    [dtContentLabel.linkLocations addObjectsFromArray:links];
    
//    [dtContentLabel imyr_setText:content parseType:IMYRTextParseTypeEmoji customLinks:links otherKey:@""];
    NSAttributedString *buildString = [dtContentLabel getBuildAttributedString];
    /// 先解析话题
    NSRegularExpression *regex = [NSRegularExpression ttq_topicExpression];
    NSArray *matches = [regex matchesInString:buildString.string options:0 range:NSMakeRange(0, buildString.string.length)];
    matches = [content ttq_checkTopicMatches:matches];
    for (NSTextCheckingResult *match in matches) {
        TTQTopicSubjectModel *subject = [TTQTopicSubjectModel new];
        subject.name = [[buildString.string substringWithRange:match.range] substringFromIndex:1];
        NSString *subjectName = subject.name;
        if ([self.subjectList match:^BOOL(NSDictionary *element) {
            return [element[@"name"] isEqualToString:subjectName];
        }]) {
            [dtContentLabel addCustomLink:subject forRange:match.range linkColor:[UIColor imy_colorForKey:kTTQTopicTextColor]];
        }
    }
    if ([TTQABTestConfig baikeHighlightOpen]) {
        /// 显示百科
        [self appendHighlightWord:dtContentLabel];
    }
    
    [dtContentLabel imyr_autoAdjustHeight];
    [self.attributedLabels addObject:dtContentLabel];
    return dtContentLabel;
}

- (void)appendHighlightWord:(IMYRM80AttributedLabel *)m80Label {
    /// 命中百科实验
    /// matchString 作为匹配计算使用
    NSAttributedString *buildString = [m80Label getBuildAttributedString];
    NSMutableString *matchString = [NSMutableString stringWithString:buildString.string];
    BOOL needAppendIcon;
    IMYABTestExperiment *exp = [[IMYABTestManager sharedInstance] experimentForKey:@"words202507"];
    if (exp && [exp.vars integerForKey:@"words"] == 1) {
        needAppendIcon = YES;
    }
    for (TTQHighLightWordModel *highlightWord in self.highlight_words) {
        NSRange range = [matchString rangeOfString:highlightWord.word];
        if (range.location != NSNotFound) {
            NSMutableString *replaceString = [NSMutableString stringWithString:@""];
            for (int i = 0; i < range.length;i++) {
                [replaceString appendString:@" "];
            }
            /// 用空格替换，避免重复匹配，如：产后月经不调会连续匹配 产后月经，月经不调
            [matchString replaceCharactersInRange:range withString:replaceString];
            
            if (needAppendIcon) {
                /// 插入搜索icon 🔍
                IMYRM80AttchmentBlockContent *blockContent = [IMYRM80AttchmentBlockContent new];
                blockContent.size = CGSizeMake(8, 14);
                [blockContent setViewBlock:^UIView *(CGSize size) {
                    UIImageView *view = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, size.width, size.height)];
                    [view imy_setImageForKey:@"details_icon_search"];
                    return view;
                }];

                [m80Label insertUIBlock:blockContent margin:UIEdgeInsetsZero alignment:IMYRM80ImageAlignmentCenter atIndex:range.location+range.length];
                /// 插入空格占位
                [matchString insertString:@" " atIndex:range.location+range.length];
                /// length + 1，变更设置url的range
                range.length += 1;
            }
            
            IMYRM80AttributedLabelURL *url = [m80Label addCustomLink:highlightWord forRange:range linkColor:[UIColor imy_colorForKey:@"#4F7CAE"]];
            url.imy_expourseView.imyut_eventInfo.eventName = [NSString stringWithFormat:@"highlight_%@_%p",highlightWord.word,self];
            @weakify(self,highlightWord);
            [url.imy_expourseView.imyut_eventInfo setExposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
                @strongify(self, highlightWord);
                if (self.highlightExpourseBlock) {
                    self.highlightExpourseBlock(highlightWord);
                }
            }];

        }
    }

}

+ (NSRegularExpression *)imyr_URLExpression {
    NSString *imyr_urlExpressionString = @"([[Hh][Tt][Tt][Pp]|[Hh][Tt][Tt][Pp][Ss]:\\/\\/]*)(([0-9]{1,3}\\.){3}[0-9]{1,3}|([0-9a-zA-Z_!~*\\'()-]+\\.)*([0-9a-zA-Z-]*)\\.(com|cn|im|org|edu|gov|net))(\\/[0-9a-zA-Z\\.\\?\\@\\&\\=\\#\\%\\_\\:\\$]*)*";
    NSRegularExpression *urlExpression = [NSRegularExpression regularExpressionWithPattern:imyr_urlExpressionString options:NSRegularExpressionCaseInsensitive error:nil];
    return urlExpression;
}

- (UILabel *)normalLabelWith:(NSString *)content {
    UILabel *normalContentLabel = [[UILabel alloc] init];
    normalContentLabel.font = [UIFont ttqFontWith:18];
    normalContentLabel.imy_width = self.imy_width;
    normalContentLabel.numberOfLines = 0;
    [normalContentLabel imy_setTextColorForKey:kCK_Black_A];
    [normalContentLabel ttq_setText:content alignment:NSTextAlignmentJustified lineSpacing:6];
    [normalContentLabel sizeToFit];
    normalContentLabel.imy_width = self.imy_width;
    return normalContentLabel;
}

- (BOOL)ttq_readCacheWithKey:(NSString *)cacheKey {
    NSArray *parserModels = [topicRichWithParserCache objectForKey:cacheKey];
    if (parserModels) {
        self.parserModels = parserModels;
        return YES;
    }
    self.parserModels = nil;
    return NO;
}

- (void)ttq_saveCacheToKey:(NSString *)cacheKey {
    if (self.parserModels.count) {
        NSArray *parserModels = self.parserModels;
        [topicRichWithParserCache setObject:parserModels forKey:cacheKey];
    } else {
        [topicRichWithParserCache removeObjectForKey:cacheKey];
    }
}

// MARK: - IMYSelectableAttributedLabelDelegate

- (void)shutOffGesture:(BOOL)shutOff {
    if ([self.delegate respondsToSelector:@selector(shutOffGestureRecognizer:)]) {
        [self.delegate shutOffGestureRecognizer:shutOff];
    }
}

- (void)attributedLabel:(IMYSelectableAttributedLabel *)attributedLabel selectorPanGestureDidChangedAtPoint:(CGPoint)point {
    if ([self.delegate respondsToSelector:@selector(attributedLabel:selectorPanGestureDidChangedAtPoint:)]) {
        [self.delegate attributedLabel:attributedLabel selectorPanGestureDidChangedAtPoint:point];
    }
}

/**
 选择位置改变回调，外部决定复制按钮显示的位置
 */
- (CGRect)attributedLabel:(IMYSelectableAttributedLabel *)attributedLabel selectedAreaChangedWithSelectedRects:(NSArray <NSString *>*)selectedRects {
    if ([self.delegate respondsToSelector:@selector(attributedLabel:selectedAreaChangedWithSelectedRects:)]) {
        return [self.delegate attributedLabel:attributedLabel selectedAreaChangedWithSelectedRects:selectedRects];
    }
    return CGRectZero;
}

/**
 判断菜单是否显示
 */
- (void)attributedLabel:(IMYSelectableAttributedLabel *)attributedLabel selectMenuIsShow:(BOOL)selectMenuIsShow {
    if (selectMenuIsShow) {
        self.tapRecognizer = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tapAction:)];
        self.tapRecognizer.enabled = YES;
        [self addGestureRecognizer:self.tapRecognizer];
    } else {
        [self removeGestureRecognizer:self.tapRecognizer];
        self.tapRecognizer = nil;
    }
}


// MARK: - Getter

- (NSMutableArray *)attributedLabels {
    if (!_attributedLabels) {
        _attributedLabels = [NSMutableArray array];
    }
    return _attributedLabels;
}


@end

#pragma mark - richImgView

@implementation TTQTopicRichWithImgView
- (instancetype)initWithCoder:(NSCoder *)coder {
    self = [super initWithCoder:coder];
    if (self) {
        [self commonInit];
    }
    return self;
}
- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self commonInit];
    }
    return self;
}
- (void)commonInit {
    //    self.backgroundColor = [UIColor imy_colorForKey:kCK_Black_G];
    self.layer.cornerRadius = 12;
    self.layer.masksToBounds = YES;
    self.imgView = [[FLAnimatedImageView alloc] initWithImageType:TTQImageTypePic];
    self.imgView.userInteractionEnabled = YES;
    self.imgView.contentMode = UIViewContentModeScaleAspectFill;
    [self.imgView imy_setDownloadFinishedBlock:^(UIImageView *imv, BOOL success) {
        /// 透明图底色要显示白色的x
        FLAnimatedImageView *flImageView = (FLAnimatedImageView *)imv;
        if (flImageView.animatedImage) {
            /// 动图这里无法直接处理，所以先把动图去掉，修改完颜色再加回来，进到这里的说明一定有图，直接设置白色即可
            FLAnimatedImage *animatedImage = flImageView.animatedImage;
            flImageView.animatedImage = nil;
            [flImageView imy_setBackgroundColorForKey:kCK_White_AN];
            flImageView.animatedImage = animatedImage;
            flImageView.backgroundColorKey = kCK_White_AN;
        } else {
            flImageView.backgroundColorKey = success?kCK_White_AN:kCK_Black_F;
            [flImageView imy_setBackgroundColorForKey:success?kCK_White_AN:kCK_Black_F];
        }
    }];
    [self addSubview:self.imgView];
    @weakify(self);
    [self.imgView bk_whenTapped:^{
        @strongify(self);
        if (self.clickImgBlock) {
            self.clickImgBlock(self.imgView);
        }
    }];
    
    self.additionLabel = [[UILabel alloc] init];
    self.additionLabel.numberOfLines = 0;
    self.additionLabel.font = [UIFont ttqFontWith:12];
    self.additionLabel.textAlignment = NSTextAlignmentLeft;
    [self.additionLabel imy_setTextColorForKey:kCK_Black_M];
    [self addSubview:self.additionLabel];
    
    self.lineView = [IMYLineView lineView];
    [self.lineView imy_setBackgroundColorForKey:kCK_Black_D];
    [self addSubview:self.lineView];
}

- (void)updateImageModel:(TTQTopicRichParserImgModel *)imgModel addition:(NSString *)addition width:(CGFloat)width isStart:(BOOL)isStart coverHeight:(CGFloat)height {
    self.imgModel = imgModel;
    NSString *imgUrl = imgModel.src;
    self.imy_width = width;
    
    CGSize imageSize = [imgUrl ttq_communityImageSizeWithMaxWidth:width];
    if ((imageSize.height / imageSize.width) >= 3.0) {
        self.imgView.imy_size = CGSizeMake(imageSize.width, SCREEN_HEIGHT / 3.0);
        UILabel *longImgLabel = [[UILabel alloc] initWithFrame:CGRectMake(imageSize.width - 38, SCREEN_HEIGHT / 3.0 - 22, 30, 16)];
        longImgLabel.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.3];
        longImgLabel.layer.cornerRadius = 4;
        longImgLabel.clipsToBounds = YES;
        longImgLabel.textAlignment = NSTextAlignmentCenter;
        [longImgLabel imy_setTextColorForKey:kCK_White_A];
        longImgLabel.font = [UIFont systemFontOfSize:11];
        [longImgLabel imy_setText:@"长图"];
        [self.imgView addSubview:longImgLabel];
        self.longImgLabel = longImgLabel;
    } else
        self.imgView.imy_size = imageSize;
    
    self.imgView.imy_left = (width - imageSize.width) / 2.0;
    [self updateImageView:self.imgView withUrl:imgUrl];
    
    if (isStart) {
        if (imageSize.height <= 94) {
            // 补充高度小于94的遮罩
            self.whiteCover = [[UIView alloc] initWithFrame:CGRectMake(0, self.imgView.imy_bottom, self.imgView.imy_width, 94-imageSize.height)];
            [self.whiteCover imy_setBackgroundColorForKey:kCK_White_A];
            [self addSubview:self.whiteCover];
        } else {
            self.imgView.clipsToBounds = YES;
        }
    }
    self.additionLabel.hidden = YES;
    self.lineView.hidden = YES;
    if ([NSString imy_isEmptyString:imgUrl]) {
        self.imy_height = 0;
        return;
    }
    self.additionLabel.imy_width = width - 16;
    if (imy_isNotEmptyString(addition) && !isStart) {
        self.additionLabel.hidden = NO;
        [self.additionLabel imy_setText:addition];
        [self.additionLabel sizeToFit];
        self.additionLabel.imy_height = MAX(self.additionLabel.imy_height, 14);
        self.additionLabel.imy_top = self.imgView.imy_bottom + 8;
        self.additionLabel.imy_width = width - 16;
        self.additionLabel.imy_left = 8;
        self.imy_height = self.additionLabel.imy_bottom + 8;
    } else {
        if (isStart) {
            self.imy_height = 94;
            self.clipsToBounds = YES;
        } else
            self.imy_height = self.imgView.imy_bottom;
    }
    
}

- (void)updateImageView:(UIImageView *)imageView withUrl:(NSString *)imageUrl {
    //cdn裁剪尺寸超过原图，直接原图请求
    CGSize imageSize = [imageUrl ttq_communityImageSizeWithMaxWidth:self.imy_width];
    CGSize originalSize = imageUrl.imy_lastComponentOriginalImageSize;
    if (imageSize.width * SCREEN_SCALE > originalSize.width || imageSize.height * SCREEN_SCALE > originalSize.height) {
        [imageView imy_setImageURL:imageUrl];
    } else {
        [imageView ttq_setImageURL:imageUrl];
    }
    
}

- (void)resetGradientViewHeight:(CGFloat)height {
    [self.gradientView removeFromSuperview];
    
    UIView *gradientView = [[UIView alloc] initWithFrame:CGRectMake(0, -height, self.imy_width, 94+height)];
    CAGradientLayer *gradientLayer = [[CAGradientLayer alloc] init];
    gradientLayer.frame = gradientView.bounds;
    gradientLayer.colors = @[(__bridge id)[[UIColor whiteColor] colorWithAlphaComponent:0].CGColor, (__bridge id)[UIColor whiteColor].CGColor];
    gradientLayer.startPoint = CGPointMake(0, 0);
    gradientLayer.endPoint = CGPointMake(0, 1);
    [gradientView.layer addSublayer:gradientLayer];
    self.gradientView = gradientView;
    [self.gradientView addSubview:self.unfoldButton];
    
    @weakify(self, gradientLayer);
    [self.gradientView imy_addThemeChangedBlock:^(UIView *weakObject) {
        @strongify(self, gradientLayer);
        if ([IMYPublicAppHelper shareAppHelper].isNight) {
            [gradientLayer removeFromSuperlayer];
        } else if (gradientLayer) {
            [self.gradientView.layer insertSublayer:gradientLayer atIndex:0];
        } else {
            gradientLayer = [[CAGradientLayer alloc] init];
            gradientLayer.frame = gradientView.bounds;
            gradientLayer.colors = @[(__bridge id)[[UIColor whiteColor] colorWithAlphaComponent:0].CGColor, (__bridge id)[UIColor whiteColor].CGColor];
            gradientLayer.startPoint = CGPointMake(0, 0);
            gradientLayer.endPoint = CGPointMake(0, 1);
            [self.gradientView.layer insertSublayer:gradientLayer atIndex:0];
        }
    }];
}

- (UIButton *)unfoldButton {
    if (!_unfoldButton) {
        _unfoldButton = [UIButton buttonWithType:UIButtonTypeCustom];
        _unfoldButton.frame = CGRectMake(0, self.gradientView.imy_height - 32, self.imy_width, 20);
        _unfoldButton.titleLabel.font = [UIFont systemFontOfSize:14];
        [_unfoldButton imy_setImageForKey:@"tata_detail_icon_arrow_down" andState:UIControlStateNormal];
        [_unfoldButton setTitleColor:[UIColor imy_colorForKey:kCK_Red_B] forState:UIControlStateNormal];
    }
    
    return _unfoldButton;
    
}

- (void)hideImageForLowQuality {
    if (!self.lowQualityView) {
        self.lowQualityView = [[UIView alloc] initWithFrame:self.imgView.frame];
        self.lowQualityView.alpha = 0;
        self.lowQualityView.layer.cornerRadius = self.imgView.layer.cornerRadius;
        self.lowQualityView.layer.masksToBounds = YES;
        [self addSubview:self.lowQualityView];
        
        YYAnimatedImageView *imgView = [[YYAnimatedImageView alloc] initWithFrame:self.lowQualityView.bounds];
        imgView.contentMode = UIViewContentModeScaleAspectFill;
        [imgView imy_setBackgroundColorForKey:kCK_Black_F];
        [self updateImageView:imgView withUrl:self.imgModel.src];
        [imgView imy_setDownloadFinishedBlock:^(UIImageView *imageView, BOOL success) {
            if (success) {
                UIImage *image = imageView.image;
                image = [image ttq_gaussianImage];
                imageView.image = image;
            }
        }];
        [self.lowQualityView addSubview:imgView];
        
        UIView *cover = [[UIView alloc] initWithFrame:imgView.frame];
        cover.backgroundColor = [UIColor colorWithWhite:0 alpha:0.2];
        [self.lowQualityView addSubview:cover];
        
        UILabel *label1 = [[UILabel alloc] init];
        [label1 imy_setTextColorForKey:kCK_White_A];
        label1.font = [UIFont systemFontOfSize:12];
        [label1 imy_setText:@"图片可能引起不适"];
        [self.lowQualityView addSubview:label1];
        
        UILabel *label2 = [[UILabel alloc] init];
        [label2 imy_setTextColorForKey:kCK_White_A];
        label2.font = [UIFont systemFontOfSize:12];
        [label2 imy_setText:@"点击查看"];
        [self.lowQualityView addSubview:label2];
        
        [label1 mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self.lowQualityView);
            make.top.equalTo(self.lowQualityView).offset((self.imgView.imy_height-42)/2);
            make.height.mas_equalTo(17);
        }];
        
        [label2 mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self.lowQualityView);
            make.bottom.equalTo(self.lowQualityView).offset(-(self.imgView.imy_height-42)/2);
            make.height.mas_equalTo(17);
        }];
        
        @weakify(self);
        [self.lowQualityView bk_whenTapped:^{
            @strongify(self);
            [UIView animateWithDuration:0.5 animations:^{
                self.lowQualityView.alpha = 0;
                [self.lowQualityView removeFromSuperview];
            }];
        }];
    }
    self.lowQualityView.alpha = 1;
}

@end
