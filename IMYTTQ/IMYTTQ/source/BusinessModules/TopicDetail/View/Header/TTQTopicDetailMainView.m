//
//  TTQTopicDetailMainView.m
//  IMYTTQ
//
//  Created by aron on 2018/10/25.
//  Copyright © 2018 MeiYou. All rights reserved.
//

#import "TTQTopicDetailMainView.h"
#import <IMYBaseKit/IMYBaseKit.h>

@interface TTQTopicDetailHeadDoctorInfoView()
@property (nonatomic, strong) IMYAvatarImageView *avatarImageView;
@property (nonatomic, strong) UIView *containerView;///< 容器视图，点击头像跳转
@property (nonatomic, strong) UILabel *nameLabel;/// 姓名
@property (nonatomic, strong) UILabel *titleLabel;///< 职称
@property (nonatomic, strong) UILabel *departmentLabel;///< 科室

@property (nonatomic, strong) UIView *levelView;///< 三甲背景
@property (nonatomic, strong) UILabel *levelLabel;///< 三甲
@property (nonatomic, strong) UILabel *hospitalLabel;///< xxx医院

@property (nonatomic, copy) NSString *redirectUrl;///< 点击头像跳转的链接
@end


@implementation TTQTopicDetailHeadDoctorInfoView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI{
    [self imy_setBackgroundColorForKey:kISY_White];
    
    UIView *containerView = [UIView new];
    @weakify(self);
    [containerView bk_whenTapped:^{
        @strongify(self);
        if (imy_isNotEmptyString(self.redirectUrl)) {
            [[IMYURIManager sharedInstance] runActionWithString:self.redirectUrl];
        }
    }];
    [self addSubview:containerView];
    [containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self);
        make.top.bottom.equalTo(self);
        make.right.lessThanOrEqualTo(self);
    }];
    self.containerView = containerView;
    
    [containerView addSubview:self.avatarImageView];
    [self.avatarImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(containerView);
        make.top.bottom.equalTo(containerView);
        make.size.mas_equalTo(CGSizeMake(36, 36));
    }];
    
    [containerView addSubview:self.nameLabel];
    [self.nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.avatarImageView.mas_right).offset(8);
        make.top.equalTo(self.avatarImageView);
    }];
    
    [containerView addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nameLabel.mas_right).mas_offset(4);
        make.centerY.equalTo(self.nameLabel);
    }];
    
    [containerView addSubview:self.departmentLabel];
    [self.departmentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.titleLabel.mas_right).mas_offset(4);
        make.centerY.equalTo(self.nameLabel);
        make.right.lessThanOrEqualTo(containerView);
    }];
    
    
    [containerView addSubview:self.levelView];
    [self.levelView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nameLabel);
        make.bottom.equalTo(containerView);
        make.height.mas_equalTo(16);
    }];
    
    [containerView addSubview:self.hospitalLabel];
    [self.hospitalLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.levelView.mas_right).mas_offset(4);
        make.centerY.equalTo(self.levelView);
        make.right.lessThanOrEqualTo(containerView);
    }];

}


- (void)updatePublisher:(TTQPublisherModel *)publisherModel{
    
    self.redirectUrl = publisherModel.redirect_url;///跳转链接
    
    [self.avatarImageView setAvatarWithURLString:publisherModel.avatar placeholder:[UIImage imy_imageForKey:@"all_usericon"]];
    self.nameLabel.text = publisherModel.screen_name;
    
    NSString *title = [[publisherModel.doctor objectForKey:@"title"] stringValue];
    NSString *department = [[publisherModel.doctor objectForKey:@"department"] stringValue];
    self.titleLabel.text = title;
    self.departmentLabel.text = department;
    
    NSString *level = [[publisherModel.hospital objectForKey:@"level"] stringValue];
    self.levelLabel.text = level;
    
    NSString *hospital = [[publisherModel.hospital objectForKey:@"name"] stringValue];
    self.hospitalLabel.text = hospital;
    
    self.levelView.hidden = imy_isEmptyString(level);
    
    [self.hospitalLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        if (self.levelView.isHidden) {
            make.left.equalTo(self.nameLabel);
        } else {
            make.left.equalTo(self.levelView.mas_right).mas_offset(4);
        }
        make.height.mas_equalTo(16);
        make.bottom.equalTo(self.containerView);
        make.right.lessThanOrEqualTo(self.containerView);
    }];
    
    [self layoutIfNeeded];
}


//MARK: - Getters
- (IMYAvatarImageView *)avatarImageView {
    if (!_avatarImageView) {
        _avatarImageView = [[IMYAvatarImageView alloc] init];
        _avatarImageView.layer.cornerRadius = 18;
        _avatarImageView.layer.masksToBounds = YES;
        _avatarImageView.userInteractionEnabled = YES;
    }
    return _avatarImageView;
}

- (UILabel *)nameLabel {
    if (!_nameLabel) {
        _nameLabel = [[UILabel alloc] init];
        _nameLabel.font = [UIFont systemFontOfSize:14];
        _nameLabel.textColor = [UIColor imy_colorForKey:kCK_Black_A];
    }
    return _nameLabel;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.font = [UIFont systemFontOfSize:11];
        _titleLabel.textColor = [UIColor imy_colorForKey:kCK_Black_B];
    }
    return _titleLabel;
}

- (UILabel *)departmentLabel {
    if (!_departmentLabel) {
        _departmentLabel = [[UILabel alloc] init];
        _departmentLabel.font = [UIFont systemFontOfSize:11];
        _departmentLabel.textColor = [UIColor imy_colorForKey:kCK_Black_B];
    }
    return _departmentLabel;
}

- (UILabel *)hospitalLabel {
    if (!_hospitalLabel) {
        _hospitalLabel = [[UILabel alloc] init];
        _hospitalLabel.font = [UIFont systemFontOfSize:11];
        _hospitalLabel.textColor = [UIColor imy_colorForKey:kCK_Black_B];
    }
    return _hospitalLabel;
}

- (UILabel *)levelLabel {
    if (!_levelLabel) {
        _levelLabel = [[UILabel alloc] init];
        _levelLabel.font = [UIFont systemFontOfSize:9];
        _levelLabel.textColor = [UIColor imy_colorForKey:kCK_White_A];
    }
    return _levelLabel;
}

- (UIView *)levelView{
    if (!_levelView) {
        UIView *view = [[UIView alloc] init];
        view.backgroundColor = [UIColor imy_colorWithHexString:@"#26BFBF"];
        view.layer.cornerRadius = 4;
        view.layer.masksToBounds = YES;
        [view addSubview:self.levelLabel];
        [self.levelLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(view).insets(UIEdgeInsetsMake(0.5, 4, 0.5, 4));
        }];
        
        _levelView = view;
    }
    return _levelView;
}

@end

//MARK: - TTQTopicDetailConsultationTipsView

@interface TTQTopicDetailConsultationTipsView()
@property (nonatomic, strong) UILabel *descriptionLabel;
@end

@implementation TTQTopicDetailConsultationTipsView


- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    
    UIView *subView = [UIView new];
    subView.backgroundColor = [UIColor imy_colorForKey:kCK_Black_F];
    subView.layer.cornerRadius = 12;
    subView.layer.masksToBounds = YES;
    [self addSubview:subView];
    [subView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self).insets(UIEdgeInsetsMake(0, 12, 12, 12));
    }];
    [subView addSubview:self.descriptionLabel];
    [self.descriptionLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(subView).insets(UIEdgeInsetsMake(8, 12, 8, 12));
    }];
}

- (CGFloat)updateWithDesc:(NSString *)desc{
    self.descriptionLabel.text = desc;
    [self layoutIfNeeded];
    
    return CGRectGetHeight(self.bounds);
}

//MARK: - getter

- (UILabel *)descriptionLabel {
    if (!_descriptionLabel) {
        _descriptionLabel = [[UILabel alloc] init];
        _descriptionLabel.font = [UIFont systemFontOfSize:12];
        _descriptionLabel.textColor = [UIColor imy_colorForKey:kCK_Black_B];
        _descriptionLabel.numberOfLines = 0;
        _descriptionLabel.text = @"";
    }
    return _descriptionLabel;
}
@end
//MARK: - TTQTopicDetailMainView
@implementation TTQTopicDetailMainView

@end
