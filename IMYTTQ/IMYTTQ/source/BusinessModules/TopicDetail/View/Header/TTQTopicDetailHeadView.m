//
//  SYCommentTitleView.m
//  Seeyou
//
//  Created by <PERSON> on 13-6-28.
//  Copyright (c) 2013年 linggan. All rights reserved.
//

#import "TTQTopicDetailHeadView.h"

#import "TTQPublisherModel+TTQCommentCellTag.h"
#import "TTQTopicViewModel.h"
#import "TTQVideoModel.h"

#import "TTQLinkView.h"
#import "TTQRecommendTopicView.h"
#import "TTQTopicActivityView.h"
#import "TTQVideoImageView.h"
#import "TTQVoteView.h"

#import "IMYRM80AttributedLabel+TTQ.h"
#import "NSString+TTQ.h"
#import "TTQABTestConfig.h"
#import "TTQDoorConfig+IconConfig.h"
#import "TTQJumpType.h"
#import "TTQMacro.h"
#import "UIFont+TTQ.h"
#import "UIImageView+TTQ.h"
#import "UILabel+TTQ.h"
#import "UIView+TTQ.h"
#import <MediaPlayer/MediaPlayer.h>

#import <IMYUGC/IMYUGCEventHelper.h>
#import "TTQFeedsVoteView.h"
#import "TTQMultipleVoteView.h"
#import "TTQTwoImageVoteView.h"
#import <UIView+IMYFoundation.h>
#import "IMYSelectableAttributedLabel.h"
#import "TTQHttpHelper.h"
#import "TTQCheckService.h"
#import <IMYCommonKit/IMYCKFollowButton.h>
#import <IMYUGC/UIImageView+AvatarCover.h>
#import <IMYUGC/IMYCKForwardArticleView.h>
//#import "TTQNewFirstPageTagVIew.h"
//#import "TTQTopics848ViewController.h"
#import "IMYCKFeedsAuthenticationView.h"
#import "TTQDetailHeaderSubjectView.h"
#import <IMYUGC/IMYCKCostomPKView.h>
#import "IMYRM80AttributedLabelURL.h"
#import "IMYCircleActivityBannerView.h"
#import "IMYUGCBannerView.h"
#import "IMYUGCBadgesTagView.h"
#import "TTQTopicDetailMainView.h"

#define FixImageTag 12549

NSString *const TTQTopicContentLinkTypeHotTopic = @"TTQTopicContentLinkTypeHotTopic";
NSString *const TTQTopicContentLinkTypeTheme = @"TTQTopicContentLinkTypeTheme";

NSInteger const TTQTopicContentElementSeparate_h = 24; // 段落标题 图像文本 间距
NSInteger const TTQTopicContentElementSeparate_m = 16;
NSInteger const TTQTopicContentElementSeparate_l = 12; //

@interface TTQTopicDetailHeadView () <UIGestureRecognizerDelegate>

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *praiseViewWidthConstraint;
@property (nonatomic, strong) TTQTopicActivityView *activityView;
@property (nonatomic, strong) IMYCKFollowButton *followButton;
@property (nonatomic, strong) UIButton *foldButton;
@property (nonatomic, strong) UIView *tagIconView;
@property (nonatomic, strong) IMYRM80AttributedLabel *cpyRightLabel;
@property (nonatomic, strong) TTQRecommendTopicView *recommendTopicView;
@property (nonatomic, strong) UIImageView *uPlusBanner;

@property (nonatomic, strong) TTQTopicViewModel *viewModel;
@property (nonatomic, strong) TTQTopicModel *bindingModel;

@property (nonatomic, assign) CGFloat heightWithoutHeader;
@property (nonatomic, assign) BOOL unfold;
@property (nonatomic, assign) BOOL isfold;

@property (strong, nonatomic) UILabel *realIPLabel;
@property (nonatomic, assign) BOOL isfoldRecommend;
@property (nonatomic, assign) BOOL hasRequestRecommend;
/// 帖子内容及底部相关的态度等控件容器，作为展开、收起推荐用户卡片时，卡片下方内容整体移动使用
@property (nonatomic, strong) UIView *dataContentView;
//@property (nonatomic, strong) TTQNewFirstPageTagVIew *tagView;//圈子tag
//@property (nonatomic, strong) UIView *tagTapView;//圈子tag
@property (nonatomic, strong) IMYUGCBadgesTagView *authView;   /// 创作者标识
///
@property (nonatomic, strong) IMYCKForwardArticleView *articleView;
@property (nonatomic, strong) FLAnimatedImageView *referencedArticleImageView;
@property (nonatomic, strong) IMYRM80AttributedLabel *referencedArticleLabel;
@property (nonatomic, assign) BOOL unfoldChange;
@property (nonatomic, strong) TTQDetailHeaderSubjectView *hotTopicBannerView;
@property (nonatomic, strong) IMYUGCBannerView *activityBannerView;
@property (nonatomic, strong) TTQTopicDetailHeadDoctorInfoView *doctorInfoView;///< 病例贴时使用 category = 18
@property (nonatomic, strong) TTQTopicDetailConsultationTipsView *consultationTipsView;///< 病例贴时使用 category = 18
@end

@implementation TTQTopicDetailHeadView

- (void)awakeFromNib {
    [super awakeFromNib];
    [self commonInit];
}

- (void)commonInit {
    self.isfoldRecommend = YES;
    self.iconTags = [NSMutableArray array];

    self.imy_width = [[UIScreen mainScreen] bounds].size.width;
    self.backgroundColor = [UIColor clearColor];
    [self.nicknameLabel imy_setTextColorForKey:kCK_Black_A];
    [self.publishTimeLabel imy_setTextColorForKey:kCK_Black_B];
    [self.ipInfoLabel imy_setTextColorForKey:kCK_Black_B];
    self.ipInfoLabel.font = [UIFont imy_FontWith:11];
    [self.mainView addSubview:self.authView];
    self.richParserView.imy_width = self.imy_width - 24;
    self.richParserView.backgroundColor = [UIColor clearColor];
    self.richParserView.userInteractionEnabled = YES;

    [self.mainView imy_setBackgroundColorForKey:kISY_White];
    [self.topicLabel imy_setTextColorForKey:kCK_Black_A];

    [self.titleLabel imy_setTextColorForKey:kCK_Black_A];
    self.titleLabel.backgroundColor = [UIColor clearColor];
    self.titleLabel.font = [UIFont ttqMediumFontWith:20];
    self.titleLabel.lineSpacing = 2;
    self.titleLabel.paragraphSpacing = 1;
    self.titleLabel.imyr_analysisDynamicEmoticon = NO;
    self.titleLabel.textAlignment = kCTTextAlignmentLeft;
    self.titleLabel.lineBreakMode = kCTLineBreakByWordWrapping;
    self.titleLabel.imy_width = self.imy_width - 24;
    
    self.avatarButton.layer.cornerRadius = 18;
    self.avatarButton.layer.borderColor = [UIColor imy_colorForKey:kCK_Black_L].CGColor;
    self.avatarButton.layer.borderWidth = 0.5f;
    
    self.followButton = [[IMYCKFollowButton alloc] init];
    [self.followButton.titleLabel setFont:[UIFont systemFontOfSize:12]];
    [self.mainView addSubview:self.followButton];
    self.followButton.imy_size = CGSizeMake(60, 26);
    self.followButton.imy_centerY = self.avatarButton.imy_centerY;
    [self.followButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.mainView).offset(-12);
        make.size.mas_equalTo(self.followButton.imy_size);
        make.centerY.equalTo(self.avatarButton);
    }];
    
    [self.authView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nicknameLabel.mas_right).offset(4);
        make.top.equalTo(self.avatarButton);
        make.size.mas_equalTo(CGSizeMake(0, 16));
    }];
    
    [self.foldButton imy_setImage:[self expandImage:YES]];
    self.foldButton = [[UIButton alloc] init];
    self.foldButton.imy_size = CGSizeMake(24, 24);
    self.foldButton.layer.cornerRadius = 12;
    self.foldButton.hidden = YES;
    [self.foldButton addTarget:self action:@selector(foldButtonClicked:) forControlEvents:UIControlEventTouchUpInside];
    
    [self.mainView addSubview:self.foldButton];
    [self.mainView addSubview:self.recommendSheet];
    
    self.dataContentView = [[UIView alloc] init];
    [self.mainView addSubview:self.dataContentView];
    
    self.foldButton.imy_size = CGSizeMake(24, 24);
    [self.foldButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.mainView).offset(-12);
        make.size.mas_equalTo(self.foldButton.imy_size);
        make.centerY.equalTo(self.avatarButton);
    }];
    
    self.realIPLabel = [[UILabel alloc] init];
    self.realIPLabel.font = [UIFont systemFontOfSize:11];
    [self.realIPLabel imy_setTextColorForKey:kCK_Black_B];
    [self.mainView addSubview:self.realIPLabel];
    // 默认所有信息都存在，ip在第三行
    [self.realIPLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.ipInfoLabel.mas_right).offset(8);
        make.centerY.equalTo(self.ipInfoLabel);
        make.height.mas_equalTo(16);
        make.right.lessThanOrEqualTo(self.followButton.mas_left).offset(-12).priorityLow;
    }];
    
    @weakify(self);
    [[[[self.followButton rac_signalForControlEvents:UIControlEventTouchUpInside] deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(id x) {
        @strongify(self);
        if (self.tapFollowAction) {
            self.tapFollowAction(x);
        }
    }];
    [self.dataContentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.ipInfoLabel.mas_bottom).offset(8);
        make.leading.trailing.equalTo(self.mainView);
        make.height.equalTo(@50);
    }];
    /// xib不会用，改手写！
    [self.richParserView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.dataContentView);
        make.leading.equalTo(self.dataContentView).offset(12);
        make.trailing.equalTo(self.dataContentView).offset(-12);
        make.height.equalTo(@50);
    }];
    self.recommendSheet.hidden = YES;
    
    [[[RACObserve(self, forumHeightConstraint.constant) distinctUntilChanged] deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        if ([x integerValue] >= 0 && self.heightWithoutHeader > 1) {
            self.imy_height = self.heightWithoutHeader + self.forumHeightConstraint.constant;
            ((UITableView *)[self imy_findParentViewWithClass:[UITableView class]]).tableHeaderView = self;
        }
    }];
    self.richParserView.unfoldImagesBlock = ^{
        @strongify(self);
        self.unfold = YES;
        self.unfoldChange = YES;
        self.viewModel.feedsSourceChange = YES;
    };
    
    [self.richParserView setHighlightExpourseBlock:^(TTQHighLightWordModel *word) {
        @strongify(self);
        if (self.highlightExpourseBlock) {
            self.highlightExpourseBlock(word);
        }
    }];
}

- (BOOL)pointInside:(CGPoint)point withEvent:(UIEvent *)event {
    BOOL pointInsede = [super pointInside:point withEvent:event];
    if (pointInsede && self.viewTouchAction) {
        // fix 如果在回复框弹出情况下，点击图片，不进入查看大图，直接收起键盘
        UIView *fitView = nil;
        int count = (int)self.subviews.count;
        for (int i = count -1; i >= 0; i--) {
            UIView *childV = self.subviews[i];
            if(childV.userInteractionEnabled == NO || childV.hidden == YES || childV.alpha <= 0.01){
                continue;// 过滤下不响应的view 提高效率
            }
            CGPoint childP = [self convertPoint:point toView:childV];
            fitView = [childV hitTest:childP withEvent:event];
        }
        if (fitView == nil || ![fitView isKindOfClass:NSClassFromString(@"FLAnimatedImageView")]) {
            self.viewTouchAction();
        }
    }
    return pointInsede;
}

- (void)dealloc {
    TTQVoteView *voteView = [self.contentView imy_findSubviewWithClass:[TTQVoteView class]];
    [voteView clearTimerHelper];
}

-(BOOL)canShowSheet{
    BOOL canshow = [self.recommendSheet canShowSheet];
    if (canshow) {
        [self.recommendSheet mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(self.userRecommendSheetHeight);
        }];
    }
    return canshow;
}

-(void)hideRecomendList{
    if(self.foldButton.hidden){
        return;
    }
    [self foldRecommendSheet:YES];
}

-(void)showRecomendList{
    // 增加按钮展示和动画
    self.isfoldRecommend = YES;
    [self.recommendSheet showWithUserId:self.bindingModel.publisher.userID];
    [self expendRecommendSheet];
}
/// 还原展开，关注等状态
- (void)reset {
    self.unfold = NO;
    if (!self.recommendSheet.hidden || !self.foldButton.hidden) {
        self.recommendSheet.hidden = YES;
        self.foldButton.hidden = YES;
        [self.foldButton imy_setImage:[self expandImage:YES]];
        [self.followButton mas_updateConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.mainView).offset(-12);
        }];
        [self.dataContentView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.ipInfoLabel.mas_bottom).offset(16);
        }];
    }
}

#pragma mark - bi

- (void)postBiAction:(NSInteger)action subAction:(NSInteger)subAction redirectUrl:(NSString *)url userId:(NSUInteger)userid{
    NSMutableDictionary *params = [[NSMutableDictionary alloc] init];
    params[@"action"] = @(action);
    if (action > 1) {
        params[@"clickpos_feedscard"] = @(subAction);
    }
    params[@"entrance"] = @(self.entrance);
    params[@"position"] = @22;
    params[@"card_type"] = @3;
    params[@"info_type"] = @57;
    params[@"info_id"] = @(userid);
    if (imy_isNotEmptyString(url)) {
        IMYURI *uri = [IMYURI uriWithURIString:url];
        [params addEntriesFromDictionary:uri.params];
    }
    [IMYGAEventHelper postWithPath:@"bi_feeds_view" params:params headers:nil completed:nil];
}

- (instancetype)bindModel:(TTQTopicModel *)topicModel cellForRowAtIndexPath:(NSIndexPath *)indexPath viewModel:(TTQTopicViewModel *)viewModel {
    self.viewModel = viewModel;
    /// 判断正文内容是否一样
    BOOL isSameContent = NO;
    BOOL isSameHighlight = NO;
    NSString *lastHighlight = [self.bindingModel.highlight_words YYJSONString];
    NSString *highlightWords = [topicModel.highlight_words YYJSONString];
    /// 高亮词是否一致
    if (!lastHighlight && !highlightWords) {
        isSameHighlight = YES;
    } else if ([lastHighlight isEqualToString:highlightWords]) {
        isSameHighlight = YES;
    }
    if (self.bindingModel && [self.bindingModel.content hash] == [topicModel.content hash] && isSameHighlight) {
        isSameContent = YES;
    }
    self.bindingModel = topicModel;
    BOOL isMine = topicModel.publisher.userID == [[IMYPublicAppHelper shareAppHelper].userid integerValue];
    BOOL is_anonymous = topicModel.is_anonymous;
    BOOL isPublisherOk = topicModel.publisher.error == 0;
    BOOL hasLogin = [IMYPublicAppHelper shareAppHelper].hasLogin;
    if(!self.hasRequestRecommend && hasLogin && !is_anonymous && !isMine && isPublisherOk){
        /// 已登录、作者状态正常、不是匿名贴
        [self.recommendSheet requestRecommendListWithUserId:topicModel.publisher.userID];
        @weakify(self);
        [self.recommendSheet setupExposureItemBlock:^(IMYRecommendFollowerModel * _Nonnull model, NSDictionary * _Nonnull params) {
            @strongify(self);
            [self postBiAction:1 subAction:0 redirectUrl:model.redirect_url userId:model.uid];
        } tapMoreBlock:^(IMYRecommendFollowerListModel * _Nonnull model) {
            @strongify(self);
            [self postBiAction:2 subAction:22 redirectUrl:nil userId:0];
        } tapItemBlock:^(IMYRecommendFollowerModel * _Nonnull model) {
            @strongify(self);
            [self postBiAction:2 subAction:1 redirectUrl:model.redirect_url userId:model.uid];
        } tapFollowBlock:^(BOOL isFollow, IMYRecommendFollowerModel * _Nonnull model) {
            @strongify(self);
            [self postBiAction:3 subAction:isFollow?2:7 redirectUrl:model.redirect_url userId:model.uid];
        }];
        self.hasRequestRecommend = YES;
    }
    
    // 封号也要显示关注
    BOOL isShow = (topicModel.publisher.error == 0 || topicModel.publisher.error == 2 || topicModel.publisher.error == 3) && topicModel.publisher.userID != [[IMYPublicAppHelper shareAppHelper].userid integerValue];
    [self updatefollowButtonShow:isShow status:topicModel.publisher.is_followed];
    @weakify(topicModel);
    void (^gotoUserIntroduce)() = ^() {
        @strongify(topicModel);
        [TTQTopicDetailHeadView gotoUserIntroduce:topicModel];
    };
    [self.avatarButton ttq_removeGestureRecognizer];
    [self.avatarButton bk_whenTapped:^{
        gotoUserIntroduce();
    }];

    [self.nicknameLabel ttq_removeGestureRecognizer];
    [self.nicknameLabel bk_whenTapped:^{
        gotoUserIntroduce();
    }];
    NSMutableArray *tagArray = [NSMutableArray new];
    if (topicModel.is_live) {
        [tagArray addObject:@(ForumTagLive)];
    } else if (topicModel.is_elite) {
        [tagArray addObject:@(ForumTagElite)];
    }
    // 详情页投票和活动共同存在不显示投票
    BOOL isActivity = (topicModel.show_icon_type == 2 || topicModel.is_activity);
    if (topicModel.is_vote && !isActivity) {
        [tagArray addObject:@(ForumTagVote)];
    }
    if (topicModel.is_qa) {
        [tagArray addObject:@(ForumTagHelp)];
    }
    if (topicModel.show_icon_type) {
        if (topicModel.show_icon_type == 1) {
            [tagArray addObject:@(ForumTagAD)];
        } else if (topicModel.show_icon_type == 2) {
            [tagArray addObject:@(ForumTagActivity)];
        } else if (topicModel.show_icon_type == 3) {
            [tagArray addObject:@(ForumTagNotice)];
        }
    } else {
        if (topicModel.is_ad) {
            [tagArray addObject:@(ForumTagAD)];
        } else if (topicModel.is_activity) {
            [tagArray addObject:@(ForumTagActivity)];
        } else if (topicModel.is_ontop) {
            [tagArray addObject:@(ForumTagTop)];
        }
    }
    CGFloat spaceHeight = 0.0;
    if (imy_isEmptyString(topicModel.title)) {
        self.titleTopConstra.constant = 0;
        self.titleHeightConstra.constant = 0;
        self.titleLabel.hidden = YES;
    } else {
        [self.titleLabel setFont:[UIFont ttqMediumFontWith:20]];
        [self.titleLabel setText:nil];
        [self.titleLabel setTopicDetailTitleWith:topicModel tagArray:nil];
        self.titleLabel.imy_width = self.imy_width - 24;

        self.titleTopConstra.constant = TTQTopicContentElementSeparate_m;
        self.titleLabel.hidden = NO;

        [self.titleLabel setNeedsDisplay];
        self.titleHeightConstra.constant = self.titleLabel.imy_height;
        spaceHeight += (TTQTopicContentElementSeparate_m + self.titleLabel.imy_height);
    }


    [self.avatarButton setAvatarWithURLString:topicModel.publisher.user_avatar.large placeholder:[UIImage imy_imageForKey:@"all_usericon"]];
    [self.avatarButton ttq_showCrownLevel:topicModel.publisher.rank];
    [self.avatarButton imy_showGuajianCover:[topicModel.publisher hasRankPendant]? nil :topicModel.publisher.pendant_url];
    self.avatarButton.tag = -11;
    spaceHeight += (TTQTopicContentElementSeparate_m + 36);
    self.nicknameLabel.text = topicModel.publisher.screen_name;
    [self.nicknameLabel sizeToFit];
    
//    用户身份信息  创作者身份认证信息>专家信息>问答之星>达人信息>身份  8.1.5调整
    NSString *babyInfoStr = topicModel.publisher.baby_info;
    if (topicModel.publisher.title) {
        babyInfoStr = topicModel.publisher.title;
    }

    // 处理nameLabel和tagIconView，tagIconView全部显示，nameLabel超出的部分截取显示
    // 需要考虑到右边的关注馈按钮
    CGFloat nameMaxWidth = 0;
    if (self.followButton.isHidden) {
        nameMaxWidth = self.imy_width - 12 - self.avatarButton.imy_right - 8;
    } else {
        nameMaxWidth = self.imy_width - 12 - self.avatarButton.imy_right - 8 - self.followButton.imy_width - 8;
    }
    
//    [self.publishTimeLabel layoutIfNeeded];
    CGFloat maxWidth = self.imy_width - self.avatarButton.imy_right - 36 - self.followButton.imy_width - self.publishTimeLabel.imy_width;
    if (babyInfoStr.length > 0) {// 用户信息存在
        self.publishTimeLabel.text = babyInfoStr;
        self.ipInfoLabel.text = [[topicModel.published_date imy_getDateTime] imy_getDisplayTimeStringYear];
    } else {// 用户信息不存在
        self.publishTimeLabel.text = [[topicModel.published_date imy_getDateTime] imy_getDisplayTimeStringYear];
        self.ipInfoLabel.imy_height = 1;
        self.ipInfoLabel.text = nil;
    }
    
    UILabel *labelBeforeIp = imy_isEmptyString(babyInfoStr)?self.publishTimeLabel:self.ipInfoLabel;
    [self.realIPLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(labelBeforeIp.mas_right).offset(8);
        make.centerY.equalTo(labelBeforeIp);
        make.height.mas_equalTo(16);
        if (!self.followButton.hidden) {
            make.right.lessThanOrEqualTo(self.followButton.mas_left).offset(-12).priorityLow;
        } else {
            make.right.lessThanOrEqualTo(self).offset(-12);
        }
    }];

    if (topicModel.ip_region.length > 0) {
        self.realIPLabel.text = [NSString stringWithFormat:@"发布于 %@",topicModel.ip_region];
    }
    
    if (topicModel.publisher.score_level > 0) {
        [self.btn_rankLevel setTitle:[NSString stringWithFormat:@"%ld", (long)topicModel.publisher.score_level] forState:UIControlStateNormal];
        self.btn_rankLevel.hidden = NO;
    }

    // 添加视频内容到content中，方便统一处理
    NSMutableString *htmlContent = [NSMutableString new];
    if (topicModel.content) {
        [htmlContent appendString:topicModel.content];
    }

    if ((topicModel.videos.count > 0 || topicModel.category == TTQTopicCategoryUGCVideo) && ![topicModel.content containsString:@"</video>"]) {
        TTQVideoModel *videoData = topicModel.videos.firstObject;
        CGFloat ratio = 0;
        if (videoData.width < 0.01 || videoData.height < 0.01) {
            ratio = 345.0 / 194;
        } else {
            ratio = videoData.width / videoData.height;
        }
        NSString *videoURLString = [TTQCommonHelp videoUrlWith264:videoData.video_url url265:videoData.video_url_h265];
        NSString *videoHtmlContent = [NSString stringWithFormat:@"<video src=\"%@\" ratio=\"%.3f\" poster=\"%@\" duration=\"%.2f\" size=\"%@\"></video>", videoURLString, ratio, videoData.thum_pic, videoData.videoDuration, videoData.size];
        [htmlContent appendString:videoHtmlContent];
    }

    spaceHeight += TTQTopicContentElementSeparate_h; //line
    // 热议话题内容
    NSMutableArray *prefixTexts = [NSMutableArray new];
    NSMutableArray *prefixLinkDatas = [NSMutableArray new];
    if (imy_isNotEmptyString(topicModel.subject.name)) {
        NSString *prefixText = [NSString stringWithFormat:@"%@", topicModel.subject.name];
        NSString *prefixLinkData = TTQTopicContentLinkTypeHotTopic;
        [prefixTexts addObject:prefixText];
        [prefixLinkDatas addObject:prefixLinkData];
    }
    if (imy_isNotEmptyString(topicModel.theme_title)) {
        NSString *prefixText = [NSString stringWithFormat:@"%@", topicModel.theme_title];
        NSString *prefixLinkData = TTQTopicContentLinkTypeTheme;
        [prefixTexts addObject:prefixText];
        [prefixLinkDatas addObject:prefixLinkData];
    }
    // 官方账号不折叠
    if (viewModel.topic.publisher.is_official) {
        self.isfold = NO;
    } else if ([TTQABTestConfig topicDetailFoldImageExp]) {
        self.isfold = YES;
    } else {
        // 其他情况均不折叠
        self.isfold = NO;
    }
    /// 设置首图的缓存
    self.richParserView.firstImagePlaceholder = viewModel.firstImagePlaceholder;
    self.richParserView.subjectList = topicModel.subjects;
    self.richParserView.highlight_words = topicModel.highlight_words;
    self.richParserView.image_list = topicModel.images_list;
    if (topicModel.subject) {
        /// 检查正文是否带了该话题，没有的话就把话题追加到文末。
        NSArray *subjects = [topicModel.content ttq_topics];
        NSDictionary *data = [subjects match:^BOOL(NSString *element) {
            return [element isEqualToString:topicModel.subject.name];
        }];
        self.richParserView.canAppendSubjectIntoContent = data?NO:YES;
    } else {
        self.richParserView.canAppendSubjectIntoContent = NO;
    }
    // 先设置richparserview可不可复制内容
    if(topicModel.publisher.userID == [[IMYPublicAppHelper shareAppHelper].userid integerValue]){
        self.richParserView.canSelectContent = YES;
    } else {
        self.richParserView.canSelectContent = NO;
    }
    if (!isSameContent || self.unfoldChange) {
        self.unfoldChange = NO;
        /// 内容一样时不重新加载，避免图片闪烁
        [self.richParserView ttq_setText:htmlContent parseType:topicModel.parseType prefixTexts:prefixTexts prefixLinkDatas:prefixLinkDatas unFold:!self.unfold && self.isfold];
    }
    @weakify(self);
    // 话题曝光
    self.richParserView.hotTopicLabel.imyut_eventInfo.eventName = [NSString stringWithFormat:@"ttq_detail_content_hottopic%@", @(self.viewModel.topic_id)];
    self.richParserView.hotTopicLabel.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
        @strongify(self);
        if (imy_isNotEmptyString(self.viewModel.topic.subject.name)) {
            [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_ht",@"public_type":@(self.viewModel.topic.subject.subjectID),@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topic.publisher.userID),@"action":@1} headers:nil completed:nil];
        }
        if (imy_isNotEmptyString(self.viewModel.topic.theme_title)) {
            [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_ht",@"public_type":@(self.viewModel.topic.theme_id),@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topic.publisher.userID),@"action":@1} headers:nil completed:nil];
        }
    };
    [self.richParserView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(self.richParserView.imy_height);
    }];
    if(self.isfoldRecommend){
        self.parserTopConstraint.constant = 8 + self.userRecommendSheetHeight + 16;
    } else {
        self.parserTopConstraint.constant = 8;
    }
    CGFloat heightBeforeDataContent = spaceHeight;
    
    spaceHeight = self.richParserView.imy_height;
    __block UIView *view = self.richParserView;

    CGFloat voteViewHeight = [self voteViewHeighWithModel:topicModel];
    IMYCKCostomPKView *voteView = [self findOrAddPKView];
    TTQTwoImageVoteView *voteView2 = [self findOrAddTwoImageView];
    TTQMultipleVoteView *voteView3 = [self findOrAddMutipleVoteView];
    voteView3.isFromDetail = YES;
    voteView.hidden  = YES;
    voteView2.hidden = YES;
    voteView3.hidden = YES;
    NSInteger topSeparator = TTQTopicContentElementSeparate_l;
    if ([view isKindOfClass:[UIImageView class]]) {
        topSeparator = TTQTopicContentElementSeparate_m;
    }
    if (topicModel.vote.item_type_new == TTQVoteItemTypeTwoItems && topicModel.vote.items.count >= 2) {
        [self voteViewUpdateConstrain:voteView];
        voteView.hidden = NO;
        voteView.imy_height = voteViewHeight;
        voteView.voteModel = topicModel.vote;
        @weakify(self);
        voteView.itemSelectedBlock = ^(NSArray *_Nonnull selectedItems) {
            @strongify(self);
            [self sendVoteRequestWithSelectedItems:selectedItems];
            [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_tp",@"public_type":@(2),@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topic.publisher.userID),@"action":@2} headers:nil completed:nil];
        };
        CGSize fittingSize = voteView.imy_size;
        [voteView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(fittingSize.height);
        }];
        spaceHeight += (topSeparator + fittingSize.height);
        view = voteView;
    } else if (topicModel.vote.item_type_new == TTQVoteItemTypeTwoImageItems && topicModel.vote.items.count >= 2) {
        [self voteViewUpdateConstrain:voteView2];
        voteView2.hidden = NO;
        voteView2.imy_height = voteViewHeight;
        [voteView2 setModel:topicModel.vote];
        @weakify(self);
        voteView2.voteActionBlock = ^(NSArray *_Nonnull voteData, id<TTQFeedsVoteViewProtocol> _Nonnull voteView) {
            @strongify(self);
            [self sendVoteRequestWithSelectedItems:voteData];
            [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_tp",@"public_type":@(3),@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topic.publisher.userID),@"action":@2} headers:nil completed:nil];
        };
        CGSize fittingSize = voteView2.imy_size;
        [voteView2 mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(fittingSize.height);
        }];
        spaceHeight += (topSeparator + fittingSize.height);
        view = voteView2;
    } else if (topicModel.vote.item_type_new == TTQVoteItemTypeMutipleItems && topicModel.vote.items.count > 0) {
        [self voteViewUpdateConstrain:voteView3];
        voteView3.hidden = NO;
        voteView3.imy_height = voteViewHeight;
        [voteView3 setModel:topicModel.vote];
        @weakify(self);
        voteView3.voteActionBlock = ^(NSArray *_Nonnull voteData, id<TTQFeedsVoteViewProtocol> _Nonnull voteView) {
            @strongify(self);
            [self sendVoteRequestWithSelectedItems:voteData];
            [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_tp",@"public_type":@(1),@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topic.publisher.userID),@"action":@2} headers:nil completed:nil];
        };
        voteView3.voteFoldBlock = ^(BOOL isFold) {
            @strongify(self);
            UITableView *tableView = [self imy_findParentViewWithClass:UITableView.class];
            [tableView reloadData];
        };
        CGSize fittingSize = voteView3.imy_size;
        [voteView3 mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(fittingSize.height);
        }];
        spaceHeight += (topSeparator + fittingSize.height);
        view = voteView3;
    }


    if (topicModel.link_body) {
        if (topicModel.link_body.type == 1) {
            [IMYEventHelper event:@"tz-gjbg"];
        }
        TTQLinkView *linkView = [self.contentView viewWithTag:-1001];
        if (linkView == nil) {
            linkView = [[TTQLinkView alloc] initWithFrame:(CGRect){12, 0, SCREEN_WIDTH - 24, 64}];
            linkView.lineSpacing = 2;
            linkView.paragraphSpacing = 1;
            linkView.tag = -1001;
            [self.dataContentView addSubview:linkView];
        }
        [linkView setText:topicModel.link_body.text subTitle:topicModel.link_body.tips];
        [linkView setIconUrl:topicModel.link_body.icon];
        [linkView setADCheckButton:NO andTitle:nil];//旧的广告链接逻辑  不需要按钮 779
        @weakify(topicModel);
        linkView.clickedBlock = ^() {
            @strongify(topicModel);
            if (topicModel.link_body.type == 1) {
                [IMYEventHelper event:@"tz-gjdj"];
            }
            if (topicModel.link_body.error.length > 0 && topicModel.link_body.type == 1 && [IMYPublicAppHelper isYunqi]) {
                [UIView imy_showTextHUD:topicModel.link_body.error];
            } else if (topicModel.link_body.uri) {
                [[IMYURIManager shareURIManager] runActionWithString:topicModel.link_body.uri];
            }
        };
        [linkView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(view.mas_bottom).offset(24);
            make.height.mas_equalTo(64);
            make.left.mas_equalTo(12);
            make.right.mas_equalTo(-12);
        }];
        view = linkView;
        spaceHeight += (TTQTopicContentElementSeparate_l + 64);
    } else {
        TTQLinkView *linkView = [self.contentView viewWithTag:-1001];
        [linkView removeFromSuperview];
    }

    if (topicModel.subject_banner != nil) {
        if (imy_isNotEmptyString(topicModel.subject_banner.image_url)) {
            [self.uPlusBanner imy_setImageURL:topicModel.subject_banner.image_url];
            self.uPlusBanner.imyut_eventInfo.eventName = [NSString stringWithFormat:@"dsq_tzxqy_ppbanner%@", @(self.viewModel.topic_id)];
            self.uPlusBanner.imyut_eventInfo.showRadius = 1;
            self.uPlusBanner.imyut_eventInfo.edgeOffset = UIEdgeInsetsMake(0, 0, 50+SCREEN_TABBAR_SAFEBOTTOM_MARGIN, 0);
            self.uPlusBanner.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
                @strongify(self);
                if (imy_isNotEmptyString(self.viewModel.topic.subject.name)) {
                    [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_tzxqy_ppbanner",@"public_type":@(self.viewModel.topic.subject.subjectID),@"info_id":@(self.viewModel.topic_id),@"action":@1} headers:nil completed:nil];
                }else{
                    [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_tzxqy_ppbanner",@"info_id":@(self.viewModel.topic_id),@"action":@1} headers:nil completed:nil];
                }
            };
            self.uPlusBanner.imy_top = spaceHeight + TTQTopicContentElementSeparate_m;
            spaceHeight += 100 + TTQTopicContentElementSeparate_m;
        }
    }
    if (topicModel.forward_content) {
        self.articleView.hidden = NO;
        self.articleView.imy_top = spaceHeight + TTQTopicContentElementSeparate_l;
        spaceHeight += self.articleView.imy_height + TTQTopicContentElementSeparate_l;
        if (topicModel.forward_content.status) {
            [self.articleView updateWithAbnormal:topicModel.forward_content.message];
        } else {
            [self.articleView updateIcon:topicModel.forward_content.cover_images.firstObject isVideo:topicModel.forward_content.media_type != 0 title:topicModel.forward_content.title content:topicModel.forward_content.content];
        }
        view = self.articleView;
    } else {
        self.articleView.hidden = YES;
    }
    self.hotTopicBannerView.hidden = YES;
    self.activityBannerView.hidden = YES;
    if ([self showActivityBanner]) {
        self.activityBannerView.hidden = NO;
        [self.dataContentView addSubview:self.activityBannerView];
        NSDictionary *activity = [self.viewModel activityData];
        NSArray *list = activity[@"material_list"];
        CGFloat bannerHeight = list.count > 1?71:64;
        self.activityBannerView.imy_height = bannerHeight;
        self.activityBannerView.imy_top = spaceHeight + TTQTopicContentElementSeparate_l;
        [self.activityBannerView setDatas:list firstIndex:0];
        @weakify(list);
        [self.activityBannerView setOnItemViewCreating:^UIView * _Nonnull(UIView * _Nonnull reusableView, NSInteger index, id  _Nonnull data) {
            @strongify(list,self);
            IMYCircleActivityBannerView *banner = [[IMYCircleActivityBannerView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH - 24, bannerHeight)];
            NSDictionary *bannerData = [list imy_objectAtIndex:index];
            NSInteger bannerId = [bannerData[@"id"] integerValue];
            [banner updateWithData:bannerData];
            banner.imyut_eventInfo.eventName = [NSString stringWithFormat:@"ttq_topic_banner_%p",banner];
            banner.imyut_eventInfo.edgeOffset = UIEdgeInsetsMake(0, 0, 52+SCREEN_TABBAR_SAFEBOTTOM_MARGIN, 0);
            [banner.imyut_eventInfo setExposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
                @strongify(self);
                NSMutableDictionary *biParams = [@{@"event":@"dsq_nrxqy_hdzyw",@"action":@1,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"public_type":@(bannerId),@"index":@(index + 1)} mutableCopy];
                if (bannerData[@"redirect_url"]) {
                    biParams[@"url"] = bannerData[@"redirect_url"];
                }
                [IMYGAEventHelper postWithPath:@"event" params:biParams headers:nil completed:nil];
            }];
            return banner;
        }];
        [self.activityBannerView setOnDidClickEvent:^(NSInteger index) {
            @strongify(list,self);
            NSDictionary *data = [list imy_objectAtIndex:index];
            NSInteger bannerId = [data[@"id"] integerValue];
            if (imy_isNotEmptyString(data[@"redirect_url"])) {
                [[IMYURIManager sharedInstance] runActionWithString:data[@"redirect_url"]];
                [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_hdzyw",@"action":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"public_type":@(bannerId),@"url":data[@"redirect_url"],@"index":@(index + 1)} headers:nil completed:nil];
            }

        }];
        spaceHeight += self.activityBannerView.imy_height + TTQTopicContentElementSeparate_l;
        view = self.activityBannerView;
    } else if ([self showHotTopicBanner] && topicModel.subject && topicModel.subject.show_publish_module) {
        self.hotTopicBannerView.hidden = NO;
        [self.hotTopicBannerView resetWithData:topicModel.subject];
        self.hotTopicBannerView.imy_top = spaceHeight + TTQTopicContentElementSeparate_l;
        spaceHeight += self.hotTopicBannerView.imy_height + TTQTopicContentElementSeparate_l;
        view = self.hotTopicBannerView;
    }
    // 版权信息Label
    self.cpyRightLabel.imy_left = 12;
    self.cpyRightLabel.imy_width = self.imy_width - 24;
    
    [self.cpyRightLabel imyr_setText:topicModel.copywriter];
    [self.cpyRightLabel imyr_autoAdjustHeight];
    [self.cpyRightLabel setNeedsDisplay];
    self.cpyRightLabel.imy_top = spaceHeight + TTQTopicContentElementSeparate_h;

    if (imy_isNotEmptyString(topicModel.copywriter)) {
        spaceHeight += self.cpyRightLabel.imy_height + TTQTopicContentElementSeparate_h;
        view = self.cpyRightLabel;
    }
    
    UIView *lastView = view;

    // 顶部间距 + 评论按钮高度 + 底部间距
    spaceHeight += TTQTopicContentElementSeparate_h;//lastView == self.attitudePickerView ? (21 + 18) : (20 + 20 + 18);

    if (viewModel.topic_activity) {
        spaceHeight += 10 + 80;
        self.activityView.hidden = NO;
        self.activityView.userInteractionEnabled = YES;
        [self.activityView setWith:viewModel.topic_activity];
        @weakify(viewModel);
        [self.activityView bk_whenTapped:^{
            @strongify(viewModel);
            [IMYEventHelper event:@"htxq-hd"];
            if (!imy_isEmptyString(viewModel.topic_activity.redirect_uri)) {
                [[IMYURIManager shareURIManager] runActionWithString:viewModel.topic_activity.redirect_uri];
            }
        }];
    } else {
        self.activityView.hidden = YES;
    }

    // 把图文图层放在最上面，防止在当前图层上复制文本操作放大镜会被更上层的图层遮挡
    [self.mainView bringSubviewToFront:self.richParserView];
    [self.mainView bringSubviewToFront:self.recommendSheet];
    // 相关推荐的View
    CGFloat recommendTopicViewHeight = [self.recommendTopicView bindModel:viewModel.recommendQATopics heightForRowAtIndexPath:indexPath viewModel:viewModel];
    [self.recommendTopicView bindModel:viewModel.recommendQATopics cellForRowAtIndexPath:indexPath viewModel:viewModel];
    self.recommendTopicView.imy_top = spaceHeight;
    self.recommendTopicView.imy_height = recommendTopicViewHeight;
    spaceHeight += recommendTopicViewHeight;
    
    
    if (topicModel.category == TTQTopicCategoryCaseHistory) {
        //病例帖子尾部
        if (!self.doctorInfoView) {
            self.doctorInfoView = [[TTQTopicDetailHeadDoctorInfoView alloc] init];
            [self.mainView addSubview: self.doctorInfoView];
        }

        [self.doctorInfoView updatePublisher:topicModel.publisher];
        
        [self.doctorInfoView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.top.equalTo(self.avatarButton);
            make.right.equalTo(self.mainView);
        }];
        
        self.ipInfoLabel.hidden = YES;
        self.realIPLabel.hidden = YES;
        self.authView.hidden = YES;
        self.followButton.hidden = YES;
        self.publishTimeLabel.hidden = YES;
        
        //免责声明
        if (imy_isNotEmptyString(topicModel.disclaimer)) {
            if (!self.consultationTipsView) {
                self.consultationTipsView = [[TTQTopicDetailConsultationTipsView alloc] init];
                [self.mainView addSubview:self.consultationTipsView];
            }
            [self.consultationTipsView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.left.right.equalTo(self.mainView);
                make.bottom.equalTo(self.mainView);
            }];
            
            CGFloat viewHeight = [self.consultationTipsView updateWithDesc:topicModel.disclaimer];
            spaceHeight += viewHeight;
        }
    }

    [self.dataContentView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(spaceHeight);
    }];
    // 设置当前View的高度
    self.imy_height = spaceHeight + self.forumHeightConstraint.constant + heightBeforeDataContent;
    self.heightWithoutHeader = self.imy_height - self.forumHeightConstraint.constant;
    
    if (!self.recommendSheet.hidden) {
        self.imy_height += ([self userRecommendSheetHeight] + 16);
    }

    ((UITableView *)[self imy_findParentViewWithClass:[UITableView class]]).tableHeaderView = self;
    
    // 彩蛋
    self.eggImageView.hidden = YES;
    return self;
}

// MARK: - Helper

+ (void)gotoUserIntroduce:(TTQTopicModel *)topicModel {
    if ([TTQABTestConfig disableInteractByType:TTQ_Interact_type_userPage]) {
        return;
    }
    [IMYEventHelper event:@"htxq_tx" attributes:@{@"weizhi": @"正文"}];
    if (topicModel.publisher.userID > 0 && topicModel.publisher.error == 0) {
        IMYURI *uri = [IMYURI uriWithPath:@"user/dynamic"
                                               params:@{ @"userID": @(topicModel.publisher.userID),
                                                         @"source": @"话题详情" }
                                                 info:nil];
        [[IMYURIManager shareURIManager] runActionWithURI:uri];
        
    } else if (topicModel.publisher.error == 1) {
        [UIView imy_showTextHUD:kStatusText_UserAnonymous];
    } else if (topicModel.publisher.error == 2 || topicModel.publisher.error == 3) {
        [UIView imy_showTextHUD:kStatusText_homePageNotOpen];
    }
    [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_tx",@"action":@2,@"info_type":@12,@"info_id":@(topicModel.topic_id),@"fuid":@(topicModel.publisher.userID),@"public_type":@1} headers:nil completed:nil];

}

- (NSString *)countString:(NSInteger)count {
    if (count < 1) {
        return IMYString(@"回复");
    }
    if (count < 10000) {
        return [NSString stringWithFormat:@"%ld", (long)count];
    } else {
        return [[NSString alloc] initWithFormat:@" %@%@", @(count / 10000), IMYString(@"万")];
    }
}

- (void)voteViewUpdateConstrain:(UIView *)voteView {
    __block UIView *view = self.richParserView;
    [voteView mas_remakeConstraints:^(MASConstraintMaker *make) {
        if ([view isKindOfClass:[UIImageView class]]) {
            make.top.equalTo(view.mas_bottom).offset(16);
        } else {
            make.top.equalTo(view.mas_bottom).offset(12);
        }
        make.left.mas_offset(12);
        make.right.mas_offset(-12);
        make.height.mas_equalTo(0);
    }];
}

- (void)sendVoteRequestWithSelectedItems:(NSArray *)selectedItems {
    if ([self.viewModel isKindOfClass:TTQTopicViewModel.class]) {
        TTQTopicViewModel *topicsViewModel = (TTQTopicViewModel *)self.viewModel;
        [topicsViewModel requestVoteWithTopicModel:self.bindingModel selectedItems:selectedItems biParams:nil];
    }
}

- (void)updatefollowButtonShow:(BOOL)show status:(NSInteger)followStatus {
    if (!show) {
        self.followButton.hidden = YES;
        self.foldButton.hidden = YES;
        [self updateBadges];
        return;
    }
    self.followButton.hidden = NO;
    [self.followButton setRelation:followStatus fullColorStyle:YES];
    if(self.foldButton.hidden){
        [self.followButton mas_updateConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.mainView).offset(-12);
        }];
    } else {
        [self.followButton mas_updateConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.mainView).offset(-44);
        }];
    }
    [self layoutIfNeeded];
    [self updateBadges];
}

-(void)updateBadges{
 
    self.nicknameLabel.lineBreakMode = NSLineBreakByTruncatingMiddle;
    self.nicknameLabel.text = self.bindingModel.publisher.screen_name;
    if (self.bindingModel.publisher.badges.count) {
        self.authView.targetUserId = self.bindingModel.publisher.userID;
        [self.authView updateBadges:self.bindingModel.publisher.badges];
        [self.authView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.right.lessThanOrEqualTo(self.followButton.hidden? self : self.followButton.mas_left).offset(-12);
            make.centerY.mas_equalTo(self.nicknameLabel);
            make.left.equalTo(self.nicknameLabel.mas_right).offset(4);
            make.size.mas_equalTo(self.authView.imy_size);
        }];
        self.authView.hidden = NO;
    } else {
        self.authView.hidden = YES;
    }
    
    [self.nicknameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        if(self.authView.hidden == NO){
            make.right.mas_equalTo(self.authView.mas_left).offset(-4).priorityHigh;
        } else {
            make.right.mas_lessThanOrEqualTo(self.followButton.hidden?self:self.followButton.mas_left).offset(-12).priorityHigh;
        }
    }];
    [self layoutIfNeeded];
}

#pragma mark - recommendsheet

- (UIImage *)expandImage:(BOOL)isExpand {
    UIImage *backImage = [UIImage imageNamed:@"homepage_follow_recommend_develop"];
    backImage = [UIImage imageWithCGImage:backImage.CGImage
                                    scale:backImage.scale
                              orientation:isExpand ? UIImageOrientationDown : UIImageOrientationUp];
    return backImage;

}

-(void)foldButtonClicked:(IMYCapsuleButton *)sender{
    imy_asyncMainBlock(^{
        if(self.isfoldRecommend){
            // 展开
            [self expendRecommendSheet];
        } else {
            [self foldRecommendSheet:NO];
        }
    });
}

-(void)expendRecommendSheet{
    self.recommendSheet.hidden = NO;
    self.recommendSheet.imy_height = self.userRecommendSheetHeight;
    self.recommendSheet.alpha = 0;
    [UIView animateWithDuration:0.3 animations:^{
        self.foldButton.hidden = NO;
        [self.foldButton imy_setImage:[self expandImage:self.isfoldRecommend]];
        [self.followButton mas_updateConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.mainView).offset(-44);
        }];
        self.recommendSheet.alpha = 1;
        self.imy_height = (self.heightWithoutHeader + self.forumHeightConstraint.constant + self.userRecommendSheetHeight + 16);
        [self.dataContentView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.ipInfoLabel.mas_bottom).offset(self.userRecommendSheetHeight + 16 + 8);
        }];
        UITableView *tableView = self.superview;
        tableView.tableHeaderView = self;
        [self.superview layoutIfNeeded];
    } completion:^(BOOL finished) {
        self.recommendSheet.hidden = NO;
        self.isfoldRecommend = NO;
        [self updateBadges];
    }];
}

-(void)foldRecommendSheet:(BOOL)hide{
    [UIView animateWithDuration:0.3 animations:^{
        self.recommendSheet.alpha = 0;
        [self.foldButton imy_setImage:[self expandImage:hide?YES: self.isfoldRecommend]];
        if(hide){
            self.foldButton.hidden = YES;
            [self.followButton mas_updateConstraints:^(MASConstraintMaker *make) {
                make.right.equalTo(self.mainView).offset(-12);
            }];
        } else {
            self.foldButton.hidden = NO;
            [self.followButton mas_updateConstraints:^(MASConstraintMaker *make) {
                make.right.equalTo(self.mainView).offset(-44);
            }];
        }
        self.imy_height = self.heightWithoutHeader + self.forumHeightConstraint.constant;
        [self.dataContentView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.ipInfoLabel.mas_bottom).offset(16);
        }];
        UITableView *tableView = self.superview;
        tableView.tableHeaderView = self;
        [self.superview layoutIfNeeded];
    } completion:^(BOOL finished) {
        self.recommendSheet.hidden = YES;
        self.isfoldRecommend = YES;
    }];
}

- (CGFloat)userRecommendSheetHeight {
    return 222;
}

// MARK: - Getter

- (IMYUGCBadgesTagView *)authView {
    if (!_authView) {
        _authView = [[IMYUGCBadgesTagView alloc] initWithFrame:CGRectMake(0, 0, 0, 16)];
        @weakify(self);
        _authView.biBadgeOnClickedBlk = ^(id<IMYCKPublisherBadge>  _Nonnull badge, BOOL result) {
            @strongify(self);
            NSDictionary *params = @{
                @"event":@"dsq_nrxqy_zzhz",
                @"action":@2,
                @"info_type":@12,
                @"info_id":@(self.viewModel.topic_id),
                @"fuid":@(self.viewModel.topic.publisher.userID),
                @"public_type":badge.badgeID
            };
            [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
        };
        _authView.biBadgeOnExposuredBlk = ^(id<IMYCKPublisherBadge>  _Nonnull badge) {
            @strongify(self);
            NSDictionary *params = @{
                @"event":@"dsq_nrxqy_zzhz",
                @"action":@1,
                @"info_type":@12,
                @"info_id":@(self.viewModel.topic_id),
                @"fuid":@(self.viewModel.topic.publisher.userID),
                @"public_type":badge.badgeID
            };
            [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
        };
    }
    return _authView;
}

- (TTQTopicActivityView *)activityView {
    if (_activityView == nil) {
        _activityView = [[TTQTopicActivityView alloc] init];
        [self addSubview:_activityView];
        [_activityView imy_setBackgroundColorForKey:kCK_White_A];
        [_activityView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.bottom.and.right.equalTo(self);
            make.height.mas_equalTo(80);
        }];
    }
    return _activityView;
}

- (IMYRM80AttributedLabel *)cpyRightLabel {
    if (!_cpyRightLabel) {
        IMYRM80AttributedLabel *cpyRightLabel = [[IMYRM80AttributedLabel alloc] init];
        cpyRightLabel.imy_width = self.imy_width;
        [cpyRightLabel imy_setTextColorForKey:kCK_Black_B];
        cpyRightLabel.font = [UIFont systemFontOfSize:15];
        cpyRightLabel.newStyle = YES;
        cpyRightLabel.lineSpacing = 4.5;
        cpyRightLabel.paragraphSpacing = 16;
        cpyRightLabel.wordSpacing = 0.4;
        cpyRightLabel.imyr_emoticonSize = CGSizeMake(22, 22);
        cpyRightLabel.imyr_dynamicEmoticonSize = CGSizeMake(80, 80);
        cpyRightLabel.imyr_analysisDynamicEmoticon = NO;
        cpyRightLabel.textAlignment = kCTTextAlignmentLeft;
        _cpyRightLabel = cpyRightLabel;
        [self.dataContentView addSubview:_cpyRightLabel];
    }
    return _cpyRightLabel;
}

-(UIImageView *)uPlusBanner{
    if (!_uPlusBanner) {
        UIImageView *imageView = [[UIImageView alloc] initWithFrame:CGRectMake(12, 0, SCREEN_WIDTH - 24, 100)];
        imageView.userInteractionEnabled = YES;
        imageView.layer.cornerRadius = 8;
        imageView.clipsToBounds = YES;
        _uPlusBanner = imageView;
        [self.dataContentView addSubview:_uPlusBanner];
        @weakify(self);
        [_uPlusBanner bk_whenTapped:^{
            @strongify(self);
            if (imy_isNotEmptyString(self.bindingModel.subject_banner.redirect_url)) {
                [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_tzxqy_ppbanner",@"public_type":@(self.viewModel.topic.subject.subjectID),@"info_id":@(self.viewModel.topic_id),@"action":@2} headers:nil completed:nil];
                [[IMYURIManager shareURIManager] runActionWithString:self.bindingModel.subject_banner.redirect_url];
            }
        }];
    }
    return _uPlusBanner;
}

- (TTQRecommendTopicView *)recommendTopicView {
    if (!_recommendTopicView) {
        _recommendTopicView = [[TTQRecommendTopicView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 0)];
        [self addSubview:_recommendTopicView];
    }
    return _recommendTopicView;
}

- (IMYCKCostomPKView *)findOrAddPKView {
    IMYCKCostomPKView *pkView = nil;
    for (UIView *subView in self.mainView.subviews) {
        if ([subView isKindOfClass:IMYCKCostomPKView.class]) {
            pkView = (IMYCKCostomPKView *)subView;
            break;
        }
    }
    if (!pkView) {
        pkView = [[IMYCKCostomPKView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH - 24, 10)];
        [self.dataContentView addSubview:pkView];
    }
    return pkView;
}

- (TTQTwoImageVoteView *)findOrAddTwoImageView {
    TTQTwoImageVoteView *voteView = nil;
    for (UIView *subView in self.mainView.subviews) {
        if ([subView isKindOfClass:TTQTwoImageVoteView.class]) {
            voteView = (TTQTwoImageVoteView *)subView;
            break;
        }
    }
    if (!voteView) {
        voteView = [TTQTwoImageVoteView viewWithModel:[self voteModel]];
        [self.dataContentView addSubview:voteView];
    }
    return voteView;
}

- (TTQMultipleVoteView *)findOrAddMutipleVoteView {
    TTQMultipleVoteView *voteView = nil;
//    for (UIView *subView in self.mainView.subviews) {
//        if ([subView isKindOfClass:TTQMultipleVoteView.class]) {
//            voteView = (TTQMultipleVoteView *)subView;
//            break;
//        }
//    }
    if (!voteView) {
        voteView = [TTQMultipleVoteView viewWithModel:[self voteModel]];
        [self.dataContentView addSubview:voteView];
    }
    return voteView;
}

- (TTQVoteModel *)voteModel {
    TTQTopicModel *topicModel = nil;
    if ([self.bindingModel isKindOfClass:TTQTopicModel.class]) {
        topicModel = (TTQTopicModel *)self.bindingModel;
    }
    topicModel.vote.isInFeeds = NO; //标识为流里面的投票jer075
    return topicModel.vote;
}

- (CGFloat)voteViewHeighWithModel:(TTQTopicModel *)model {
    return [self voteViewHeightWithModel:model.vote];
}

- (CGFloat)voteViewHeightWithModel:(TTQVoteModel *)model {
    model.isInFeeds = NO;
    if (model.item_type_new == 1) {
        return [IMYCKCostomPKView viewHeightWithModel:model];
    } else if (model.item_type_new == 2) {
        //双图
        return [TTQTwoImageVoteView viewHeightWithModel:model];
    } else if (model.item_type_new == 3) {
        return [TTQMultipleVoteView viewHeightWithModel:model];
    }
    return 0;
}

- (IMYCKForwardArticleView *)articleView {
    if (!_articleView) {
        _articleView = [[IMYCKForwardArticleView alloc] initWithFrame:CGRectMake(12, 0, SCREEN_WIDTH - 24, 56) type:ForwardArticleViewTwoLine];
        [self.dataContentView addSubview:_articleView];
        @weakify(self);
        [_articleView setForwardArticleTapBlock:^{
            @strongify(self);
            [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_tzlj",@"action":@2,@"info_type":@12,@"info_id":@(self.bindingModel.topic_id),@"fuid":@(self.bindingModel.publisher.userID)} headers:nil completed:nil];
            if (self.bindingModel.forward_content.status == 0) {
                [[IMYURIManager shareURIManager] runActionWithString:self.bindingModel.forward_content.redirect_url];
            }
        }];
        _articleView.imyut_eventInfo.eventName = [NSString stringWithFormat:@"ttq_detail_forwardContent_%p",self];
        _articleView.imyut_eventInfo.showRadius = 1;
        _articleView.imyut_eventInfo.edgeOffset = UIEdgeInsetsMake(0, 0, 52+SCREEN_TABBAR_SAFEBOTTOM_MARGIN, 0);
        [_articleView.imyut_eventInfo setExposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
            @strongify(self);
            [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_tzlj",@"action":@1,@"info_type":@12,@"info_id":@(self.bindingModel.topic_id),@"fuid":@(self.bindingModel.publisher.userID)} headers:nil completed:nil];
        }];
    }
    return _articleView;
}

- (TTQDetailHeaderSubjectView *)hotTopicBannerView {
    if (!_hotTopicBannerView) {
        _hotTopicBannerView = [[TTQDetailHeaderSubjectView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 96)];
        [self.dataContentView addSubview:_hotTopicBannerView];
        @weakify(self);
        [_hotTopicBannerView setTapActionBlock:^{
            @strongify(self);
            NSMutableDictionary *params = [NSMutableDictionary dictionary];
            params[@"subject_id"] = @(self.viewModel.topic.subject.subjectID);
            params[@"subject_name"] = self.viewModel.topic.subject.name;
            params[@"publish_entrance"] = @(18);
            [[IMYURIManager shareURIManager] runActionWithPath:@"circles/publish" params:params info:nil];
        
            [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"TopicPublish_entrance",@"publish_entrance":@18,@"action":@2,@"public_info":@(self.viewModel.topic_id)} headers:nil completed:nil];
        }];
        
        _hotTopicBannerView.imyut_eventInfo.eventName = [NSString stringWithFormat:@"topic_header_hot_publish_%p",self];
        _hotTopicBannerView.imyut_eventInfo.showRadius = 1;
        _hotTopicBannerView.imyut_eventInfo.edgeOffset = UIEdgeInsetsMake(0, 0, 52+SCREEN_TABBAR_SAFEBOTTOM_MARGIN, 0);
        [_hotTopicBannerView.imyut_eventInfo setExposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
            @strongify(self);
            [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"TopicPublish_entrance",@"publish_entrance":@18,@"action":@1,@"public_info":@(self.viewModel.topic_id)} headers:nil completed:nil];
        }];
    }
    return _hotTopicBannerView;
}

- (IMYUGCBannerView *)activityBannerView {
    if (!_activityBannerView) {
        _activityBannerView = [[IMYUGCBannerView alloc] initWithFrame:CGRectMake(12, 0, SCREEN_WIDTH - 24, 64)];
        _activityBannerView.layer.cornerRadius = 12;
        _activityBannerView.layer.masksToBounds = YES;
        _activityBannerView.animationType = IMYUGCBannerAnimationTypeHide;
        _activityBannerView.pageControlEdgeBottom = 8;
        _activityBannerView.layer.borderColor = [UIColor imy_colorForKey:@"e8e8e8"].CGColor;
        _activityBannerView.layer.borderWidth = 1/SCREEN_SCALE;
        [self.dataContentView addSubview:_hotTopicBannerView];
    }
    return _activityBannerView;
}

- (BOOL)showActivityBanner {
    IMYABTestExperiment *exp = [[IMYABTestManager sharedInstance] experimentForKey:@"tiezi_banner"];
    if (exp && [exp.vars integerForKey:@"banner"] == 1) {
        if ([self.viewModel activityData]) {
            NSDictionary *activity = [self.viewModel activityData];
            NSArray *list = activity[@"material_list"];
            if (list && [list isKindOfClass:NSArray.class] && list.count) {
                return YES;
            }
        }
    }
    return NO;
}
/// 是否展示话题反布，优先级低于活动banner
- (BOOL)showHotTopicBanner {
    IMYABTestExperiment *exp = [[IMYABTestManager sharedInstance] experimentForKey:@"tiezi_banner"];
    if (exp && [exp.vars integerForKey:@"banner"] == 2) {
        return NO;
    }
    return YES;
}
@end
