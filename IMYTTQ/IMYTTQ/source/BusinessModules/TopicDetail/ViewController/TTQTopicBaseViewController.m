//
//  TTQTopicBaseViewController.m
//  IMYTTQ
//
//  Created by king on 15/7/16.
//  Copyright © 2015年 MeiYou. All rights reserved.
//

#import "TTQTopicBaseViewController.h"
#import "GCDObjC.h"
#import "IMYAccountCheckService.h"
#import "IMYJumpManager.h"
#import "IMYRM80AttributedLabelURL.h"
#import "TTQABTestConfig.h"
#import "TTQForumModel.h"
#import "TTQJumpType.h"
#import "TTQMessageDetailViewModel.h"
#import "TTQTopicCache.h"
#import "TTQTopicDetailViewController.h"
#import "TTQTopicQuoteCell.h"
#import "TTQTopicReferenceViewModel.h"
#import "UIViewController+TTQ.h"
#import <IMYAccount/IMYAccountServerURL.h>
#import "TTQCommentContentCacheManager.h"
#import "TTQNewbeeTaskManager.h"
#import <IMYBaseKit/IMYHTTPBackupHostConfig.h>
#if __has_include(<IMYEBPublic/IMYEBYoubiTaskManager.h>)
#import "IMYEBYoubiTaskManager.h"
#endif
#import <IMYUGC/IMYRewardAlertView.h>
#import <IMYUGC/IMYCKInputWithStickerView.h>
#import <IMYUGC/IMYUGCImageObject.h>
#import <IMYBaseKit/IMYVKStickerManager.h>

#define kJindouReviewTaskKey @"community_review"

@implementation TTQReplyObj

@end

@interface TTQTopicBaseViewController ()
@property (nonatomic, strong) TTQTopicViewModel *viewModel;
@property (nonatomic, strong) IMYPhotoBrowser *photoBrowser;
@property (nonatomic, assign) BOOL shouldBecomeFirstResponderWhenAppear;
@property (nonatomic, strong) NSIndexPath *lastSelectedReplyIndex;
@property (nonatomic, assign) CGPoint disappearContentOffset;
@property (nonatomic, assign) BOOL prepageNetErrorToast;
@property (nonatomic, assign) CGFloat startDragOffsetY;  /// 开始滚动时的offsetY

@end

@implementation TTQTopicBaseViewController

- (void)bindViewModel {
    @weakify(self);
    [self ttq_addFooterWithMoreBlock:^{
        @strongify(self);
        [self requestRemoteDataForType:TTQTopicDetailRequestTypeNextPage params:nil];
    }];
    self.viewModel.automaticallyRefresh = YES;
    [super bindViewModel];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupInputView];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(onKeyboardHideNotification:) name:UIKeyboardWillHideNotification object:nil];
    
    @weakify(self);
    RACSignal *netSingal = [IMYNetState networkChangedSignal];
    [[[netSingal distinctUntilChanged] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(id  _Nullable x) {
        if ([IMYNetState networkEnable]){
            @strongify(self);
            self.prepageNetErrorToast = NO;
        }
    }];
    
    [[[[RACObserve(self, prepageNetErrorToast) distinctUntilChanged] deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(id  _Nullable x) {
        if (![IMYNetState networkEnable] && [x boolValue] == YES){
            [UIWindow imy_showTextHUD:MT_Request_NoNetToast];
        }
    }];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    //fix bug https://www.tapd.cn/22362561/prong/stories/view/1122362561001058900
    if (!CGPointEqualToPoint(self.disappearContentOffset, self.tableView.contentOffset)) {
        imy_asyncMainBlock(^{
            [self.tableView setContentOffset:self.disappearContentOffset animated:NO];
        });
    }
    if (self.shouldBecomeFirstResponderWhenAppear) {
        self.viewModel.selectedReplyIndex = self.lastSelectedReplyIndex;
        if (![self.inputContentsView.textView isFirstResponder]) {
            //bugfix 修复第一次进入详情页选择照片后, 键盘主动弹起会遮住图片预览缩略的bug
            [self.inputContentsView.textView becomeFirstResponder];
        }
        self.shouldBecomeFirstResponderWhenAppear = NO;
    }
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    self.disappearContentOffset = self.tableView.contentOffset;
    [self.navigationController.view.subviews bk_each:^(UIView *subview) {
        if ([subview isKindOfClass:[IMYActionSheet class]]) {
            [((IMYActionSheet *)subview)dismiss];
        }
    }];
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    [self.photoBrowser dismiss];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    [self setupInputViewWhenViewDidAppear];
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark - UITableViewDelegate methods
- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
}

- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView {
    self.startDragOffsetY = scrollView.contentOffset.y;
    if (self.inputContentsView.isFirstResponder) {
        [self hideKeyboard];
    }
}


#pragma mark IMYRM80AttributedLabelDelegate
- (void)m80AttributedLabel:(IMYRM80AttributedLabel *)label longedOnLink:(IMYRM80AttributedLabelURL *)linkURL {
    [label resetLongPressedStatus];
    [self longPressWithLink:(NSString *)linkURL.linkData];
}

- (void)longPressWithLink:(NSString *)linkUrl {
    NSURL *url = [NSURL URLWithString:linkUrl];
    if (url == nil) {
        return;
    }
    
    [self.inputContentsView.textView resignFirstResponder];
    [self.inputContentsView resignFirstResponder];
    @weakify(self);
    [IMYActionSheet sheetWithCancelTitle:IMYString(@"取消")
                             otherTitles:@[IMYString(@"打开")]
                                 summary:nil
                              showInView:self.navigationController.view
                                  action:^(NSInteger index) {
        @strongify(self);
        if (index == 1) {
            NSString *topic_id = [url imy_queryDictionary][@"topic_id"];
            if (topic_id) {
                TTQTopicViewModel *model = [[TTQTopicViewModel alloc] initWithTopicID:[topic_id intValue]];
                [self imy_push:[[TTQTopicDetailViewController alloc] initWithViewModel:model]];
            } else {
                if ([url.absoluteString hasPrefix:@"http"]) {
                    [self imy_push:[IMYVKWebViewController webWithURLString:url.absoluteString]];
                } else {
                    IMYURI *uri = [IMYURI uriWithURIString:linkUrl];
                    if ([[IMYURIManager shareURIManager] containScheme:uri.scheme] && [[IMYURIManager shareURIManager] runActionWithURI:uri]) {
                        return;
                    }
                    [[UIApplication sharedApplication] openURL:url options:nil completionHandler:nil];
                }
            }
        }
    }];
}
/**
 1  '外链',
 2  '内链',
 3  '主题商城',
 4  '皮肤详情',
 5  '话题',          xixiaoyou.com?__type=5&topic_id=234325
 6  '话题专题',	    xixiaoyou.com?__type=6&catid=0&specialid=0
 7  '消息',	    xixiaoyou.com?__type=7
 8  '意见反馈',      xixiaoyou.com?__type=8
 9  '柚子街',        xixiaoyou.com?__type=9
 10  '我的柚币',     xixiaoyou.com?__type=10
 11  '签到',         xixiaoyou.com?__type=11
 12  '求助区入口',   xixiaoyou.com?__type=12&forum_id=0 //已经删除了，没有求助区了哟
 13  '达人堂入口'    xixiaoyou.com?__type=13&forum_id=0
 */
- (void)m80AttributedLabel:(IMYRM80AttributedLabel *)label clickedOnLink:(IMYRM80AttributedLabelURL *)linkURL {
    NSString *copyString = linkURL.linkData;
    if ([copyString isEqualToString:@"from"] || [copyString isEqualToString:@"to-reply"]) {
        TTQTopicQuoteCell *cell = [label imy_findParentViewWithClass:[TTQTopicQuoteCell class]];
        NSIndexPath *indexPath = [self.tableView indexPathForCell:cell];
        TTQCommentModel *model = [self tableCellModelAtIndexPath:indexPath];
        TTQPublisherModel *publisher = nil;
        if ([copyString isEqualToString:@"to-reply"]) {
            publisher = model.replygoal;
        } else {
            publisher = model.publisher;
        }
        if (publisher.userID > 0 && publisher.error == 0) {
            IMYURI *uri = [IMYURI uriWithPath:@"user/dynamic"
                                       params:@{ @"userID": @(publisher.userID)
                                                 ,@"source": @"话题详情"}
                                         info:nil];
            [[IMYURIManager shareURIManager] runActionWithURI:uri];
            
        } else if (publisher.error == 1) {
            [UIView imy_showTextHUD:kStatusText_UserAnonymous];
        } else if (publisher.error == 2) {
            [UIView imy_showTextHUD:kStatusText_homePageNotOpen];
        }else if (publisher.error == 3){
            [UIView imy_showTextHUD:IMYString(kStatusText_homePageNotOpen)];
        }
        NSIndexPath *mainCommentIndexPath = [NSIndexPath indexPathForRow:0 inSection:indexPath.section];
        TTQCommentModel *mainComment = [self tableCellModelAtIndexPath:mainCommentIndexPath];
        [self postCommentBiFeedsView:2 commentId:model.commentID floor:indexPath.row clickpos:1];
    } else if ([copyString isEqualToString:@"to"]) {
        TTQTopicQuoteCell *cell = [label imy_findParentViewWithClass:NSClassFromString(@"TTQTopicReferenceCell")];
        TTQCommentModel *model = [self tableCellModelAtIndexPath:[self.tableView indexPathForCell:cell]];
        
        
        if (model.replygoal.userID > 0 && model.replygoal.error == 0) {
            IMYURI *uri = [IMYURI uriWithPath:@"user/dynamic"
                                       params:@{ @"userID": @(model.replygoal.userID)
                                                 ,@"source": @"话题详情"}
                                         info:nil];
            [[IMYURIManager shareURIManager] runActionWithURI:uri];
            NSIndexPath *indexPath = [self.tableView indexPathForCell:cell];
            [self postCommentBiFeedsView:2 commentId:model.commentID floor:indexPath.row + 1 clickpos:1];
        } else if (model.replygoal.error == 1) {
            [UIView imy_showTextHUD:kStatusText_UserAnonymous];
        } else if (model.replygoal.error == 2) {
            [UIView imy_showTextHUD:kStatusText_homePageNotOpen];
        }
    } else {
        [self clickWithLink:copyString];
    }
}

- (void)clickWithLink:(NSString *)linkUrl {
    NSURL *url = [NSURL URLWithString:linkUrl];
    if ([url.absoluteString containsString:@"itunes.apple.com"]) {
        [[UIApplication sharedApplication] openURL:url options:nil completionHandler:nil];
    } else {
        IMYURI *uri = nil;
        if (linkUrl) {
            if ([self isKindOfClass:[TTQTopicDetailViewController class]]) {
                if (((TTQTopicDetailViewController *)self).viewModel.topic.is_experience && [linkUrl containsString:@"circles/publish?"]) {
                    NSMutableString *tmp = [[NSMutableString alloc] initWithString:linkUrl];
                    [tmp replaceOccurrencesOfString:@"circles/publish" withString:@"circles/publish/experience" options:NSCaseInsensitiveSearch range:NSMakeRange(0, linkUrl.length)];
                    linkUrl = [tmp copy];
                }
            }
            uri = [IMYURI uriWithURIString:linkUrl];
            /// 发布入口埋点
            [uri appendingParams:@{@"publish_entrance":@7}];
            if ([[IMYURIManager shareURIManager] containScheme:uri.scheme] && [self injectSaleUri:uri]) {
                return;
            }
        }
        
        NSDictionary *dic = [linkUrl imy_queryDictionary];
        
        NSString *uriString = dic[@"uri"];
        if (uriString) {
            uri = [IMYURI uriWithURIString:[uriString imy_base64DecodedSafeURLString]];
            if ([[IMYURIManager shareURIManager] containScheme:uri.scheme] && [self injectSaleUri:uri]) {
                return;
            }
        }
        NSString *topic_id = dic[@"topic_id"];
        //旧的跳转方式.只提供topic_id.没有提供__type
        if (topic_id) {
            [IMYEventHelper event:@"ttq-2"];
            [[IMYURIManager shareURIManager]
             runActionWithURI:[IMYURI uriWithPath:@"circles/group/topic"
                                           params:@{@"topicID": topic_id}
                                             info:nil]];
            return;
        }
        NSString *type = dic[@"__type"];
        if (type) {
            [IMYEventHelper event:[NSString stringWithFormat:@"ttq-%@", type]];
            if ([type isEqualToString:@"1"]) {
                [[UIApplication sharedApplication] openURL:url options:nil completionHandler:nil];
            } else if ([type isEqualToString:@"2"]) {
                NSDictionary *params = @{ @"url": url.absoluteString,
                                          @"usingWK": @(YES),
                };
                [[IMYURIManager shareURIManager] runActionWithPath:@"web" params:params info:nil];
            } else if ([type isEqualToString:@"3"]) {
                [IMYEventHelper event:@"gxzt" addType:0 attributes:@{@"来源": @"话题-主题商城"}];
                //跳到主题商城
                [[IMYURIManager shareURIManager] runActionWithPath:@"theme" params:nil info:nil];
                return;
            } else if ([type isEqualToString:@"4"]) {
                [IMYEventHelper event:@"gxzt" addType:0 attributes:@{@"来源": @"话题-皮肤详情"}];
                //跳到主题详情
                [[IMYURIManager shareURIManager] runActionWithPath:@"theme/detail" params:@{@"themeID": dic[@"skinid"]} info:nil];
                return;
            } else if ([type isEqualToString:@"5"]) {
                // 5  '话题',           xixiaoyou.com?__type=5&topic_id=234325
                [IMYEventHelper event:@"ttq-2"];
                [[IMYURIManager shareURIManager]
                 runActionWithURI:[IMYURI uriWithPath:@"circles/group/topic"
                                               params:@{@"topicID": dic[@"topic_id"]}
                                                 info:nil]];
            } else if ([type isEqualToString:@"6"]) {
                // 6  '话题专题',         xixiaoyou.com?__type=6&catid=0&specialid=0
                IMYURI *uri = [IMYURI uriWithPath:@"news/special" params:dic info:nil];
                [[IMYURIManager shareURIManager] runActionWithURI:uri];
            } else if ([type isEqualToString:@"7"]) {
                // 7  '消息',            xixiaoyou.com?__type=7
                [[IMYURIManager shareURIManager] runActionWithURI:[IMYURI uriWithPath:@"msg/entrance" params:nil info:nil]];
            } else if ([type isEqualToString:@"8"]) {
                // 8  '意见反馈',      xixiaoyou.com?__type=8
                [[IMYURIManager shareURIManager] runActionWithString:@"feedback"];
                
            } else if ([type isEqualToString:@"9"]) {
                // 9  '柚子街',        xixiaoyou.com?__type=9
                [[IMYURIManager shareURIManager] runActionWithString:@"sale/home"];
            } else if ([type isEqualToString:@"10"]) {
                // 10  '我的柚币',     xixiaoyou.com?__type=10
                [[IMYURIManager shareURIManager] runActionWithString:@"youbi"];
            } else if ([type isEqualToString:@"11"]) {
                // 11  '签到',         xixiaoyou.com?__type=11
                [[IMYURIManager shareURIManager] runActionWithString:@"sale/sign"];
            } else if ([type isEqualToString:@"13"]) {
                // 13  '达人堂入口'    xixiaoyou.com?__type=13&forum_id=0
                if ([IMYPublicAppHelper shareAppHelper].hasLogin) {
                    [[IMYURIManager shareURIManager]
                     runActionWithURI:[IMYURI uriWithPath:@"circles/honorhall"
                                                   params:@{ @"forum_id": @([dic[@"forum_id"] integerValue]) }
                                                     info:nil]];
                } else {
                    [IMYEventHelper event:@"dl" addType:0 attributes:@{@"来源": @"话题详情"}];
                    [UIWindow imy_showTextHUD:kStatusText_unLogin];
                    [[IMYURIManager shareURIManager] runActionWithString:@"login"];
                }
            } else {
                NSLog(@"传入的type有问题");
                return;
            }
        } else {
            [self imy_push:[IMYVKWebViewController webWithURLString:url.absoluteString]];
        }
    }
}
/// 835电商有个埋点需求，前后版本电商需要的协议不一样，但服务端只会下发旧协议，会添加一个特定的协议参数下来，这里需要调用下对方的协议
- (BOOL)injectSaleUri:(IMYURI *)uri {
    if ([[IMYURIManager shareURIManager] containActionBlockForPath:uri.path]) {
        /// 有相应电商的协议才生效，防止电商版本不一致，过滤下热议话题业务
        if (![uri.params objectForKey:@"topic_id"] && ![uri.path hasSuffix:@"circles/topic/subject"]) {
            [uri appendingParams:@{@"topic_id":@(self.viewModel.topic_id)}];
        }
        IMYURIActionBlockObject *objc = [IMYURIActionBlockObject actionBlockWithURI:[IMYURI uriWithPath:@"sale/interceptor" params:@{@"redirect_url":[uri uri]} info:@{}]];
        __block BOOL uriComplete = NO;
        /// 这里是电商替换完redirect_url后最终执行的地方
        objc.implCallbackBlock = ^(id result, NSError *error, NSString *eventName) {
            NSString *redirect_url = result[@"redirect_url"];
            uriComplete = [[IMYURIManager shareURIManager] runActionWithString:redirect_url];
        };
        [[IMYURIManager shareURIManager] runActionWithActionObject:objc completed:nil];
        return uriComplete;
    } else {
        return [[IMYURIManager shareURIManager] runActionWithURI:uri];
    }
}

#pragma mark - ShowLargePhotosDelegate methods

- (void)showWithPhotos:(NSMutableArray *)photos atIndex:(NSUInteger)index {
    if (self.inputContentsView.isFirstResponder) {
        [self hideKeyboard];
        return;
    }
    self.photoBrowser = [IMYPhotoBrowser showWithPhotos:photos atIndex:index];
    self.photoBrowser.pageControlStyle = IMYBrowserPageControlStyleText;
    self.photoBrowser.delegate = self;
    
    [self postHandlePhotoBroswerShow];
}

// MARK: - IMYPhotoBrowserDelegate

- (void)photoBrowserWillHide:(IMYPhotoBrowser *)photoBrowser {
    [self postHandlePhotoBroswerHide];
}

#pragma mark - setup

- (void)onKeyboardHideNotification:(NSNotification *)notification {
    // 处理发送评论未登录的情况，登录完成返回来显示评论内容
    if (self.inputContentsView.isFirstResponder) {
        @weakify(self);
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            @strongify(self);
            if (![[UIViewController imy_currentVisibleViewController] isEqual:self]) {
                if ([[UIViewController imy_currentViewControlloer] isKindOfClass:TTQTopicDetailViewController.class]) {
                    self.shouldBecomeFirstResponderWhenAppear = NO;
                    return;
                }
                self.shouldBecomeFirstResponderWhenAppear = YES;
                self.lastSelectedReplyIndex = self.viewModel.selectedReplyIndex;
            }
        });
    }
}

- (void)setInputContentsView:(IMYCKInputWithStickerView *)inputView {
    [super setInputContentsView:inputView];
    [self.view bringSubviewToFront:self.captionView];
}

- (void)setupInputViewWhenViewDidAppear {
    if (![self.inputContentsView isFirstResponder]) {
        [self.inputContentsView resignFirstResponder];
        self.tableView.imy_height = self.inputContentsView.imy_top;
    }
}

- (void)takeImage {
    if (self.viewModel.forum.open_pregnancy_album) {
        @weakify(self);
        [IMYActionSheet
         sheetWithCancelTitle:IMYString(@"取消")
         otherTitles:@[IMYString(@"从我的待产包选择"), IMYString(@"从手机相册选择"), IMYString(@"拍照") ]
         summary:nil
         showInView:self.navigationController.view
         action:^(NSInteger index) {
            @strongify(self);
            if (index == 0) {
                return;
            }
            [IMYEventHelper event:@"hf-tjtp"];
            UIImagePickerController *imagePickerController = [[UIImagePickerController alloc] init];
            if (index == 1) {
                [IMYEventHelper event:@"hf-dcb"];
                [[IMYURIManager shareURIManager]
                 runActionWithURI:[IMYURI uriWithPath:@"tools/childbirthBagList"
                                               params:@{@"selectCallback":^(NSDictionary *dic){
                    @strongify(self);
                    NSString *str = nil;
                    NSArray *urls = nil;
                    NSArray *draft = dic[@"draft"];
                    if ([draft isKindOfClass:[NSArray class]]) {
                        NSDictionary *firstDraft = draft.firstObject;
                        if ([firstDraft isKindOfClass:[NSDictionary class]]) {
                            str = firstDraft[@"content"];
                            urls = firstDraft[@"urls"];
                        }
                    }
                    if (imy_isNotEmptyString(str)) {
                        NSMutableString *content = self.inputContentsView.textView.text.mutableCopy;
                        [content appendString:str];
                        self.inputContentsView.textView.text = content;
                    }
                    
                    if (urls.count > 0 ) {
                        self.cameraButton.assetURL = [NSURL URLWithString:urls[0]];
                        [self.cameraButton setupOriginalImage];
                    }
                }}
                                                 info:nil]];
                return;
                
            } else if (index == 2) {
                [IMYEventHelper event:@"tjtp" label:@"系统相册"];
                if (![IMYImagePickerController isAccessible]) {
                    [UIWindow imy_showTextHUD:IMYString(@"相册照片无法显示啦，请在系统设置-隐私-照片中打开美柚开关")];
                }
                imagePickerController.sourceType = UIImagePickerControllerSourceTypePhotoLibrary;
            } else {
                [IMYEventHelper event:@"tjtp" label:@"拍照"];
                imagePickerController = [UIViewController ttq_imagePickerController];
            }
            imagePickerController.delegate = self;
            [self presentViewController:imagePickerController
                               animated:YES
                             completion:^{
                [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleDefault;
            }];
        }];
    }
    else {
        [super takeImage];
    }
}

// changeHeight:改变的高度,正数为增加，负数为减小
- (void)inputViewDidChangeHeight:(IMYREasyInputView *)inputView changeHeight:(float)changeHeight {
    self.tableView.imy_height = self.view.imy_height - inputView.imy_height;
}

- (void)inputViewWillBecomeFirstResponder:(IMYREasyInputView *)inputView
                           keyboardHeight:(CGFloat)height
                        animationDuration:(CGFloat)duration
                                   option:(UIViewAnimationOptions)option {
    //判断一下是否会越界。ios8不会有这个问题。ios7才会
    [super inputViewWillBecomeFirstResponder:inputView keyboardHeight:height animationDuration:duration option:option];
}

- (void)inputViewWillBecomeFirstResponderWith:(CGFloat)height {
    if (self.viewModel.selectedReplyIndex && [self.tableView numberOfSections] > self.viewModel.selectedReplyIndex.section) {
        CGRect rect = [self.tableView rectForRowAtIndexPath:self.viewModel.selectedReplyIndex];
        CGFloat y = rect.origin.y + rect.size.height - self.tableView.imy_height + height;
        if (y > 0) {
            [self.tableView setContentOffset:CGPointMake(0, y) animated:NO];
        }
    }
}

- (void)postWithImageNames:(NSArray *)imageNames {
    @weakify(self);
    ProgressCallback progressBlock = nil;
    if (imageNames.count == 0) {
        [self hidenProgressHUD];
        [UIWindow imy_showLoadingHUDWithText:IMYString(@"正在发送...")];
    } else {
        [self setProcessHUD:0.9];
        progressBlock = ^(int64_t completedUints, int64_t totalUnits) {
            imy_asyncMainBlock(^{
                @strongify(self);
                [self setProcessHUD:0.1 * completedUints / (double)totalUnits + 0.9f];
            });
        };
    }
    __block NSMutableDictionary *params = [[NSMutableDictionary alloc] init];
    params[@"content"] = [[self inputContentFromTextView] imy_trimString];
    if ([self.inputContentsView respondsToSelector:NSSelectorFromString(@"machTopics")]) {
        params[@"subject_struct"] = self.inputContentsView.machTopics;
    }
    if (imageNames.count > 0) {
        params[@"images"] = imageNames;
    }
    NSInteger imageCounts = 0;
    if ([params[@"images"] isKindOfClass:NSArray.class]) {
        NSArray *images = params[@"images"];
        imageCounts = images.count;
    }
    [self appendImagesV2Params:params];
    if (self.inputContentsView.postArticleButton.selected) {
        params[@"is_forward"] = @1;
    }

    NSDate *requestStartTime = [NSDate date];
    [[[self.viewModel.replyCommand execute:(progressBlock == nil ? @[params] : @[params, progressBlock])] deliverOnMainThread] subscribeNext:^(id<IMYHTTPResponse> response) {
        @strongify(self);
        NSString *content = self.inputContentsView.textView.text;
        [self hidenProgressHUD];
        IMYUGCImageObject *imageObj = [self.inputContentsView imageModel];
        self.viewModel.inputDefaultText = nil;
        self.inputContentsView.textView.text = nil;
        [self.inputContentsView clearPhoto];
        [self.inputContentsView resetPostArticle];
        [self.inputContentsView restPose];
        // 需要在重置inputContentsView内容之后调用隐藏键盘，否则隐藏键盘的步骤会记录数据导致错误
        [self hideKeyboardForceDispatchToMain:NO];
        
        // 清除缓存记录
        // referenced_id 需要使用 NSInteger 接收，详情页帖子评论该值为-1
        NSInteger commentID = [params[@"referenced_id"] integerValue];
        NSUInteger topicID = [params[@"topic_id"] integerValue];
        if (commentID > 0) {
            [[TTQCommentContentCacheManager sharedManager] setContent:nil withCommentID:commentID];
            [[TTQCommentContentCacheManager sharedManager] setIsForward:NO withCommentID:commentID];
        } else if (topicID) {
            [[TTQCommentContentCacheManager sharedManager] setContent:nil withTopicID:topicID];
            [[TTQCommentContentCacheManager sharedManager] setIsForward:NO withTopicID:topicID];
        }
        
        self.viewModel.selectedReplyIndex = nil;
        NSInteger timestamp = [response.responseObject[@"timestamp"] integerValue];
        NSInteger score = [response.responseObject[@"score"] integerValue];
        NSInteger statusCode = response.response.statusCode;
        [[IMYURIManager shareURIManager] runActionWithPath:@"push/checkPushSettings" params:@{@"position":@(IMYNABoxShowPosition_ttqPublic)} info:nil];
        if (score) {
            [UIWindow imy_hideHUD];
            [[TTQNewbeeTaskManager sharedManager] showTipWithMessage:[NSString stringWithFormat:@"恭喜完成评论帖子任务 贡献值+%li",(long)score]];
            [self finishedReplyRequest:content timestamp:timestamp];
        } else {
            NSDictionary *result_popup = response.responseObject[@"result_popup"];
            if (!result_popup || result_popup.allKeys.count == 0) {
                /// 没有物品奖励弹窗时出现
                [self showReplySuccessHub];
            } else {
                [UIView imy_hideHUD];
            }
        }
        //        [self finishedRequest:nil];
        [self.tableView reloadData];
        if ([self isKindOfClass:TTQTopicDetailViewController.class]) {
            if (commentID < 1) {
                /// finishedRequest 给详情页的数据太少，分不清是不是评论会来的请求，所以在这里做了，回复主楼后定位到第一条
                [self.tableView scrollToRowAtIndexPath:[NSIndexPath indexPathForRow:0 inSection:1] atScrollPosition:UITableViewScrollPositionTop animated:YES];
            }
            if (imageObj && [imageObj.userInfo isKindOfClass:IMYVKStickerItemModel.class]) {
                IMYVKStickerItemModel *sticker = imageObj.userInfo;
                NSString *typeString = [NSString stringWithFormat:@"%@_%ld",sticker.groupName, sticker.stickerId];
                [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqypl_fsemoji",@"action":@2,@"public_type":typeString} headers:nil completed:nil];
            }
        }
        if (score == 0) {
            [self finishedReplyRequest:content timestamp:timestamp];
        }
        [TTQCommonHelp GATopicPostSuccessRate:(statusCode == 200)
                                      topicID:topicID
                                  imageCounts:imageCounts
                                    errorCode:statusCode errorMessage:nil
                              requestCostTime:[[NSDate date] timeIntervalSinceDate:requestStartTime] eventType:2];
#if __has_include(<IMYEBPublic/IMYEBYoubiTaskManager.h>)
        [[IMYEBYoubiTaskManager shareManager] oprationTaskFinishedWithKey:kJindouReviewTaskKey uploadParams:nil];
#endif
        
    } error:^(NSError *error) {
        @strongify(self);
        [self hidenProgressHUD];
        [UIWindow imy_hideHUD];
        /// 先报埋点
        NSString *postFaildMsg = [[NSString alloc] initWithData:error.af_responseData encoding:NSUTF8StringEncoding];
        NSString *stringError = [NSString stringWithFormat:@"接口报错:%@",error.localizedDescription];
        stringError = imy_isNotEmptyString(postFaildMsg)?postFaildMsg:stringError;
        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_fssb",@"action":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topicUserID),@"publi_info":stringError} headers:nil completed:nil];
        NSMutableDictionary *errorData = [NSMutableDictionary dictionary];
        if (error.userInfo) {
            [errorData addEntriesFromDictionary:error.userInfo];
        }
        if (params) {
            errorData[@"requestParams"] = params;
        }
        if (error.af_responseData) {
            errorData[@"responseData"] = [error.af_responseData responseString];
        }
        
        [IMYErrorTraces postWithType:IMYErrorTraceTypeOthers pageName:[self ga_pageName] category:IMYErrorTraceCategoryCommunity message:@"帖子评论失败" detail:errorData];
        
        /**
         //775 如果进入安全实验, 服务器返回403请求失败后, 会用备用域名再次发起请求, 这样就导致服务器会返回error错误提示, 认为在10s重复发帖, 返回提示信息,  这时候我们不应该展示提示信息
         【【iOS】触发403状态码，吐司提示：request failed：forbidden（403），优化去掉】
         https://www.tapd.cn/67189002/bugtrace/bugs/view/1167189002001087095
         */
        [TTQCommonHelp GATopicPostSuccessRate:0
                                      topicID:[params[@"topic_id"] integerValue]
                                  imageCounts:imageCounts errorCode:error.code
                                 errorMessage:error.description
                              requestCostTime:[[NSDate date] timeIntervalSinceDate:requestStartTime]
                                    eventType:2];
        if (error.code % 1000 == 400) {
            if (![IMYNetState networkEnable]) {
                [UIView imy_showTextHUD:MT_Request_NoNetToast];
            } else {
                IMYWebMessageModel *failModel = [error.af_responseData toModel:[IMYWebMessageModel class]];
                if (failModel.message.length > 0) {
                    [UIView imy_showTextHUD:failModel.message];
                } else {
                    [UIView imy_showTextHUD:IMYString(@"网络缓慢，请稍后再试")];
                }
            }
            [self clearInputView];//清除输入框内容
        } else if (error.code == kPhoneDubiousErrorCode || error.code == kPhoneStolenErrorCode) {
            [self hideKeyboard];
            imy_asyncMainBlock(0.15, ^{
                NSInteger type = (error.code == kPhoneStolenErrorCode) ? 1 : 2;
                [[IMYURIManager shareURIManager] runActionWithPath:@"account/phone/verify" params:@{ @"type": @(type) } info:nil];
            });
        } else if (error.code % 1000 == 422) {
            if (![IMYNetState networkEnable]) {
                [UIView imy_showTextHUD:MT_Request_NoNetToast];
            } else {
                IMYWebMessageModel *failModel = [error.af_responseData toModel:[IMYWebMessageModel class]];
                if (failModel.message.length > 0) {
                    [UIView imy_showTextHUD:failModel.message];
                } else {
                    [UIView imy_showTextHUD:IMYString(@"网络缓慢，请稍后再试")];
                }
            }
            NSUInteger commentID = [params[@"referenced_id"] integerValue];
            if (commentID) {
                [TTQMessageDetailViewModel deleteMessageByReviewId:commentID topic_id:self.viewModel.topic_id postNotification:NO];
            }
            
        } else {
            if (error.code != ********) {
                [self hideKeyboard];
            }
            
            ///10000113 代表用户安全问题, 不需要额外弹窗 775
            NSData *responseData = error.af_responseData;
            NSDictionary *errorMap = [NSData imy_dictionaryWithJSONData:responseData];
            NSInteger errorCode = [[errorMap objectForKey:@"code"] integerValue];
            if (errorCode == 10000113) {
                return;
            }
            TTQTopicCurrentUserInfo *currentUserInfo = [errorMap[@"user_info"] toModel:[TTQTopicCurrentUserInfo class]];
            if ([currentUserInfo isKindOfClass:TTQTopicCurrentUserInfo.class] && currentUserInfo.feedback_id) {
                [self judgeBlockedWithUserInfo:currentUserInfo];
                return;
            }
            IMYWebMessageModel *messageModel = nil;
            if (error.code != -999) {
                messageModel = [error.af_responseData toModel:[IMYWebMessageModel class]];
            }
            if (![IMYNetState networkEnable]) {
                [UIView imy_showTextHUD:MT_Request_NoNetToast];
            } else {
                if (messageModel.message.length > 0) {
                    [UIView imy_showTextHUD:messageModel.message];
                } else {
                    [UIView imy_showTextHUD:IMYString(@"网络缓慢，请稍后再试")];
                }
            }
            // code = 16/11的情况在底层未做处理，业务端处理
            if (messageModel.code == 16 || messageModel.code == 11) {
                NSString *title = messageModel.code == 11? IMYString(@"违反平台规范被封号") : IMYString(@"违反平台规范被禁言");
                [UIAlertController imy_showAlertViewWithTitle:title message:IMYString(@"可以到”帮助与反馈“里申诉反馈") cancelButtonTitle:IMYString(@"取消") otherButtonTitles:@[IMYString(@"去反馈")] handler:^(UIAlertController *alertController, NSInteger buttonIndex) {
                    if (buttonIndex == 1) {
                        [[IMYURIManager shareURIManager] runActionWithString:@"meiyou:///qiyu/chat?params=eyJncm91cElkIjogNDgwODE1ODc4fQ=="];
                    }
                }];
                if (messageModel.code == 11) {
                    IMY_POST_NOTIFY(@"UserAcessBanned");
                }
            }
        }
    }];
}

- (void)postImagesFailed {
    [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_fssb",@"action":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topicUserID),@"publi_info":@"图片上传失败"} headers:nil completed:nil];
    [IMYErrorTraces postWithType:IMYErrorTraceTypeOthers pageName:NSStringFromClass([UIViewController imy_currentViewControlloer].class) category:IMYErrorTraceCategoryCommunity message:@"社区评论图片上传失败" detail:@{@"message":@"上传失败"}];
    
}

- (void)showReplySuccessHub {
    [UIWindow imy_showTextHUD:IMYString(@"评论成功")];
}

- (void)finishedReplyRequest:(NSString *)content timestamp:(NSInteger)timestamp {
}

- (void)appendImagesV2Params:(NSMutableDictionary *)params {
    if (params[@"images"]) {
        /// 有图片的才处理
        NSMutableDictionary *imageDic = [NSMutableDictionary dictionaryWithCapacity:1];
        if ([self isKindOfClass:TTQTopicDetailViewController.class]) {
            IMYUGCImageObject *image = [self.inputContentsView imageModel];
            IMYVKStickerItemModel *obj = image.userInfo;
            if ([image.userInfo isKindOfClass:NSClassFromString(@"IMYVKStickerItemModel")]) {
                /// 贴纸
                imageDic[@"url"] = obj.url;
                imageDic[@"referer"] = @1;
                imageDic[@"referer_id"] = @(obj.stickerId);
            } else {
                imageDic[@"url"] = [params[@"images"] firstObject];
                imageDic[@"referer"] = @0;
            }
            params[@"images_v2"] = @[imageDic];
        }
    }
}

#pragma mark IMYREasyInputViewDelegate

- (BOOL)inputViewShouldBeginEdit:(IMYREasyInputView *)inputView {
    // 判断在点击发送的地方处理 TTQInputTableViewController#inputViewSend
    [UITextView imy_addLineBreakActionMenu];
    return YES;
}

- (void)inputViewSendWithFromKeyboard:(BOOL)fromKeyboard {
    NSMutableDictionary *plParams = [@{@"event":@"dsq_nrxqy_djfs",@"action":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topicUserID)} mutableCopy];
    if (fromKeyboard) {
        plParams[@"public_info"] = @"输入法发送";
    }
    
    [IMYGAEventHelper postWithPath:@"event" params:plParams headers:nil completed:nil];
    if (![self loginActicon]) {
        self.shouldBecomeFirstResponderWhenAppear = YES;
        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_fssb",@"action":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topicUserID),@"publi_info":@"未登录"} headers:nil completed:nil];
        return;
    }
    [super inputViewSendWithFromKeyboard:fromKeyboard];
    
}

#pragma mark - Action


/**
 主楼的评论对象
 详情页->topicID
 回复详情页->commentID
 @return 主楼的评论ID
 */
- (TTQReplyObj *)mainReplyObj {
    return nil;
}

- (BOOL)theSameReplyAtIndexPath:(NSIndexPath *)indexPath {
    return (self.viewModel.selectedReplyIndex == indexPath) || (indexPath && [self.viewModel.selectedReplyIndex isEqual:indexPath]);
}

- (void)replyActionAtIndexPath:(NSIndexPath *)indexPath {
    if ([TTQABTestConfig disableInteractByType:TTQ_Interact_type_comment]) {
        return;//互动禁止793
    }
    if ([self hasBlocked]) {
        [self judgeBlocked];//判断禁言
        return;
    }
    if ([IMYRewardAlertView showInWindow]) {
        /// 有弹窗就不响应
        return;
    }
    self.inputContentsView.textView.internalTextView.backgroundColor = [UIColor clearColor];
    self.viewModel.selectedReplyIndex = indexPath;
    NSString *cachedContent = nil;
    BOOL shouldReply = YES;
    BOOL isforward = NO;
    if (indexPath == nil) {
        self.viewModel.inputDefaultText = nil;
        TTQReplyObj *replyObj = [self mainReplyObj];
        if (replyObj.isComment) {
            cachedContent = [[TTQCommentContentCacheManager sharedManager] contentWithCommentID:replyObj.itemID];
            isforward = [[TTQCommentContentCacheManager sharedManager] isForwardWithCommentID:replyObj.itemID];
        } else {
            cachedContent = [[TTQCommentContentCacheManager sharedManager] contentWithTopicID:replyObj.itemID];
            isforward = [[TTQCommentContentCacheManager sharedManager] isForwardWithTopicID:replyObj.itemID];
        }
    } else {
        TTQCommentModel *comment = [self tableCellModelAtIndexPath:self.viewModel.selectedReplyIndex];
        if (comment) {
            self.viewModel.inputDefaultText = [NSString stringWithFormat:@"@%@：", comment.publisher.screen_name];
            cachedContent = [[TTQCommentContentCacheManager sharedManager] contentWithCommentID:comment.commentID];
            isforward = [[TTQCommentContentCacheManager sharedManager] isForwardWithCommentID:comment.commentID];
        } else {
            shouldReply = NO;
        }
    }
    if (shouldReply) {
        if (cachedContent.length) {
            NSAttributedString *attributeStrToShow = [IMYREmoticonManager decodeEmojiText:cachedContent attributes:self.inputContentsView.textView.internalTextView.typingAttributes];
            self.inputContentsView.textView.selectedRange = NSMakeRange(attributeStrToShow.length,0);
            self.inputContentsView.textView.internalTextView.attributedText = attributeStrToShow;
            self.inputContentsView.postArticleButton.selected = isforward;
        } else {
            self.inputContentsView.textView.internalTextView.attributedText  = nil;
        }
        if (![self.inputContentsView.textView isFirstResponder]) {
            [self.inputContentsView.textView becomeFirstResponder];
        }
        [self.inputContentsView.textView refreshHeight];
    }
}
- (void)finishedRequest:(NSError *)error {
    [super finishedRequest:error];
    @weakify(self);
    if (error.code % 1000 == 404 || error.code % 1000 == 410) {
        if (error.code % 1000 == 404) {
            [self.captionView setTitle:IMYString(@"该话题已被删除") andState:IMYCaptionViewStateNoResult];
            [UIWindow imy_showTextHUD:IMYString(@"该话题已被删除")];
        } else {
            [UIWindow imy_showHUDwithNetworkError:error andResponseObject:error.af_responseData];
        }
        self.isTopicDeletedError = YES;
        [TTQMessageDetailViewModel deleteMessageByTopicId:self.viewModel.topic_id];
        
        self.viewModel.dataSource = nil;
        [[GCDQueue mainQueue] queueBlock:^{
            @strongify(self);
            [self imy_pop:YES];
        }
                              afterDelay:0.5];
        
    } else if (error.code % 1000 == 430) {
        [TTQMessageDetailViewModel deleteMessageByTopicId:self.viewModel.topic_id];
        [UIWindow imy_showHUDwithNetworkError:error andResponseObject:error.af_responseData];
        [[GCDQueue mainQueue] queueBlock:^{
            @strongify(self);
            [self imy_pop:YES];
            [TTQCommonHelp sharedCommonHelp].needRefreshGroups = true;
        }
                              afterDelay:0.5];
    }
}



- (void)clearInputView {
    if (self.inputContentsView.textView.internalTextView.inputView != nil) {
        self.inputContentsView.textView.internalTextView.inputView = nil;
        [self.inputContentsView.textView.internalTextView reloadInputViews];
        [self.inputContentsView restPoseForInputText];
    }
}

// MARK: - Override
/**
 是否加圈判断，子类实现
 */
- (BOOL)canReply {
    return self.viewModel.canReply;
}

- (BOOL)canAccess {
    /// 8.0.6统一改为发送评论后再判断是否封号、违规
    return YES;
}

- (BOOL)hasBlocked {
    return self.viewModel.hasBlocked;
}

- (void)judgeBlockedWithUserInfo:(TTQTopicCurrentUserInfo*)userInfo {
    //禁言处理
    [self.view endEditing:YES];
    NSString *message = userInfo.error == 3 ? @"违反圈规被禁言" : (userInfo.error == 2 ? @"违反圈规被封号" : @"违反圈规被禁言");
    [UIAlertController imy_showAlertViewWithTitle:message
                                          message:@"可以到\"帮助与反馈\"里申诉反馈"
                                cancelButtonTitle:IMYString(@"取消")
                                otherButtonTitles:@[IMYString(@"去反馈")]
                                          handler:^(UIAlertController *alertController, NSInteger buttonIndex) {
        if (buttonIndex == 1) {
            [[IMYURIManager shareURIManager] runActionWithString:@"meiyou:///qiyu/chat?params=eyJncm91cElkIjogNDgwODE1ODc4fQ=="];
        }
    }];
}

- (void)judgeBlocked {
    //禁言处理
    [self judgeBlockedWithUserInfo:self.viewModel.currentUserInfo];
}
/**
 加入圈子，子类实现
 */
- (void)joinForum {
    [self.viewModel joinForum];
}

/**
 子类实现，强制滚动到顶部
 */
- (void)forceScrollToTop {
    
}


- (void)postHandleInputViewResign {
    // 键盘隐藏，处理保存评论数据的逻辑
    if (self.viewModel.selectedReplyIndex) {
        TTQCommentModel *comment = [self tableCellModelAtIndexPath:self.viewModel.selectedReplyIndex];
        if ([comment isKindOfClass:TTQCommentModel.class]) {
            [[TTQCommentContentCacheManager sharedManager] setContent:[self inputContentFromTextView] withCommentID:comment.commentID];
            [[TTQCommentContentCacheManager sharedManager] setIsForward:self.inputContentsView.postArticleButton.selected withCommentID:comment.commentID];
        }
    } else {
        TTQReplyObj *replyObj = [self mainReplyObj];
        if (replyObj.isComment) {
            [[TTQCommentContentCacheManager sharedManager] setContent:[self inputContentFromTextView] withCommentID:replyObj.itemID];
            [[TTQCommentContentCacheManager sharedManager] setIsForward:self.inputContentsView.postArticleButton.selected withCommentID:replyObj.itemID];
        } else {
            [[TTQCommentContentCacheManager sharedManager] setContent:[self inputContentFromTextView] withTopicID:replyObj.itemID];
            [[TTQCommentContentCacheManager sharedManager] setIsForward:self.inputContentsView.postArticleButton.selected withTopicID:replyObj.itemID];
        }
    }
}

- (NSString *)inputContentFromTextView {
    NSAttributedString *attributedText = self.inputContentsView.textView.internalTextView.attributedText;
    if (attributedText) {
        return [IMYREmoticonManager encodeEmojiText:attributedText];
    }
    return nil;
}

/**
 图片显示处理，子类重写
 1、处理视频自动播放逻辑
 */
- (void)postHandlePhotoBroswerShow {}

/**
 图片隐藏处理，子类重写
 1、处理视频自动播放逻辑
 */
- (void)postHandlePhotoBroswerHide {}

/**
 评论埋点，子类实现
 */
- (void)postCommentBiFeedsView:(NSInteger)action commentId:(NSInteger)commentId floor:(NSInteger)floor clickpos:(NSInteger)clickpos {}

- (void)setCommentForceTop:(BOOL)top comment:(NSInteger)commentId completeBlock:(void(^)(BOOL success))completeBlock {
    
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"review_id"] = @(commentId);
    params[@"role_type"] = @1;
    params[@"forum_id"] = @(self.viewModel.forum_id);
    //   7=作者置顶评论 8=作者取消置顶
    params[@"item_type"] = top?@7:@8;
    params[@"topic_id"] = @(self.viewModel.topic_id);
    
    @weakify(self);
    [[[IMYServerRequest getPath:@"v2/user_operate" host:circle_seeyouyima_com params:params headers:nil] deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        [[NSNotificationCenter defaultCenter]postNotificationName:@"TTQDetailControllerRefresh" object:nil];
        [UIView imy_showTextHUD:top?IMYString(@"置顶成功"):IMYString(@"取消置顶成功")];
        if (completeBlock) {
            completeBlock(YES);
        }
    } error:^(NSError * _Nullable error) {
        if (completeBlock) {
            completeBlock(NO);
        }
        IMYWebMessageModel *failModel =
        [error.af_responseData toModel:[IMYWebMessageModel class]];
        NSString *toast = failModel.message;
        if (imy_isEmptyString(toast)) {
            toast = top?IMYString(@"置顶失败，请重试"):IMYString(@"取消置顶失败，请重试");
        }
        [UIView imy_showTextHUD:toast];
    }];
}

@end
