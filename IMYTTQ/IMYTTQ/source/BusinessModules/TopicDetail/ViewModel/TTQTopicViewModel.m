//
// Created by <PERSON> on 15/5/7.
//
//

#import "TTQTopicViewModel.h"
#import "GCDObjC.h"
#import "IMYAdvertisementManager.h"
#import "IMY_ViewKit.h"
#import "NSString+TTQ.h"
#import "UIFont+TTQ.h"
#import "TTQForumModel.h"
#import "TTQReferenceCommentModel.h"
#import "TTQCommentModel.h"
#import "TCPSendMessage.h"
#import "TTQTopicCache.h"
#import "TTQTopicPlaceCache.h"
#import "TTQTopicReferenceViewModel.h"
#import "TTQVideoModel.h"
#import <IMYVideoPlayer.h>
#import "TTQDoorConfig.h"
#import "TTQABTestConfig.h"
#import "TTQMacro.h"
#import "TTQTopicContentYYCacheManager.h"

#import "TTQTopicModel+VideoWaterFollow.h"
#import "TTQHome5TopicModel.h"
#import "TTQEmptyCommentModel.h"
#import "TTQTopicModel+TTQTopicDetailListType.h"
#import "TTQDetailListHeaderModel.h"
#import "TTQHttpHelper.h"
#import "TCPClientManager.h"
#import "TTQBusinessRequestHelper.h"
#import "TTQBaseViewModelProtocol.h"
#import "IMYUGCPublishManager.h"
#import "IMYUGC/IMYUGCEventHelper.h"
#import <IMYUGC/IMYUGC.h>
#import <IMYBaseKit/IMYBaseKit.h>
#import "TTQNewbeeTaskManager.h"

@interface TTQTopicViewModel ()<TTQBaseViewModelProtocol>

@property (nonatomic, strong) IMYAdvertisementManager *advertisementManager;
@property (nonatomic, assign) NSInteger reveal_num;
@property (nonatomic, assign) NSInteger requestSizeCount;// 每次加载评论的条数：分为普通加载更多：20条；折叠加载更多从开关读取

/// 缓存数据
@property (nonatomic, strong) TTQTopicCache *topicCache;
@property (nonatomic, assign) BOOL isInOrderLoading;// 处在切换排序中
@property (nonatomic, copy) NSArray *originDataSource;// 保存loading前的数据，请求失败需要重新显示出来
@property (nonatomic, assign) BOOL notDeliverReviewError; // 主楼内容返回正常，评论接口异常，不向上传递异常
@property (nonatomic, assign) BOOL isFirstRequestReviewData;
@property (nonatomic, assign) BOOL isReviewError;// 评论接口http返回失败
@property (nonatomic, assign) BOOL isReviewPhpError;// 评论接口服务端返回失败
@property (nonatomic, strong) NSError *reviewError;// 评论接口返回的失败

@property (nonatomic, copy) NSDictionary  *latestReviewsCacacheDic;
@property (nonatomic, copy) NSDictionary  *hotReviewsCacacheDic;

// 评论折叠 && 推荐相关
@property (nonatomic, assign) BOOL isHitFoldCommentTest;    /// 是否命中折叠
@property (nonatomic, assign) BOOL isCommentFolding;// 评论当前是否折叠，未返回推荐时，即时命中折叠也不会是折叠样式
@property (nonatomic, assign) NSInteger recommendCount;// 推荐数据请求的条数
@property (nonatomic, assign) NSInteger commentFoldMax;// 最初评论折叠的阈值
@property (nonatomic, assign) BOOL commentRefreshNoMore;

@end

@implementation TTQTopicViewModel
@synthesize forum_id = _forum_id;
@synthesize replyCommand = _replyCommand;
@synthesize favoriteCommand = _favoriteCommand;

- (instancetype)initWithTopicID:(NSInteger)topic_id {
    if (self = [self init]) {
        self.gotoID = -1;
        self.topic_id = topic_id;
        self.showTableHeader = YES;
        self.reveal_num = 1;//[TTQDoorConfig revealNumConfig];
//        self.recommendVM = [[IMYTTQDetailRecommendViewModel alloc] init];
        self.isFirstRequestReviewData = YES;
        self.isInOrderLoading = NO;
        self.requestSizeCount = 20;
        self.commentRefreshNoMore = YES;
        self.isHitFoldCommentTest = [TTQABTestConfig commentFoldExp];
        [self initRecommendABTest];
    }
    return self;
}

- (BOOL)contentReady {
    return self.topic != nil;
}

- (void)setIsUGCUIStlye:(BOOL)isUGCUIStlye {
    _isUGCUIStlye = isUGCUIStlye;
    self.isRecommendBack = YES;
}

- (void)setGotoID:(NSInteger)gotoID {
    [super setGotoID:gotoID];
    if (gotoID > 0) {
        self.isRecommendBack = YES;
    }
}

- (void)initRecommendABTest {
    /// 8.84版本不再请求推荐了
    self.isRecommendBack = YES;
    self.requestSizeCount = 10;
}

//排序的
- (NSString *)paramsOrderByWith:(TTQOrderByFilter)orderByFilter {
    // 第一次请求返回服务端配置的默认排序条件或者空值
    if (orderByFilter == TTQOrderByFilterHot) {
        return @"reviewed_hot";
    } else if (orderByFilter == TTQOrderByFilterLastest) {
        return @"reviewed_date";
    } else if (orderByFilter == TTQOrderByFilterNone) {
        return @"reviewed_default";
    }
    return @"reviewed_hot";
}

- (TTQOrderByFilter)orderByFilterWithOrderByString:(NSString *)orderByString {
    NSDictionary *orderByStringToOrderByFilterMap
    = @{
        @"reviewed_hot": @(TTQOrderByFilterHot),
        @"reviewed_date": @(TTQOrderByFilterLastest),
        @"reviewed_default": @(TTQOrderByFilterNone),
        };
    NSNumber *orderByFilterValue = orderByStringToOrderByFilterMap[orderByString];
    if (orderByFilterValue) {
        return [orderByFilterValue integerValue];
    } else {
        // 默认排序
        return TTQOrderByFilterNone;
    }
}

- (void)reportAction:(TTQCommentModel *)comment {
    NSArray *title = @[IMYString(@"广告信息"), IMYString(@"人身攻击"), IMYString(@"色情话题"),IMYString(@"内容侵权"),IMYString(@"涉企侵权"), IMYString(@"其他理由")];
    @weakify(self);
    [IMYActionSheet sheetWithCancelTitle:IMYString(@"取消")
                             otherTitles:title
                                 summary:comment == nil ? IMYString(@"举报楼层，请长按相关楼层") : nil
                              showInView:nil
                                  action:^(NSInteger index) {
                                      @strongify(self);
                                      if (index != 0) {
                                          NSMutableDictionary *params = [[NSMutableDictionary alloc] init];
                                          if (comment) {
                                              params[@"post_id"] = @(comment.commentID);
                                              params[@"type"] = @(2);
                                          } else {
                                              params[@"type"] = @(1);
                                              params[@"post_id"] = @(self.topic_id);
                                          }
                                          //'1,广告信息；2,情色话题；3,人身攻击；4,其他理由', 5、内容侵权
                                          if (index == 1) {
                                              params[@"reasonid"] = @(1);
                                          } else if(index == 2) {
                                              params[@"reasonid"] = @(3);
                                          } else if (index == 3) {
                                              params[@"reasonid"] = @(2);
                                          } else if (index == 4) {
                                              params[@"reasonid"] = @(5);
                                          } else if (index == 5) {
                                              params[@"reasonid"] = @(4);
                                          }
                                          params[@"topic_id"] = @(self.topic_id);
                                          [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_gdan",@"public_type":@"举报",@"info_type":@12,@"info_id":@(self.topic_id),@"fuid":@(self.topic.publisher.userID),@"action":@2} headers:nil completed:nil];
                                          [[[self.reportCommand execute:params] deliverOnMainThread] subscribeNext:^(id x) {
                                              [UIWindow imy_showTextHUD:IMYString(@"谢谢，美柚会尽快受理您的举报")];
                                          }
                                              error:^(NSError *error) {
                                              if (![IMYNetState networkEnable]) {
                                                 [UIWindow imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
                                              } else{
                                                 [UIWindow imy_showTextHUD:IMYString(@"举报失败")];
                                              }
//                                                  [UIWindow imy_showHUDwithNetworkError:error andResponseObject:error.af_responseData];

                                              }];
                                      }

                                  }];
}

// MARK: TTQBaseViewModelProtocol

/// 是否使用”topic_categoty“字段生成标签
- (BOOL)useCategoryGenerateTags {
    return YES;
}


// MARK: - ===== Request =====

- (RACCommand *)replyCommand {
    if (_replyCommand == nil) {
        @weakify(self);
        _replyCommand = [RACCommand commandWithSignalBlock:^RACSignal *(NSArray *array) {
            
            NSMutableDictionary *inputParams = array.firstObject;
            ProgressCallback progressBlock = nil;
            if (array.count > 1) {
                progressBlock = array[1];
            }
            
            // 参数
            NSMutableDictionary *params = [NSMutableDictionary dictionary];
            if (inputParams && [inputParams isKindOfClass:NSDictionary.class]) {
                [params addEntriesFromDictionary:inputParams];
            }
            if (self.selectedReplyIndex) {
                // 二级评论参数
                [params addEntriesFromDictionary:[self subCommentReplyParams]];
            } else {
                // 话题详情评论参数
                [params addEntriesFromDictionary:[self topicReplyParams]];
            }
            
            NSString *content = params[@"content"];
            if ([content isKindOfClass:NSString.class] && content.length) {
                params[@"content_size"] = @(content.ttq_textLength);
            }
            NSString *biURI = @"";
            if (self.redirect_url.length && [self.redirect_url containsString:@"params="]) {
                NSArray *urlArray = [self.redirect_url componentsSeparatedByString:@"params="];
                if (urlArray.count > 1) {
                    biURI = urlArray[1];
                }
            }
            params[@"bi_uri"] = biURI;
            params[@"last"] = @(0);
            params[@"diff_data"] = @(YES);
            
            if (self.taskID > 0) {
                params[@"task_id"] = @(self.taskID);
                params[@"task_type"] = @(self.taskType);
            }
            
            // 数据填充到inputParams，基类处理结果使用
            if ([inputParams isKindOfClass:NSMutableDictionary.class]) {
                [inputParams addEntriesFromDictionary:params.mutableCopy];
            }

            NSString *path = @"v2/topic_review";// 发送评论
            return [[[TTQHttpHelper postPath:path params:params progressBlock:progressBlock] deliverOnMainThread] doNext:^(id<IMYHTTPResponse> response) {
                @strongify(self);
                if (self.selectedReplyIndex) {
                    // 二级评论结果处理
                    if (self.from_community_home) {
                        [TTQCommonHelp GAEventForEventWithName:@"ttq_tzxqy_hfpl" action:2];
                    }
                    [self handleSubCommentReplyWithResponse:response content:content];
                } else {
                    // 话题详情评论结果处理
                    if (self.from_community_home) {
                        [TTQCommonHelp GAEventForEventWithName:@"ttq_tzxqy_hftz" action:2];
                    }
                    [self handleTopicReplyWithResponse:response content:content];
                }
                // 埋点：圈子详情-回复
                [IMYEventHelper event:@"qzxq-hf"];
            }];
        }];
    }
    return _replyCommand;
}

-(BOOL)stopDeliverReviewError{
    return self.notDeliverReviewError;
}

- (void)handleSubCommentReplyWithResponse:(id<IMYHTTPResponse>)response content:(NSString *)content {
    NSArray *reviews = response.responseObject[@"reviews"];
    if (!reviews) {
        reviews = response.responseObject[@"data"][@"reviews"];
    }
    NSArray *comments = [reviews toModels:[TTQCommentModel class]];
    /*
     iOS的 CFArrayGetCount(runs); 方法, 如果格式一致,获取到的runs就只有一行,会导致CGFloat xOffset = CTLineGetOffsetForStringIndex(line, CTRunGetStringRange(run).location, nil);计算不准, 进而导致如果两条热议话题连在一起的情况下, m
     80Attributedlabel的highlightRect计算不准, 点击事件只会响应第一个CTRun对象的点击事件  如果用空格隔开则不会有这种情况
     相关bug:
     【【iOS】主评论和子评论点击连续连着的两个热议话题的第二个，总会跳到第一个热议话题有误（历史问题）】
     https://www.tapd.cn/22362561/bugtrace/bugs/view/1122362561001096560
     */
    for (TTQCommentModel *comment in comments) {
        comment.isUserAdded = YES;
        if ([self voteIndex]) {
            comment.voteType = 1;
            comment.votedIndexData = @[@([self voteIndex])];
        }
    }
    if (comments) {
        // 处理二级评论 这里子评论数据要挂再主评论下面
        NSIndexPath *mainCommentIndexPath = [NSIndexPath indexPathForRow:0 inSection:self.selectedReplyIndex.section];
        TTQCommentModel *model = [self tableCellModelAtIndexPath:mainCommentIndexPath];
        if ([model isKindOfClass:TTQCommentModel.class]) {
            model.referenced_num += 1;
            if (model.referenceds == nil) {
                model.referenceds = (NSArray<TTQCommentModel> *)comments;
            } else {
                // 倒序插入子评论
                NSMutableArray *array = [NSMutableArray array];
                [array addObjectsFromArray:comments];
                [array addObjectsFromArray:model.referenceds];
                // 截取子评论
//                NSInteger reveal_num = [TTQDoorConfig revealNumConfig];
//                if (reveal_num > 0 && array.count > reveal_num) {
//                    array = [array subarrayWithRange:(NSRange){0, reveal_num}].mutableCopy;
//                }
                model.referenceds = (NSArray<TTQCommentModel> *)array;
            }
            self.topic.total_review += 1;
        }
        NSString *pageInfo = self.bi_pageSource?self.bi_pageSource:@"内容详情页";
        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_pl",@"action":@2,@"public_type":@2,@"info_type":@12,@"info_id":@(self.topic_id),@"fuid":@(self.topicUserID),@"comment_uid":@(model.publisher.userID),@"comment_id":@(model.commentID),@"interact_num":@(self.topic.total_review - 1),@"public_info":pageInfo,@"is_on_period":@([IMYUGCEventHelper userIsInPhysiological]),@"redirect_url":[IMYUGCEventHelper currentPageRedirectUrl]} headers:nil completed:nil];
    }
}

- (void)handleTopicReplyWithResponse:(id<IMYHTTPResponse>)response content:(NSString *)content {
    NSDictionary *dataDict = nil;
    if (response.responseObject[@"data"]) {
        dataDict = response.responseObject[@"data"];
    } else {
        dataDict = response.responseObject;
    }
    self.topic.total_review = [dataDict[@"total_review"] integerValue];
    self.topic.total_floor = [dataDict[@"total_floor"] integerValue];
    NSArray *comments = [self parseCommentsFromResponseObject:response.responseObject];
    TTQCommentModel *commentData = comments.firstObject;
    if ([self voteIndex]) {
        commentData.voteType = 1;
        commentData.votedIndexData = @[@([self voteIndex])];
    }
    if ([self isCurFilterDataSourceNil]) {
        self.dataSource = comments;
        self.automaticallyRefresh = NO;
        // 强制处理附加的数据：空数据和相关推荐
        [self forceHandleAttachDatas];
        [self addCommentFooterWhenNoMoreComment];
    } else if (comments.count) {
        NSMutableArray *array = [NSMutableArray new];
//        if (self.orderByFilter == TTQOrderByFilterLastest) {
            // 最新排序添加在第一个位置
            id firstData = self.dataSource.firstObject;
            [array addObjectsFromArray:[self filteredCommentsFromComments:[[comments reverseObjectEnumerator] allObjects]]];
            [array addObjectsFromArray:self.dataSource];
            if (firstData) {
                [array removeObject:firstData];
                [array insertObject:firstData atIndex:0];
            }
        self.dataSource = array;
    }
    NSString *pageSource= self.bi_pageSource?self.bi_pageSource:@"内容详情页";
    
    NSMutableDictionary *params = @{@"event":@"dsq_nrxqy_pl",
                                    @"action":@2,
                                    @"public_type":@1,
                                    @"info_type":@12,
                                    @"info_id":@(self.topic_id),
                                    @"fuid":@(self.topicUserID),
                                    @"interact_num":@(self.topic.total_review -1),
                                    @"public_info":pageSource,
                                    @"is_on_period":@([IMYUGCEventHelper userIsInPhysiological]),
                                    @"redirect_url":[IMYUGCEventHelper currentPageRedirectUrl]}.mutableCopy;
    params[@"public_key"] = self.bi_isRelyFromCommentIcon ? @2 : @1;
    
    [IMYGAEventHelper postWithPath:@"event" params:params.copy headers:nil completed:nil];
}

- (NSDictionary *)subCommentReplyParams {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    TTQCommentModel *model = [self tableCellModelAtIndexPath:self.selectedReplyIndex];
    if ([model isKindOfClass:TTQCommentModel.class]) {
        params[@"referenced_id"] = @(model.commentID);
        params[@"parent_referenced_id"] = @(model.commentID);
//        TTQCommentModel *comment = model.referenceds.lastObject;
//        if (comment) {
//            params[@"last"] = @(comment.commentID);
//        } else {
//            params[@"last"] = @(1);
//        }
//        params[@"diff_data"] = @(true);
        params[@"topic_id"] = @(self.topic_id);
    }
    return params;
}

- (NSDictionary *)topicReplyParams {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"referenced_id"] = @(self.referenced_id);
    TTQCommentModel *comment = nil;
    // 最新排序的last参数为第一条评论的id，其它情况为最后一条评论的id
    if (self.orderByFilter == TTQOrderByFilterLastest) {
        comment = [self firstComment];
    } else {
        comment = [self lastComment];
    }
    if ([comment isKindOfClass:[TTQCommentModel class]]) {
        params[@"last"] = [comment valueForKey:@"commentID"];
        if (![comment valueForKey:@"appoint"]) {
            params[@"last_floor"] = [comment valueForKey:@"floor_no"];
        }
    } else {
        params[@"last"] = @(1);
    }
    // 最热排序和默认不传递last参数，只返回那一条
    if (self.orderByFilter == TTQOrderByFilterHot || self.orderByFilter == TTQOrderByFilterNone) {
        [params removeObjectForKey:@"last"];
    }
    
    // 评论插入的规则，全部-最新->第一位；全部-最热/默认->最后面
    params[@"diff_data"] = @(self.commentFilter == TTQTopicFilterNone ? YES : NO);
    params[@"topic_id"] = @(self.topic_id);
    // 楼主上传图片带水印和设置【通用-发布时图片带水印】
    if (self.topic.user_id == [IMYPublicAppHelper shareAppHelper].userid.integerValue
        && [IMYPublicAppHelper shareAppHelper].hasWatermark) {
        params[@"is_watermark"] = @(YES);
    }
    return params;
}

- (RACCommand *)favoriteCommand {
    if (_favoriteCommand == nil) {
        @weakify(self);
        _favoriteCommand = [RACCommand commandWithSignalBlock:^RACSignal *(NSDictionary *params) {
            @strongify(self);
            NSString *path = [NSString stringWithFormat:@"users/me/favorite-topics/%ld", (long)self.topic_id];
            if (self.topic.is_favorite) {// 取消收藏
                return [[[[TTQHttpHelper deletePath:path params:params] deliverOnMainThread] doNext:^(id<IMYHTTPResponse> response) {
                    @strongify(self);
                    self.topic.favorite_num --;
                    self.topic.is_favorite = NO;
                    [UIWindow imy_showTextHUD:IMYString(@"已取消收藏")];
                }] doError:^(NSError *error) {
                    @strongify(self);
                    [UIWindow imy_showTextHUD:IMYString(@"取消收藏失败，请重试")];
                    [IMYErrorTraces postWithType:IMYErrorTraceTypeOthers pageName:NSStringFromClass([UIViewController imy_currentViewControlloer].class) category:IMYErrorTraceCategoryCommunity message:@"帖子取消收藏失败" detail:[IMYUGCRequest errorData:error requestParams:@{@"id":@(self.topic_id)}]];
                }];
            } else {// 收藏
                NSString *biURI = @"";
                NSMutableDictionary *mutaDic = [NSMutableDictionary dictionaryWithDictionary:params];
                if (self.redirect_url.length && [self.redirect_url containsString:@"params="]) {
                    NSArray *urlArray = [self.redirect_url componentsSeparatedByString:@"params="];
                    if (urlArray.count > 1) {
                        biURI = urlArray[1];
                    }
                }
                if (biURI.length) {
                    mutaDic[@"bi_uri"] = biURI;
                }
                return [[[[TTQHttpHelper putPath:path params:mutaDic] deliverOnMainThread] doNext:^(id<IMYHTTPResponse> response) {
                    @strongify(self);
                    self.topic.favorite_num ++;
                    self.topic.is_favorite = YES;
                    if ([UIView showCollectSuccessHUD]) {
                        [UIView imy_showTextHUD:IMYString(@"已收藏")];
                    }
                }] doError:^(NSError *error) {
                    @strongify(self);
                    [UIWindow imy_showTextHUD:IMYString(@"收藏失败")];
                    [IMYErrorTraces postWithType:IMYErrorTraceTypeOthers pageName:NSStringFromClass([UIViewController imy_currentViewControlloer].class) category:IMYErrorTraceCategoryCommunity message:@"帖子收藏失败" detail:[IMYUGCRequest errorData:error requestParams:@{@"id":@(self.topic_id)}]];
                }];
            }
        }];
    }
    return _favoriteCommand;
}

/**
 点赞
 @param param 参数
 @param completion YES or NO
 */
- (void)doPriaseWithParam:(NSDictionary *)param completion:(void(^)(id  resData, NSError * error))completion {
    [TTQBusinessRequestHelper requestPriaseWithParam:param useABTest:NO completion:completion];
}

/**
 验证帖子是否可编辑
 @param param 参数
 @param completion YES or NO
 */
- (void)checkEditableWithParam:(NSDictionary *)param completion:(void(^)(id isModified, id resData, NSError * error))completion{
    if (!param) {
        !completion ?: completion(@NO, nil, nil);
        return;
    }
//    IMYUGCPublishTask *task = [[IMYUGCPublishManager shareInstance] imy_getTask:[IMYUGCPublishTask taskIdWithDraft:1000 contentId:self.topic_id]];
//    if (task && [task isWatingInQueue]) {
//        [UIWindow imy_showTextHUD:@"内容正在发布中，请稍后再试"];
//        !completion ?: completion(nil, nil, nil);
//        return;
//    }
    
    NSString *checkModify = @"v2/check_topic_modified";
    [UIWindow imy_showLoadingHUD];
    [[[IMYServerRequest getPath:checkModify host:circle_seeyouyima_com params:param headers:nil] deliverOnMainThread] subscribeNext:^(id<IMYHTTPResponse> x) {
        [UIWindow imy_hideHUD];
        NSInteger code = [[[x responseObject] objectForKey:@"code"] integerValue];
        NSNumber *is_modified = x.responseObject[@"is_modified"];
        NSDictionary *hint = x.responseObject[@"hint"];
        if (completion) {
            imy_asyncMainBlock(^{
                if (code == 0) {
                    completion(is_modified, hint, nil);
                } else {
                    NSString *message = x.responseObject[@"message"];
                    if (imy_isNotEmptyString(message)) {
                        [UIWindow imy_showTextHUD:message];
                    }
                    completion(nil, nil, nil);
                }
            });
        }
    } error:^(NSError *error) {
        [UIWindow imy_hideHUD];
        if (completion) {
            imy_asyncMainBlock(^{
                if (![IMYNetState networkEnable]) {
                   [UIWindow imy_showTextHUD:IMYString(kStatusText_networkDisconnectCache)];
                }
//                else{
//                   [UIWindow imy_showTextHUD:IMYString(@"网络缓慢，请稍后再试")];
//                }
                [UIWindow imy_showHUDwithNetworkError:error andResponseObject:error.af_responseData];
                completion(nil, nil, error);
            });
        }
    }];
}

- (void)checkNotesEditableWithParam:(NSDictionary *)param completion:(void(^)(id isModified, id resData, NSError * error))completion{
    if (!param) {
        !completion ?: completion(@NO, nil, nil);
        return;
    }
    NSUInteger topic_id = [param[@"item_id"] unsignedIntegerValue];
    
    IMYUGCPublishTask *task = [[IMYUGCPublishManager shareInstance] imy_getTask:[IMYUGCPublishTask taskIdWithDraft:1000 contentId:topic_id]];
    if (task && [task isWatingInQueue]) {
        [UIWindow imy_showTextHUD:@"内容正在发布中，请稍后再试"];
        !completion ?: completion(nil, nil, nil);
        return;
    }

    NSString *checkModify = @"v5/check_article_modified";
    [UIWindow imy_showLoadingHUD];
    [[[IMYServerRequest getPath:checkModify host:circle_seeyouyima_com params:param headers:nil] deliverOnMainThread] subscribeNext:^(id<IMYHTTPResponse> x) {
        [UIWindow imy_hideHUD];
        NSInteger code = [[[x responseObject] objectForKey:@"code"] integerValue];
        NSNumber *is_modified = x.responseObject[@"is_modified"];
        NSDictionary *hint = x.responseObject;
        if (completion) {
            imy_asyncMainBlock(^{
                if (code == 0) {
                    completion(is_modified, hint, nil);
                } else {
                    NSString *message = x.responseObject[@"message"];
                    if (imy_isNotEmptyString(message)) {
                        [UIWindow imy_showTextHUD:message];
                    }
                    completion(nil, nil, nil);
                }
            });
        }
    } error:^(NSError *error) {
        [UIWindow imy_hideHUD];
        if (completion) {
            imy_asyncMainBlock(^{
                if (![IMYNetState networkEnable]) {
                   [UIWindow imy_showTextHUD:IMYString(kStatusText_networkDisconnectCache)];
                }
//                else{
//                   [UIWindow imy_showTextHUD:IMYString(@"网络缓慢，请稍后再试")];
//                }
                [UIWindow imy_showHUDwithNetworkError:error andResponseObject:error.af_responseData];
                completion(nil, nil, error);
            });
        }
    }];
}

///type: 0 下拉刷新，1 加载下一页，2 加载上一页,3 跳楼,4 只请求TTQTopicModel，投票的话，要实时的去请求，不然倒计时的时间会错掉。
- (RACSignal *)requestRemoteDataForType:(NSInteger)type params:(NSDictionary *)parameters_ {
    @weakify(self);
    if (type != TTQTopicDetailRequestTypeRefresh) {
        self.offset_y = 0;
    }
    NSMutableDictionary *parameters = [NSMutableDictionary dictionaryWithDictionary:parameters_];
    if (self.commentFilter == TTQTopicFilterImage) {
        parameters[@"filter_by"] = @"image";
    } else if (self.commentFilter == TTQTopicFilterOwner) {
        parameters[@"filter_by"] = @"owner";
    }
    parameters[@"order_by"] = [self paramsOrderByWith:self.orderByFilter];
    parameters[@"reveal_num"] = @(self.reveal_num);//父评论携带子评论的数量
    parameters[@"mode"] = @([IMYPublicAppHelper shareAppHelper].userMode);
    parameters[@"topic_id"] = @(self.topic_id);

    self.isReviewError = NO;
    self.isReviewPhpError = NO;
    self.notDeliverReviewError = NO;
    self.reviewError = nil;
    
    BOOL onlyRequestReview = self.changeOrderRequest;
    BOOL changeOrderRequest = self.changeOrderRequest;
    BOOL requestRecommend = NO;// 本次请求，是否请求了推荐数据
    
    if (type == TTQTopicDetailRequestTypeRefresh) {
        self.isLoadingMoreComments = NO;
        self.gotoID = -1;
        self.animationCommentIdWhenAppear = -1;
        if (self.isFromRecommend) {
            self.isRecommendBack = YES;
        }
        if (self.top_review_id && self.orderByFilter != TTQOrderByFilterLastest) {
            parameters[@"goto"] = @(self.top_review_id);
            if (self.sub_comment_id) {
                parameters[@"sub_comment_id"] = @(self.sub_comment_id);
            }
        }
    } else if (type == TTQTopicDetailRequestTypeNextPage) {
        TTQCommentModel *model = [self lastComment];
        if ([model isKindOfClass:[TTQCommentModel class]]) {
            parameters[@"last"] = @(model.commentID);
        }
        self.isLoadingMoreComments = YES;
        onlyRequestReview = YES;
    } else if (type == TTQTopicDetailRequestTypePrePage) {
        if (self.dataSource.count > 1) {
            TTQCommentModel *model = [self firstComment];
            if ([model isKindOfClass:[TTQCommentModel class]]) {
                parameters[@"last"] = @(model.commentID);
            }
        }
        parameters[@"load_direction"] = (self.orderByFilter == TTQOrderByFilterLastest) ? @"next" : @"prev";
        onlyRequestReview = YES;
    } else if (type == TTQTopicDetailRequestTypeGoto) {
        if (self.gotoID > 0) {
            parameters[@"goto"] = @(self.gotoID);
            if (self.sub_comment_id) {
                parameters[@"sub_comment_id"] = @(self.sub_comment_id);
            }
        }
        self.isRecommendBack = YES;
    } else if (type == TTQTopicDetailRequestTypeTopicOnly) {
        parameters[@"is_return_topic"] = @(1);
    }
    parameters[@"bottom_recommend_version"] = @(1);
    if (!(type == TTQTopicDetailRequestTypeNextPage || type == TTQTopicDetailRequestTypePrePage)) {
        // 第一次进入页面请求数据，如已经有缓存数据，不要显示loading
        if (self.isFirstRequestReviewData) {
            self.isFirstRequestReviewData = NO;
            if ([self commentCount] == 0) {
                [self showCommentLoadingUI:YES withError:nil];
            }
        } else {
            [self showCommentLoadingUI:YES withError:nil];
        }
    }
    
    // 新版接口
    parameters[@"size"] = @(self.requestSizeCount);
    // 拼接相关推荐请求参数
    requestRecommend = [self appendRecommendRequestParamsWithOriParmas:parameters andRequestType:type];
    // 帖子详情页接口拆分为上下两部分
    // 1 文章信息 https://apidoc.seeyouyima.com/doc/609521e9b7d68f1e1d57aada
    NSMutableDictionary *articleParam = nil;
    // 从大社区进来的，要求显示短图文样式
    articleParam = [@{@"topic_id":@(self.topic_id),@"req_source":self.isUGCUIStlye?@1:@0} mutableCopy];
    if (self.is_from_ad == 1) {
        articleParam[@"referer"] = @1;
    }
    IMYHTTPBuildable *articleBuild = [IMYServerRequest get:@"/v5/article_detail" host:circle_seeyouyima_com params:articleParam headers:nil];
    RACSignal *articleSignal = [articleBuild.signal deliverOnMainThread];
    // 2 评论分页接口 https://apidoc.seeyouyima.com/doc/611a302fc1250022b1040bb5
    IMYHTTPBuildable *reviewBuild = [IMYServerRequest get:@"/v5/article_review_list" host:circle_seeyouyima_com params:parameters headers:nil];
    RACSignal *reviewSignal = [reviewBuild.signal deliverOnMainThread];
    
    __block id<IMYHTTPResponse> reviewsResponse = nil;
    __block id<IMYHTTPResponse> articleResponse = nil;
    reviewSignal = [[reviewSignal doNext:^(id<IMYHTTPResponse> x) {
        NSLog(@"topicDetailSignal doNext == %@", x);
        @strongify(self);
        reviewsResponse = x;
        NSDictionary *result = reviewsResponse.responseObject;
        if ([result isKindOfClass:[NSDictionary class]]) {
            self.main_total_review = [[result objectForKey:@"main_total_review"] integerValue];
        }
        
    }] doError:^(NSError * _Nonnull error) {
        @strongify(self);
        if ([error.domain isEqualToString:IMYMeetYouServiceErrorDomain]) {
            // 接口200 数据code非0情况 13098400越权操作
            self.isReviewPhpError = YES;
        } else {
            // 非200情况
            self.isReviewError = YES;
        }

        self.reviewError = error;
    }];
    
    articleSignal = [articleSignal doNext:^(id<IMYHTTPResponse> x) {
        NSLog(@"topicDetailSignal doNext == %@", x);
        articleResponse = x;
    }];
    
    RACSignal *zipSignal;
    if (!onlyRequestReview) {
        zipSignal = [RACSignal merge:[@[articleSignal,reviewSignal].rac_sequence map:^(RACSignal *signal) {
            if (signal == articleSignal) {
                return signal;
            }
            return [signal materialize];
        }]];
    } else {
        zipSignal = reviewSignal;
    }
    self.changeOrderRequest = NO;
    __block id<IMYHTTPResponse> topicRecommendResponse = nil;
    RACSignal *resultSignal = [[[zipSignal then:^RACSignal *{
        return [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
            [subscriber sendNext:nil];
            [subscriber sendCompleted];
            return nil;
        }];
    }] doNext:^(id x) {
         @strongify(self);
        if (!(type == TTQTopicDetailRequestTypeNextPage || type == TTQTopicDetailRequestTypePrePage)) {
            [self showCommentLoadingUI:NO withError:nil];
        }
       
        NSLog(@"resultSignal doNext == %@", x);
        topicRecommendResponse = x;
        NSMutableDictionary *responseObject = [NSMutableDictionary dictionary];
        if ([reviewsResponse.responseObject isKindOfClass:NSDictionary.class]) {
            [responseObject addEntriesFromDictionary:reviewsResponse.responseObject];
        }
        if ([articleResponse.responseObject isKindOfClass:NSDictionary.class]) {
            [responseObject addEntriesFromDictionary:articleResponse.responseObject];
        }
        if (self.isUsingCacheData) {// 折叠场景下，使用缓存的时候，会禁用底部的加载更多
            self.automaticallyRefresh = YES;
        }
        self.isUsingCacheData = NO;
        IMYHTTPResponse *response = [IMYHTTPResponse new];
        response.OBJECT(responseObject);
        [self handleDataWithResponse:response type:type error:nil change:changeOrderRequest requestRecommend:requestRecommend];
        if (self.reviewError) {
            [self showReviewErrorToast:self.reviewError isChangeOrderRequest:changeOrderRequest];
        }
    }] doError:^(NSError *error) {
        @strongify(self);
        if (!(type == TTQTopicDetailRequestTypeNextPage || type == TTQTopicDetailRequestTypePrePage)) {
            [self showCommentLoadingUI:NO withError:error];
        }
        NSLog(@"resultSignal doError == %@", error);
        NSMutableDictionary *responseObject = [NSMutableDictionary dictionary];
        if ([reviewsResponse.responseObject isKindOfClass:NSDictionary.class]) {
            [responseObject addEntriesFromDictionary:reviewsResponse.responseObject];
        }
        if ([articleResponse.responseObject isKindOfClass:NSDictionary.class]) {
            [responseObject addEntriesFromDictionary:articleResponse.responseObject];
        }
        if (self.isUsingCacheData) {// 折叠场景下，使用缓存的时候，会禁用底部的加载更多
            self.automaticallyRefresh = YES;
        }
        self.isUsingCacheData = NO;
        IMYHTTPResponse *response = [IMYHTTPResponse new];
        response.OBJECT(responseObject);
        if (articleResponse.responseObject != nil) {
            self.notDeliverReviewError = YES;
            [self handleDataWithResponse:response type:type error:nil change:changeOrderRequest requestRecommend:requestRecommend];
        } else {
            if (onlyRequestReview) {
                self.notDeliverReviewError = YES;
                [self handleDataWithResponse:response type:type error:nil change:changeOrderRequest  requestRecommend:requestRecommend];
            } else {
                [self handleDataWithResponse:response type:type error:error change:changeOrderRequest  requestRecommend:requestRecommend];
            }
        }
        if (self.reviewError) {
            [self showReviewErrorToast:self.reviewError isChangeOrderRequest:changeOrderRequest];
        }
    }];
    return resultSignal;
}


/// 加载二级评论接口+加载更多
/// - Parameters:
///   - indexPath:
///   - count: 请求个数
- (RACSignal *)requestSecondaryCommentsWithIndexPath:(NSIndexPath *)indexPath count:(NSInteger)sizeCount{
    
    NSIndexPath *mainIndexPath = [NSIndexPath indexPathForRow:0 inSection:indexPath.section];
    TTQCommentModel *mainCommentModel = [self tableCellModelAtIndexPath:mainIndexPath];
    TTQCommentModel *lastReferencedModel = [self tableCellModelAtIndexPath:indexPath];
    
    NSMutableDictionary * params = @{}.mutableCopy;
    params[@"order_by"] = @"reviewed_date";
    params[@"size"] = @(sizeCount);
    params[@"topic_id"] = @(self.topic_id);
    params[@"referenced_id"] = @(mainCommentModel.commentID);
    if (self.sub_comment_id > 0 && self.sub_comment_id == lastReferencedModel.commentID) {
        //最近一条评论是跳楼进来的，不走翻页
    }else if( mainCommentModel == lastReferencedModel){
        //传进来的是主评论的 index 表示，子评论没有了，需要重新加载
    } else{
        params[@"last"] = @(lastReferencedModel.commentID);
        params[@"load_direction"] = @"next";
    }
    
    @weakify(self, indexPath);
    IMYHTTPBuildable *paramBuild = [IMYServerRequest get:@"v2/stacke_review_detail" host:circle_seeyouyima_com params:params headers:nil];
    RACSignal *reviewDetailSignal = paramBuild.signal;
    return [[reviewDetailSignal  deliverOnMainThread]doNext:^(id<IMYHTTPResponse> response) {
        @strongify(self, indexPath);
        NSArray<TTQCommentModel *> *referenceds = [response.responseObject[@"references"] toModels:[TTQCommentModel class]];
        NSIndexPath *mainIndexPath = [NSIndexPath indexPathForRow:0 inSection:indexPath.section];
        TTQCommentModel *mainCommnent = [self tableCellModelAtIndexPath:mainIndexPath];
        if (mainCommnent.isMoreCommentsLoading) {//不在加载中状态，可能是数据重置了，这里不要设置
            NSMutableArray *subModels = mainCommnent.referenceds.mutableCopy;
            NSInteger referenceCount = referenceds.count;
            //第一条是跳楼进来的，需要在接口返回中的数据移除
            TTQCommentModel *firstModel = subModels.firstObject;
            if (self.sub_comment_id > 0 && firstModel.commentID == self.sub_comment_id) {
                referenceds = [referenceds bk_reject:^BOOL(TTQCommentModel *obj) {
                    return  obj.commentID == self.sub_comment_id;
                }];
            }
            [subModels addObjectsFromArray:referenceds];
            mainCommnent.referenceds = [subModels copy];
            
            //获取到的子评论为 0，表示没有更多， 更正一下主评论的referenced_num
            if (referenceCount < sizeCount && subModels.count < mainCommnent.referenced_num) {
                mainCommnent.referenced_num = subModels.count;
            }
        }
    }];
}


- (RACSignal *)deleteCommentAtIndexPath:(NSIndexPath *)indexPath {
    
    NSIndexPath *mainIndexPath = [NSIndexPath indexPathForRow:0 inSection:indexPath.section];
    TTQCommentModel *mainCommentModel = [self tableCellModelAtIndexPath:mainIndexPath];
    TTQCommentModel *targetCommentModel = [self tableCellModelAtIndexPath:indexPath];
    NSInteger commentID = targetCommentModel.commentID;
    NSDictionary *parameters = @{ @"topic_id": @(self.topic_id),
                                  @"forum_id": @(self.forum_id),
                                  @"review_id": @(commentID) };
    @weakify(self, mainCommentModel, targetCommentModel);
    return [[[[TTQHttpHelper postPath:@"user_del_review" params:parameters] deliverOnMainThread] doNext:^(id<IMYHTTPResponse> response) {
        @strongify(self, mainCommentModel, targetCommentModel);
        [UIWindow imy_showTextHUD:IMYString(@"删除回复成功")];
        if (mainCommentModel == targetCommentModel) {
            [self addDeleteDataSourceWithCommentID:commentID];
            self.dataSource = [self.dataSource bk_select:^BOOL(TTQCommentModel *model) {
                if (self.keepRowsWhenDeleteComment && [model isKindOfClass:TTQCommentModel.class] && model.commentID == commentID) {
                    model.isDeletedLocal = YES;///本地标记删除， 但数据源不移除
                    model.referenceds = @[];///删除评论时，子评论也要删除
                }else if ([model isKindOfClass:[TTQCommentModel class]]) {
                    return model.commentID != commentID;
                }
                return true;
            }];
        }else{
            NSArray *referenceds = [mainCommentModel.referenceds bk_reject:^BOOL(TTQCommentModel *obj) {
                if (self.keepRowsWhenDeleteComment && obj == targetCommentModel) {
                    obj.isDeletedLocal = YES;///本地标记删除， 但数据源不移除
                    return NO;///不移除
                }
                return obj == targetCommentModel;
            }];
            mainCommentModel.referenceds = [referenceds copy];
            mainCommentModel.referenced_num -= 1;
        }

    }] doError:^(NSError *error) {
        if (![IMYNetState networkEnable]) {
            [UIView imy_showTextHUD:MT_Request_NoNetToast];
        } else {
            if (error.af_responseData == nil) {
                [UIWindow imy_showTextHUD:@"删除回复失败"];
            } else {
                [UIWindow imy_showHUDwithNetworkError:error andResponseObject:error.af_responseData];
            }
        }
    }];
}

/**
 投票
 @param topicModel 投票对应的帖子
 @param selectedItems 投票
 @param biParams BI埋点参数
 */
- (void)requestVoteWithTopicModel:(TTQTopicModel *)topicModel selectedItems:(NSArray *)selectedItems biParams:(NSDictionary *)biParams {
    @weakify(self);
    NSMutableArray *biDic = [NSMutableDictionary dictionaryWithDictionary:biParams];
    biDic[@"topicDetailVote"] = @1;
    [TTQBusinessRequestHelper requestVoteWithTopicModel:topicModel selectedItems:selectedItems biParams:biDic.copy resultBlock:^(BOOL success) {
        @strongify(self);
        if (success) {
            [IMYGAEventHelper postWithPath:@"/event" params:@{@"event":@"sq_tzxqytp",@"topic_id":@(topicModel.topic_id)} headers:nil completed:nil];
            if (self.from_community_home) {
                [TTQCommonHelp GAEventForEventWithName:@"ttq_tzxqy_tp" action:2];
            }
            // 通知更新类表
            self.feedsSourceChange = YES;
        }
    }];
}

#pragma mark - 大社区推荐

- (BOOL)appendRecommendRequestParamsWithOriParmas:(NSMutableDictionary *)oriParmas andRequestType:(TTQTopicDetailRequestType)type {
    return NO;
}

// MARK: - ===== Data Handler =====

/**
 处理返回的response
 @param type 0 下拉刷新，1 加载下一页，2 加载上一页,3 跳楼,4 只请求TTQTopicModel
 */
- (void)handleDataWithResponse:(id<IMYHTTPResponse>)response type:(NSInteger)type error:(NSError *)error change:(BOOL)isChange requestRecommend:(BOOL)requestRecommend {
    // 和错误无关的数据更新
    // 加载更多评论状态更新
    self.isLoadingMoreComments = NO;
    // 更新业务数据
    self.lastResType = -1;
    if (!error) {
        // 这里得到的评论总数是好像是无效的，后面在读取帖子信息的时候，topic的的total_review属性会重新设置
        NSInteger total_review = 0;
        NSInteger total_floor = 0;
        self.lastResType = type;
        if ([response.responseObject[@"total_review"] isKindOfClass:NSNumber.class] &&
            [response.responseObject[@"total_floor"] isKindOfClass:NSNumber.class]) {
            total_review = [response.responseObject[@"total_review"] integerValue];
            total_floor = [response.responseObject[@"total_floor"] integerValue];
            if (total_floor > 0) {
                self.topic.total_floor = total_floor;
            }
            if (total_review > 0) {
                self.topic.total_review = total_review;
            }
        }

        if ([response.responseObject[@"topic_review_order_type"] isKindOfClass:[NSString class]]) {
            NSString *orderTypeFromServer = response.responseObject[@"topic_review_order_type"];
            if (!self.orderByFilter) {
                //首次进入帖子,排序按照服务器下发决定
                self.orderByFilter = [self orderByFilterWithOrderByString:orderTypeFromServer];//根据服务器返回的排序来决定首次进入帖子的排序
            }
        }
        
        if (response.responseObject[@"user_info"]) {
            TTQTopicCurrentUserInfo *currentUserInfo = [response.responseObject[@"user_info"] toModel:[TTQTopicCurrentUserInfo class]];
            self.currentUserInfo = currentUserInfo;
        }
        [self handleCommentsDataWithResponse:response type:type error:error innerError:nil change:isChange];
        [self handleTopicContentWithResponse:response type:type error:error innerError:nil];
        if (response.responseObject[@"highlight_words"]) {
            self.topic.highlight_words = [response.responseObject[@"highlight_words"] toModels:TTQHighLightWordModel.class];
        }
        if (response.responseObject[@"resource_list"]) {
            self.resource_list = response.responseObject[@"resource_list"];
        }
        
        if (response.responseObject[@"recommend_tag"]) { // 主楼里面的搜索推荐标签
            NSArray *recommendQATopics = response.responseObject[@"recommend_tag"];
            if ([recommendQATopics isKindOfClass:NSArray.class]) {
                self.recommendTags = [recommendQATopics toModels:TTQTopicRichParserTagModel.class];
            }
        }
        
        // 处理空数据，当做一个特殊的列表数据类型
        [self handleEmptyDatasByUserChange:isChange];
        
    }
}

- (void)requestRecommendData{
}

- (void)refreshAfterRecommendBack:(BOOL)hasRecommend {
    if (hasRecommend) {
        // 折叠评论并且展示相关推荐
        if ([self isHitFoldCommentTest]) {
            self.automaticallyRefresh = self.commentRefreshNoMore;
        }
        [self addCommentMore];
        [self addCommentFooterWhenNoMoreComment];
        // 处理空数据，当做一个特殊的列表数据类型
        [self handleEmptyDatasByUserChange:NO];
        if (self.footerLoadingBlock) {
            self.footerLoadingBlock(NO);
        }
    } else {
        // 无相关推荐内容
        self.automaticallyRefresh = self.commentRefreshNoMore;
        if (self.isRequestNext && self.automaticallyRefresh) {
            self.isRequestNext = NO;
            if (self.footerLoadMoreBlock) {
                self.footerLoadMoreBlock(NO);
            }
        } else {
            [self addCommentMore];
            [self addCommentFooterWhenNoMoreComment];
            if (self.footerLoadingBlock) {
                self.footerLoadingBlock(NO);
            }
        }
    }
}


- (void)handleCommentsDataWithResponse:(id<IMYHTTPResponse>)response type:(NSInteger)type error:(NSError *)serverError innerError:(NSError *)innerError change:(BOOL)isChange{
    // 错误的情况不改变该数据
    // self.automaticallyRefresh = [response.responseObject[@"next_more"] boolValue];
    // 处理评论数据
    // 处理推荐数
    
    if (self.isReviewError || self.isReviewPhpError) {
        if (!isChange) {
            return;// 当数据请求失败，不是用户主动切换【最近】和【最热】，就不往下走
        }
    }
    NSArray *comments = [self parseCommentsFromResponseObject:response.responseObject];
    /// 标记需要高亮展示的暖评数据
    __block BOOL isForceShowHeader = NO;
    if (self.animationCommentIdWhenAppear > 0) {
        [comments match:^BOOL(TTQCommentModel *element) {
            TTQCommentModel *subModel = nil;
            if (element.commentID == self.animationCommentIdWhenAppear) {
                if (self.sub_comment_id > 0) {//先定位到主评论，再定位子评论
                    subModel = [element.referenceds bk_match:^(TTQCommentModel *subModel) {
                        if (subModel.commentID == self.sub_comment_id) {
                            subModel.needHighlightWhenAppear = YES;
                            return YES;
                        }
                        return NO;
                    }];
                }
                if (!subModel) {
                    element.needHighlightWhenAppear = YES;
                }
                return YES;
            }
            return NO;
        }];
    }
    self.animationCommentIdWhenAppear = -1;
    if (type != TTQTopicDetailRequestTypePrePage && type != TTQTopicDetailRequestTypeNextPage) {
        if (self.reviewError) {
            // 读取缓存
            if (self.orderByFilter == TTQOrderByFilterHot) {
                response.OBJECT(self.hotReviewsCacacheDic);
            } else if (self.orderByFilter == TTQOrderByFilterLastest) {
                response.OBJECT(self.latestReviewsCacacheDic);
            }
            if (response.responseObject == nil) {// 没有缓存
                if (self.isReviewError) { // http != 200，没有网络会走到这里吗
                    self.dataSource = @[@[@"request_failed_cell"]];
                    self.automaticallyRefresh = !self.isRecommendBack; // no
                    return;
                } else {
                    comments = nil;// 评论数为空，展示无评论空页面/过滤空页面
                    [UIWindow imy_showTextHUD:[self.reviewError localizedDescription]];
                }
            } else {// 有缓存
                if (self.isReviewError) { // http != 200，没有网络会走到这里吗
                    if (![IMYNetState networkEnable]) {
                        [UIWindow imy_showTextHUD:IMYString(kStatusText_networkDisconnectCache)];
                    } else {
                        IMYWebMessageModel *failModel = [serverError.af_responseData toModel:[IMYWebMessageModel class]];
                        if (imy_isNotEmptyString(failModel.message)) {
                            [UIWindow imy_showTextHUD:failModel.message]; // 越权操作的吐司
                        } else {
                            [UIWindow imy_showTextHUD:@"网络缓慢，请稍后再试"];
                        }
                    }
                } else {// 请求成功，code!=0
                    [UIWindow imy_showTextHUD:[self.reviewError localizedDescription]];
                }
               
                comments = [self parseCommentsFromResponseObject:response.responseObject];
            }
        } else { // 数据完全正常，没有任何错误
            // 保存最近的一次刷新数据到内存中
            if (self.orderByFilter == TTQOrderByFilterHot) {
                self.hotReviewsCacacheDic = response.responseObject;
            } else if (self.orderByFilter == TTQOrderByFilterLastest) {
                self.latestReviewsCacacheDic = response.responseObject;
            }
        }
    }
    self.lastCommentCount = comments.count;
    
    switch (type) {
        case TTQTopicDetailRequestTypeRefresh: {
            [self clearDeleteDataSource];
            BOOL lastCommentFold = self.isCommentFolding;
            self.showTableHeader = true;
            self.dataSource = comments;
            self.commentRefreshNoMore = [response.responseObject[@"is_more"] boolValue];
            self.automaticallyRefresh = [response.responseObject[@"is_more"] boolValue];
            if (self.isRecommendBack) {
                if (!self.isHitFoldCommentTest) {
                    if (self.recommendVM.recommentCount && !self.automaticallyRefresh) {
                        lastCommentFold = YES;
                    } else {
                        lastCommentFold = NO;
                    }
                }
                if (lastCommentFold) {
                    [self addCommentMore];
                    [self addCommentFooterWhenNoMoreComment];
                }
            }
            if (lastCommentFold != self.isCommentFolding) {
                self.isCommentFolding = lastCommentFold;
            }
            break;
        }
        case TTQTopicDetailRequestTypeNextPage: {
            if (comments.count) {
                NSMutableArray *array = [[NSMutableArray alloc] initWithArray:self.dataSource];
                NSArray *realComments = [self filteredCommentsFromComments:comments];
                self.lastCommentCount = realComments.count;
                [self appendToTailWithComments:realComments dataSource:array];
                self.dataSource = array;
            }
            self.automaticallyRefresh = [response.responseObject[@"is_more"] boolValue];
            if (!self.automaticallyRefresh && !self.isHitFoldCommentTest && self.recommendVM.recommentCount) {
                /// 不能再加载数据了，但有推荐数据，需要展示推荐数据，未命中折叠评论实验的，在这里修改下isCommentFolding
                self.isCommentFolding = YES;
            }
            [self addCommentFooterWhenNoMoreComment];
            break;
        }
        case TTQTopicDetailRequestTypePrePage: {
            if (comments.count) {
                NSMutableArray *array = [self filteredCommentsFromComments:comments].mutableCopy;
                self.lastCommentCount = array.count;
                id firstData = self.dataSource.firstObject;
                [array addObjectsFromArray:self.dataSource];
                if (firstData) {
                    [array removeObject:firstData];
                    [array insertObject:firstData atIndex:0];
                }
                self.dataSource = array;
            }
            self.showTableHeader = ![response.responseObject[@"is_more"] boolValue];
            if (self.showTableHeader) {
                self.gotoID = -1;
            }
            break;
        }
        case TTQTopicDetailRequestTypeGoto: {
            // 显示详情的条件：评论小于20；评论楼层从1开始；不是跳楼模式
            [self clearDeleteDataSource];
            self.automaticallyRefresh = [response.responseObject[@"is_more"] boolValue];
            self.dataSource = comments;
            [self addCommentMore];
            [self addCommentFooterWhenNoMoreComment];
            break;
        }
        default:
            break;
    }
}
- (void)handleTopicContentWithResponse:(id<IMYHTTPResponse>)response type:(NSInteger)type error:(NSError *)serverError innerError:(NSError *)innerError {
    // 帖子正文
    NSDictionary *topicDic = response.responseObject[@"topic"];
    TTQTopicModel *topicModel = [topicDic toModel:[TTQTopicModel class]];
    if (topicModel) {
        //要更新下forum_id。因为有些帖子比较旧forum_id的值可能是错的
        self.forum_id = topicModel.forum_id;
        self.topic = topicModel;
        NSDictionary *templateInfo = topicDic[@"tpl_info"];
        if (templateInfo) {
            self.topic.templateID = [templateInfo[@"id"] integerValue];
            self.topic.templateType = [templateInfo[@"type"] integerValue];
        }
        NSInteger main_total_review = 0;
        if ([response.responseObject[@"main_total_review"] isKindOfClass:NSNumber.class]) {
            main_total_review = [response.responseObject[@"main_total_review"] integerValue];
        }
        self.topic.main_total_review = main_total_review;
        
        TTQUPlusBanner *uModel = [response.responseObject[@"subject_banner"] toModel:[TTQUPlusBanner class]];
        topicModel.subject_banner = uModel;
    }
    
    if (response.responseObject[@"share_body"]) {
        TTQShareBodyModel *shareBody = [response.responseObject[@"share_body"] toModel:[TTQShareBodyModel class]];
        self.shareBody = shareBody;
    }
    if (response.responseObject[@"topic_activity"]) {
        self.topic_activity = [response.responseObject[@"topic_activity"] toModel:[TTQTopicActivity class]];
    }
    
    if (response.responseObject[@"is_user_operate"]) {
        //判断是否是圈主或者运营人员 790
        self.is_user_operate = [response.responseObject[@"is_user_operate"] integerValue];
    }
    
    if (response.responseObject[@"subsequent_info"]) {
        self.subsequentModel = [response.responseObject[@"subsequent_info"] toModel:IMYSubsequentModel.class];
    }
    
    if (response.responseObject[@"quick_access"] && topicModel.category == TTQTopicCategoryCaseHistory) {
        self.quickAccessModel = [response.responseObject[@"quick_access"] toModel:TTQTopicQuickAccess.class];
        self.subsequentModel = nil;///病例贴不展示蹲蹲
    }
    
    // 柚+插件内容
    NSArray *shopping_plugin = response.responseObject[@"shopping_plugin"];
    NSMutableArray *showPluginModels = [NSMutableArray array];
    for (NSDictionary *dic in shopping_plugin) {
        TTQShoppingPlugin *model = [dic toModel:[TTQShoppingPlugin class]];
        [showPluginModels addObject:model];
    }
    self.shopPlugins = showPluginModels;
}

-(BOOL)checkTopicValidate{
    if(self.topic.tip != nil){
        return NO;
    }
    return YES;
}

/**
 添加评论到数据源末尾，需要放在推荐数据之前
 */
- (void)appendToTailWithComments:(NSArray *)comments dataSource:(NSMutableArray *)dataSource {
    // 过滤重复的评论数据
    NSArray *insertComments = [self filteredCommentsFromComments:comments];
    NSInteger insertCommentIndex = [self insertCommentIndexFromDataSource:dataSource];
    // 从尾巴到头部遍历数据插入指定位置，这样整个顺序刚好是按顺序的
    [insertComments enumerateObjectsWithOptions:NSEnumerationReverse usingBlock:^(TTQCommentModel *  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        [dataSource insertObject:obj atIndex:insertCommentIndex];
    }];
}

- (NSInteger)firstIndexWithObjClass:(Class)objClass dataSource:(NSArray *)dataSource{
    NSInteger firstIndex = 0;
    for (id data in dataSource) {
        if ([data isKindOfClass:objClass]) {
            break;
        }
        firstIndex++;
    }
    firstIndex = MIN(firstIndex, dataSource.count);
    return firstIndex;
}

/**
 获取加载更多评论Cell的位置
 */
- (NSInteger)insertCommentIndexFromDataSource:(NSArray *)dataSource {
    if (dataSource.count == 0) {
        return 0;
    }
    NSInteger loadMoreIndex = dataSource.count;
    for (int i = (int)self.dataSource.count - 1; i >= 0; i--) {
        id obj = self.dataSource[i];
        if ([obj isKindOfClass:NSArray.class] && [((NSArray *)obj).firstObject isKindOfClass:NSString.class] && [(NSString *)(((NSArray *)obj).firstObject) isEqualToString:@"moreComment"]) {
            NSMutableArray *array = [NSMutableArray arrayWithArray:self.dataSource];
            [array removeObjectAtIndex:i];
            self.dataSource = [array copy];
            loadMoreIndex = i;
            break;
        }
    }
    return loadMoreIndex;
}

/**
 获取相关推荐Header的位置
 */
- (NSInteger)recommendHeaderIndexFromDataSource:(NSArray *)dataSource {
    return [self firstIndexWithObjClass:TTQDetailListHeaderModel.class dataSource:dataSource];
}

/**
 解析和处理评论数据，如果推荐有指定了位置，那么会把推荐的数据
 */
- (NSArray *)parseCommentsFromResponseObject:(NSDictionary *)responseObject {
    NSArray *dictArray = responseObject[@"reviews"];
    if (!dictArray) {
        dictArray = responseObject[@"data"][@"reviews"];
    }
    NSMutableArray *comments = [[dictArray bk_map:^id(NSDictionary *dic) {
        if ([dic[@"type"] integerValue] == 2) {
            // 推荐评论
            TTQTopicModel *topicModel = [dic toModel:[TTQTopicModel class]];
            topicModel.listType = TTQTopicDetailListTypeRecommendTopicForComment;
            return topicModel;
        } else {
            // 普通评论
            TTQCommentModel *commentModel = [dic toModel:[TTQCommentModel class]];
            commentModel.contentLabelHeight = [self getContentLabelHeightByModel:commentModel];//计算contentLabel的高度
            return commentModel;
        }
    }] mutableCopy];
    return comments;
}

- (CGFloat)getContentLabelHeightByModel:(TTQCommentModel*)model {
    CGFloat height = 0;
    if (!model.useThird) {
        height = [TTQCommonHelp computePureStringLabelHeightWithText:model.content
                                                   lineSpace:3
                                                    fontName:[UIFont ttqFontWith:17]
                                                        size:CGSizeMake(SCREEN_WIDTH - 60 - 16, CGFLOAT_MAX)];
    }
    return height;
}

- (void)addCommentMore {
    if (self.automaticallyRefresh && self.isCommentFolding) {// 还有数据要加载，并且处于折叠样式，在最后的位置插入加载更多数据cell
        for (int i = (int)self.dataSource.count - 1; i >= 0; i--) {
            id obj = self.dataSource[i];
            if ([obj isKindOfClass:[TTQCommentModel class]]) {
                NSMutableArray *array = [NSMutableArray arrayWithArray:self.dataSource];
                [array insertObject:@[@"moreComment"] atIndex:i+1];
                self.dataSource = [array copy];
                break;
            } else if ([obj isKindOfClass:NSArray.class] && [((NSArray *)obj).firstObject isKindOfClass:NSString.class] && [(NSString *)(((NSArray *)obj).firstObject) isEqualToString:@"moreComment"]) {
                NSMutableArray *array = [NSMutableArray arrayWithArray:self.dataSource];
                [array removeObjectAtIndex:i];
                self.dataSource = [array copy];
                break;
            }
        }
    }
}

- (void)addCommentFooterWhenNoMoreComment {
    if (!self.automaticallyRefresh && self.dataSource.count > 0) {// 已经不能自动刷新了，数据已经加载完成
        // 如果处于折叠样式，数据加载完要移除【更多】cell
        if (self.isCommentFolding) {
            for (int i = (int)self.dataSource.count - 1; i >= 0; i--) {
                id obj = self.dataSource[i];
                if ([obj isKindOfClass:NSArray.class] && [((NSArray *)obj).firstObject isKindOfClass:NSString.class] && [(NSString *)(((NSArray *)obj).firstObject) isEqualToString:@"moreComment"]) {
                    NSMutableArray *array = [NSMutableArray arrayWithArray:self.dataSource];
                    [array removeObjectAtIndex:i];
                    self.dataSource = [array copy];
                    break;
                }
            }
        }
//        // 增加【已过滤UI】  8.77 去掉了，先保留代码
//        for (int i = (int)self.dataSource.count - 1; i >= 0; i--) {
//            id obj = self.dataSource[i];
//            if ([obj isKindOfClass:[TTQCommentModel class]]) {
//                NSMutableArray *array = [NSMutableArray arrayWithArray:self.dataSource];
//                [array insertObject:@[@"commentfooter"] atIndex:i+1];
//                self.dataSource = [array copy];
//                break;
//            } else if ([obj isKindOfClass:NSArray.class] && [((NSArray *)obj).firstObject isKindOfClass:NSString.class] && [(NSString *)(((NSArray *)obj).firstObject) isEqualToString:@"commentfooter"]) {
//                break;
//            } 
//        }
//    }
//    
//    if ([self commentCount] == 0) {// ????
//        for (int i = (int)self.dataSource.count - 1; i >= 0; i--) {
//            id obj = self.dataSource[i];
//            if ([obj isKindOfClass:NSArray.class] && [((NSArray *)obj).firstObject isKindOfClass:NSString.class] && [(NSString *)(((NSArray *)obj).firstObject) isEqualToString:@"commentfooter"]) {
//                NSMutableArray *array = [NSMutableArray arrayWithArray:self.dataSource];
//                [array removeObjectAtIndex:i];
//                self.dataSource = [array copy];
//                break;
//            }
//        }
    }
}



/**
 评论数据过滤，删除在列表中已存在的数据
 @param comments 服务端返回的评论数据
 */
- (NSArray *)filteredCommentsFromComments:(NSArray *)comments {
    NSArray *filteredComments = [comments bk_select:^BOOL(TTQCommentModel *obj) {
        for (TTQCommentModel *existComment in self.dataSource) {
            if ([obj isKindOfClass:TTQCommentModel.class]
                && [existComment isKindOfClass:TTQCommentModel.class]
                && obj.commentID == existComment.commentID) {
                return NO;
            }
        }
        return YES;
    }];
    return filteredComments;
}

/**
 处理空的数据
 */
- (void)handleEmptyDatasByUserChange:(BOOL)userChange {
    if (self.isReviewError && userChange) {// 如果是用户切换最新最热，且网络请求http错误，前面已经加了request_failed_cell数据源，这里就不要继续往下走了
        return;
    }
    NSMutableArray *escapeEmptyContentDataSource = [self.dataSource bk_select:^BOOL(id obj) {
        return ![obj isKindOfClass:TTQEmptyCommentModel.class];
    }].mutableCopy;
    if ([self isCurFilterDataSourceNil]) {
        TTQEmptyCommentModel *emptyCommentModel = [TTQEmptyCommentModel new];
        emptyCommentModel.isRealEmpty = YES;
        if (self.topic.total_review > 0) {
            emptyCommentModel.isRealEmpty = NO; // 并非真的没有评论
        }
        NSInteger insertCommentIndex = [self recommendHeaderIndexFromDataSource:escapeEmptyContentDataSource];
        [escapeEmptyContentDataSource insertObject:emptyCommentModel atIndex:insertCommentIndex];
    }
    self.dataSource = escapeEmptyContentDataSource;
}

/**
 强制处理附加的数据：空数据和相关推荐
 */
- (void)forceHandleAttachDatas {
    // 处理空数据，当做一个特殊的列表数据类型
    [self handleEmptyDatasByUserChange:NO];
}

/**
 设置列表的数据，列表存在多种数据：占位（NSString）、评论(TTQCommentModel)、空数据（TTQEmptyCommentModel）
 占位的内容是历史的原因需要用到
 评论的内容会拆分为主评论和子评论多个Cell，主评论和子评论在一个section中
 推荐的内容整体当做一个Cell
 空数据内容整体当做一个Cell
 */
- (void)setDataSource:(NSArray *)dataSource {
    BOOL containPlaceHolder = [dataSource bk_any:^BOOL(id obj) {
        return [obj isKindOfClass:[NSArray class]]
        && [((NSArray *)obj).firstObject isKindOfClass:NSString.class]
        && [(NSString *)((NSArray *)obj).firstObject isEqualToString:@""];
    }];
    self.dataSourceContainPlaceHolder = containPlaceHolder;
    if (!containPlaceHolder) {
        NSMutableArray *tmpDataSource = [NSMutableArray array];
        [tmpDataSource addObject:@[@""]];
        if ([dataSource isKindOfClass:[NSArray class]]) {
            [tmpDataSource addObjectsFromArray:dataSource];
        }
        [super setDataSource:tmpDataSource];
    } else {
        [super setDataSource:dataSource];
    }
    if (self.dataSourceChangeBlock) {
        self.dataSourceChangeBlock();
    }
}

- (void)clearData {
    self.topic = nil;
    [self setDataSource:nil];
}

// MARK: ===== Cache =====

- (TTQTopicCache *)requestTopicDataCache {
    __block TTQTopicModel *topic = nil;
    __block NSArray *commentArray = nil;
    __block TTQTopicCache *topicCache = nil;
    NSString *keyPrefix = @"";
    if(self.isUGCUIStlye){
        keyPrefix = @"IMYUGC";
    }
    [[TTQTopicContentYYCacheManager shareInstance] queryCacheWithTopicID:self.topic_id keyPrefix:keyPrefix done:^(TTQTopicModel * _Nullable cachedTopic, NSArray * _Nullable cachedCommentArray, TTQTopicCache * _Nullable cachedTopicCache) {
        // 进入缓存实验设置缓存数据，如果有缓存gotoID，走gotoID跳转评论的流程，不设置帖子详情内容和评论列表
//        if (![IMYNetState networkEnable] || cachedTopicCache.gotoID <= 0) {
            topic = cachedTopic;
            commentArray = cachedCommentArray;
//        }
        topicCache = cachedTopicCache;
    }];
    
    // 设置缓存数据
    // 设置内容
    self.topic = topic;
    
    // 设置位置缓存数据
    if (topicCache) {
        self.isRead = topicCache.isRead;
        self.forum_id = topic.forum_id;
        self.commentFilter = topicCache.menu_index;
        self.orderByFilter = (topicCache.orderByFilter == TTQOrderByFilterLastest) ? TTQOrderByFilterLastest : TTQOrderByFilterHot;
        self.datasourceOrderFilter = self.orderByFilter;
        self.offset_y = 0;
        self.showTableHeader = topicCache.hasTableHead;
        self.recommendHeaderStartY = topicCache.recommendHeaderStartY;
        self.automaticallyRefresh = topicCache.automaticallyRefresh;
        self.commentRefreshNoMore = self.automaticallyRefresh;
        self.subsequentModel = topicCache.subsequentModel;
    }
    [self showCommentLoadingUI:NO withError:nil];
    // 处理评论数据
    if (commentArray.count) {
        // 手工进行排序，因为有插楼的需求
        NSMutableDictionary *commentDic = [[NSMutableDictionary alloc] initWithCapacity:commentArray.count];
        for (TTQCommentModel *model in commentArray) {
            commentDic[[NSString stringWithFormat:@"%@", @(model.commentID)]] = model;
            model.cellHeight = 0;
            model.contentLabelHeight = 0;
        }
        NSMutableArray *comments = [NSMutableArray new];
        for (NSString *commentID in [topicCache.commentids componentsSeparatedByString:@","]) {
            TTQCommentModel *model = commentDic[commentID];
            if (model) {
                [comments addObject:model];
            }
        }
        self.dataSource = comments;
    } else {
        // ！这里需要设置一个空的数据，后面添加空白数据模型的流程才会正常
        self.dataSource = @[];
//        else {
//            self.gotoID = topicCache.gotoID;
//        }
    }
    
    // 强制处理附加的数据：空数据和相关推荐
    [self forceHandleAttachDatas];
    if (self.isRecommendBack) {
        [self addCommentFooterWhenNoMoreComment];
    }
    
    return topicCache;
}

- (RACSignal *)requestTopicCache {
    @weakify(self);
    return [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        if ([self commentCount] == 0) {
            [self showCommentLoadingUI:YES withError:nil];
        }
        [[GCDQueue backgroundPriorityGlobalQueue] queueBlock:^{
            @strongify(self);
            TTQ_INSERT_START_DEBUG_TIME
            TTQTopicCache *topicCache = [self requestTopicDataCache];
            self.topicCache = topicCache;
            TTQ_INSERT_END_DEBUG_TIME(@"request cache")
            [subscriber sendNext:topicCache];
            [subscriber sendCompleted];
        }];
        return [RACDisposable disposableWithBlock:^{
            [subscriber sendCompleted];
        }];
    }];
}

- (void)saveOffsetCache:(NSDictionary *)offsetDic {
    if (self.topic.is_followup == 1) {
        [self sendTCPMessage:false];
    }
    
    if (self.topic == nil) {
        [[TTQTopicContentYYCacheManager shareInstance] deleteTopicCacheWithTopicID:self.topic_id];
        return;
    }
    
    // 只看图片不缓存
    if (self.commentFilter == TTQTopicFilterImage) {
        [[TTQTopicContentYYCacheManager shareInstance] deleteTopicPlaceCacheWithTopicID:self.topic_id];
        return;
    }
    
    NSIndexPath *indexPath = offsetDic[@"firstIndexPath"];
    CGFloat contentOffset = [offsetDic[@"contentOffset"] floatValue];
    CGFloat indexPathOffset = [offsetDic[@"indexPathOffset"] floatValue]; //
    BOOL hasTableHead = [offsetDic[@"hasTableHead"] boolValue];
    CGFloat firstSectionHeaderY = [offsetDic[@"firstSectionHeaderY"] floatValue];

    // 缓存数据： 1、位置信息内容；2、帖子详情内容；3、评论列表数据
    TTQTopicCache *cache = [TTQTopicCache new];
    cache.topic_id = self.topic_id;
    cache.menu_index = self.commentFilter;
    cache.orderByFilter = self.datasourceOrderFilter;
    cache.recommendHeaderStartY = self.recommendHeaderStartY;
    cache.hasTableHead = hasTableHead;
    cache.hasTableFoot = !self.automaticallyRefresh;
    cache.automaticallyRefresh = self.automaticallyRefresh;
    cache.isRead = YES;
    cache.subsequentModel = self.subsequentModel;
    // 评论ID
    NSArray *commentids = [self.dataSource valueForKeyPath:@"commentID"];
    if ([commentids.firstObject isKindOfClass:[NSArray class]]) {
        if (commentids.count >= 1) {
            cache.commentids = [[commentids subarrayWithRange:NSMakeRange(1, commentids.count - 1)] componentsJoinedByString:@","];
        }
    } else {
        cache.commentids = [commentids componentsJoinedByString:@","];
    }
    
    // 缓存gotoID -> 1、没有帖子详情内容hasTableHead=NO；2、有帖子详情&&滚动到详情不可见的位置（contentOffset>firstSectionHeaderY）
    BOOL needCacheGotoID = NO;
    if (hasTableHead) {
        if (contentOffset>firstSectionHeaderY) {
            needCacheGotoID = YES;
        }
    } else {
        needCacheGotoID = YES;
    }

    // 处理 gotoID、offset_y 缓存
    if (indexPath && contentOffset > 0 && needCacheGotoID) {
        TTQCommentModel *model = [self tableCellModelAtIndexPath:indexPath];
        if (![model isKindOfClass:[TTQCommentModel class]]) {
            // 列表中包含相关推荐的数据，如果不是评论数据，取最后一条评论
            NSInteger lastCommentIndex = [self lastCommentIndex];
            if (lastCommentIndex < self.dataSource.count) {
                model = self.dataSource[lastCommentIndex];
            }
        }
        if ([model isKindOfClass:[TTQCommentModel class]]) {
            cache.gotoID = model.commentID;
            cache.offset_y = contentOffset - indexPathOffset;
        } else {
            cache.offset_y = contentOffset;
        }
    } else {
        cache.offset_y = contentOffset;
    }
    
    // 问答帖、投票帖不缓存内容
    BOOL needCacheTopicContent = YES;
    if (self.topic.vote != nil || self.topic.is_ask) {
        needCacheTopicContent = NO;
    }
    
    [[TTQTopicContentYYCacheManager shareInstance] storeTopic:needCacheTopicContent ? self.topic : nil
                                                 commentArray:needCacheTopicContent ? self.dataSource : nil
                                                   topicCache:cache
                                                    keyPrefix:self.isUGCUIStlye?@"IMYUGC":nil];
}

#pragma mark - 反馈按钮删除对应的数据
- (void)removeObject:(id)object atIndexPath:(NSIndexPath *)indexPath {
    if (object) {
        if ([object isKindOfClass:TTQTopicModel.class]) {
            TTQTopicModel *topicModel = (TTQTopicModel *)object;
            if (topicModel.listType == TTQTopicDetailListTypeQATopic) {
                // 处理相关问答帖子，位于Header中
                NSMutableArray *tmpRecommendQATopics = self.recommendQATopics.mutableCopy;
                [tmpRecommendQATopics removeObject:topicModel];
                self.recommendQATopics = tmpRecommendQATopics;
                self.feedsSourceChange = YES;
            } else {
                // 处理猜你喜欢、推荐评论帖子，位于列表中
                NSMutableArray *tmpDataSource = self.dataSource.mutableCopy;
                [tmpDataSource removeObject:object];
                self.dataSource = tmpDataSource;
                self.feedsSourceChange = YES;
            }
        } else if ([object isKindOfClass:TTQCommentModel.class]) {
            NSMutableArray *tmpDataSource = self.dataSource.mutableCopy;
            [tmpDataSource removeObject:object];
            self.dataSource = tmpDataSource;
            self.feedsSourceChange = YES;
        }
    }
}

// MARK: - UITableView Datasource
- (NSInteger)numberOfRowsInSection:(NSInteger)section {
    if ([self isArray:section]) {
        return 1;
    } else {
        id data = nil;
        if (section < self.dataSource.count) {
            data = self.dataSource[section];
        }
        if ([data isKindOfClass:TTQCommentModel.class]) {// 主评论+引用评论
            NSInteger count = ((TTQCommentModel *)data).referenceds.count;
            if (count) {
                return count + 1;
            } else {
                return 1;
            }
        } else if ([data isKindOfClass:TTQEmptyCommentModel.class]) {
            return 1;
        } else if ([data isKindOfClass:TTQTopicModel.class]) {
            return 1;
        } else if ([data isKindOfClass:TTQDetailListHeaderModel.class]) {
            return 1;
        }
    }
    return 0;
}

- (id)tableCellModelAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath == nil) {
        return nil;
    }
    if ([self isArray:indexPath.section]) {
        return [(NSArray *)self.dataSource[indexPath.section] firstObject];
    } else {
        if (indexPath.section < self.dataSource.count) {
            TTQCommentModel *model = self.dataSource[indexPath.section];
            if (indexPath.row == 0) {
                return model;
            } else {
                // 子评论数据
                if ([model isKindOfClass:TTQCommentModel.class] && indexPath.row - 1 < model.referenceds.count) {
                    return model.referenceds[indexPath.row - 1];
                }
            }
        }
    }
    return nil;
}

- (TTQCommentModel *)tableCellCommentModelAtIndexPath:(NSIndexPath *)indexPath {
    id model = [self tableCellModelAtIndexPath:indexPath];
    // 因为列表中可能有推荐数据，需要找到合适的评论数据
    while (![model isKindOfClass:TTQCommentModel.class] && indexPath.section > 0) {
        indexPath = [NSIndexPath indexPathForRow:0 inSection:indexPath.section - 1];
        model = [self tableCellModelAtIndexPath:indexPath];
    }
    if ([model isKindOfClass:TTQCommentModel.class]) {
        return model;
    }
    return nil;
}

- (NSString *)identifierRowAtIndexPath:(NSIndexPath *)indexPath {
    id model = [self tableCellModelAtIndexPath:indexPath];
    if ([model isKindOfClass:[NSString class]]) {
        if ([((NSString *)model) isEqualToString:@"loading"] || [((NSString *)model) isEqualToString:@"request_failed_cell"]) {
            return @"TTQDetailCommentLoadingCell";
        } else if ([((NSString *)model) isEqualToString:@"commentfooter"]) {
            return @"TTQDetailCommentFooterCell";
        } else if ([((NSString *)model) isEqualToString:@"moreComment"]) {
            return @"TTQDetailCommentMoreCell";
        }
        return @"TTQTopicScreenPlaceholderCell";
    } else if ([self isArray:indexPath.section]) {
        return @"TTQTopicScreenPlaceholderCell";
    } else if ([model isKindOfClass:[TTQCommentModel class]]){
        TTQCommentModel *commentModel = (TTQCommentModel *)model;
        if (self.keepRowsWhenDeleteComment && commentModel.isDeletedLocal) {
            return @"TTQTopicScreenPlaceholderCell";
        }else if (indexPath.row == 0) {
            if (commentModel.needHighlightWhenAppear) {
                return @"TTQPrimaryCommentCell_highlight";
            }
            return @"TTQPrimaryCommentCell";
        } else {
            if (commentModel.needHighlightWhenAppear) {
                return @"TTQSecondaryCommentCell_highlight";
            }
            return @"TTQSecondaryCommentCell";
        }
    } else if ([model isKindOfClass:[TTQEmptyCommentModel class]]){
        // 空白页
//        if (((TTQEmptyCommentModel *)model).isRealEmpty) {
            return @"TTQTopicCommentEmptyCell";
//        } else {
//            return @"TTQDetailCommentFooterCell";
//        }
    } else if ([model isKindOfClass:[TTQTopicModel class]]){
        TTQTopicModel *topicModel = (TTQTopicModel *)model;
        if (topicModel.listType == TTQTopicDetailListTypeRecommendTopic) {
            if (topicModel.model_type == TTQTopicModelTypeVideo && ![model isValidVoteCard]) {
                return @"TTQNewFirstPageVideoCell";//视频Cell
            }
            // 底部的相关推荐帖子
            return @"TTQNewFirstPageCell";
        } else {
            // 推荐评论
            return @"TTQRecommendCommentCell";
        }
    } else if ([model isKindOfClass:TTQDetailListHeaderModel.class]) {
        // 底部的相关推荐帖子Header
        return @"TTQDetailListCommonHeaderCell";
    }
    return @"TTQTopicScreenPlaceholderCell";
}


// MARK: - Helper

- (BOOL)isArray:(NSInteger)section {
    if (section < self.dataSource.count) {
        return [self.dataSource[section] isKindOfClass:[NSArray class]];
    }
    return NO;
}

- (NSInteger)forum_id {
    if (!_forum_id) {
        _forum_id = self.topic.forum_id;
    }
    return _forum_id;
}

- (void)setForum_id:(NSInteger)forum_id {
    if (forum_id && _forum_id != forum_id) {
        _forum_id = forum_id;
        [NSObject imy_asyncBlock:^{
            TTQForumModel *forumModel = [TTQForumModel forumOfID:self.forum_id];
            if (forumModel) {
                imy_asyncMainBlock(^{
                    self.forum = forumModel;
                });
            } else {
                @weakify(self);
                [[TTQForumHelper getForumByID:self.forum_id] subscribeNext:^(TTQForumModel *forum) {
                    @strongify(self);
                    [forum saveToDB];
                    if (self) {
                        // 这里需要使用点语法，RACObserve(self.viewModel, forum)才会接收到通知
                        self.forum = forum;
                    }
                }];
            }
        } level:IMYQueueLevelDefault];
    }
}

- (void)showCommentLoadingUI:(BOOL)show withError:(NSError *)error {
    // 什么时候展示loading：一开始进页面，没有数据(注意有缓存数据时，不要展示)；切换最新最热；下拉刷新请求
    if (self.isInOrderLoading == show) {
        return;
    }
    self.isInOrderLoading = show;
    if (show) {
        // 产品想在评论区加一个loading的状态，因为原来的代码复杂，已知的最小的改动量就将数据源清空，加上一个loading的数据
        // 但是loading结束，可能并没有数据，原先的数据也丢失了；这里需要保存原来的数据，在loading到空数据的时候(网络问题)，还原原来的数据；
        self.originDataSource = self.dataSource;
        self.dataSource = @[@[@"loading"]];
    } else {
        self.dataSource = self.originDataSource;
        self.originDataSource = nil;
    }
}

- (BOOL)canReply {
    return self.is_ask || !self.topic.join_reply || self.forum.is_joined;
}

- (BOOL)canAccess {
    return self.currentUserInfo.error != 2;
}

- (BOOL)hasBlocked {
    if (self.becomeFirstResponder) { //如果是刚打开页面的第一次弹窗允许弹窗
        return NO;
    }
    return NO;
    return self.currentUserInfo.error == 3 || self.currentUserInfo.error == 2;
}

- (NSUInteger)topicUserID {
    return self.topic.publisher.userID;
}

- (BOOL)is_ask {
    return self.topic.is_ask;
}

- (NSInteger)referenced_id {
    return -1;
}

- (NSInteger)voteIndex {
    if (self.topic.vote && self.topic.vote.is_voted && self.topic.vote.item_type_new == TTQVoteItemTypeTwoItems) {
        TTQVoteItemModel *votedItem = [self.topic.vote.items match:^BOOL(TTQVoteItemModel *element) {
            return element.is_selected;
        }];
        return votedItem == self.topic.vote.items.firstObject? 1: 2;
    }
    return 0;
}

- (TTQTopicReferenceViewModel *)createTopicReferenceViewModel {
    TTQCommentModel *commentModel;
    if ([self isArray:self.selectedReplyIndex.section]) {
        commentModel = [self tableCellModelAtIndexPath:self.selectedReplyIndex];
    } else {
        if (self.selectedReplyIndex.section < self.dataSource.count) {
            commentModel = self.dataSource[self.selectedReplyIndex.section];
        }
    }
    TTQTopicReferenceViewModel *topicReferenceViewModel =
        [[TTQTopicReferenceViewModel alloc] initWithTopicID:self.topic_id
                                              referenced_id:commentModel.commentID];
    topicReferenceViewModel.dataSource = commentModel.referenceds;
    topicReferenceViewModel.topic = self.topic;
    TTQReferenceCommentModel *referenceCommentModel = [[TTQReferenceCommentModel alloc] init];
    referenceCommentModel.ip_region = commentModel.ip_region;
    referenceCommentModel.join_reply = self.forum.join_reply;
    referenceCommentModel.title = self.topic.title;
    referenceCommentModel.topic_forum_id = self.forum_id;
    referenceCommentModel.topic_user_id = self.topicUserID;
    referenceCommentModel.topic_id = self.topic_id;
    referenceCommentModel.content = commentModel.content;
    referenceCommentModel.images = commentModel.images;
    referenceCommentModel.commentID = commentModel.commentID;
    referenceCommentModel.updated_date = commentModel.updated_date;
    referenceCommentModel.publisher = commentModel.publisher;
    referenceCommentModel.floor_no = commentModel.floor_no;
    referenceCommentModel.referenced_num = commentModel.referenced_num;
    referenceCommentModel.privilege = commentModel.privilege;
    referenceCommentModel.praise_num = commentModel.praise_num;
    referenceCommentModel.has_praise = commentModel.has_praise;
    referenceCommentModel.subject_struct = commentModel.subject_struct;
    if (![self isArray:self.selectedReplyIndex.section]) {
        topicReferenceViewModel.commentModel = commentModel;
        if (self.selectedReplyIndex.row > 0) {
            topicReferenceViewModel.selectedReplyIndex = [NSIndexPath indexPathForRow:self.selectedReplyIndex.row - 1 inSection:0];
        }
    }
    topicReferenceViewModel.showTableHeader = true;
    topicReferenceViewModel.referenced = referenceCommentModel;
    topicReferenceViewModel.currentUserInfo = self.currentUserInfo;
    topicReferenceViewModel.from_community_home = self.from_community_home;
    topicReferenceViewModel.orderByFilter = self.orderByFilter;
    topicReferenceViewModel.topic_review_nums = self.topic.total_review;
    topicReferenceViewModel.isUGCUIStlye = self.isUGCUIStlye;
    //要重新计算高端。因为引用信息可能会有变化
    commentModel.cellHeight = 0;
    commentModel.contentLabelHeight = 0;
    return topicReferenceViewModel;
}

//当前评论为空
- (BOOL)isCurFilterDataSourceNil {
    BOOL isDataSourceNil = YES;
    for (id obj in self.dataSource) {
        if ([obj isKindOfClass:TTQCommentModel.class]) {
            if (self.keepRowsWhenDeleteComment && ((TTQCommentModel *)obj).isDeletedLocal) {
                continue; // 如果是被删除的评论，跳过
            }
            isDataSourceNil = NO;
        }
    }
    return isDataSourceNil && !self.isInOrderLoading;
}

//全部评论为空
- (BOOL)isDataSourceNil {
    return [self isCurFilterDataSourceNil] && self.commentFilter == TTQTopicFilterNone;
}

- (BOOL)isShowCommentsSwitch {
    BOOL show = YES;
    show = ![self isDataSourceNil];
    if (!show && self.isReviewError && !(self.hotReviewsCacacheDic == nil && self.latestReviewsCacacheDic == nil)) {
        show = YES;
    }
    return show;
}

-(BOOL)isFoldImgs{
    if ([TTQABTestConfig topicDetailFoldImageExp]) {
        // 除官方账号，其他均折叠
        if (self.topic.publisher.is_official) {
            return NO;
        }else {
            return YES;
        }
    }
    return NO;

}

//修正当页面没有数据的时候， 请求接口失败且无网络的情况下，走请求失败逻辑
- (void)fixDataSourceWhenRequestOccurError:(NSError *)error{
    if (error && ![IMYNetState networkEnable] && [self isCurFilterDataSourceNil]) {
        //无网络无数据的情况下，插入一条加载更多数据
        self.dataSource = @[@[@"request_failed_cell"]];
    }
}

/**
 评论数量
 */
- (NSInteger)commentCount {
    NSInteger commentCount = 0;
    NSArray *tmpDataSource = self.dataSource.copy;
    for (int i = 0; i< tmpDataSource.count; i++) {
        id data = tmpDataSource[i];
        if ([data isKindOfClass:TTQCommentModel.class]) {
            commentCount ++;
        }
    }
    return commentCount;
}
 
- (TTQCommentModel *)lastComment {
    for (int i = (int)self.dataSource.count - 1; i >= 0; i--) {
        TTQCommentModel *data = self.dataSource[i];
        // 跳过用户手动添加的评论，否则会影响分页
        if ([data isKindOfClass: TTQCommentModel.class] && !data.isUserAdded) {
            return data;
        }
    }
    return nil;
}

- (TTQCommentModel *)firstComment {
    for (int i = 0; i < (int)self.dataSource.count; i++) {
        id data = self.dataSource[i];
        if ([data isKindOfClass: TTQCommentModel.class]) {
            return data;
        }
    }
    return nil;
}

- (void)sendTCPMessage:(Boolean)read {
    TCPFollowTopicSendMessage *message = [TCPFollowTopicSendMessage new];
    if (read) {
        message.on_off = 1;
    } else {
        message.on_off = 0;
    }
    message.status_name = [NSString stringWithFormat:@"TpcRd%@", @(self.topic_id)];
    [[TCPClientManager sendMessage:message] subscribeNext:^(TCPSendMessage *x) {
    } error:^(NSError *error){
    }];
}

/**
 最后一条评论的位置
 */
- (NSInteger)lastCommentIndex {
    NSInteger insertCommentIndex = [self insertCommentIndexFromDataSource:self.dataSource];
    if (insertCommentIndex > 0) {
        insertCommentIndex --;
    }
    return insertCommentIndex;
}

- (BOOL)automaticallyRefresh {
    if ([IMYNetState networkEnable]) {
        return [super automaticallyRefresh];
    }
    return NO;
}

- (void)postYouPinBIData:(NSDictionary *)paramsDic {
    NSMutableDictionary *tmpDic = [[NSMutableDictionary alloc] initWithDictionary:paramsDic];
    tmpDic[@"entrance_id"] = @"12";
    tmpDic[@"event"] = @"ypgoods";
    tmpDic[@"label"] = @{
        @"goods_id" : paramsDic[@"goods_id"],
        @"topic_id" : @(self.topic.topic_id)
    };
    [tmpDic removeObjectForKey:@"goods_id"];
    [IMYGAEventHelper postWithPath:@"event" params:[tmpDic copy] headers:nil completed:nil];
}

- (void)postYouPlusBIData:(NSDictionary *)paramsDic {
    NSMutableDictionary *tmpDic = [[NSMutableDictionary alloc] initWithDictionary:paramsDic];
    tmpDic[@"info_type"] = @(12);
    tmpDic[@"info_id"] = @(self.topic.topic_id);
    tmpDic[@"event"] = @"dsq_nrxqy_ggcj";
    [tmpDic imy_setNonNilObject:self.global_track_id forKey:@"global_track_id"];
    [tmpDic imy_setNonNilObject:self.ad_id forKey:@"ad_id"];
    [IMYGAEventHelper postWithPath:@"event" params:[tmpDic copy] headers:nil completed:nil];
}

- (NSString *)totalReviewText {
    return [self countStringWithCount:self.topic.total_review];
}

- (NSString *)countStringWithCount:(NSInteger)count {
    return [NSString stringShowWithCount:count];;
}

- (NSInteger)gotoIDCommenPraiseCount {
    if (self.gotoID < 1) {
        return 0;
    }
    NSInteger praiseCount = 0;
    for (TTQCommentModel *obj in self.dataSource) {
        if ([obj isKindOfClass:TTQCommentModel.class] && obj.commentID == self.gotoID) {
            praiseCount = obj.praise_num;
            break;
        }
    }
    return praiseCount;
}

- (BOOL)isGotoIDCommentPraised {
    if (self.gotoID < 1) {
        return NO;
    }
    for (TTQCommentModel *obj in self.dataSource) {
        if ([obj isKindOfClass:TTQCommentModel.class] && obj.commentID == self.gotoID) {
            return obj.has_praise;
        }
    }
    return NO;
}

#pragma mark - show Toast

- (void)showReviewErrorToast:(NSError *)error isChangeOrderRequest:(BOOL)changeOrderRequest {
    if (!changeOrderRequest) {
        if (![IMYNetState networkEnable]) {
            [UIView imy_showTextHUD:MT_Request_NoNetToast];
        } else {
            IMYWebMessageModel *failModel = [error.af_responseData toModel:[IMYWebMessageModel class]];
            if (failModel.message != nil && imy_isNotEmptyString(failModel.message)) {
                [UIWindow imy_showTextHUD:failModel.message]; // 越权操作的吐司
            }else{
                [UIWindow imy_showTextHUD:@"网络缓慢，请稍后再试"];
            }
        }
    }
}

- (NSDictionary *)activityData {
    NSDictionary *data = nil;
    if (self.resource_list.count) {
        for (NSDictionary *dic in self.resource_list) {
            if ([dic[@"code"] isEqualToString:@"topic_detail_activity"]) {
                data = dic;
                break;
            }
        }
    }
    return data;
}

#pragma mark - 重载父类数据

/// 收藏数
- (NSInteger)favoriteCount {
    return self.topic.favorite_num;
}

- (void)updateFavoriteCount:(NSUInteger)num {
    self.topic.favorite_num = num;
}
/// 点赞数
- (NSInteger)praiseCount {
    return self.topic.praise_num;
}
- (void)updatePraiseCount:(NSInteger)num {
    self.topic.praise_num = num;
}
/// 评论数
- (NSInteger)reviewCount {
    return self.topic.total_review;
}
- (void)updateReviewCount:(NSInteger)num {
    self.topic.total_review = num;
}

- (NSString *)shareImageUrl {
    if (imy_isNotEmptyString(self.shareBody.share_image)) {
        return self.shareBody.share_image;
    }
    if (self.topic.images.count) {
        return self.topic.images.firstObject;
    }
    return nil;
}

/// 评论点赞/// 处理评论点赞操作
/// - Parameters:
///   - model: TTQCommentModel
///   - isPraise: 是否点赞, YES为点赞，NO为取消点赞
///   - completionBlk: 操作完成回调，返回结果和错误信息
- (void)handleCommentPraiseWithModel:(TTQCommentModel *)model
                            isPraise:(BOOL)isPraise
                       completionBlk:(void(^)(id <IMYHTTPResponse>resData, NSError * error))completionBlk  {
    
    [super handleCommentPraiseWithModel:model isPraise:isPraise completionBlk:^(id <IMYHTTPResponse>resData, NSError * error) {
        if (self.from_community_home > 0 && isPraise) {
            [TTQCommonHelp GAEventForEventWithName:@"ttq_tzxqy_dzpl" action:2];//点赞埋点
        }
        
        NSNumber *score = resData.responseObject[@"score"];
        if (score.integerValue) {
            //7.6.0
            [[TTQNewbeeTaskManager sharedManager] showTipWithMessage:[NSString stringWithFormat:@"恭喜完成点赞帖子任务 贡献值+%@",score]];
        }

        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        params[@"event"] = @"dsq_nrxqy_dz";
        params[@"action"] = @2;
        params[@"info_type"] = @12;
        params[@"info_id"] = @(model.topic_id);
        params[@"fuid"] = @(self.topicUserID);
        params[@"public_type"] = isPraise ? @21 : @22;
        params[@"comment_uid"] = @(model.publisher.userID);
        params[@"comment_id"] = @(model.commentID);
        params[@"public_info"] = @"内容详情页";
        params[@"is_on_period"] = @([IMYUGCEventHelper userIsInPhysiological]);
        params[@"redirect_url"] = [IMYUGCEventHelper currentPageRedirectUrl];
        
        [IMYGAEventHelper postWithPath:@"event" params:params.copy headers:nil completed:nil];
        !completionBlk ?: completionBlk(resData, error);
    }];
}
@end

