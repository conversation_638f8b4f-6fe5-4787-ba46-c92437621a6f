//
//  TTQTopicReferenceViewModel.h
//  IMYTTQ
//
//  Created by king on 15/7/16.
//  Copyright © 2015年 MeiYou. All rights reserved.
//

#import "TTQTopicBaseViewModel.h"
#import "TTQReferenceCommentModel.h"
#import "TTQTopicModel.h"

@interface TTQTopicReferenceViewModel : TTQTopicBaseViewModel

//从话题详情才会传过来的.为了回退的时候能进行刷新
@property (nonatomic, strong) TTQCommentModel *commentModel;
@property (nonatomic, strong) TTQTopicModel *topic;
// 评论回调的block，不依赖commentModel属性
@property (nonatomic, copy) void(^commentResultBlock)(BOOL has_praise, NSInteger praise_num, NSInteger referenced_num, NSInteger addNum, NSArray *commentDataDictArray);
@property (nonatomic, strong) TTQReferenceCommentModel *referenced;
@property (nonatomic, assign) TTQCommentBindTopicType topicType;
@property (nonatomic, assign) BOOL isNewCommuity;///新社区
@property (nonatomic, strong) NSIndexPath *subCommentIndexPath;
@property (nonatomic, copy) NSString *redirect_url;
@property (nonatomic, copy) void (^youPlusBIBlock)(NSDictionary *data);
// 请求条数
@property (nonatomic, assign) NSInteger requestSize;
/// 帖子的评论数量，埋点要用
@property (nonatomic, assign) NSInteger topic_review_nums;
/// 禁止回复
@property (nonatomic, assign) BOOL is_close_comment;
@property (nonatomic, assign) BOOL bi_isRelyFromCommentIcon;///< 是否从评论icon点击进入
/// 跳转至详情页要展示二级评论
@property (nonatomic, assign) NSInteger animationCommentIdWhenAppear;

@property (nonatomic, assign) NSInteger forceTopCommentId;
@property (nonatomic, assign) BOOL isUGCUIStlye;
@property (nonatomic, assign) BOOL isCommentDetail;

@property (nonatomic, copy) void (^dataSourceChangeBlock)(void);

- (instancetype)initWithTopicID:(NSInteger)topic_id referenced_id:(NSInteger)referenced_id;

- (void)preSaveUserAddedComments;

- (NSInteger)commentCount;

@end
