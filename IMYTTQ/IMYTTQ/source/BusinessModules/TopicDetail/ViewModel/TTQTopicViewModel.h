//
// Created by <PERSON> on 15/5/7.
// Copyright (c) 2015 Mei<PERSON><PERSON>. All rights reserved.
//

#import "TTQCommentModel.h"
#import "TTQTopicBaseViewModel.h"
#import "TTQTopicModel.h"
#import "TTQViewModel.h"
#import "TTQVoteModel.h"
#import "TTQRecommendTopicModel.h"
#import <Foundation/Foundation.h>
#import "TTQSharedObject.h"
#import "TTQTopicRichParserModel.h"
#import "IMYTTQDetailRecommendViewModel.h"
#import <IMYUGC/IMYSubsequentModel.h>

typedef void (^TTQTopicViewModelDataSourceBlock)(void);

@class TTQTopicReferenceViewModel, IMYVideoModel, TTQHome5TopicModel, TTQTopicCache;

@interface TTQTopicViewModel : TTQTopicBaseViewModel
 
@property (nonatomic, assign) BOOL isUGCUIStlye;// 默认她她圈图文样式，yes为大社区短图文样式
@property (nonatomic, copy) TTQTopicViewModelDataSourceBlock dataSourceChangeBlock;
@property (nonatomic, strong) TTQForumModel *forum;
@property (nonatomic, strong) TTQTopicModel *topic;
@property (nonatomic, strong) NSArray<TTQShoppingPlugin *> *shopPlugins; // 柚+商品卡片
@property (nonatomic, strong) TTQShareBodyModel *shareBody;
@property (nonatomic, strong) NSArray<TTQTopicModel *> *recommendQATopics;
@property (nonatomic, copy) NSArray<TTQTopicRichParserTagModel *> *recommendTags;
@property (nonatomic, assign) CGFloat hasViewMaxRange;//浏览过的最大内容高度 BI上报
@property (nonatomic, assign) CGFloat hasReachMaxRange;//浏览过的最大高度 BI上报
@property (nonatomic, copy) NSString *redirect_url;
@property (nonatomic, assign) NSInteger needPopToTop;//需要直接返回顶部
@property (nonatomic, assign) BOOL isFromShipin; // 是否为全屏流点击评论按钮进入
@property (nonatomic, assign) BOOL isRead;// 是否浏览过
@property (nonatomic, assign, readonly) BOOL isHitRecommendTest;// 是否命中推荐实验
@property (nonatomic, assign, readonly) BOOL isHitFoldCommentTest;    /// 是否命中折叠
@property (nonatomic, assign, readonly) BOOL isCommentFolding;// 评论当前是否折叠，未返回推荐时，即时命中折叠也不会是折叠样式
@property (nonatomic, assign) BOOL isUsingCacheData;// 标志是否正在使用缓存数据
@property (nonatomic, assign) NSInteger ad_position; // page bi埋点 广告位
// 推荐接口拆分相关字段
@property (nonatomic, assign) BOOL isRecommendBack; // 相关推荐是否已经请求过
@property (nonatomic, assign) BOOL isRequestNext; // 相关推荐未完成请求前是否请求下一刷，记录等推荐结果返回再请求
// 进来的时候请求推荐footer一直保持loading回调
@property (nonatomic, copy) void (^footerLoadingBlock)(BOOL loading);
@property (nonatomic, copy) void (^footerLoadMoreBlock)();
// 广告赏金任务
@property (nonatomic, assign) NSInteger  taskID;
@property (nonatomic, assign) NSInteger  taskType;
/**
 用于处理负反馈从界面上移除Cell
 */
@property (nonatomic, assign) BOOL feedsSourceChange;

@property (nonatomic, assign) TTQOrderByFilter datasourceOrderFilter; //正在展示的数据的排序

/// 缓存数据
@property (nonatomic, strong, readonly) TTQTopicCache *topicCache;

//收藏
@property (nonatomic, strong) RACCommand *favoriteCommand;
@property (nonatomic, assign) TTQPraiseType praiseType;
@property (nonatomic, assign) NSInteger entrance; //来源，统计要用-无语 1首页，2她她圈首页，3视频流 9、发现页
@property (nonatomic, assign) NSInteger channelID;


@property (nonatomic, assign) BOOL bi_isRelyFromCommentIcon;///< 是否是从评论 icon 触发的评论回复

/**
 bi_ttqvideoplay埋点位置上报使用到的，如果该值有内容则上报，否则使用entrance字段的值
 视频曝光位置：1 首页；  2 她她圈首页；  3 圈子列表；  4 除首页、她她圈首页、圈子列表外，其他地方均上报4（如个人主页、收藏、历史、消息）
 */
@property (nonatomic, assign) NSInteger bi_video_play_entrance;

/**
 埋点参数：频道id，73：关注；74：推荐;   80: 圈子
 */
@property (nonatomic, assign) NSInteger bi_catid;

/* 上次看到的偏移量，使用情况：
 1.有网络的情况下：不跳转到楼层或者某一个ID（因为跳转会获取ID或者楼层前后20条数据，跟缓存情况会有出入）
 2.无网络的情况下
 */
@property (nonatomic, assign) CGFloat offset_y;
/**
 猜你喜欢Header的开始位置，猜你喜欢Header滚动到顶部的时候需要隐藏评论切换的Tab
 recommendHeaderStartY值在TableView的Cell回调方法中获取
 */
@property (nonatomic, assign) CGFloat recommendHeaderStartY;
/**
 话题活动
 */
@property (nonatomic, strong) TTQTopicActivity *topic_activity;

/**
 加载更多评论的标记位
 */
@property (nonatomic, assign) BOOL isLoadingMoreComments;


///page 埋点用的
@property (nonatomic, copy) NSString *search_key;

//790操作帖子
@property (nonatomic, assign) NSInteger is_user_operate;//0普通 1圈主 2运营人员

// 相关推荐，新版的相关推荐的逻辑都放在`IMYTTQDetailRecommendViewModel`中
// 这部分逻辑和资讯模块的资讯详情页底部的`相关推荐`基本是一致的，存在一些公用类，但是现在有两套代码
@property (nonatomic, assign) BOOL isFromRecommend;
@property (nonatomic, strong) IMYTTQDetailRecommendViewModel *recommendVM;

// 辅助参数
@property (nonatomic, assign) BOOL dataSourceContainPlaceHolder;


@property (nonatomic, assign, readonly) BOOL isReviewError;// 评论接口服务端返回失败
@property (nonatomic, strong, readonly) NSError *reviewError;// 评论接口返回的失败
@property (nonatomic, assign, readonly) BOOL isReviewPhpError;// 评论接口服务端返回失败

@property (nonatomic, assign) BOOL is_videoTopic;   /// 是否视频帖，详情页里无法判断，单独用这个

@property (nonatomic, assign) NSInteger main_total_review; //一级评论总数量，取接口/v5/article_review_list

@property (nonatomic,copy) NSString *global_track_id; //柚+广告
@property (nonatomic,copy) NSString *ad_id; //柚+广告
/*
 跳转至详情页需要做高亮显示，并做消失动画的主评论id。目前用于暖评跳转   addby linyf 8.64
 */
@property (nonatomic, assign) NSInteger animationCommentIdWhenAppear;
/// 从列表带进来的，作为第一张图片的placeholder，让详情页加载看起来更快点
@property (nonatomic, copy) NSString *firstImagePlaceholder;

/// 协议里带的广告业务参数，在请求帖子详情接口时，如果值1，就写入在refer字段
@property (nonatomic, assign) NSInteger is_from_ad;
/// 蹲蹲
@property (nonatomic, strong) IMYSubsequentModel *subsequentModel;
@property (nonatomic, strong) TTQTopicQuickAccess *quickAccessModel; ///< 快速问诊

@property (nonatomic, strong) NSArray *resource_list;  /// 资源位 这里的数据不固定，详见 https://apidoc.seeyouyima.com/doc/6094938232b0231e1ccfb94e
@property (nonatomic, assign) NSInteger sub_comment_id;///<美柚890新增【子评论id】跳楼，带了这个id，goto必须要带, 外部传递进来

-(BOOL)checkTopicValidate;
/**
 使用topicID初始化
 */
- (instancetype)initWithTopicID:(NSInteger)topic_id;
/**
 请求缓存
 */
- (RACSignal *)requestTopicCache;
/**
 保存缓存数据
 */
- (void)saveOffsetCache:(NSDictionary *)offsetDic;

/// 清除数据，帖子删除需要用到
- (void)clearData;

/**
 点赞
 @param param 参数
 @param completion YES or NO
 */
- (void)doPriaseWithParam:(NSDictionary *)param completion:(void(^)(id  resData, NSError * error))completion;
/**
 投票
 @param topicModel 投票对应的帖子
 @param selectedItems 投票
 @param biParams BI埋点参数
 */
- (void)requestVoteWithTopicModel:(TTQTopicModel *)topicModel selectedItems:(NSArray *)selectedItems biParams:(NSDictionary *)biParams;;

/// 加载二级评论接口+加载更多
/// - Parameters:
///   - indexPath:
///   - count: 请求个数
- (RACSignal *)requestSecondaryCommentsWithIndexPath:(NSIndexPath *)indexPath count:(NSInteger)sizeCount;

/// 删除评论， 支持子评论
/// @param indexPath indexPath
- (RACSignal *)deleteCommentAtIndexPath:(NSIndexPath *)indexPath;

/**
 验证帖子是否可编辑
 @param param 参数
 @param completion YES or NO
 */
- (void)checkEditableWithParam:(NSDictionary *)param completion:(void(^)(id isModified, id resData, NSError * error))completion;
- (void)checkNotesEditableWithParam:(NSDictionary *)param completion:(void(^)(id isModified, id resData, NSError * error))completion;
/**
 进入评论详情页创建一个TTQTopicReferenceViewModel引用，方便用于模块之间使用简单的JSON数据类型传递参数以及处理回调
 */
- (TTQTopicReferenceViewModel *)createTopicReferenceViewModel;
/**
 当前评论为空
 */
- (BOOL)isCurFilterDataSourceNil;
/**
 全部评论为空
 */
- (BOOL)isDataSourceNil;
/**
 评论数量
 */
- (NSInteger)commentCount;
/**
 获取indexPath位置的评论数据模型，在缓存评论位置的场景中因为需要获取合适的indexpath对应的评论数据需要使用到
 */
- (TTQCommentModel *)tableCellCommentModelAtIndexPath:(NSIndexPath *)indexPath;

/**
 强制处理附加的数据：空数据和相关推荐
 */
- (void)forceHandleAttachDatas;


//修正当页面没有数据的时候， 请求接口失败且无网络的情况下，走请求失败逻辑
- (void)fixDataSourceWhenRequestOccurError:(NSError *)error;

/**
 发送TCP消息
 */
- (void)sendTCPMessage:(Boolean)read;

/**
 最后一条评论的位置
 */
- (NSInteger)lastCommentIndex;
- (CGFloat)getContentLabelHeightByModel:(TTQCommentModel*)model;

/**
 优品埋点
 */
- (void)postYouPinBIData:(NSDictionary *)dic;

- (NSString *)totalReviewText;

- (void)showCommentLoadingUI:(BOOL)show withError:(NSError *)error;

- (void)postYouPlusBIData:(NSDictionary *)dic;

- (BOOL)stopDeliverReviewError;
/**
 一些其他逻辑
 */
- (BOOL)isShowCommentsSwitch;
/**
 是折叠图片实验+非官方账号（不走定位）
 */
- (BOOL)isFoldImgs;
/**
 评论区曝光后去请求相关推荐接口数据
 */
- (void)requestRecommendData;
/// 更新暖评的点赞数
- (NSInteger)gotoIDCommenPraiseCount;
/// 更新暖评是否点赞
- (BOOL)isGotoIDCommentPraised;

- (void)addCommentFooterWhenNoMoreComment;
/// PK类型下的投票选项
- (NSInteger)voteIndex;

- (NSDictionary *)activityData;

@end
