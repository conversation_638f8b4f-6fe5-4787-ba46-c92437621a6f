//
//  TTQTopicReferenceViewModel.m
//  IMYTTQ
//
//  Created by king on 15/7/16.
//  Copyright © 2015年 MeiYou. All rights reserved.
//

#import "TTQTopicReferenceViewModel.h"
#import "TTQForumModel.h"
#import "NSString+TTQ.h"
#import "TTQHttpHelper.h"
#import "TTQABTestConfig.h"
#import "TTQDoorConfig.h"
#import <IMYBaseKit/IMYViewKit.h>
#import <IMYUGC/IMYUGCEventHelper.h>

@interface TTQTopicReferenceViewModel()
@property (nonatomic, strong) NSMutableArray *userAddedComments;
@property (nonatomic, assign) BOOL isFirstRequest;
@end

@implementation TTQTopicReferenceViewModel
@synthesize forum_id = _forum_id;
@synthesize replyCommand = _replyCommand;

- (instancetype)init {
    self = [super init];
    if (self) {
        self.isFirstRequest = YES;
        self.topicType = TTQCommentBindTopicTypeUnknown;
        self.requestSize = 20;
    }
    return self;
}

- (instancetype)initWithTopicID:(NSInteger)topic_id referenced_id:(NSInteger)referenced_id {
    if (self = [self init]) {
        self.topic_id = topic_id;
        self.referenced_id = referenced_id;
    }
    return self;
}

- (BOOL)contentReady {
    return self.referenced != nil;
}

- (void)preSaveUserAddedComments {
    self.userAddedComments = [NSMutableArray new];
    for (TTQCommentModel *comment in self.dataSource) {
        if ([comment isKindOfClass:TTQCommentModel.class] && comment.isUserAdded) {
            [self.userAddedComments addObject:comment];
        }
    }
}
// 请求接口
///type: 0 下拉刷新，1 加载下一页，2 加载上一页,3 跳楼
- (RACSignal *)requestRemoteDataForType:(NSInteger)type params:(NSDictionary *)parameters {
    @weakify(self);
    NSMutableDictionary * params = parameters.mutableCopy;
    if (self.commentModel || self.isFromMyReply) {
        params[@"order_by"] = @"reviewed_date";
    }
    params[@"size"] = @(self.requestSize);
    params[@"topic_id"] = @(self.topic_id);
    params[@"referenced_id"] = @(self.referenced_id);
    if (type == 1) {
        TTQCommentModel *comment = self.dataSource.lastObject;
        params[@"last"] = @(comment.commentID);
        params[@"load_direction"] = @"next";
    } else if (type == 2) {
        TTQCommentModel *comment = self.dataSource.firstObject;
        params[@"last"] = @(comment.commentID);
        params[@"load_direction"] =  @"prev";
    } else if (type == 3) {
        params[@"goto"] = @(self.gotoID);
    } else if (type == 0) {
        if (self.animationCommentIdWhenAppear) {
            params[@"top_review_id"] = @(self.animationCommentIdWhenAppear);
        }
    }
    if ([TTQDoorConfig revealNumConfig] > 0) {
        params[@"reveal_num"] = @([TTQDoorConfig revealNumConfig]);
    }
    IMYHTTPBuildable *paramBuild = [IMYServerRequest get:@"v2/stacke_review_detail" host:circle_seeyouyima_com params:params headers:nil];
    RACSignal *reviewDetailSignal = paramBuild.signal;
    return [[reviewDetailSignal doNext:^(id<IMYHTTPResponse> response) {
        @strongify(self);
        NSArray<TTQCommentModel *> *referenceds = [response.responseObject[@"references"] toModels:[TTQCommentModel class]];
        self.lastResType = -1;
        // 如果有强插的数据需要去重
        BOOL containsUserAddedData = NO;
        if (type == 0 && self.userAddedComments && self.isFirstRequest) {
            containsUserAddedData = YES;
        }
        if (type != 0 && self.dataSource.firstObject == self.userAddedComments.firstObject) {
            containsUserAddedData = YES;
        }
        if (containsUserAddedData) {
            referenceds = [referenceds bk_select:^BOOL(TTQCommentModel *obj) {
                for (TTQCommentModel *userAddedComment in self.userAddedComments) {
                    if (obj.commentID == userAddedComment.commentID) {
                        return NO;
                    }
                }
                return YES;
            }];
        }
        if (self.animationCommentIdWhenAppear > 0) {
            [referenceds match:^BOOL(TTQCommentModel *element) {
                if (element.commentID == self.animationCommentIdWhenAppear) {
                    element.needHighlightWhenAppear = YES;
                    element.isSetToTop = YES;
                    return YES;
                }
                return NO;
            }];
            self.animationCommentIdWhenAppear = 0;
        }

        if (response.responseObject[@"user_info"]) {
            TTQTopicCurrentUserInfo *currentUserInfo = [response.responseObject[@"user_info"] toModel:[TTQTopicCurrentUserInfo class]];
            self.currentUserInfo = currentUserInfo;
        }
        self.lastResType = type;
        self.lastCommentCount = referenceds.count;
        switch (type) {
            case 0: {
                // 强插数据
                NSMutableArray* dataSource = [NSMutableArray array];
                if (self.isFirstRequest) {
                    if (self.userAddedComments) {
                        [dataSource addObjectsFromArray:self.userAddedComments];
                    }
                    self.isFirstRequest = NO;
                }
                if (referenceds) {
                    [dataSource addObjectsFromArray:referenceds];
                }
                self.dataSource = dataSource;
                self.lastCommentCount = self.dataSource.count;
                self.referenced = [response.responseObject[@"review"] toModel:[TTQReferenceCommentModel class]];
                self.referenced.type = self.topicType;
                self.referenced.topic = [response.responseObject[@"topic"] toModel:[TTQTopicModel class]];
                self.shareBody = [response.responseObject[@"share_body"] toModel:[TTQShareBodyModel class]];
                self.showTableHeader = true;
                self.automaticallyRefresh = referenceds.count >= self.requestSize;
                break;
            }
            case 1:
                if (referenceds.count) {
                    NSMutableArray *array = [[NSMutableArray alloc] initWithArray:self.dataSource];
                    [array addObjectsFromArray:referenceds];
                    TTQCommentModel *fistComment = self.dataSource.firstObject;
                    if ([fistComment isKindOfClass:TTQCommentModel.class] && fistComment.isSetToTop) {
                        array = [array filter:^BOOL(TTQCommentModel * element) {
                            if (element.commentID == fistComment.commentID && !element.isSetToTop) {
                                return NO;
                            }
                            return YES;
                        }];
                    }
                    self.lastCommentCount = array.count - self.dataSource.count;
                    self.dataSource = array;
                }
                self.automaticallyRefresh = referenceds.count >= self.requestSize;
                break;
            case 2:
                if (referenceds.count) {
                    NSMutableArray *array = referenceds.mutableCopy;
                    [array addObjectsFromArray:self.dataSource];
                    self.dataSource = array;
                }
                self.lastCommentCount = self.dataSource.count;
                self.showTableHeader = referenceds.count < self.requestSize;
                if (self.showTableHeader) {
                    self.gotoID = -1;
                }
                break;
            case 3:
                self.dataSource = referenceds;
                self.referenced = [response.responseObject[@"review"] toModel:[TTQReferenceCommentModel class]];
                self.referenced.type = self.topicType;
                self.showTableHeader = referenceds.count < self.requestSize;
                break;
            default:
                break;
        }
        if (self.inputDefaultText == nil) {
            self.inputDefaultText = nil;
        }
    }] doError:^(NSError * _Nonnull error) {
        self.lastResType = -1;
    }];
}
- (RACCommand *)replyCommand {
    if (_replyCommand == nil) {
        @weakify(self);
        _replyCommand = [RACCommand commandWithSignalBlock:^RACSignal *(NSArray *array) {
            @strongify(self);
            NSMutableDictionary *params = array.firstObject;
            ProgressCallback progressBlock = nil;
            if (array.count > 1) {
                progressBlock = array[1];
            }
            TTQCommentModel *model = [self tableCellModelAtIndexPath:self.selectedReplyIndex];
            if (model) {
                params[@"referenced_id"] = @(model.commentID);
                params[@"parent_referenced_id"] = @(self.referenced_id);
            } else {
                params[@"referenced_id"] = @(self.referenced_id);
                params[@"parent_referenced_id"] = @(self.referenced_id);
            }
            TTQCommentModel *comment = self.dataSource.lastObject;
            params[@"last"] = @(0);
            params[@"diff_data"] = @(YES);
            params[@"topic_id"] = @(self.topic_id);
            NSString *content = params[@"content"];
            if ([content isKindOfClass:NSString.class] && content.length) {
                params[@"content_size"] = @(content.ttq_textLength);
            }
            NSString *biURI = @"";
            if (self.redirect_url.length && [self.redirect_url containsString:@"params="]) {
                NSArray *urlArray = [self.redirect_url componentsSeparatedByString:@"params="];
                if (urlArray.count > 1) {
                    biURI = urlArray[1];
                }
            }
            if (biURI.length) {
                params[@"bi_uri"] = biURI;
            }
            @weakify(model);
            NSString *path = @"v2/topic_review";
            return [[TTQHttpHelper postPath:path params:params progressBlock:progressBlock] doNext:^(id<IMYHTTPResponse> response) {
                @strongify(self,model);
                [IMYEventHelper event:@"plxq-hf"];
                NSDictionary *dic = response.responseObject[@"reviews"];
                
                if (!dic) {
                    dic = response.responseObject[@"data"][@"reviews"];
                }
                if (self.from_community_home) {
                    [TTQCommonHelp GAEventForEventWithName:@"ttq_tzplxqy_hfpl" action:2];
                }
                NSArray *comments = [dic toModels:[TTQCommentModel class]];
                if (comments) {
                    TTQCommentModel *commentData = comments.firstObject;
                    if ([self voteIndex]) {
                        /// 服务端不返回投票信息，需要自己实时做
                        commentData.voteType = 1;
                        commentData.votedIndexData = @[@([self voteIndex])];
                    }
                    if (self.dataSource == nil) {
                        self.dataSource = comments;
                    } else {
                        NSMutableArray *array = self.dataSource.mutableCopy;
                        if (self.commentModel || self.isFromMyReply) {
                            [array insertObjects:comments atIndexes:[NSIndexSet indexSetWithIndexesInRange:NSMakeRange(0, comments.count)]];
                        } else {
                            [array addObjectsFromArray:comments];
                        }
                        self.dataSource = array;
                    }
                    self.referenced.referenced_num += 1;
                    if (self.referenced.referenced_num < self.dataSource.count) {
                        self.referenced.referenced_num = self.dataSource.count;
                    }
                }
                NSInteger addNum = self.referenced.referenced_num - self.commentModel.referenced_num;
                NSString *pageInfo = self.bi_pageSource?self.bi_pageSource:@"内容详情页";
                TTQCommentModel *biComment = model?:self.referenced;
                NSMutableDictionary *gaDic = [@{@"event":@"dsq_nrxqy_pl",@"action":@2,@"public_type":@2,@"info_type":@12,@"info_id":@(self.topic_id),@"fuid":@(self.topicUserID),@"comment_uid":@(biComment.publisher.userID),@"comment_id":@(biComment.commentID),@"interact_num":@(self.topic_review_nums + addNum - 1),@"public_info":pageInfo,@"is_on_period":@([IMYUGCEventHelper userIsInPhysiological]),@"redirect_url":[IMYUGCEventHelper currentPageRedirectUrl]} mutableCopy];
                if (!self.selectedReplyIndex) {
                    gaDic[@"public_key"] = self.bi_isRelyFromCommentIcon ? @2 : @1;
                }
                if ([[UIViewController imy_currentViewControlloer] isKindOfClass:NSClassFromString(@"TTQCommentDetailViewController")]) {
                    if (!model) {
                        /// 评论详情页回主楼
                        gaDic[@"public_type"] = @1;
                    }
                }
                [IMYGAEventHelper postWithPath:@"event" params:gaDic headers:nil completed:nil];
            }];
        }];
    }
    return _replyCommand;
}

- (BOOL)canReply {
    return self.is_ask || !self.referenced.join_reply || [TTQForumModel forumOfID:self.forum_id].is_joined;
}

- (BOOL)canAccess {
    return self.currentUserInfo.error != 2;
}

- (BOOL)hasBlocked {
    return NO;
    return self.currentUserInfo.error == 3 || self.currentUserInfo.error == 2;
}

- (NSInteger)forum_id {
    if (!_forum_id) {
        _forum_id = self.referenced.topic_forum_id;
    }
    return _forum_id;
}

- (NSUInteger)topicUserID {
    return self.referenced.topic_user_id > 0 ? self.referenced.topic_user_id : [TTQPublisherModel anonymousUserID];
}

- (BOOL)is_ask {
    return self.referenced.is_ask;
}

- (NSInteger)numberOfSections {
    return 1;
}
- (NSInteger)numberOfRowsInSection:(NSInteger)section {
    return self.dataSource.count;
}
- (id)tableCellModelAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath == nil) {
        return nil;
    }
    if (self.isCommentDetail) {
        return [self.dataSource imy_objectAtIndex:indexPath.section];
    } else {
        if (indexPath.row < self.dataSource.count) {
            return self.dataSource[indexPath.row];
        }
    }
    return nil;
}

- (void)setDataSource:(NSArray *)dataSource {
    [super setDataSource:dataSource];
    if (self.dataSourceChangeBlock) {
        self.dataSourceChangeBlock();
    }
}

/**
 评论数量
 */
- (NSInteger)commentCount {
    NSInteger commentCount = 0;
    NSArray *tmpDataSource = self.dataSource.copy;
    for (int i = 0; i< tmpDataSource.count; i++) {
        id data = tmpDataSource[i];
        if ([data isKindOfClass:TTQCommentModel.class]) {
            commentCount ++;
        }
    }
    return commentCount;
}

- (NSString *)identifierRowAtIndexPath:(NSIndexPath *)indexPath {
    return @"TTQTopicReferenceCell";
}

- (NSInteger)voteIndex {
    if (self.topic.vote && self.topic.vote.is_voted && self.topic.vote.item_type_new == TTQVoteItemTypeTwoItems) {
        TTQVoteItemModel *votedItem = [self.topic.vote.items match:^BOOL(TTQVoteItemModel *element) {
            return element.is_selected;
        }];
        return votedItem == self.topic.vote.items.firstObject? 1: 2;
    }
    return 0;
}

@end
