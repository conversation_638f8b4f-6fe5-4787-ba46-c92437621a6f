//
//  TTQTopics848ViewController.m
//  IMYTTQ
//
//  Created by ltj on 2022/10/14.
//

#import "TTQTopics848ViewController.h"
#import <IMYBaseKit/IMYViewKit.h>
#import <IMYBaseKit/IMYVKNestScrollView.h>
#import <IMYCommonKit/IMYCRefreshHeader.h>
#import "TTQTopicsListViewController.h"
#import "TTQCircleHeaderView.h"
#import "TTQCircleSegmentView.h"
#import "TTQTopicsListViewController.h"
#import "TTQHome5TopicModel.h"
#import "TTQCircleTalentCell.h"
#import "TTQCircleTopCell.h"
#import "TTQDesiresCell.h"
#import "TTQTopicsEmptyCell.h"
#import "TTQTopicsEmptyModel.h"
#import "TTQVideoPostNavigationButton.h"
#import "TTQTopicsTabModel.h"
#import "TTQMessageTopButton.h"
#import "TTQABTestConfig.h"

#import "TTQForumInfo849ViewController.h"
#import "TTQClickReportModel.h"
#import "GCDObjC.h"

#import <IMYCommonKit/IMYCKSearchHelper.h>
#import <IMYUGC/IMYUGCTaskManager.h>

@interface UITableViewCircle : UITableView
@property (nonatomic, copy) void (^reloadDataCompletionBlock)(void);
@end

@implementation UITableViewCircle

- (void)layoutSubviews
{
    [super layoutSubviews];

    if (self.reloadDataCompletionBlock) {
        self.reloadDataCompletionBlock();
    }
}

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldRecognizeSimultaneouslyWithGestureRecognizer:(UIGestureRecognizer *)otherGestureRecognizer {
    if (otherGestureRecognizer.view == self) {
        return NO;
    }
    
    // Ignore other gesture than pan
    if (![gestureRecognizer isKindOfClass:[UIPanGestureRecognizer class]]) {
        return NO;
    }
    
    // Lock horizontal pan gesture.
    CGPoint velocity = [(UIPanGestureRecognizer*)gestureRecognizer velocityInView:self];
    if (fabs(velocity.x) > fabs(velocity.y)) {
        return NO;
    }
    
    // Consider scroll view pan only
    if (![otherGestureRecognizer.view isKindOfClass:[UIScrollView class]]) {
        return NO;
    }
    
    UIScrollView *scrollView = (id)otherGestureRecognizer.view;
    
    // Tricky case: UITableViewWrapperView
    if ([scrollView.superview isKindOfClass:[UITableView class]]) {
        return NO;
    }
    //tableview on the MXScrollView
    NSString *className = NSStringFromClass(scrollView.superview.class);
    if ([className hasPrefix:@"UITableViewCell"] && [className hasSuffix:@"llContentView"]) {
        return NO;
    }
    
    return YES;
}

@end

@interface TTQTopics848ViewController ()<UIScrollViewDelegate, IMYPageViewControllerDelegate, IMYPageViewControllerDataSource, UITableViewDataSource, UITableViewDelegate>

@property (nonatomic, assign) NSInteger showHotTopicIndex;
// 视图
@property (nonatomic, strong) IMYAvatarImageView *avatar;
@property (nonatomic, strong) UILabel *nameLabel;
@property (nonatomic, strong) UILabel *titleView;
@property (nonatomic, strong) IMYTouchEXButton *leftBtn;
@property (nonatomic, strong) IMYTouchEXButton *searchBtn;
@property (nonatomic, strong) IMYBadgeView *badge;
@property (nonatomic, strong) UIView  *navbarView;
@property (nonatomic, strong) UIView  *bgView;
@property (nonatomic, strong) CAGradientLayer *gradientLayer;
@property (nonatomic, strong) UIScrollView *pageScrollView;
@property (nonatomic, strong) TTQCircleHeaderView *headerView;
@property (nonatomic, strong) TTQCircleSegmentView *segmentView;

@property (nonatomic, strong) UITableViewCircle *tableView;

@property (nonatomic, strong) IMYPageViewController *pageViewController;

@property (nonatomic, strong) IMYCaptionView *captionView;
@property (nonatomic, strong) IMYButton *publishButton;

@property (nonatomic, strong) UIColor *forumColor; // 圈子主题色
@property (nonatomic, strong) IMYCKSearchHelper *searchHelper;
// status相关参数
@property (nonatomic, assign) BOOL isOnTop;
@property (nonatomic, assign) BOOL innerViewCanScroll;
@property (nonatomic, assign) BOOL outterViewCanScroll;
@property (nonatomic, assign) BOOL hasUpdatedSegment;
@property (nonatomic, assign) BOOL hasJumped;
@property (nonatomic, assign) BOOL showDesireLine;
@property (nonatomic, assign) BOOL desireBGClear;
@property (nonatomic, assign) BOOL firstRefreshAd;

@property (nonatomic, assign) BOOL firstLoadForum;
// data
@property (nonatomic, strong) NSMutableDictionary<NSNumber *,TTQTopicsListViewController *> *controllers;
@property (nonatomic, assign) NSInteger msgCount;

// 视频相关参数

// 广告参数
@property (nonatomic, strong) id<IMYIAdapterAdManager> adManager; //圈子头部广告
@property (nonatomic, strong) id<IMYITableViewAdManager> sideAdManager; //贴边广告 
@property (nonatomic, assign) BOOL hasBannerAd; //section0 头部Banner
@property (nonatomic, assign) BOOL hasTopAd; //置顶帖子位置的广告
@property (nonatomic, assign) CGPoint lastContentViewContentOffset;

@end

@implementation TTQTopics848ViewController

#pragma mark - basic flow
- (void)viewDidLoad {
    [super viewDidLoad];
    self.showHotTopicIndex = -1;
    self.outterViewCanScroll = YES;
    self.innerViewCanScroll = NO;
    self.enableIOS7EdgesForExtendedLayout = YES;
    self.imy_config.preferredStatusBarStyle = UIStatusBarStyleLightContent;
    
    // init
    self.controllers = [NSMutableDictionary dictionary];
    
    // setup bgImage
    [self setupBgImage:nil];
    
    // add header
    self.tableView.tableHeaderView = self.headerView;
    self.headerView.layer.zPosition = -102;
    self.searchHelper = [[IMYCKSearchHelper alloc] initWithSearchLabel:nil viewController:nil fromType:IMYCKSearchFromTypeTTQCircle];
    [self.searchHelper refreshSearchSuggestWords];
//    [self setupSectionBg:self.hasBannerAd hasTopAd:self.hasTopAd];
    [self addTableBgLayer];
    
    // add refresh header
    [self addRefreshHeader];
    
    // add navigation bar
    [self setupCustomNavigation];
    [self setNavigationBarColor:0];
    
    self.pageScrollView.bouncesZoom = false;
    
    @weakify(self);    
    [self showCache];
    
    [self refreshTableHeaderInfo];
    
    [[[[NSNotificationCenter defaultCenter] rac_addObserverForName:kTTQForumChangeNotification object:nil] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNotification *x) {
        @strongify(self); // 详情页加圈联动
        if ([x.object isKindOfClass:TTQForumModel.class]) {
            TTQForumModel *updatedForum = (TTQForumModel *)x.object;
            if (updatedForum.forum_id == self.viewModel.forum.forum_id) {
                self.viewModel.forum.is_joined = updatedForum.is_joined;
                [self.headerView bindWithModel:self.viewModel color:self.forumColor];
            }
        }
    }];
    
    [self imy_addThemeChangedBlock:^(id weakObject) {
        @strongify(self);
        [self setNavigationBarColor:self.tableView.contentOffset.y];
    }];
}

-(void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];
    [[NSNotificationCenter defaultCenter] postNotificationName:@"TTQTopicsViewDidAppear" object:nil];
    [IMYUGCTaskManager getUGCTaskRequest];
}

-(void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    if(self.isOnTop){
        self.imy_config.preferredStatusBarStyle = UIStatusBarStyleDefault;
    } else {
        self.imy_config.preferredStatusBarStyle = UIStatusBarStyleLightContent;
    }
}

-(void)showCache{
    UIView *bgView = [[UIView alloc] initWithFrame:self.captionView.bounds];
    
    IMYCKLoadingView *loading = [[IMYCKLoadingView alloc] initWithtype:IMYCKLoadingDetail];
    CGRect frame = self.captionView.bounds;
    frame.origin.y = SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT;
    loading.frame = frame;
    [bgView addSubview:loading];
    
    IMYTouchEXButton *leftButton = [[IMYTouchEXButton alloc] initWithFrame:CGRectMake(0, SCREEN_STATUSBAR_HEIGHT, 38, 44)];
    [leftButton imy_setImage:[UIImage imy_imageForKey:@"nav_btn_back_black"]];
    leftButton.backgroundColor = [UIColor clearColor];
    [leftButton addTarget:self action:@selector(imy_topLeftButtonTouchupInside) forControlEvents:UIControlEventTouchUpInside];
    [bgView addSubview:leftButton];
    
    [self.captionView setStateView:bgView forState:IMYCaptionViewStateLoading];
    
    // 先展示圈子头部缓存
    if ([self.viewModel dataSourceFromCacheExist] || self.viewModel.forum != nil) {
        // 有圈缓存，再feeds流loading
        self.captionView.state = IMYCaptionViewStateHidden;
        [self updateSegmentView];
    } else {
        // 无圈缓存，整个loading
        if (![IMYNetState networkEnable]) {
            [self.captionView setTitle:MT_Request_NoNet andState:IMYCaptionViewStateRetry];
        } else {
            self.captionView.state = IMYCaptionViewStateLoading;
        }
    }
    
}

-(void)refreshTableHeaderInfo{
    // header信息接口
    @weakify(self);
    self.viewModel.isForumDetailRequesting = YES;
    if ([IMYNetState networkEnable]) {
        [[[TTQForumHelper updateForumByID:self.viewModel.forum_id] deliverOnMainThread] subscribeNext:^(id x) {
            @strongify(self);
            // 此处判断 self.viewModel.forum 是否为nil，nil 为空 且 x 有值的话更新 forum 之后发起广告请求
            self.viewModel.forum = x;
            if (self.viewModel.forum.activityTabs.count == 0) {
                //没下发的时候兜底两个
                NSArray<NSDictionary *> *items = @[@{@"name":@"最新评论",@"code":@"reviewed_date_desc"},
                                                    @{@"name":@"最新发帖",@"code":@"publish_date_desc"}];
                  NSArray *tabs = [items toModels:TTQTopicsTabModel.class];
                self.viewModel.forum.activityTabs = tabs;
            }
            [self setupCircleColor];
            [self.headerView bindWithModel:self.viewModel color:nil];
            self.nameLabel.text = self.viewModel.forum.name;
            [self.avatar setAvatarWithURLString:self.viewModel.forum.icon2 placeholder:[UIImage imageWithColor:[UIColor imy_colorForKey:kCK_Black_F]]];
            self.viewModel.isForumDetailRequesting = NO;
            [self refreshAD];
            // 等详情接口返回回来的时候再刷新列表数据
            if (![self.viewModel shouldSetNewTabSelected] && self.viewModel.source_from != 3) {
                TTQTopicsListViewController *vc = [self viewControllerAtIndex:self.pageViewController.currentPageIndex];
                vc.viewModel.forum = self.viewModel.forum;
                vc.hasRequestData = YES;
                [vc refresh:NO];
            }
            else {
                // 需要先更新tab 再定位到制定tab
                [self updateSegmentView];
                TTQTopicsListViewController *vc = [self viewControllerAtIndex:self.pageViewController.currentPageIndex];
                vc.viewModel.forum = self.viewModel.forum;
                vc.hasRequestData = YES;
                [vc refresh:NO];
            }
            
            TTQTopicsListViewController *vc = [self viewControllerAtIndex:self.showHotTopicIndex];
            vc.themeColor = self.forumColor;
            self.captionView.state = IMYCaptionViewStateHidden;
        } error:^(NSError *error) {
            @strongify(self);
            self.viewModel.isForumDetailRequesting = NO;
            self.captionView.state = IMYCaptionViewStateHidden;
            NSInteger statusCode = error.af_httpResponse.statusCode;
            if (statusCode == 430 || statusCode == 400) {
                [UIWindow imy_showHUDwithNetworkError:error andResponseObject:error.af_responseData];
                [[GCDQueue mainQueue] queueBlock:^{
                    @strongify(self);
                    [TTQCommonHelp sharedCommonHelp].needRefreshGroups = true;
                    [self imy_pop:YES];
                } afterDelay:0.5];
            } else {
                TTQTopicsListViewController *vc = [self viewControllerAtIndex:self.pageViewController.currentPageIndex];
                [vc finishedRequest:error];
            }
            
        }];
    } else {
        self.viewModel.isForumDetailRequesting = YES;
        // order_by="publish_date_desc"，会切换到最新Tab，切换逻辑中处理缓存数据读取和数据请求操作
        if (![self.viewModel shouldSetNewTabSelected] || ([self.viewModel shouldSetNewTabSelected] && self.pageViewController.currentPageIndex != 2)) {
            // 读取第一个Tab的数据
            TTQTopicsListViewController *vc = [self viewControllerAtIndex:self.pageViewController.currentPageIndex];
            [vc refresh:false];
            [self.viewModel changeTopTopics:vc.viewModel.topTopics];
            [self.tableView reloadData];
            [self setupSectionBg:self.hasBannerAd hasTopAd:self.hasTopAd];
            // 置顶栏目更新
            [self updateSegmentView];
        }
        else{
            // 读取最新tab的缓存数据
            NSInteger lastSelectedIndex = [self.segmentView currentSelectedIndex];
            if (lastSelectedIndex != 2) {
                [self.pageViewController setViewControllerAtIndex:2 animated:YES];
            }
            TTQTopicsListViewController *vc = [self viewControllerAtIndex:2];
            [vc refresh:false];
            [self.viewModel changeTopTopics:vc.viewModel.topTopics];
            [self.tableView reloadData];
            [self setupSectionBg:self.hasBannerAd hasTopAd:self.hasTopAd];
            // 置顶栏目更新
            [self updateSegmentView];
        }
        
        
    }
}

- (BOOL)isNavigationBarHidden {
    return YES;
}

- (void)setupBgImage:(UIImage *)imageName {
    // 平铺头像样式背景
    if(self.bgView == nil){
        self.bgView = [UIView new];
        self.bgView.frame = CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);
        if(self.forumColor != nil){
            self.bgView.backgroundColor = self.forumColor;
        } else {
            self.bgView.backgroundColor = [UIColor imy_colorWithHexString:@"0xCCB8B8"];
        }
        [self.view insertSubview:self.bgView atIndex:0];
    }
    
    if(imageName != nil){
        self.bgView.backgroundColor = self.forumColor;
        UIImage *image = [imageName imy_scaledImageWithSize:CGSizeMake(40, 40)];
        UIGraphicsBeginImageContextWithOptions(CGSizeMake(40, 40), NO, [UIScreen mainScreen].scale);
        CGContextRef ctx = UIGraphicsGetCurrentContext();
        CGRect rect = CGRectMake(0, 0, 40, 40);
        CGContextAddEllipseInRect(ctx, rect);
        CGContextClip(ctx);
        [image drawInRect:rect];
        UIImage *roundImage = UIGraphicsGetImageFromCurrentImageContext();
        UIGraphicsEndImageContext();
        
        UIImage *imageWithen = [roundImage imy_drawImageAtCenterWithSize:CGSizeMake(60, 50) fillColor:[UIColor clearColor]];
        UIImage *tiledImage = [imageWithen resizableImageWithCapInsets:UIEdgeInsetsZero resizingMode:UIImageResizingModeTile];
        UIImageView *imageView1 = [[UIImageView alloc] initWithImage:tiledImage];
        imageView1.frame = CGRectMake(0, 0, SCREEN_WIDTH, 50);
        
        UIImageView *imageView2 = [[UIImageView alloc] initWithImage:tiledImage];
        imageView2.frame = CGRectMake(30, 50, SCREEN_WIDTH, 50);
        
        UIImage *image1 = imageView1.image;
        UIImage *image2 = imageView2.image;
        
        // 拼接上下两个image
        UIGraphicsBeginImageContextWithOptions(CGSizeMake(SCREEN_WIDTH, 100), NO, [UIScreen mainScreen].scale);
        [image1 drawInRect:CGRectMake(0, 0, SCREEN_WIDTH, 50)];
        [image2 drawInRect:CGRectMake(30, 50, SCREEN_WIDTH, 50)];
        UIImage *finalImage = UIGraphicsGetImageFromCurrentImageContext();
        UIGraphicsEndImageContext();
        
        UIImageView *imageView = [[UIImageView alloc] initWithFrame:CGRectMake(-50, -100, SCREEN_WIDTH + 100, SCREEN_HEIGHT + 200)];
        imageView.backgroundColor = [[UIColor colorWithPatternImage:finalImage] colorWithAlphaComponent:0.08];
        
        imageView.transform = CGAffineTransformMakeRotation(M_PI/18);
        [self.bgView addSubview:imageView];
        self.bgView.clipsToBounds = YES;
    }
}

- (void)addRefreshHeader {
    @weakify(self);
    IMYCRefreshHeader *refreshHeader = [IMYCRefreshHeader headerWithRefreshingBlock:^{
        @strongify(self);
        [self refresh];
    }];
    self.tableView.mj_header = refreshHeader;
    self.tableView.mj_header.ignoredScrollViewContentInsetTop = -SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT;
}

-(void)refresh{
    self.hasUpdatedSegment = NO;
    [self refreshTableHeaderInfo];
}

- (void)showUserTitle:(BOOL)show {
    CGFloat alp = show?1:0;
    if(self.titleView.alpha == alp){
        return;
    }
    self.titleView.hidden = NO;
    self.titleView.alpha = show?1:0;
}

- (void)setNavigationBarColor:(CGFloat)offset {
    CGFloat height = self.headerView.imy_height - SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT;
    if (offset < height) {
        self.navbarView.backgroundColor = [[UIColor imy_colorForKey:kCK_Black_F] colorWithAlphaComponent:0];
        [self.leftBtn imy_setImage:@"nav_btn_back"];
        [self.searchBtn imy_setImage:@"nav_search_white"];
        [self showUserTitle:NO];
        self.imy_config.preferredStatusBarStyle = UIStatusBarStyleLightContent;
    } else {
        CGFloat ratio = MAX(0, height - offset)/(height);
        if(ratio == 0){
            // 改变搜索等颜色为黑色
            [self.leftBtn imy_setImage:@"nav_btn_back_black"];
            [self.searchBtn imy_setImage:@"nav_icon_search"];
            [self showUserTitle:YES];
            self.imy_config.preferredStatusBarStyle = UIStatusBarStyleDefault;
        }
        self.navbarView.backgroundColor = [[UIColor imy_colorForKey:kCK_Black_F] colorWithAlphaComponent:1.0 - ratio];
        //        [UIColor colorWithWhite:1 alpha:1.0 - ratio];
    }
}

- (void)setupCustomNavigation {
    UIView *newNavBar = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT)];
    newNavBar.backgroundColor = [UIColor clearColor];
    [self.view addSubview:newNavBar];

    // left back btn
    IMYTouchEXButton *leftButton = [[IMYTouchEXButton alloc] initWithFrame:CGRectMake(0, SCREEN_STATUSBAR_HEIGHT, 38, 44)];
    //nav_btn_back nav_btn_back_black
    [leftButton imy_setImage:[UIImage imy_imageForKey:@"nav_btn_back"]];
    leftButton.backgroundColor = [UIColor clearColor];
    [leftButton addTarget:self action:@selector(imy_topLeftButtonTouchupInside) forControlEvents:UIControlEventTouchUpInside];
    [newNavBar addSubview:leftButton];
    self.leftBtn = leftButton;
    
    // titleview
    UIView *titleView = [[UIView alloc] init];
    self.titleView = titleView;
    self.titleView.hidden = YES;
    [newNavBar addSubview:self.titleView];
    
    IMYAvatarImageView *avatar = [[IMYAvatarImageView alloc] init];
    avatar.frame = CGRectMake(0, 10, 24, 24);
    avatar.needShowCicrle = YES;
    self.avatar = avatar;
    [self.avatar setAvatarWithURLString:self.viewModel.forum.icon2 placeholder:[UIImage imageWithColor:[UIColor imy_colorForKey:kCK_Black_F]]];
    [self.titleView addSubview:avatar];
    
    UILabel *nameLabel = [[UILabel alloc] init];
    nameLabel.textAlignment = NSTextAlignmentLeft;
    nameLabel.lineBreakMode = NSLineBreakByTruncatingTail;
    nameLabel.font = [UIFont boldSystemFontOfSize:17];
    nameLabel.text = self.viewModel.forum.name;
    [nameLabel sizeToFit];
    [nameLabel imy_setTextColorForKey:kCK_Black_A];
    nameLabel.frame = CGRectMake(32, 0, 85, 24);
    self.nameLabel = nameLabel;
    [self.titleView addSubview:nameLabel];
    
    // right message btn & search btn
    self.searchBtn = [[IMYTouchEXButton alloc] init];
    [self.searchBtn imy_setImage:[UIImage imy_imageForKey:@"nav_search_white"]];
    self.searchBtn.backgroundColor = [UIColor clearColor];
    [self.searchBtn addTarget:self action:@selector(searchButtonAction:) forControlEvents:UIControlEventTouchUpInside];
    [newNavBar addSubview:self.searchBtn];
    
    [leftButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(24, 24));
        make.top.equalTo(newNavBar).offset(SCREEN_STATUSBAR_HEIGHT + 10);
        make.leading.equalTo(newNavBar).mas_offset(16);
    }];
    
    [self.searchBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(newNavBar).offset(-16);
        make.size.mas_equalTo(CGSizeMake(24, 24));
        make.centerY.equalTo(leftButton);
    }];
    
    [self.titleView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_greaterThanOrEqualTo(leftButton.mas_right).offset(8);
        make.right.mas_lessThanOrEqualTo(self.searchBtn.mas_left).offset(-8);
        make.height.mas_equalTo(44);
        make.top.equalTo(newNavBar).offset(SCREEN_STATUSBAR_HEIGHT);
        make.centerX.mas_equalTo(newNavBar);
    }];
    
    [avatar mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(24, 24));
        make.top.equalTo(self.titleView).offset(10);
        make.left.equalTo(self.titleView).mas_offset(0);
    }];
    
    [nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.titleView);
        make.height.mas_equalTo(44);
        make.top.mas_equalTo(0);
        make.left.equalTo(avatar.mas_right).offset(8);
    }];
    
    [self.publishButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(64, 64));
        make.right.equalTo(self.view).offset(-12);
        make.bottom.equalTo(self.view).offset(-SCREEN_TABBAR_SAFEBOTTOM_MARGIN-75);
    }];
    
    self.navbarView = newNavBar;
}

- (void)initPageVC {
    [self addChildViewController:self.pageViewController];
    self.pageViewController.view.frame = CGRectMake(0, self.segmentView.imy_bottom, SCREEN_WIDTH, SCREEN_HEIGHT - SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT - 44);
    self.pageViewController.view.autoresizingMask = UIViewAutoresizingNone;
//    [self.scrollView addSubview:self.pageViewController.view];
    [self.pageViewController.scrollView imy_setBackgroundColorForKey:kIMY_BG];
}

#pragma mark - UIScrollViewDelegate

- (UIScrollView *)pageScrollView {
    if (_pageScrollView == nil) {
        for (UIScrollView *scrollView in self.pageViewController.view.subviews) {
            if ([NSStringFromClass(scrollView.class) hasSuffix:@"QueuingScrollView"]) {
                _pageScrollView = scrollView;
                break;
            }
        }
    }
    return _pageScrollView;
}

- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView {
    if (scrollView == self.pageScrollView) {
        self.tableView.scrollEnabled = NO;
    } else {
        if (scrollView == self.tableView) {
            self.pageScrollView.panGestureRecognizer.enabled = NO;
        }
    }
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    if (scrollView == self.pageScrollView) {
        //        [self pageScrollViewDidScroll:scrollView];
        return;
    }
    if (scrollView == self.pageViewController.scrollView) {
        if (scrollView.isDragging || scrollView.isDecelerating) {
            /// 用户滚动
            [self changeSegmentWhenScroll:scrollView];
            self.lastContentViewContentOffset = scrollView.contentOffset;
        }
    }
    if (scrollView != self.tableView) {
        return;
    }
    self.pageScrollView.panGestureRecognizer.enabled = NO;
    CGFloat contentOffset = self.headerView.imy_height - SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT;
    if (contentOffset < 1) {
        return;
    }
    BOOL isOnTop = NO;
    UIScrollView *currentPageScrollView = nil;
    if (self.controllers.allValues.count) {
        TTQTopicsListViewController *vc = [self viewControllerAtIndex:self.pageViewController.currentPageIndex];
        currentPageScrollView = [vc tableView];
    }
    if(currentPageScrollView.contentSize.height == currentPageScrollView.imy_height){
        self.outterViewCanScroll = YES;
    }
    if (!self.outterViewCanScroll) {
        isOnTop = YES;
        scrollView.contentOffset = CGPointMake(0, self.segmentView.frame.origin.y - SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT);
        self.innerViewCanScroll = YES;
    } else {
        // segmentview 转换为window坐标系
        CGRect segFrame = [self.segmentView convertRect:self.segmentView.bounds toView:nil];
        if(segFrame.origin.y > 0 && segFrame.origin.y <= SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT){
            // segment 背景色改变
            [self.segmentView changeThemeColor:YES forumcolor:self.forumColor];
            isOnTop = YES;
            if (currentPageScrollView.contentSize.height > currentPageScrollView.imy_height) {
                self.outterViewCanScroll = NO;
                // 允许信息流scrollView滚动
                self.innerViewCanScroll = YES;
            }
        }
    }
    if(!isOnTop){
        [self.segmentView changeThemeColor:NO forumcolor:self.forumColor];
    }
    if (self.isOnTop != isOnTop) {
        self.isOnTop = isOnTop;
        [self showUserTitle:isOnTop];
    }
    [self setNavigationBarColor:scrollView.contentOffset.y];
}

- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView {
    if (scrollView == self.pageScrollView) {
        self.tableView.scrollEnabled = YES;
        return;
    }
    if (scrollView == self.pageViewController.scrollView) {
        NSLog(@"change +++++++++");

    }
    self.pageScrollView.panGestureRecognizer.enabled = TRUE;
    TTQTopicsListViewController *vc = [self viewControllerAtIndex:self.pageViewController.currentPageIndex];
    UITableView *tableView = [vc tableView];
    if (tableView.contentOffset.y <= -tableView.contentInset.top) {
        self.innerViewCanScroll = NO;
        self.outterViewCanScroll = YES;
    }
}

- (void)scrollViewDidEndDragging:(UIScrollView *)scrollView willDecelerate:(BOOL)decelerate {
    if (scrollView == self.pageScrollView) {
        self.tableView.scrollEnabled = YES;
        return;
    }
    self.pageScrollView.panGestureRecognizer.enabled = TRUE;
    if (decelerate == NO) {
        TTQTopicsListViewController *vc = [self viewControllerAtIndex:self.pageViewController.currentPageIndex];
        UITableView *tableView = [vc tableView];
        if (tableView.contentOffset.y <= -tableView.contentInset.top) {
            self.innerViewCanScroll = NO;
            self.outterViewCanScroll = YES;
        }
    }
}

#pragma mark - IMYPageViewControllerDelegate

- (nullable UIViewController *)pageViewController:(IMYPageViewController *)pageViewController controllerAtIndex:(NSUInteger)index {
    TTQTopicsListViewController *vc = [self viewControllerAtIndex:index];
    if (self.hasJumped) {
        vc.showHotTopics = (index == self.showHotTopicIndex);
        vc.themeColor = self.forumColor;
    } else {
        vc.showHotTopics = NO;
    }
    return vc;
}

- (NSUInteger)numberOfControllersInPageViewController:(IMYPageViewController *)pageViewController {
    return self.viewModel.forum.activityTabs.count;
}

- (void)pageViewController:(IMYPageViewController *)pageViewController didTransitionFromIndex:(NSUInteger)fromIndex toIndex:(NSUInteger)toIndex {
    if (fromIndex != toIndex) {
        [self biPostChangedTabAtIndex:toIndex];
    }
    [self.segmentView setSelectedAtIndex:toIndex];
    [self.segmentView layoutIfNeeded];
    if ([self.tableView.mj_header isRefreshing]) {
        [self.tableView imy_headerEndRefreshing];
    }
    TTQTopicsListViewController *vc = [self viewControllerAtIndex:toIndex];
    [self updateTAdTabScrollView:vc.tableView];//更新底部tab scrollview给广告
    if (!vc.hasRequestData) {
        vc.hasRequestData = YES;
        [self changeFilter:toIndex viewModel:vc.viewModel];
        [vc refresh:false];
    }
}

- (void)pageViewController:(IMYPageViewController *)pageViewController willTransitionFromIndex:(NSUInteger)fromIndex toIndex:(NSUInteger)toIndex {
    if (fromIndex != toIndex) {
        BOOL canSetZero = NO;
        if (!self.isOnTop) {
            [[[self viewControllerAtIndex:toIndex] tableView] setContentOffset:CGPointMake(0, 0) animated:NO];
        }
    }
}

- (TTQTopicsListViewController *)viewControllerAtIndex:(NSUInteger)index {
    TTQTopicsListViewController *vc = [self.controllers objectForKey:@(index)];
    if (vc == nil) {
        vc = [TTQTopicsListViewController new];
        TTQTopicsViewModel *viewModel = [[TTQTopicsViewModel alloc] initWithforum_id:self.viewModel.forum_id];
        viewModel.currentIndex = index + 1;
        [self changeFilter:index viewModel:viewModel];
        [viewModel dataSourceFromCacheWithTabIndex:index];
        vc.viewModel = viewModel;
    
        @weakify(self,vc);
        [vc setDidScrollBlock:^(UIScrollView * _Nonnull scrollView) {
            @strongify(self);
            if (self.pageViewController.currentPageIndex != index) {
                return;
            }
            if (!self.innerViewCanScroll) {
                scrollView.contentOffset = CGPointMake(0, 0);
            } else if (scrollView.contentOffset.y < 0) {
                self.innerViewCanScroll = NO;
                self.outterViewCanScroll = YES;
            }
            [self.segmentView showBottomLineWithOffset:scrollView.contentOffset];
        }];

        [[vc.requestFinishedSignal deliverOnMainThread] subscribeNext:^(id _Nullable x) {
            @strongify(self,vc);
            if (self.pageViewController.currentPageIndex == index) {
                [self.tableView imy_headerEndRefreshing];
            }
            if([x boolValue]){
                if(self.hasUpdatedSegment){
                    return;
                }
                self.hasUpdatedSegment = YES;
                [self.viewModel changeTopTopics:vc.viewModel.topTopics];
//                if([self topIsEmpty]){
//                    [self.headerView changeHeaderHeight:NO];
//                } else{
//                    [self.headerView changeHeaderHeight:YES];
//                }
                
//                [self.segmentView changeClearBG:[self topIsEmpty]];
                [self.tableView reloadData];
                [self setupSectionBg:self.hasBannerAd hasTopAd:self.hasTopAd];
                
                // 置顶栏目更新
                [self updateSegmentView];
            }
        }];
//        vc.listType = index;
//        if (index == 0) {
//            vc.userId = self.userId;
//        }
        [self.controllers setObject:vc forKey:@(index)];
    }
    return vc;
}

-(void)changeFilter:(NSInteger)index viewModel:(TTQTopicsViewModel *)viewModel{
    if(self.viewModel.forum != nil){
        if(!self.firstLoadForum){
            [self setupSectionBg:self.hasBannerAd hasTopAd:self.hasTopAd];
        }
        self.firstLoadForum = YES;
        viewModel.forum = self.viewModel.forum; // 更新下拿到的最新的forum
    }
    [viewModel.requestRemoteDataCommand cancel];
    TTQTopicsTabModel *tabModel = self.viewModel.forum.activityTabs[index];
    viewModel.tabModel = tabModel;
    viewModel.tabModel.tabIndex = index;
}


- (void)jumpToDestinatedTab{
    if(self.hasJumped){
        return;
    }
    self.hasJumped = YES;
    NSInteger index = 0;
    TTQTopicsTabModel *selectedModel = [self.viewModel.forum.activityTabs bk_match:^BOOL(TTQTopicsTabModel * obj) {
        return obj.selected;
    }];
    if (selectedModel) {
        index = [self.viewModel.forum.activityTabs indexOfObject:selectedModel];
    }
    self.showHotTopicIndex = index;
    TTQTopicsTabModel *tabModel = self.viewModel.forum.activityTabs[index];
    [self clickRedDotAtTab:tabModel.tab_id];
    [self.pageViewController setViewControllerAtIndex:index animated:NO];
}

-(void)headerViewLayout{
    // header显示的几种样式：
    // 1 无头部任何信息 header 88
    // 2 仅有贡献榜
    // 3 仅有私域
    // 4 仅有公告和置顶
    // 5 贡献榜+公告
    // 6 贡献榜+私域
    // 7 贡献榜+公告+私域
    // 8 公告+私域
}

#pragma mark - UITableViewDelegate & DataSource

-(NSInteger)numberOfSectionsInTableView:(UITableView *)tableView{
    NSUInteger sections = 1;
    if([self hasTalent]){
        sections += 1;
    }
//    if([self hasTopDatas]){
        sections += 1;
//    }
    if([self hasDesire]){
        sections += 1;
    }
    return sections;
}

-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    if([self hasTalent] && section == 1){
        NSArray *arr = self.viewModel.topTopics;
        if([self hasDesire]){
            return arr.count - 1;
        } else {
            return arr.count;
        }
        
    }
    if(![self hasTalent] && section == 0){
        NSArray *arr = self.viewModel.topTopics;
        if([self hasDesire]){
            return arr.count - 1;
        } else {
            return arr.count;
        }
    }
    return 1;
}

-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    BOOL hasTalent = self.viewModel.forum.has_expert && self.viewModel.forum.expert.count > 0;
    BOOL isTopCell = NO;
    BOOL isDesire = NO;
    NSUInteger sections = 1;
    if([self hasTalent]){
        sections += 1;
    }
//    if([self hasTopDatas]){
        sections += 1;
//    }
    if([self hasDesire]){
        sections += 1;
    }
    
    if(indexPath.section == sections-2 && [self hasDesire]){
        isDesire = YES;
        isTopCell = YES;
    }
    if(indexPath.section == 0){
        if(indexPath.row == 0){
            if (hasTalent) {
                // 有贡献榜
                TTQCircleTalentCell *cell = [tableView dequeueReusableCellWithIdentifier:@"TTQCircleTalentCell" forIndexPath:indexPath];
                NSArray *tmpData = self.viewModel.forum.expert;
                NSUInteger maxCount = 6;
                if(maxCount > tmpData.count){
                    maxCount = tmpData.count;
                }
                NSMutableArray *dataArr = [NSMutableArray new];
                for (int i = 0; i < maxCount; i++) {
                    TTQGroupExpertModel *model = [tmpData[i] toModel:[TTQGroupExpertModel class]];
                    [dataArr addObject:model];
                }
                
                [cell setDataArray:dataArr color:self.forumColor];
                return cell;
            }
        }
//        if([self hasTopDatas]){
            isTopCell = YES;
//        } else if([self hasDesire]){
//            isDesire = YES;
//            isTopCell = YES;
//        }
    }
    
    if(indexPath.section == 1){
        if(hasTalent){
            isTopCell = YES;
        }
    }
    
//    if([self hasTalent] && indexPath.section == 1){
//        if([self hasTopDatas]){
//            isTopCell = YES;
//        } else if([self hasDesire]){
//            isDesire = YES;
//            isTopCell = YES;
//        }
//    }
//
//    if(hasTalent && [self hasTopDatas] && indexPath.section == 2){
//        if([self hasDesire]){
//            isDesire = YES;
//            isTopCell = YES;
//        }
//    }
//    if(![self hasTopDatas] && !isDesire){
//        isTopCell = NO;
//    }
    if(isTopCell){
//        NSArray *topTopics = self.viewModel.topTopics;
        TTQHome5TopicModel *topicModel = self.viewModel.topTopics[indexPath.row];
        if(isDesire){
            topicModel = self.viewModel.topTopics.lastObject;
        }
        if([topicModel isKindOfClass:[TTQTopicsEmptyModel class]]){
            TTQTopicsEmptyCell *cell = [tableView dequeueReusableCellWithIdentifier:@"TTQTopicsEmptyCell" forIndexPath:indexPath];
            [cell bindModel:topicModel cellForRowAtIndexPath:indexPath viewModel:self.viewModel];
            return cell;
        }
        if (topicModel.type == TTQHome5TypeDesiresCard) {
            TTQDesiresCell *cell = [tableView dequeueReusableCellWithIdentifier:@"TTQDesiresCell" forIndexPath:indexPath];
            if(self.desireBGClear){
                [cell changeBgColor:[UIColor clearColor]];
            } else {
                [cell changeBgColor:[UIColor imy_colorForKey:kIMY_BG]];
            }
            [cell imy_lineViewWithDirection:IMYDirectionUp show:self.showDesireLine margin:0];
            [cell bindModel:topicModel cellForRowAtIndexPath:indexPath viewModel:self.viewModel];
            return cell;
        }
        //  置顶和公告
        TTQCircleTopCell *cell = [tableView dequeueReusableCellWithIdentifier:@"TTQCircleTopCell" forIndexPath:indexPath];
        cell.forumColor = self.forumColor;
        [cell bindModel:topicModel cellForRowAtIndexPath:indexPath viewModel:self.viewModel];
        return cell;
    }
    
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"cell"];
    if(cell == nil){
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"cell"];
        [self addChildViewController:self.pageViewController];
        self.pageViewController.view.frame = CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT - SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT - 44);
        self.pageViewController.view.autoresizingMask = UIViewAutoresizingNone;
        [cell.contentView addSubview:self.pageViewController.view];
        [self.pageViewController.scrollView imy_setBackgroundColorForKey:kIMY_BG];
    }
    return cell;
}

-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [tableView deselectRowAtIndexPath:indexPath animated:NO];
    BOOL isTopCell = NO;
    BOOL isDesire = NO;
    BOOL hasTalent = self.viewModel.forum.has_expert && self.viewModel.forum.expert.count > 0;
    NSUInteger sections = 1;
    if([self hasTalent]){
        sections += 1;
    }
//    if([self hasTopDatas]){
        sections += 1;
//    }
    if([self hasDesire]){
        sections += 1;
    }
    
    if(indexPath.section == sections-2 && [self hasDesire]){
        isDesire = YES;
        isTopCell = YES;
    }
    
    if(indexPath.section == 0){
        if(indexPath.row == 0){
            if (hasTalent) {
                [self gotoHonorHallDirect:YES];
                return;
            }
        }
//        if([self hasTopDatas]){
            isTopCell = YES;
//        } else if([self hasDesire]){
//            isDesire = YES;
//            isTopCell = YES;
//        }
    }
    if(indexPath.section == 1){
        if(hasTalent){
            isTopCell = YES;
        }
    }
//
//    if([self hasTalent] && indexPath.section == 1){
//        if([self hasTopDatas]){
//            isTopCell = YES;
//        } else if([self hasDesire]){
//            isDesire = YES;
//            isTopCell = YES;
//        }
//    }
//
//    if(hasTalent && [self hasTopDatas] && indexPath.section == 2){
//        if([self hasDesire]){
//            isDesire = YES;
//            isTopCell = YES;
//        }
//    }
//    if(![self hasTopDatas] && !isDesire){
//        isTopCell = NO;
//    }
    // topcell跳转
    if(isTopCell){
        TTQTopicModel *topicModel = self.viewModel.topTopics[indexPath.row];
        if(isDesire){
            topicModel = self.viewModel.topTopics.lastObject;
        }
        if (topicModel.type == TTQHome5TypeDesiresCard) {
//            TTQDesiresCell *cell = [tableView cellForRowAtIndexPath:indexPath];
            
        }
        //  置顶和公告
        if (topicModel.link) {
            if (topicModel.link.type == 1) {
                [IMYEventHelper event:@"qzxq-1"];
                [[UIApplication sharedApplication] openURL:[NSURL URLWithString:topicModel.link.url] options:nil completionHandler:nil];

                return;
            } else if (topicModel.link.type == 2) {
                [IMYEventHelper event:@"qzxq-2"];
                IMYVKWebViewController *webViewController = [IMYVKWebViewController new];
                webViewController.urlString = topicModel.link.url;
                webViewController.navTitleString = topicModel.link.url;
                [[UIViewController imy_currentTopViewController] imy_push:webViewController animated:YES];
                return;
            }
        }
        TTQClickReportModel *reportModel = [[TTQClickReportModel alloc] init];
        reportModel.type_id = 32;
        reportModel.topic_id = topicModel.topic_id;
        reportModel.position = indexPath.row;
        [reportModel commitModel];
        
        IMYURI *uri = [IMYURI uriWithURIString:topicModel.real_redirect_url];
        if ([uri.path isEqualToString:@"circles/group/topic"]) {
            [uri appendingParams:@{@"bi_video_play_entrance": @(3)}];
        }
        [[IMYURIManager shareURIManager] runActionWithURI:uri];
    }
}

-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    BOOL hasTalent = self.viewModel.forum.has_expert && self.viewModel.forum.expert.count > 0;
    BOOL isTopCell = NO;
    BOOL isDesire = NO;
    NSUInteger sections = 1;
    if([self hasTalent]){
        sections += 1;
    }
//    if([self hasTopDatas]){
        sections += 1;
//    }
    if([self hasDesire]){
        sections += 1;
    }
    
    if(indexPath.section == sections-2 && [self hasDesire]){
        isDesire = YES;
        isTopCell = YES;
    }
    
    if(indexPath.section == 0){
        if(indexPath.row == 0){
            if (hasTalent) {
                NSArray *arr = self.viewModel.dataSource[indexPath.section];
//                if(indexPath.row == arr.count){
//                    return [TTQCircleTalentCell cellHeight] + 8;
//                }
                return [TTQCircleTalentCell cellHeight];
            }
        }
//        if([self hasTopDatas]){
            isTopCell = YES;
//        } else if([self hasDesire]){
//            isDesire = YES;
//            isTopCell = YES;
//        }
    }
    
    if(indexPath.section == 1){
        if(hasTalent){
            isTopCell = YES;
        }
    }
    
//    if([self hasTalent] && indexPath.section == 1){
//        if([self hasTopDatas]){
//            isTopCell = YES;
//        } else if([self hasDesire]){
//            isDesire = YES;
//            isTopCell = YES;
//        }
//    }
//
//    if(hasTalent && [self hasTopDatas] && indexPath.section == 2){
//        if([self hasDesire]){
//            isDesire = YES;
//            isTopCell = YES;
//        }
//    }
//    if(![self hasTopDatas] && !isDesire){
//        isTopCell = NO;
//    }
    if(isTopCell){
        TTQHome5TopicModel *topicModel = self.viewModel.topTopics[indexPath.row];
        if(isDesire){
            topicModel = self.viewModel.topTopics.lastObject;
        }
        if ([topicModel isKindOfClass:[TTQTopicsEmptyModel class]] || topicModel.type == TTQHome5TypeDesiresCard) {
            TTQDesiresCell *cell = [[TTQDesiresCell alloc] init];
            return [cell bindModel:topicModel heightForRowAtIndexPath:indexPath viewModel:self.viewModel];
        }
        TTQCircleTopCell *cell = [[TTQCircleTopCell alloc] init];
        CGFloat height = [cell bindModel:topicModel heightForRowAtIndexPath:indexPath viewModel:self.viewModel];
        NSArray *arr = self.viewModel.dataSource[indexPath.section];
//        if(indexPath.row == arr.count - 1){
//            height += 8;
//        }
        return height;
    }
//    if (self.isOnTop) {
//        return SCREEN_HEIGHT;
//    }
    return SCREEN_HEIGHT - SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT - 44;
}

-(UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section{
    NSUInteger sections = 1;
    if([self hasTalent]){
        sections += 1;
    }
//    if([self hasTopDatas]){
        sections += 1;
//    }
    if([self hasDesire]){
        sections += 1;
    }
    if(section == sections - 1){
        return self.segmentView;
    }
//    BOOL hasTalent = self.viewModel.forum.has_expert && self.viewModel.forum.expert.count > 0;
//    if(hasTalent && section == 2){
//        return self.segmentView;
//    } else if (!hasTalent && section == 1){
//        return self.segmentView;
//    }
    return nil;
}

-(CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section{
    NSUInteger sections = 1;
    if([self hasTalent]){
        sections += 1;
    }
//    if([self hasTopDatas]){
        sections += 1;
//    }
    if([self hasDesire]){
        sections += 1;
    }
    if(section == sections - 1){
        return 44.0;
    }
//    BOOL hasTalent = self.viewModel.forum.has_expert && self.viewModel.forum.expert.count > 0;
//    if(hasTalent && section == 2){
//        return 44.0;
//    } else if (!hasTalent && section == 1){
//        return 44.0;
//    }
    return 0.0;
}

-(UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section{
    UIView *footer = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 8)];
    [footer imy_setBackgroundColorForKey:kIMY_BG];
    BOOL hasTalent = [self hasTalent];
    if([self hasTopDatas] || self.hasTopAd){
        if(hasTalent && section == 1){
            return footer;
        } else if (!hasTalent && section == 0){
            return footer;
        }
    }
    return nil;
}

-(CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section{
    BOOL hasTalent = [self hasTalent];
    if([self hasTopDatas] || self.hasTopAd){
        if(hasTalent && section == 1){
            return 8.0;
        } else if (!hasTalent && section == 0){
            return 8.0;
        }
    }
    return 0.0;
}

#pragma mark - section helper

-(BOOL)hasTalent{
    return self.viewModel.forum.has_expert && self.viewModel.forum.expert.count > 0;
}

-(BOOL)hasDesire{
    NSMutableArray *arr = self.viewModel.topTopics;
    TTQHome5TopicModel *topicModel = arr.lastObject;
    BOOL isLastDesire = NO;
    if (topicModel.type == TTQHome5TypeDesiresCard) {
        isLastDesire = YES;
    }
    return isLastDesire;
}

-(BOOL)hasTopDatas{
    // 是否有置顶公告等，慎用，要等广告的置顶回来才能确定有无数据
    NSMutableArray *arr = [[NSMutableArray alloc] initWithArray:self.viewModel.topTopics];
    TTQHome5TopicModel *topicModel = arr.lastObject;
    
    if (topicModel.type == TTQHome5TypeDesiresCard) {
        [arr removeLastObject];
    }
    if(arr.count > 0){
        return YES;
    }
    return NO;
}

#pragma mark - data

-(BOOL)topIsEmpty{
    // 判断头部信息区域是否为空
    NSArray *arr = self.viewModel.dataSource[0];
    if (self.viewModel.forum.has_expert && self.viewModel.forum.expert.count > 0) {
        return NO;
    } else if(arr.count > 0){
        return NO;
    } else {
        return YES;
    }
}

- (void)biPostChangedTabAtIndex:(NSInteger)index{
    TTQTopicsTabModel *tabModel = self.viewModel.forum.activityTabs[index];
    NSDictionary *params = @{@"event":@"dsq_qzxqy_nrtabqh",
                             @"public_type":tabModel.name,
                             @"public_info":@(self.viewModel.forum_id),
                             @"action":@2};
    [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
}

#pragma mark - getter & setter

-(TTQCircleHeaderView *)headerView{
    if(!_headerView){
        _headerView = [[TTQCircleHeaderView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 100+SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT)]; // 88header高度 12圆角
    }
    return _headerView;
}

- (TTQCircleSegmentView *)segmentView {
    if (!_segmentView) {
        NSMutableArray *items = [NSMutableArray array];
        _segmentView = [[TTQCircleSegmentView alloc] initWithItems:@[]];
        @weakify(self);
        [_segmentView setSegmentClickBlock:^(NSUInteger index) {
            @strongify(self);
            TTQTopicsTabModel *tabModel = self.viewModel.forum.activityTabs[index];
            [self clickRedDotAtTab:tabModel.tab_id];
            [self.pageViewController setViewControllerAtIndex:index animated:YES];
        }];
    }
    return _segmentView;
}

- (IMYPageViewController *)pageViewController {
    if (!_pageViewController) {
        _pageViewController = [[IMYPageViewController alloc] init];
        _pageViewController.delegate = self;
        _pageViewController.dataSource = self;
        _pageViewController.scrollViewForwardDelegate = self;
        _pageViewController.scrollView.bounces = NO;
    }
    return _pageViewController;
}

-(UITableViewCircle *)tableView{
    if(!_tableView){
        _tableView = [[UITableViewCircle alloc] initWithFrame:self.view.bounds style:UITableViewStylePlain];
        _tableView.showsVerticalScrollIndicator = NO;
        _tableView.delegate = self;
        _tableView.panGestureRecognizer.cancelsTouchesInView = NO;
//        _tableView.bounces = NO;
        @weakify(self);
        _tableView.reloadDataCompletionBlock = ^{
            @strongify(self);
            if(self.isOnTop){
                [self.segmentView changeThemeColor:YES forumcolor:self.forumColor];
                self.tableView.contentOffset = CGPointMake(0, self.segmentView.frame.origin.y - SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT);
            }
        };
        _tableView.dataSource = self;
        _tableView.backgroundColor = [UIColor clearColor];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        // 本页面的cell有三种类型，贡献榜、置顶（公告）、私域
        [_tableView registerClass:[TTQCircleTalentCell class] forCellReuseIdentifier:@"TTQCircleTalentCell"];
        [_tableView registerClass:[TTQDesiresCell class] forCellReuseIdentifier:@"TTQDesiresCell"];
        [_tableView registerClass:[TTQCircleTopCell class] forCellReuseIdentifier:@"TTQCircleTopCell"];
        [_tableView registerClass:[TTQTopicsEmptyCell class] forCellReuseIdentifier:@"TTQTopicsEmptyCell"];
        [self.view addSubview:_tableView];
    }
    return _tableView;
}

- (IMYButton *)publishButton {
    if (!_publishButton) {
        _publishButton = [[IMYButton alloc] initWithFrame:CGRectMake(0, 0, 64, 64)];
        [_publishButton imy_setImage:@"content_float_icon_publish"];
        [self.view addSubview:_publishButton];
        @weakify(self);
        [[_publishButton rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
            @strongify(self);
            // 直接打开新发布器
            if ([TTQVideoPostNavigationButton shouldEnterPost:NO]) {
                NSInteger forumID = self.viewModel.forum.forum_id ?: self.viewModel.forum_id;
                [[IMYURIManager shareURIManager] runActionWithPath:@"community/publish" params:@{@"forum_id":@(forumID),@"forum_name":self.viewModel.forum.name,@"publish_entrance":@3} info:nil];
                [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"TopicPublish_entrance",@"publish_entrance":@3,@"action":@(2)} headers:nil completed:nil];
            }
        }];
        
        _publishButton.imyut_eventInfo.eventName = @"ttq_topics_list_publishBtn";
        [_publishButton.imyut_eventInfo setExposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
            [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"TopicPublish_entrance",@"publish_entrance":@3,@"action":@(1)} headers:nil completed:nil];
        }];
        
    }
    [self.view bringSubviewToFront:_publishButton];
    return _publishButton;
}

- (IMYCaptionView *)captionView {
    if (_captionView == nil) {
        _captionView = [[IMYCaptionView alloc] initWithFrame:CGRectMake(0, self.tableView.imy_top, SCREEN_WIDTH, SCREEN_HEIGHT)];
        [self.view addSubview:_captionView];
        _captionView.state = IMYCaptionViewStateLoading;
        @weakify(self);
        [_captionView setRetryBlock:^{
            @strongify(self);
            self.captionView.state = IMYCaptionViewStateLoading;
            [self refresh];

        }];
    }
    return _captionView;
}

#pragma mark - api helper

- (void)joinForum {
    @weakify(self);
    [[[TTQForumHelper joinForum:self.viewModel.forum nameType:0] deliverOnMainThread] subscribeNext:^(id x) {
        @strongify(self);
        
    }];
}

- (void)goLoginWithText:(NSString *)text {
    [UIWindow imy_showTextHUD:text ?: kStatusText_unLogin];
    [[IMYURIManager shareURIManager] runActionWithString:@"login"];
}
 
#pragma mark - 广告
- (NSInteger)topAdSection {
    NSInteger beginSection = 0;
    if (self.viewModel.forum.has_expert && self.viewModel.forum.expert.count > 0) { //贡献榜位置+1
        beginSection = 1;
    }
    return beginSection;
}
- (void)initHeaderAd {
    @weakify(self);
    NSInteger (^desiresCardIndexBlock)() = ^NSInteger() {
        @strongify(self);
         return [self.viewModel getLustsIndex]; //私域卡片位置
    };
     
    NSInteger (^maxRowsBlock)() = ^NSInteger() {  //头部cell rows数量
        @strongify(self);
        NSInteger beginSection = [self topAdSection];
        NSInteger rows = [self tableView:self.tableView numberOfRowsInSection:beginSection];
        return rows;
    };
     
    NSInteger (^topAdSectionBlock)() = ^NSInteger() {
        @strongify(self);
        return [self topAdSection];
    };
    
    void (^reloadDataAdBlock)(BOOL haveBannerAd,BOOL haveTopAd) = ^void(BOOL haveBannerAd,BOOL haveTopAd) {
        @strongify(self);
        self.hasBannerAd = haveBannerAd;
        self.hasTopAd = haveTopAd;
        if(self.hasBannerAd && ![self hasTalent]){ // 先增加下高度防止抖动
            [self.headerView changeHeaderHeight:YES];
//            self.tableView.tableHeaderView = self.headerView;
        }
        imy_asyncMainBlock(0.2, ^{
            [self setupSectionBg:haveBannerAd hasTopAd:haveTopAd];
        });
    };
    
    UIColor* (^forumColorBlock)(void) = ^UIColor*(void) {
        @strongify(self);
        return self.forumColor;
    };
     
    NSMutableDictionary *userInfo = [[NSMutableDictionary alloc] init];
    NSMapTable *mapTable = [NSMapTable weakToWeakObjectsMapTable];
    [mapTable setObject:self.segmentView forKey:@"segement_tab_view"];
    userInfo[@"segement_tab_view_map"] = mapTable;
    userInfo[@"desiresCardIndexBlock"] = desiresCardIndexBlock;
    userInfo[@"maxRowsBlock"] = maxRowsBlock;
    userInfo[@"topAdSectionBlock"] = topAdSectionBlock;
    userInfo[@"reloadDataAdBlock"] = reloadDataAdBlock;
    userInfo[@"forumColorBlock"] = forumColorBlock;
    
    IMYAdvertiserInfo *adInfo = [IMYAdvertiserInfo adInfoWithSource:nil page:IMYADPageTopics position:0 userInfo:userInfo viewController:self];
    self.adManager = [[IMYAdFactories getAdManagerFactory] getAdapterAdManagerWithADInfo:adInfo];
    self.adManager.tableView = self.tableView;
}
 
- (void)initSideAD {
   
    NSMutableDictionary *sideUserInfo = [[NSMutableDictionary alloc] init];
    sideUserInfo[@"shouldSide"] = @(YES);
    IMYAdvertiserInfo *sideAdInfo = [IMYAdvertiserInfo adInfoWithSource:nil page:IMYADPageSide position:IMYADPositionTopicsListSidebar userInfo:sideUserInfo viewController:self];
    self.sideAdManager = [[IMYAdFactories getAdManagerFactory] getTableViewAdManagerWithADInfo:sideAdInfo];
    [self.sideAdManager setTableView:self.tableView];
}

- (void)updateTAdTabScrollView:(UITableView*)tableView {
    NSMapTable *mapTable = [NSMapTable weakToWeakObjectsMapTable];
    [mapTable setObject:tableView forKey:@"subview_tableview"];
    [self.sideAdManager.adInfo unlock];
    [self.sideAdManager.adInfo appendUserInfo:@{@"subview_tableview_map": mapTable}];
    [self.sideAdManager.adInfo lock];
    
    [self.adManager.adInfo unlock];
    [self.adManager.adInfo appendUserInfo:@{@"subview_tableview_map": mapTable}];
    [self.adManager.adInfo lock];
}

- (void)refreshAD {
    if (self.adManager == nil) {
        [self initHeaderAd];
    }
    if (self.sideAdManager == nil) {
        [self initSideAD];
    }
    [self.adManager.adInfo unlock];
    [self.adManager.adInfo appendUserInfo:@{@"forumId" : @(self.viewModel.forum_id)}];
    [self.adManager.adInfo lock];
    [self.adManager refreshData];
    
    [self.sideAdManager.adInfo unlock];
    [self.sideAdManager.adInfo appendUserInfo:@{@"forumId" : @(self.viewModel.forum_id)}];
    [self.sideAdManager.adInfo lock];
    [self.sideAdManager refreshData];
} 

#pragma mark - Search Helper


#pragma mark - button event

-(void)searchButtonAction:(UIButton *)sender{
    // enter search page
    self.imy_config.preferredStatusBarStyle = UIStatusBarStyleDefault;
    [self.searchHelper refreshSearchSuggestWords];
    [[IMYURIManager shareURIManager] runActionWithURI:[IMYURI uriWithPath:@"circles/search"
                                                                   params:@{@"from": @(13),
                                                                            @"forum_id": @(self.viewModel.forum_id),
                                                                            @"pos_id": @(2),
                                                                            @"current_tab": @(2),
                                                                            @"shouldLoadCache": @(YES)
                                                                   }
                                                                     info:nil]];
}

-(void)messageButtonAction:(UIButton *)sender{
    // enter message module
    [[IMYURIManager shareURIManager] runActionWithString:@"msg/entrance"];
}

- (void)gotoHonorHallDirect:(BOOL)bDirect {
    if ([IMYPublicAppHelper shareAppHelper].hasLogin) {
//        [IMYEventHelper event:@"qz-drt"];
        [[IMYURIManager shareURIManager]runActionWithString:self.viewModel.forum.expert_redirect_url];
    } else {
        [UIWindow imy_showTextHUD:kStatusText_unLogin];
        [[IMYURIManager shareURIManager] runActionWithString:@"login"];
    }
}

#pragma mark - helper

-(void)updateSegmentView{

    NSMutableArray *arr = [NSMutableArray new];
    for (TTQTopicsTabModel *model in self.viewModel.forum.activityTabs) {
        TTQCircleSegmentItem *item = [[TTQCircleSegmentItem alloc] initWithTitle:model.name color:self.forumColor];
        [arr addObject:item];
    }
    if(arr.count > 0){
        [self.segmentView changeSegmentItems:arr];
        for (int i = 0; i < self.viewModel.forum.activityTabs.count; i++) {
            TTQTopicsTabModel *model = self.viewModel.forum.activityTabs[i];
            if ([self shouldShowRedDotAtTab:model.tab_id]) {
                [self.segmentView showRedDotAtIndex: i];
            }
        }
    }
    // 增加定位到指定tab逻辑
    [self jumpToDestinatedTab];
    [self.pageViewController reloadData];
}

- (void)changeSegmentWhenScroll:(UIScrollView *)scrollView {
    if (self.segmentView.items == 0) {
        return;
    }
    CGPoint contentOffset = scrollView.contentOffset;
    CGFloat ratio = contentOffset.x/scrollView.bounds.size.width;
    if (ratio > self.segmentView.items.count - 1 || ratio < 0) {
        //超过了边界，不需要处理
        return;
    }
    if (contentOffset.x == 0 && self.segmentView.currentSelectedIndex == 0 && self.lastContentViewContentOffset.x == 0) {
        //滚动到了最左边，且已经选中了第一个，且之前的contentOffset.x为0
        return;
    }
    CGFloat maxContentOffsetX = scrollView.contentSize.width - scrollView.bounds.size.width;
    if (contentOffset.x == maxContentOffsetX && self.segmentView.currentSelectedIndex == self.segmentView.items.count - 1 && self.lastContentViewContentOffset.x == maxContentOffsetX) {
        //滚动到了最右边，且已经选中了最后一个，且之前的contentOffset.x为maxContentOffsetX
        return;
    }
    ratio = MAX(0, MIN(self.segmentView.items.count - 1, ratio));
    NSInteger baseIndex = floorf(ratio);
    CGFloat remainderRatio = ratio - baseIndex;

    if (remainderRatio == 0) {
        //快速滑动翻页，用户一直在拖拽contentScrollView，需要更新选中状态
        //滑动一小段距离，然后放开回到原位，contentOffset同样的值会回调多次。例如在index为1的情况，滑动放开回到原位，contentOffset会多次回调CGPoint(width, 0)
        if (!(self.lastContentViewContentOffset.x == contentOffset.x && self.segmentView.currentSelectedIndex == baseIndex)) {
            [self.segmentView setSelectedAtIndex:baseIndex];
        }
    } else {
        //快速滑动翻页，当remainderRatio没有变成0，但是已经翻页了，需要通过下面的判断，触发选中
        if (fabs(ratio - self.segmentView.currentSelectedIndex) > 1) {
            NSInteger targetIndex = baseIndex;
            if (ratio < self.segmentView.currentSelectedIndex) {
                targetIndex = baseIndex + 1;
            }
            [self.segmentView setSelectedAtIndex:targetIndex];
        }
    }
}

- (BOOL)shouldShowRedDotAtTab:(NSInteger)tabId {
    TTQTopicsTabModel *model = [self.viewModel.forum.activityTabs filter:^BOOL(TTQTopicsTabModel *element) {
        if (element.tab_id == tabId) {
            return YES;
        }
        return NO;
    }].lastObject;
    if (model && model.is_red_dot) {
        NSString *key = [NSString
                         stringWithFormat:@"ttqTabRed_%ld_%@",tabId, [IMYPublicAppHelper shareAppHelper].userid];
        NSInteger lastTime = [[IMYUserDefaults standardUserDefaults] integerForKey:key];
        if (lastTime != model.time) {
            return YES;
        }
    }
    return NO;
}

- (void)clickRedDotAtTab:(NSInteger)tabId {
    NSString *key = [NSString stringWithFormat:@"ttqTabRed_%ld_%@", tabId, [IMYPublicAppHelper shareAppHelper].userid];
    TTQTopicsTabModel *model = [self.viewModel.forum.activityTabs filter:^BOOL(TTQTopicsTabModel *element) {
        if (element.tab_id == tabId) {
            return YES;
        }
        return NO;
    }].lastObject;
    if (model) {
        [[IMYUserDefaults standardUserDefaults] setInteger:model.time forKey:key];
    }
}

-(void)setupCircleColor{
    if(self.viewModel == nil || self.forumColor != nil){
        return;
    }
    [self.headerView bindWithModel:self.viewModel color:nil];
    if(imy_isNotEmptyString(self.viewModel.forum.icon)){
        @weakify(self);
        [self.headerView.avatarImgV setAvatarWithURLString:self.viewModel.forum.icon2 placeholder:[UIImage imageWithColor:[UIColor imy_colorForKey:kCK_Black_F]] completion:^(BOOL succeed, UIImage *image) {
            @strongify(self);
            if(succeed){
                if (image != nil) {
                    [self pixelColorFromImage:image];
                    [self setupBgImage:image];
                }
            }
        }];
    } else {
        @weakify(self);
        [[[[RACObserve(self.viewModel.forum, icon) distinctUntilChanged] skip:1] deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
            @strongify(self);
            if(imy_isNotEmptyString(self.viewModel.forum.icon)){
                [self setupCircleColor];
            }
        }];
    }
    
}

/// 获取图片上某个点的颜色值(不包含alpha)。
- (void)pixelColorFromImage:(UIImage *)image{
    CGPoint point = CGPointMake(3, image.size.height/2);
    // 将像素绘制到一个1×1像素字节数组和位图上下文。
    NSInteger pointX = trunc(point.x);
    NSInteger pointY = trunc(point.y);
    CGImageRef cgImage = image.CGImage;
    CGFloat width = image.size.width;
    CGFloat height = image.size.height;
    CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceRGB();
    int bytesPerPixel = 4;
    int bytesPerRow = bytesPerPixel * 1;
    NSUInteger bitsPerComponent = 8;
    unsigned char pixelData[4] = {0, 0, 0, 0};
    CGContextRef context = CGBitmapContextCreate(pixelData, 1, 1, bitsPerComponent, bytesPerRow, colorSpace, kCGImageAlphaPremultipliedLast | kCGBitmapByteOrder32Big);
    CGColorSpaceRelease(colorSpace);
    CGContextSetBlendMode(context, kCGBlendModeCopy);
    
    // 将指定像素绘制到上下文中
    CGContextTranslateCTM(context, -pointX, pointY - height);
    CGContextDrawImage(context, CGRectMake(0.0f, 0.0f, width, height), cgImage);
    CGContextRelease(context);
    
    CGFloat red = (CGFloat)pixelData[0];
    CGFloat green = (CGFloat)pixelData[1];
    CGFloat blue = (CGFloat)pixelData[2];
    
    UIColor *color = [UIColor colorWithRed:(red/255.0f) green:(green/255.0f) blue:(blue/255.0f) alpha:1];
    CGFloat hue = 0;
    CGFloat saturation = 0;
    CGFloat brightness = 0;
    CGFloat alpha = 0;
    [color getHue:&hue saturation:&saturation brightness:&brightness alpha:&alpha];
    saturation = (saturation * 1000.0 + 100)/1000.0;
    brightness = (brightness * 1000.0 - 150)/1000.0;
    UIColor *newColor = [UIColor colorWithHue:hue saturation:saturation brightness:brightness alpha:1];
    
    self.forumColor = newColor;
    // 更新需要颜色的几个地方 1.背景 2.header 3.贡献榜 4.置顶 5.segment
    [[NSNotificationCenter defaultCenter] postNotificationName:@"TTQCircleForumColor" object:self.forumColor];
}
// 置顶、公告section增加渐变层背景色
-(void)setupSectionBg:(BOOL)hasBannerAd hasTopAd:(BOOL)hasTopAd{
    // TODO: 置顶区域的广告加入后，需要重新布局渐层
    NSMutableArray *arr = [[NSMutableArray alloc] initWithArray:self.viewModel.dataSource[0]];
    BOOL hasTalent = self.viewModel.forum.has_expert && self.viewModel.forum.expert.count > 0;
    
    TTQHome5TopicModel *topicModel = arr.lastObject;
    BOOL isLastDesire = NO;
    
    if (topicModel.type == TTQHome5TypeDesiresCard) {
        isLastDesire = YES;
        [arr removeLastObject];
    }
    
    NSInteger section = 0;
    BOOL isClear = NO;
    if(arr.count <= 0 && !hasTopAd){
        if(hasBannerAd || hasTalent){
            [self.headerView changeHeaderHeight:YES];
//            [self.segmentView changeClearBG:NO];
        } else {
            [self.headerView changeHeaderHeight:NO];
        }
        
        if(isLastDesire){
            if(hasTalent){
                self.desireBGClear = NO;
                self.showDesireLine = YES;
            } else{
                self.desireBGClear = YES;
                self.showDesireLine = NO;
            }
//            [self.segmentView changeClearBG:NO];
            [self.segmentView hideLineView:YES];
        } else {
            
            [self.segmentView hideLineView:NO];
        }
        if(hasTalent){
            [self.segmentView changeClearBG:NO];
        } else{
            [self.segmentView changeClearBG:YES];
        }
        
        self.tableView.tableHeaderView = self.headerView;
        if(self.gradientLayer != nil)
        {
            [self.gradientLayer removeFromSuperlayer];
        }
        return;
    }
    
    if(isLastDesire){
        self.desireBGClear = NO;
        self.showDesireLine = YES;
        if(hasTopAd && !self.firstRefreshAd){
            self.firstRefreshAd = YES;
            [self.tableView reloadData];
        }
        [self.segmentView hideLineView:YES];
    } else {
        [self.segmentView hideLineView:NO];
    }
    
    [self.segmentView changeThemeColor:NO forumcolor:self.forumColor];
    
    BOOL hasSubSection = NO;
    hasSubSection = hasTopAd || arr.count > 0;
    // 置顶 公告section加渐层
    if(hasTalent && (arr.count > 0 || hasTopAd)){
        section += 1;
    }
    
    [self.headerView changeHeaderHeight:YES];
    self.tableView.tableHeaderView = self.headerView;
    [self.segmentView changeClearBG:isClear];
    
    CGRect section_rect = [self.tableView rectForSection:section]; // 这里aop了 不需要手动加广告楼层

    if(self.gradientLayer == nil){
        self.gradientLayer = [CALayer layer];
        self.gradientLayer.backgroundColor = [UIColor imy_colorForKey:kIMY_BG].CGColor;
//        self.gradientLayer.colors = @[(id)[UIColor imy_colorForKey:kCK_White_A].CGColor,(id)[UIColor imy_colorForKey:kIMY_BG].CGColor];
//        self.gradientLayer.startPoint = CGPointMake(0.5, .0);
//        self.gradientLayer.endPoint = CGPointMake(0.5, 1.0);
    }

    if(section == 0 && !hasBannerAd){
        self.gradientLayer.cornerRadius = 12;
        self.gradientLayer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
    } else {
        self.gradientLayer.cornerRadius = 0;
    }

    self.gradientLayer.frame = section_rect;
    self.gradientLayer.zPosition = -101;
    [self.tableView.layer insertSublayer:self.gradientLayer atIndex:1];
}

-(void)addTableBgLayer{
    CAGradientLayer *gradientLayer = [CALayer layer];
//    gradientLayer.colors = @[(id)[UIColor imy_colorForKey:kIMY_BG].CGColor,(id)[UIColor imy_colorForKey:kIMY_BG].CGColor];
//    gradientLayer.startPoint = CGPointMake(0.5, .0);
//    gradientLayer.endPoint = CGPointMake(0.5, 1.0);
    gradientLayer.backgroundColor = [UIColor imy_colorForKey:kIMY_BG].CGColor;
    gradientLayer.frame = CGRectMake(0, self.headerView.imy_bottom + 100, SCREEN_WIDTH, SCREEN_HEIGHT*2);
    gradientLayer.zPosition = -103;
    [self.tableView.layer insertSublayer:gradientLayer atIndex:0];
    
    [gradientLayer imy_addThemeChangedBlock:^(CAGradientLayer * weakObj) {
        weakObj.backgroundColor = [UIColor imy_colorForKey:kIMY_BG].CGColor;
    }];
}

@end
