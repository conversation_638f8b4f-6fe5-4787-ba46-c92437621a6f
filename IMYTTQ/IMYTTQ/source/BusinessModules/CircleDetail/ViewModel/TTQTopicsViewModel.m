//
// Created by <PERSON> on 15/5/4.
//
//
#import "TTQHome5TopicModel.h"
#import "TTQTopicsViewModel.h"
#import "TTQTopicsEmptyModel.h"
#import "GCDObjC.h"
#import "TTQJumpType.h"
#import "TTQReadRecord.h"
#import "TTQSignRedirectModel.h"
#import "TTQTopicsTabModel.h"
#import "TTQTopicModel+ExtraData.h"
#import "TTQTopicModel+VideoWaterFollow.h"
#import "TTQTopicDetailViewController.h"
#import "TTQTopicModel+TTQVideoUpload.h"
#import "TTQABTestConfig.h"
#import "TTQHttpHelper.h"
#import "TTQBusinessRequestHelper.h"
#import "TTQDesiresCell.h"
#import <IMYUGC/IMYUGCEventHelper.h>

@interface TTQTopicsViewModel ()
/**
 记录Tab位置是否有获取过缓存数据的标记位，使用二进制，比如 0x0011 表示第一个、第二个Tab有请求到数据
 */
@property (nonatomic, assign) NSInteger tabFirstGetDataFlag;
/**
 首次请求标志位，广告请求使用到
 */
@property (nonatomic, assign) BOOL isFirstRequest;
/**
 第一个Tab首次请求添加is_first参数判断标记
 */
@property (nonatomic, assign) BOOL isFirstTabRequest;

@property (nonatomic, assign) NSUInteger refreshTimes; // 新圈柚+广告算法使用，按每个tab计算刷新次数

@end

@implementation TTQTopicsViewModel
+ (instancetype)viewModelWithforum_id:(NSInteger)forum_id {
    return [[self alloc] initWithforum_id:forum_id];
}

+ (instancetype)viewModelWithForum:(TTQForumModel *)forum {
    return [[self alloc] initWithForum:forum];
}

- (void)dealloc {
    NSLog(@"====dealloc====");
}

- (instancetype)initWithforum_id:(NSInteger)forum_id {
    self = [self init];
    if (self) {
        self.forum_id = forum_id;
        self.forum = [TTQForumModel forumOfID:forum_id];
        [self commonInit];
    }
    return self;
}

- (instancetype)initWithForum:(TTQForumModel *)forum {
    self = [self init];
    if (self) {
        self.forum = forum;
        [self commonInit];
    }
    return self;
}

- (void)commonInit {
    self.tabModel = [TTQTopicsTabModel new];
    self.isFirstRequest = YES;
    self.isFirstTabRequest = YES;
    self.tabModel.brushNumber = 1;
    self.refreshTimes = 0;
    self.uplusBITimes = 0;
    @weakify(self);
    [[[[NSNotificationCenter defaultCenter] rac_addObserverForName:TTQAddTopicModel object:nil] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNotification *note) {
        @strongify(self);
        // 圈子列表发帖后插入
        TTQHome5TopicModel *topicModel = note.object;
        TTQVideoUploadObject *videoUploadObject = [TTQVideoUploadObject new];
        topicModel.updated_date = [[NSDate date] imy_getDateTimeString];
        topicModel.reviewed_date = topicModel.updated_date;
        topicModel.videoUploadObject = videoUploadObject;
        if ([@"publish_date_desc" isEqualToString:self.tabModel.code] || [@"reviewed_date_desc" isEqualToString:self.tabModel.code]) {
            if (topicModel.forum_id == self.forum_id) {
                imy_asyncMainBlock(^{
                    [self.topics insertObject:topicModel atIndex:0];
                    [self updateTopicsEmptyState];
                    self.sourceChange = true;
                    self.addTopChange = YES;
                });
            }
        }
    }];
    [[RACObserve(self, forum.is_show_image) distinctUntilChanged] subscribeNext:^(id x) {
        @strongify(self);
        NSArray *topics = self.topics;
        [topics bk_each:^(TTQHome5TopicModel *model) {
            if ([model isKindOfClass:TTQHome5TopicModel.class]) {
                model.cellHeight = 0;
                
                // 处理无图模式
                if (!self.forum.is_show_image) {
                    model.imageCount = MAX(model.images.count, model.answer.images.count);
                    
                    if (model.isAnswerTopic) {
                        model.images = nil;
                        model.answer.images = nil;
                    }
                }
            }
        }];
    }];
    
    // 初始化设置数据为空
    [self setTopTopics:nil topics:nil];
}

#pragma mark - 反馈按钮删除对应的数据
- (void)removeObject:(id)object atIndexPath:(NSIndexPath *)indexPath {
    id model = [self tableCellModelAtIndexPath:indexPath];
    if ([model isKindOfClass:[TTQTopicModel class]]) {
        TTQHome5TopicModel *sectionModel;
        if (indexPath.section == 0) {
            sectionModel = self.topTopics[indexPath.row];
        }else{
            sectionModel = self.topics[indexPath.row];
        }
        if (sectionModel.type == TTQHome5TypeDesiresCard) {
            sectionModel.cellHeight = NSIntegerMax;
            NSMutableArray *ary = [self.dataSource firstObject];
            if ([ary containsObject:sectionModel]) {
                [ary removeObject:sectionModel];
            }
        }else{
            if (model == sectionModel) {
                sectionModel.cellHeight = NSIntegerMax;
            }
        }
        self.feedsSourceChange = YES;
        [self saveCacheWithCurSource];
    }
}

#pragma mark - source data
- (void)setTopTopics:(NSArray *)topTopics topics:(NSArray *)topics {
    if (topTopics.count || topics.count) {
        NSMutableArray *tmpTopTopics = [NSMutableArray new];
        NSMutableArray *tmpTopics = [NSMutableArray new];
        
        // 置顶帖处理
        if (topTopics.count) {
            [tmpTopTopics addObjectsFromArray:topTopics];
        }
        
        // 流帖子处理
        if (topics && topics.count > 0) {
            [tmpTopics addObjectsFromArray:topics];
        }
        
        //删除旧的私域群入口
        tmpTopTopics = [tmpTopTopics bk_select:^BOOL(id obj) {
            if ([obj isKindOfClass:TTQHome5TopicModel.class]) {
                TTQHome5TopicModel *topicModel = (TTQHome5TopicModel *)obj;
                if (topicModel.type == TTQHome5TypeDesiresCard) {
                    return NO;
                }
            }
            return YES;
        }].mutableCopy;
        
        self.dataSource = @[tmpTopTopics, tmpTopics];
        
        //加入私域群
        IMYABTestVariables *vars = [[IMYCommonConfig sharedInstance] configForKey:@"Private_sphere"];
        NSInteger ttqsy = [vars integerForKey:@"qzxqy"];
        NSArray *qzAry = [[vars stringForKey:@"qzID"] componentsSeparatedByString:@","];
        NSString *qzIdString = [NSString stringWithFormat:@"%ld",self.forum_id];
        BOOL isQzId = NO;
        if ([qzAry containsObject:qzIdString]) {
            isQzId = YES;
        }
        if (ttqsy == 1 && ![TTQDesiresCell checkTheData:NO] && isQzId) {
            TTQHome5TopicModel *desiresModel = [[TTQHome5TopicModel alloc] init];
            desiresModel.type = TTQHome5TypeDesiresCard;
            NSMutableArray *ary = [self.dataSource firstObject];
            [ary addObject:desiresModel];
            self.dataSource = @[ary, tmpTopics];
        }
        self.groupSourceChange = YES;
    } else {
        // 新圈详情页不需要加占位model，直接用vc的captionview
        self.dataSource = @[[NSMutableArray array], [NSMutableArray array]];
    }
}

/**
 点赞
 
 @param param 参数
 @param completion YES or NO
 */
- (void)doPriaseWithParam:(NSDictionary *)param completion:(IMYTopicsVCompletionBlk)completion {
    [TTQBusinessRequestHelper requestPriaseWithParam:param useABTest:NO completion:completion];
}

//MARK: - private methods

- (void)setDataSource:(NSArray *)dataSource {
    [super setDataSource:dataSource];
}

- (NSMutableArray *)topTopics {
    return self.dataSource.firstObject;
}

-(void)changeTopTopics:(NSMutableArray *)topTopics{
    NSMutableArray *arr = self.dataSource.firstObject;
    if(arr != nil){
        NSMutableArray *dataS = [NSMutableArray arrayWithArray:self.dataSource];
        if (topTopics == nil) {
            topTopics = [NSMutableArray new];
        }
        dataS[0] = topTopics;
        self.dataSource = [dataS copy];
    }
}

- (void)setTopics:(NSArray *)topics {
    NSMutableArray *array = [NSMutableArray arrayWithArray:self.dataSource];
    array[0] = self.dataSource.firstObject ? self.dataSource.firstObject : @[];
    NSMutableArray *showTopics = [NSMutableArray array];
    if ([topics isKindOfClass:NSArray.class] && topics.count > 0) {
        [showTopics addObjectsFromArray:topics];
    }
    array[1] = showTopics;
    self.dataSource = array;
}

- (NSMutableArray *)topics {
    if (self.dataSource.count > 1) {
        return self.dataSource[1];
    }
    return nil;
}

/// 判断是否需要选中最新Tab
- (BOOL)shouldSetNewTabSelected {
    // 不需要的定位到最新的情况：
    // 1、source_from == 1
    // 2、order_by != publish_date_desc
    return [self.forum.order_by isEqualToString:@"publish_date_desc"] && self.source_from != 1 && self.source_from != 2;
}

/**
 更新话题状态，有帖子的时候删除EmptyModel，没有帖子的时候添加EmptyModel
 */
- (void)updateTopicsEmptyState {
    return;
    if (self.topicsCount == 0) {
        // 删除旧的EmptyModel，添加EmptyModel到最后
        if (self.topics) {
            NSMutableArray *tmpTopics = [self.topics bk_select:^BOOL(id obj) {
                return ![obj isKindOfClass:TTQTopicsEmptyModel.class];
            }].mutableCopy;
            [tmpTopics addObject:[TTQTopicsEmptyModel new]];
            self.topics = tmpTopics;
        }
    } else {
        NSMutableArray *tmpTopics = [self.topics bk_select:^BOOL(id obj) {
            return ![obj isKindOfClass:TTQTopicsEmptyModel.class];
        }].mutableCopy;
        self.topics = tmpTopics;
    }
}

- (NSInteger)allDataSourceCount {
    return self.topicsCount + self.topTopicsCount;
}

- (NSInteger)topTopicsCount {
    return self.topTopics.count;
}

- (NSInteger)topicsCount {
    return [self.topics bk_select:^BOOL(id obj) {
        return [obj isKindOfClass:TTQHome5TopicModel.class];
    }].count;
}

- (NSInteger)numberOfSections {
    return 1;
}

- (NSInteger)numberOfRowsInSection:(NSInteger)section {
    return self.topics.count;
}

- (id)tableCellModelAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath == nil) {
        return nil;
    }
    if (indexPath.section == 0 && indexPath.row >= self.topics) {
        return nil;
    }
    TTQHome5TopicModel *topic = self.topics[indexPath.row];
    return topic;
}

- (id)tableSectionModelWith:(NSInteger)section {
    // TODO: 如广告需要用此方法，需矫正位置
    if (section >= self.numberOfSections) {
        return nil;
    }
    if (section < 0) {
        return nil;
    }
    if (section == 0) {
        return self.topTopics;
    }
    return self.topics[section - 1];
}

- (NSString *)identifierRowAtIndexPath:(NSIndexPath *)indexPath {
    // 新圈子详情页返回
    TTQHome5TopicModel *topicModel = [self tableCellModelAtIndexPath:indexPath];
    if ([topicModel isKindOfClass:TTQHome5TopicModel.class]) {
//            if (topicModel.is_special) {
//                if (topicModel.attr_type == TTQHome5SpecialAttrtypeCSPic) {
//                    return @"TTQTopicOneCenterImageBaseCell";
//                }
//                return @"TTQTopicThreeImageBaseCell";
//            }
//            if (topicModel.type == TTQHome5TypeCard && topicModel.attr_type == TTQHome5CardAttrtypeCustom) {
//                //自定义卡片
//                if (topicModel.show_style == TTQHome5CardOneImageType) {
//                    return @"TTQTopicOneSideImageBaseCell";
//                } else if (topicModel.show_style == TTQHome5CardThreeImagesType) {
//                    return @"TTQTopicThreeImageBaseCell";
//                } else {
//                    return @"TTQTopicOneCenterImageBaseCell";
//                }
//            }
        if (topicModel.model_type == TTQTopicModelTypeVideo && ![topicModel isValidVoteCard]) {
            return @"TTQNewFirstPageVideoCell"; //视频Cell
        }
        if (topicModel.type == TTQHome5TypeTopic && topicModel.attr_type == TTQHome5CardAttrtypeVideo) {
            return @"TTQNewFirstPageVideoCell";
        }
        if (topicModel.type == TTQHome5TypeFromNews
            || topicModel.type == TTQHome5TypeTopic) {
            // 处理资讯引入内容和话题帖中的视频内容
            if (topicModel.model_type == TTQTopicModelTypeVideo) {
                if (topicModel.image_type == TTQTopicImageTypePortrait) {
                    return @"TTQNewFirstPageVideoCell";
                }
            }
        }
        return @"TTQNewFirstPageCell";
    } else if ([topicModel isKindOfClass:TTQTopicsEmptyModel.class]) {
        return @"TTQTopicsEmptyCell";
    }
    return @"TTQNewFirstPageCell";
}

//圈子首页的样式
- (NSInteger)pageType{
    return 0;
}

#pragma mark - 广告
/**
 刷新广告的请求，之所以封装起来是因为广告依赖于 forum，而第一次进入到话题时，forum == nil，所以需要在 forum 更新之后刷新广告
 */
- (void)refreshAdManager {
    if (self.topicsCount <= 0) {
        return; //tab底部流无数据不请求广告
    }
    
    [[self.adManager adInfo] unlock];
    //圈内新样式暂时隐藏列表内广告
    [[self.adManager adInfo] appendUserInfo:@{ @"showtopads": @([@"reviewed_date_desc" isEqualToString:self.tabModel.code]),
                                               @"isshowimage": @(self.forum.is_show_image),@"pagetype":@(self.pageType)}];
    [[self.adManager adInfo] appendUserInfo:@{@"hiddenFeeds":self.pageType?@(YES):@(NO)}];
    [[self.adManager adInfo] appendUserInfo:@{@"fresh":@(self.topicsCount)}]; //帖子条数
    [[self.adManager adInfo] appendUserInfo:@{@"noData": @(self.topicsCount == 0)}];
    [[self.adManager adInfo] appendUserInfo:@{@"totalDataSourceCount": @(self.topicsCount)}];
    
    [[self.adManager adInfo] lock];
    [self.adManager refreshData];
    
    // 贴边广告刷新
    [self.sideAdManager refreshData];
}

- (void)updateAdManagerConfig {
    [[self.adManager adInfo] unlock];
    [[self.adManager adInfo] appendUserInfo:@{@"noData": @(self.topicsCount == 0)}];
    [[self.adManager adInfo] lock];
}

#pragma mark - request
- (RACSignal *)requestRemoteDataForType:(NSInteger)type params:(NSDictionary *)parameters {
    @weakify(self);
    __block BOOL manualTriggered = [parameters[@"manualTriggered"] boolValue];
    __block BOOL tmpIsFirstRequest = self.isFirstRequest;
    NSDictionary *requestParams = [parameters bk_reject:^BOOL(id key, id obj) {
        if ([key isKindOfClass:NSString.class] && [((NSString *)key) containsString:@"manualTriggered"]) {
            return YES;
        }
        return NO;
    }];
    // brushnumber旧版再使用，不改变逻辑，新版用refreshtimes做区分
    self.refreshTimes += 1;
    if(type <= 0){
        self.uplusBITimes += 1;
    }
    self.isForumListRequesting = YES;
    return [[[[self httpBuilderForNextPage:type andInput:requestParams] deliverOnMainThread] doNext:^(id<IMYHTTPResponse> response) {
        @strongify(self);
        self.isFirstRequest = NO;
        self.newestTopicId = 0;
        self.locate_topic_id = 0;
        self.isForumListRequesting = NO;
        NSArray *top_topics = [response.responseObject[@"top_topics"] toModels:[TTQHome5TopicModel class]];
        NSArray *rawTopics = response.responseObject[@"topics"];
        self.is_user_operate = [response.responseObject[@"is_user_operate"] integerValue];
        if (response.responseObject[@"resource_list"]) {
            self.resource_list = response.responseObject[@"resource_list"];
        }
        //893版本新增，本次列表最后一个last信息，可能为帖子id或时间等， 请求下一页时需要作为参数传入
        self.lastId = response.responseObject[@"last"];
        
        NSMutableArray *topics = [NSMutableArray array];
        if ([rawTopics isKindOfClass:NSArray.class]) {
            for (NSDictionary *dic in rawTopics) {
                if ([dic isKindOfClass:NSDictionary.class]) {
                    TTQHome5TopicModel *topicModel = [dic toModel:[TTQHome5TopicModel class]];
                    [topics addObject:topicModel];
                }
            }
        }
        NSDictionary *userInfo = response.responseObject[@"user_info"];
        BOOL tabIsNew = self.tabIsNew;
        BOOL has_more = [response.responseObject[@"is_more"] boolValue];
        self.automaticallyRefresh = has_more;
        
        if (userInfo&&[userInfo.allKeys containsObject:@"error"]) {
            NSNumber *errorNum = userInfo[@"error"];
            self.user_infoCode = errorNum.integerValue;
        }
        
        [top_topics bk_each:^(TTQHome5TopicModel *model) {
            if ([model isKindOfClass:[TTQHome5TopicModel class]] && self.shouldShowReadStatus) {
                model.hasShow = [TTQReadRecord hasRead:model.redirect_url inPage:self.ttqReadPage];
            }
        }];
        [topics bk_each:^(TTQHome5TopicModel *model) {
            if ([model isKindOfClass:[TTQHome5TopicModel class]]) {
                if (self.shouldShowReadStatus) {
                    // 处理已读状态
                    model.hasShow = [TTQReadRecord hasRead:model.redirect_url inPage:self.ttqReadPage];
                }
                
                // 处理无图模式
                if (!self.forum.is_show_image) {
                    model.imageCount = MAX(model.images.count, model.answer.images.count);

                    if (model.isAnswerTopic) {
                        model.images = nil;
                        model.answer.images = nil;
                    }
                }
            }
        }];
        
        NSArray *adArray = nil;
        if (type == 0) {
            adArray = topics;
            if (top_topics.count > 0 || topics.count > 0) {
                [self setTopTopics:top_topics topics:topics];
            } else {
                [self setTopTopics:nil topics:nil];
            }
            if (top_topics.count > 0) {
                self.topTopicChanged = YES;
            }
            [self updateOrSaveCacheWithResponse:response];
        } else {
            if (topics.count) {
                // 过滤重复数据
                NSArray *insertTopics = [topics bk_select:^BOOL(TTQHome5TopicModel *obj) {
                    for (TTQHome5TopicModel *existTopic in self.topics) {
                        if (![existTopic isKindOfClass:TTQHome5TopicModel.class] || ![obj isKindOfClass:TTQHome5TopicModel.class]) {
                            continue;
                        }
                        if (existTopic.topic_id == obj.topic_id) {
                            return NO;
                        }
                    }
                    return YES;
                }];
                [self.topics addObjectsFromArray:insertTopics];
                adArray = insertTopics;
                [self updateTopicsEmptyState];
            }
        }
        
        NSMutableArray *totalArticelIdModels = [[NSMutableArray alloc] init];
        if (adArray.count > 0) { //本次刷新业务内容id要上报
            [totalArticelIdModels addObjectsFromArray:adArray];
        }
        if (type != 0 && self.lastOnceItems.count > 0) { //上一刷的业务内容id也要上报
            [totalArticelIdModels addObjectsFromArray:self.lastOnceItems];
        }
        self.lastOnceItems = adArray;
        
        NSString *session_id = [IMYGAEventHelper sessionID];
        /// 开始刷新广告
        NSMutableDictionary *adUserInfo = [[NSMutableDictionary alloc] init];
        adUserInfo[@"refresh_id"] = @(self.refreshTimes); //todo 柚+广告 参数
        if (imy_isNotBlankString(session_id)) {
            adUserInfo[@"session_id"] = session_id; //todo 柚+广告 参数
        }

        if (type == 0) { //下拉刷新
            [self.adManager.adInfo unlock];
            [self.adManager.adInfo appendUserInfo:adUserInfo];
            [self.adManager.adInfo lock];
            [self refreshAdManager];
        }else {
            //加载更多
            NSArray *adItems = [self.topics filter:^BOOL(TTQHome5TopicModel *element) {
                return element.isAd;
            }];
            NSInteger totalDataSourceCount = self.topicsCount - adItems.count;
            adUserInfo[@"totalDataSourceCount"] = @(totalDataSourceCount);
            [self.adManager.adInfo unlock];
            [[self.adManager adInfo] appendUserInfo:adUserInfo];
            [self.adManager.adInfo lock];
            [self.adManager loadMoreData:totalDataSourceCount];
        }
        NSLog(@"requestRemoteDataForType=====");
    }] doError:^(NSError *error) {
        // 刷新失败，重置次数
        NSInteger refreshTime = self.refreshTimes - 1;
        if(refreshTime < 0){
            self.refreshTimes = 0;
        } else {
            self.refreshTimes = refreshTime;
        }
        if(type <= 0){
            NSInteger uplusBITimes = self.uplusBITimes - 1;
            if(uplusBITimes < 0){
                self.uplusBITimes = 0;
            } else {
                self.uplusBITimes = uplusBITimes;
            }
        }
        self.isForumListRequesting = NO;
    }];
}

/**
 投票
 @param topicModel 投票对应的帖子
 @param selectedItems 投票
 @param biParams BI埋点参数
 */
- (void)requestVoteWithTopicModel:(TTQTopicModel *)topicModel selectedItems:(NSArray *)selectedItems biParams:(NSDictionary *)biParams {
    @weakify(self);
    [TTQBusinessRequestHelper requestVoteWithTopicModel:topicModel selectedItems:selectedItems biParams:biParams resultBlock:^(BOOL success) {
        @strongify(self);
        if (success) {
            // 通知更新类表
            self.sourceChange = YES;
        }
    }];
}

- (RACCommand *)signInCommand {
    if (!_signInCommand) {
        _signInCommand = [RACCommand commandWithSignalBlock:^RACSignal *(id input) {
            NSString *path = [NSString stringWithFormat:@"checkin?forum_id=%@", input];
            if ([TTQSignRedirectModel isRedirect_on]) {
                path = [NSString stringWithFormat:@"%@&redirect_on=%@&mode=%@", path, @1, @([IMYPublicAppHelper shareAppHelper].userMode)];
            }
            return [TTQHttpHelper postPath:path params:nil];
        }];
    }
    return _signInCommand;
}

- (RACSignal *)httpBuilderForNextPage:(BOOL)nextPage andInput:(NSDictionary *)input {
    NSMutableDictionary *parameters = [[NSMutableDictionary alloc] init];
    if (self.locate_topic_id) {
        parameters[@"locate_topic_id"] = @(self.locate_topic_id);
    }
    if (self.priorityid) {
        [parameters setValue:@(self.priorityid) forKey:@"priorityid"];
    }
    
    // 公共的分页参数
    if (nextPage && imy_isNotEmptyString(self.lastId)) {
        [parameters setValue:self.lastId forKey:@"last"];
    }
    if (nextPage) {
        [parameters setValue:@(++self.tabModel.brushNumber) forKey:@"brush_num"];
    } else {
        self.tabModel.brushNumber = 1;
        [parameters setValue:@(self.tabModel.brushNumber) forKey:@"brush_num"];
    }
    if (imy_isNotEmptyString(self.tabModel.code)) {
        [parameters setValue:self.tabModel.code forKey:@"tab_code"];
    }
    // 针对不同的Tab进行特殊处理参数
    if (self.tabModel.tab_id > 0) {
        parameters[@"tab_id"] = @(self.tabModel.tab_id);
    }
    
    //最新评论保留这些参数
    if ([@"reviewed_date_desc" isEqualToString:self.tabModel.code]) {
        if (self.newestTopicId > 0) {
            [parameters setValue:@(self.newestTopicId) forKey:@"priorityid"];
        }
        if (nextPage) {
            if (self.source_from == 1 || self.source_from == 2) {
                parameters[@"source_from"] = @(self.source_from);
            }
        } else {
            if (self.source_from == 1 || self.source_from == 2) {
                parameters[@"source_from"] = @(self.source_from);
            }
        }
    }
    
    if (self.currentTagModel.tagID > 0) {
        [parameters setValue:@(self.currentTagModel.tagID) forKey:@"tag_id"];
    }
    [parameters setValue:@"20" forKey:@"size"];
    [parameters setValue:@(self.forum_id) forKey:@"forum_id"];

    if (self.filterKeys) {
        NSString *filterKeyString = [self.filterKeys imy_jsonString];
        [parameters setValue:filterKeyString forKey:@"filterKeys"];
    }
    if (self.pageType) {
        [parameters setValue:@(2) forKey:@"feeds_type"];//如果是新圈子样式
    }

    [parameters setValuesForKeysWithDictionary:input];
    return [TTQHttpHelper getPath:@"v2/forum_topic_list" params:parameters];
}

- (RACSignal *)checkTopicOperation:(TTQHome5TopicModel *)topic {
    NSDictionary *params = @{@"topic_id":@(topic.topic_id),
                             @"forum_id":@(self.forum_id),
                             @"source":@(1)};
    return [TTQHttpHelper getPath:@"v2/check_user_operate" params:params];
}

- (RACSignal *)joinTopicToHighPool:(TTQHome5TopicModel *)topic {
    NSDictionary *params = @{@"topic_id":@(topic.topic_id),
                             @"forum_id":@(self.forum_id),
                             @"item_type":@(1)};
    IMYHTTPBuildable *paramBuild = [IMYServerRequest get:@"v2/user_operate" host:circle_seeyouyima_com params:params headers:nil];
    return paramBuild.signal;
}

- (RACSignal *)joinTopicToLowPool:(TTQHome5TopicModel *)topic {
    NSDictionary *params = @{@"topic_id":@(topic.topic_id),
                             @"forum_id":@(self.forum_id),
                             @"item_type":@(2)};
    IMYHTTPBuildable *paramBuild = [IMYServerRequest get:@"v2/user_operate" host:circle_seeyouyima_com params:params headers:nil];
    return paramBuild.signal;
}

- (RACSignal *)hiddenTopic:(TTQHome5TopicModel *)topic {
    NSDictionary *params = @{@"topic_id":@(topic.topic_id),
                             @"forum_id":@(self.forum_id),
                             @"item_type":@(4)};
    IMYHTTPBuildable *paramBuild = [IMYServerRequest get:@"v2/user_operate" host:circle_seeyouyima_com params:params headers:nil];
    return paramBuild.signal;
}

- (RACSignal *)getForumList {
    return [TTQHttpHelper getPath:@"v2/get_forum_category_list" params:nil];
}

- (RACSignal *)transferTopic:(TTQHome5TopicModel *)topic ToForum:(NSInteger)toForumId  move_status:(NSInteger)moveStatus{
    NSDictionary *params = @{@"topic_id":@(topic.topic_id),
                             @"forum_id":@(self.forum_id),
                             @"item_type":@(3),
                             @"to_forum_id":@(toForumId),
                             @"move_status":@(moveStatus)
    };
    IMYHTTPBuildable *paramBuild = [IMYServerRequest get:@"v2/user_operate" host:circle_seeyouyima_com params:params headers:nil];
    return paramBuild.signal;
}

- (TTQHome5TopicModel *)lastContainReviewedDataTopicInFeedsDatas {
    for (int i = (int)self.topics.count - 1; i>=0; i--) {
        TTQHome5TopicModel *data = self.topics[i];
        if ([data isKindOfClass:TTQHome5TopicModel.class]
            && imy_isNotEmptyString(data.reviewed_date)
            && data.type == TTQHome5TypeTopic && !data.isAd) {
            return data;
        }
    }
    return nil;
}

- (TTQHome5TopicModel *)lastTopicInFeedsDatas {
    for (int i = (int)self.topics.count - 1; i>=0; i--) {
        TTQHome5TopicModel *data = self.topics[i];
        if ([data isKindOfClass:TTQHome5TopicModel.class]) {
            if (data.type == TTQHome5TypeTopic && !data.isAd) {
                return data;
            }
        }
    }
    return nil;
}

- (TTQHome5TopicModel *)firstTopicInFeedsDatas {
    for (int i = 0; i<self.topics.count; i++) {
        TTQHome5TopicModel *data = self.topics[i];
        if ([data isKindOfClass:TTQHome5TopicModel.class]) {
            return data;
        }
    }
    return nil;
}

#pragma mark - custom action
//是否显示图片
- (BOOL)isShowImage:(NSIndexPath *)indexPath {
    if (!self.forum.is_show_image) {
        TTQHome5TopicModel *section = [self tableCellModelAtIndexPath:indexPath];
        if ([section isKindOfClass:[TTQHome5TopicModel class]]) {
            if (section.type == TTQHome5TypeCard && section.attr_type == TTQHome5CardAttrtypeCustom) {
                return YES;
            } else if (section.is_activity) {
                return YES;
            } else if (section.is_special) {
                return YES;
            } else if (section.isCircleRecommendCell) {
                return YES;
            }
        }
        return NO;
    }
    return YES;
}

/// 是否使用”topic_categoty“字段生成标签
- (BOOL)useCategoryGenerateTags {
    return YES;
}

- (void)gotoHonorHallDirect:(BOOL)bDirect {
    if ([IMYPublicAppHelper shareAppHelper].hasLogin) {
        [IMYEventHelper event:@"qz-drt"];
        [[IMYURIManager shareURIManager]runActionWithString:self.forum.expert_redirect_url];
    } else {
        [UIWindow imy_showTextHUD:kStatusText_unLogin];
        [[IMYURIManager shareURIManager] runActionWithString:@"login"];
    }
}

- (NSInteger)forum_id {
    return _forum ? _forum.forum_id : _forum_id;
}

- (BOOL)isShowExpert {
    return self.forum.expert_show_type == 1;
}

- (void)navigateWithSpecialIndex:(NSInteger)index {
    if (!self.forum.special_topic || self.forum.special_topic.count <= index) {
        return;
    }
    TTQSpecialTopic *sp = [[self.forum.special_topic imy_objectAtIndex:index] toModel:[TTQSpecialTopic class]];
    if (!sp) {
        return;
    }
    if (sp.redirect_url) {
        [[IMYURIManager shareURIManager] runActionWithString:sp.redirect_url];
        return;
    }
    //以下为6.6之前的跳转，过两个版本后去掉吧
    UIViewController *navi = [UIViewController imy_currentTopViewController];
    if (sp.type == 85) { //达仁堂
        [self gotoHonorHallDirect:YES];
    } else if (sp.type == 1) {
        [IMYEventHelper event:@"zt-1"];
        TTQTopicDetailViewController *detailViewController =
            [[TTQTopicDetailViewController alloc] initWithViewModel:[[TTQTopicViewModel alloc] initWithTopicID:sp.attr_id]];
        [navi imy_push:detailViewController];
    } else if (sp.type == 2) {
        [IMYEventHelper event:@"zt-2"];
        [[IMYURIManager shareURIManager] runActionWithPath:@"circles/group" params:@{ @"groupID": @(sp.attr_id), @"disableGAEvent": @(YES) } info:nil];
    } else if (sp.type == 3) {
        [IMYEventHelper event:@"zt-3"];
        [[UIApplication sharedApplication] openURL:[NSURL URLWithString:sp.url] options:nil completionHandler:nil];
    } else if (sp.type == 4) {
        [IMYEventHelper event:@"zt-4"];
        IMYVKWebViewController *web = [[IMYVKWebViewController alloc] init];
        web.urlString = sp.url;
        [navi imy_push:web];
    } else if (sp.type == 70) {
        [IMYEventHelper event:@"zt-70"];
        [[IMYURIManager shareURIManager] runActionWithPath:@"news/special" params:@{ @"special_id": @(sp.attr_id) } info:nil];
        //下面这个只为了给统计用，跟跳转所约定的有所不同
        IMYURI *uri = [IMYURI uriWithPath:@"news/special"
                                   params:@{ @"project_id": @(sp.attr_id),
                                             @"redirect-type": @(14) }
                                     info:nil];
        [TTQCommonHelp GAEventForInformationWithURL:uri.uri floor:0 action:2 params:@{ @"entrance": @(2) }];
    }
}

#pragma mark - cache
- (void)clearOldCacheBelow6_1 {
    NSString * cacheKey = [NSString stringWithFormat:@"community/forums/%ld/topics_Tab_%0ld/6.1", (long)self.forum_id, (long)self.tabModel.tab_id];
    [IMYCacheHelper setCacheData:nil forKey:cacheKey];
}

- (NSString *)cacheKey {
    NSString *newKey = [NSString stringWithFormat:@"community/forums/%ld/newtopics_Tab_%0ld/6.1/source_%ld", (long)self.forum_id, (long)self.tabModel.tab_id, self.source_from];
    return [newKey imy_sha1];
}

- (void)saveCacheWithCurSource {
    NSMutableDictionary *dic = [NSMutableDictionary dictionary];

    NSMutableArray *topicsDictionaries = [[NSMutableArray alloc] init];
    NSArray *topTopics = [self.topTopics copy];
    [topTopics enumerateObjectsUsingBlock:^(TTQHome5TopicModel *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
        if (obj.cellHeight != NSIntegerMax && obj.type != TTQHome5TypeDesiresCard){
            id jsonObj = obj.YYJSONDictionary;
            if (jsonObj) {
                [topicsDictionaries addObject:jsonObj];
            }
        }
    }];
    dic[@"top_topics"] = topicsDictionaries;

    topicsDictionaries = [[NSMutableArray alloc] init];
    NSArray *topics = [self.topics copy];
    [topics enumerateObjectsUsingBlock:^(TTQHome5TopicModel *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
        if ([obj isKindOfClass:TTQHome5TopicModel.class]) {
            id jsonObj = obj.YYJSONDictionary;
            if (jsonObj) {
                [topicsDictionaries addObject:jsonObj];
            }
        }
    }];
    dic[@"topics"] = topicsDictionaries;
    [self saveCache:[dic imy_jsonData]];
    self.feedsSourceChange = YES;
}

- (void)updateOrSaveCacheWithResponse:(id<IMYHTTPResponse>) response {
    NSDictionary *cacheDataDict = [response.responseData imy_jsonObject];
    NSMutableDictionary *existsDataDict = [self cacheDictionary].mutableCopy;
    if (!existsDataDict) {
        existsDataDict = [NSMutableDictionary dictionary];
    }
    NSArray *topics = cacheDataDict[@"topics"];
    if ([topics isKindOfClass:NSArray.class] && topics.count > 0) {
        existsDataDict[@"topics"] = topics;
    }
    NSArray *topTopics = cacheDataDict[@"top_topics"];
    existsDataDict[@"top_topics"] = topTopics;
    
//    existsDataDict[@"is_user_operate"] = @(self.is_user_operate);
    
    NSData *cacheData = [existsDataDict imy_jsonData];
    [self saveCache:cacheData];
}

- (void)saveCache:(NSData *)data {
    [IMYCacheHelper setCacheData:data forKey:self.cacheKey];
}

- (NSDictionary *)cacheDictionary {
    NSData *responseData = [IMYCacheHelper cacheDataForKey:self.cacheKey];
    return [responseData imy_jsonObject];
}

- (void)removeCacheData {
    [IMYCacheHelper setCacheData:nil forKey:self.cacheKey];
}

- (void)dataSourceFromCache {
    NSDictionary *dic = [self cacheDictionary];
    // 进行赋值
    NSArray *top_topics = [dic[@"top_topics"] toModels:[TTQHome5TopicModel class]];
    if (self.topTopicsCount) {
        top_topics = self.topTopics;
    }
    NSArray *topics = [dic[@"topics"] toModels:[TTQHome5TopicModel class]];
    
    [topics bk_each:^(TTQHome5TopicModel *model) {
        if ([model isKindOfClass:[TTQHome5TopicModel class]] && self.shouldShowReadStatus) {
            // 处理已读状态
            model.hasShow = [TTQReadRecord hasRead:model.redirect_url inPage:self.ttqReadPage];
            
            // 处理无图模式
            if (!self.forum.is_show_image) {
                model.imageCount = MAX(model.images.count, model.answer.images.count);
                if (model.isAnswerTopic) {
                    model.images = nil;
                    model.answer.images = nil;
                }
            }
        }
    }];
//    self.is_user_operate = [dic[@"is_user_operate"] integerValue];
    [self setTopTopics:top_topics topics:topics];
}

- (BOOL)dataSourceFromCacheExist {
    TTQForumModel *forum = [TTQForumModel forumOfID:self.forum.forum_id];
    if (forum != nil) {
        self.forum = forum;
        return YES;
    }
    return NO;
}

- (BOOL)dataSourceFromCacheWithTabIndex:(NSInteger)tabIndex {
    if (self.topicsCount == 0) {
        [self dataSourceFromCache];
    }
    BOOL isTabGetDatas = self.tabFirstGetDataFlag & (1 << tabIndex);
    self.tabFirstGetDataFlag |= (1 << tabIndex);
    return !isTabGetDatas;
}


#pragma mark - section header or footer dataSource

- (BOOL)isHiddenPartSectionHeader:(NSInteger)section {
    if (section == 0) {
        return NO;
    }
    return NO;
}

- (NSInteger)getLustsIndex{
    if (self.topTopics.count) {
        for (NSInteger i = 0; i < self.topTopics.count; i ++) {
            TTQHome5TopicModel *data = self.topTopics[i];
            if ([data isKindOfClass:TTQHome5TopicModel.class]) {
                if (data.type == TTQHome5TypeDesiresCard) {
                    return i;
                }
            }
        }
    }
    return -1;
}

- (NSDictionary *)sectionHeaderDic:(NSInteger)section {
    if (section == 0) {
        return nil;
    }
    return nil;
}

//有灰色条都会优先在footer上，上一个section有下一个section也需要的话，一定是下一个section的header丢掉灰色，保留上一个section的footer灰色
- (NSDictionary *)sectionFooterDic:(NSInteger)section {
    if (section == 0) {
        return @{ @"height": @(10) };
    }
    return nil;
}

#pragma mark - 
- (NSArray *)subjectBannerData {
    if (self.resource_list.count) {
        for (NSDictionary *dic in self.resource_list) {
            /// 获取绑定的话题数据
            if ([dic[@"code"] isEqualToString:@"forum_subject"]) {
                if (dic[@"material_list"]) {
                    return dic[@"material_list"];
                }
                break;
            }
        }
    }
    return nil;
}


#pragma mark - new BI

- (void)postBIwithAction:(NSInteger)action index:(NSIndexPath *)indexPath model:(TTQHome5TopicModel *)model clickpos:(NSUInteger)clickpos
{
    //action 1曝光 2跳转点击 3无跳转点击
    if (model.isAd) {
        // 广告位展示柚加文章的埋点在这里单独处理：
        // 1.曝光上报showpingurls 2.点击上报clickpingurls 3.重复的文章id要重复曝光（广告的测试会拿重复数据放在首页去测试）
        // 补充：广告产品要求不进详情页的点击位置不上报点击埋点
        // redirect_url 解析后拿到show_ping_urls click_ping_urls
        IMYURI *uri = [IMYURI uriWithURIString:model.redirect_url];
        NSDictionary *param = uri.params;
        NSArray *show_ping_urls = [param objectForKey:@"show_ping_urls"];
        NSArray *click_ping_urls = [param objectForKey:@"click_ping_urls"];
        if (action == 1) {
            // 曝光
            for (NSString *url in show_ping_urls) {
                [self requestUPlusBi:url];
            }
        } else if (action == 2 && (clickpos == 0 || clickpos == 6)){
            // 点击到有跳转到详情页的区域
            for (NSString *url in click_ping_urls) {
                [self requestUPlusBi:url];
            }
        }
        return; // 广告不上报bi_feeds_view埋点，我们的产品不希望有这些数据
    }
    NSInteger exceptAdFloor = [self floorWithIndexPath:indexPath];
    NSMutableDictionary *params = @{@"floor" : @(exceptAdFloor),
                                    @"action" : @(action),
                                    @"entrance" : @(2),
                                    @"position" : @(83),
                                    }.mutableCopy;
    if (action > 1) {
        params[@"clickpos_feedscard"] = @(clickpos);
    }
    params[@"info_type"] = @(12);
    params[@"forum_id"] = @(self.forum_id);
    params[@"sub_tab"] = @(self.currentIndex);
    params[@"info_id"] = @(model.topic_id);
    NSString *redirect_url = model.redirect_url;
    if (model.type == TTQHome5TypeNewHotTopicCard) {
        /// 热议话题的曝光、点击uri不同
        redirect_url = action == 1?model.card_redirect_uri:model.click_card_redirect_uri;
    }
    IMYURI *uri = [IMYURI uriWithURIString:redirect_url];
//    params[@"review_num"] = @(model.total_view);
//    params[@"like_num"] = @(model.praise_num);
    if (model.forward_content) {
        params[@"data_id"] = @(model.forward_content.itemId);
    }
    params[@"is_on_period"] = @([IMYUGCEventHelper userIsInPhysiological]);
    if (imy_isNotEmptyString(redirect_url)) {
        params[@"redirect_url"] = redirect_url;
    }
    [IMYGAEventHelper postWithPath:@"bi_feeds_view" params:[params copy] headers:nil completed:nil];
}

-(void)requestUPlusBi:(NSString *)url{
    [[IMYServerRequest getPath:url
                          host:nil
                        params:nil
                       headers:nil] subscribeNext:^(id x){
        
    }];
}

/// 计算上报位置
- (NSInteger)floorWithIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.row < self.topics.count) {
        TTQHome5TopicModel *model = self.topics[indexPath.row];
        if (model.isAd) {
            return model.real_floor; //是广告直接返回
        }
    }
    
    NSInteger floor =  indexPath.row + 1; //业务楼层
    for (int i = 0; i < indexPath.row; i++) {
        if (i < self.topics.count) {
            TTQHome5TopicModel *model = self.topics[i];
            if (model.isAd) { //减去前面柚+广告数量
                floor -= 1;
            }
        }
    }
    return floor;
}

- (void)biVideoEventWithModel:(TTQHome5TopicModel *)cellModel atFloor:(NSInteger)floor {
    if (!cellModel.real_redirect_url.length) {
        return;
    }
    IMYURI *uri = [IMYURI uriWithURIString:cellModel.real_redirect_url];
    NSMutableDictionary *params = [[NSMutableDictionary alloc] init];
    if (!uri.params.count) {
        return;
    }
    
    if(uri.params[@"entrance"]) [params setObject:uri.params[@"entrance"] forKey:@"entrance"];
    [params setObject:@(78) forKey:@"position"];
    [params setObject:@(floor) forKey:@"floor"];
    
    [params setObject:@(1) forKey:@"video_type"];
    if(uri.params[@"al_source"]) [params setObject:uri.params[@"al_source"] forKey:@"al_source"];
    if(uri.params[@"info_type"]) [params setObject:uri.params[@"info_type"] forKey:@"info_type"];
    if(uri.params[@"info_id"]) [params setObject:uri.params[@"info_id"] forKey:@"info_id"];
    if(uri.params[@"algorithm"]) [params setObject:uri.params[@"algorithm"] forKey:@"algorithm"];
    if (uri.params[@"hitExps"]) {
        params[@"hitExps"] = uri.params[@"hitExps"];
    }
    [params setObject:@(cellModel.start_type) forKey:@"start_type"];
    [params setObject:@(cellModel.end_type) forKey:@"end_type"];
    [params setObject:@(cellModel.videoEndTime) forKey:@"end_duration"];
    [params setObject:@(cellModel.videoBeginTime) forKey:@"star_duration"];
    [params setObject:@(cellModel.video_duration) forKey:@"duration"];
    params[@"redirect_url"] = cellModel.real_redirect_url;
    params[@"is_on_period"] = @([IMYUGCEventHelper userIsInPhysiological]);

    [IMYGAEventHelper postWithPath:@"bi_ysp_play" params:[params copy] headers:nil completed:nil];
}

@end
