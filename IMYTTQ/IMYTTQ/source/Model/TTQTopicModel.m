//
// Created by <PERSON> on 15/5/5.
//
//

#import "TTQTopicModel.h"
#import "IMY_ViewKit.h"
#import "NSArray+TTQ.h"
#import "NSString+IMYR.h"
#import "TTQCommonHelp.h"
#import "TTQTopicRichParserModel.h"
#import <IMYRM80AttributedLabel+IMYR.h>
#import "TTQABTestConfig.h"

@implementation TTQTopicModel {
    BOOL hasCheckUseNormalLabel;
}
+ (NSDictionary *)modelContainerPropertyGenericClass {
    return @{@"hot_reviews" : [TTQHotCommentModel class],
             @"list" : [TTQTopicCircleHotTopic class],
             @"daren_list" : [TTQFocusExpert class],
             @"subject_banner" : [TTQUPlusBanner class],
             @"warm_data" : [TTQWarmReview class],
             @"highlight_words" : [TTQHighLightWordModel class]
    };
}
- (IMYVideoModel *)videoModel {
    if (!_videoModel) {
        IMYVideoModel *videoModel = [IMYVideoModel new];
        NSString *videoUrl = [TTQCommonHelp videoUrlWith264:self.video_url url265:self.video_url_h265];
        videoModel.sdVideoURL = [NSURL URLWithString:videoUrl];
        videoModel.hdVideoURL = [NSURL URLWithString:videoUrl];
        
        NSString *coverUrl = self.images.firstObject;
        if (imy_isEmptyString(coverUrl) == NO) {
            videoModel.coverImageURL = [NSURL URLWithString:[coverUrl qiniuURLType:IMY_QiNiu_WEBP]];
        }
        //        videoModel.duration = self.video_time;
        //        videoModel.fileSize = self.sd_size;
        _videoModel = videoModel;
    }
    return _videoModel;
}
+ (void)initialize {
    [self bindYYJSONKey:@"id" toProperty:@"topic_id"];
//    [self bindYYJSONKey:@"video_url" toProperty:@"homeVideoURL"];//video_url 在ttqtopicmodel中是个对象,所以在这边映射成newVideoURL属性
    [self imy_yyjsonRemoveProperty:@"shareImageUrl"];
    [self imy_yyjsonRemoveProperty:@"extraTag"];
    [self imy_yyjsonRemoveProperty:@"parseType"];
    [self imy_yyjsonRemoveProperty:@"cellHeight"];
    [self bindYYJSONKey:@"eggs_list" toProperty:NSStringFromSelector(@selector(eggList))];
}
+ (NSString *)getPrimaryKey {
    return @"topic_id";
}

- (NSInteger)parseType {
    if (!hasCheckUseNormalLabel) {
        _parseType = 0;
        if (!self.is_deleted) {
            if ([IMYREmoticonManager hasContainEmojiWithString:self.content]) {
                _parseType |= IMYRTextParseTypeEmoji;
            }
            if (self.privilege) {
                _parseType |= IMYRTextParseTypeURL;
            }
            if ([TTQTopicRichParserModel hasContainImgWithString:self.content]) {
                _parseType |= IMYRTextParseTypeImg;
            }
            if ([TTQTopicRichParserModel hasContainVideoWithString:self.content]
                || self.videos.count > 0) {
                _parseType |= IMYRTextParseTypeVideo;
            }
        }
        hasCheckUseNormalLabel = YES;
    }
    return _parseType;
}

- (NSString *)removeUrlString {
    if (!_removeUrlString && self.content) {
        NSString *regular = @"<a[^<>]+>.+?</a>";
        NSMutableString *mutableStr = [NSMutableString stringWithString:self.content];
        [mutableStr replaceOccurrencesOfString:regular withString:@" " options:NSRegularExpressionSearch range:NSMakeRange(0, [mutableStr length])];
        self.removeUrlString = mutableStr;
    }
    return _removeUrlString;
}

- (BOOL)is_special {
    if (self.type == 0) {
        if (self.redirect_url != nil && ([self.redirect_url hasPrefix:@"meiyou:///circles/specialtopic"] || [self.redirect_url hasPrefix:@"meiyou:///news/special"])) {
            return YES;
        }
    } else if (self.type == TTQHome5TypeSpecial) {
        return YES;
    }
    return NO;
}
- (BOOL)is_news {
    return [self.redirect_url hasPrefix:@"meiyou:///news"];
}

/**
 是否是问答帖
 */
- (BOOL)isAnswerTopic {
    return (self.topic_category & TTQTopicCategoryMaskAsk) > 0;
}

- (BOOL)isSubject {
    return self.recomm_type == 12;
}
- (BOOL)isMutableSubject {
    if ([self isSubject]) {
        if (self.items.count > 1) {
            return TRUE;
        }
    }
    return FALSE;
}

- (NSString *)redirect_url {
    if (_redirect_url == nil) {
        IMYURI *uri;
        if (self.link == nil) {
            if (self.topic_id) {
                uri = [IMYURI uriWithPath:@"circles/group/topic" params:@{ @"topicID": @(self.topic_id) } info:nil];
            }
        } else {
            if (self.link.type == 1) {
                uri = [IMYURI uriWithPath:@"open" params:@{@"url": self.link.url} info:nil];
            } else if (self.link.type == 2) {
                uri = [IMYURI uriWithPath:@"web" params:@{@"url": self.link.url} info:nil];
            } else if (self.link.type == 3) {
                uri = [IMYURI uriWithPath:@"circles/group/topic" params:@{ @"topicID": @(self.topic_id) } info:nil];
            } else if (self.link.type == 4) {
                uri = [IMYURI uriWithPath:@"circles/group" params:@{ @"groupID": @(self.forum_id) } info:nil];
            }
        }
        if (uri != nil) {
            _redirect_url = uri.uri;
        }
    }
    return _redirect_url;
}

- (NSString *)real_redirect_url {
    // 问答帖
    if ([self isAnswerTopic]) {
        if (imy_isNotEmptyString(_askuri)) {
            return _askuri;
        }
    }
    // 全屏小视频帖子
    NSUInteger topicCategory = [[self valueForKey:@"topic_category"] unsignedIntValue];
    if (self.model_type == TTQTopicModelTypeVideo
        && [TTQABTestConfig TTQShortVideoTesting]
        && imy_isNotEmptyString(self.video_redirect_uri)
        && ![self isAnswerTopic]
        && ![self isValidVoteCard]
        && !(topicCategory & TTQTopicCategoryMaskActivity)) {
        return self.video_redirect_uri;
    }
    return self.redirect_url;
}

- (BOOL)shouldEnterFullPageVideo {
    if (self.video_redirect_uri && [self.real_redirect_url isEqualToString:self.video_redirect_uri]) {
        return YES;
    }
    return NO;
}

- (NSInteger)newsId {
    return self.topic_id;
}

- (NSString *)shareImageUrl {
    if (self.images && [self.images count] > 0) {
        return [self.images firstObject];
    }
    return nil;
}

- (BOOL)is_video {
    return self.videos.count > 0;
}

- (BOOL)is_image {
    return self.images.count > 0;
}

//删除状态--允许删除：直接删除+申请删除
- (BOOL)is_deletable {
    return ([TTQCommonHelp deleteTopic] && (self.deleted_status == 2 || self.deleted_status == 4 || self.deleted_status == 5));
}

//删除状态--允许直接删除
- (BOOL)is_deletableForDir {
    return ([TTQCommonHelp deleteTopic] && self.deleted_status == 2);
}

//删除状态--允许申请删除
- (BOOL)is_deletableForAsk {
    return ([TTQCommonHelp deleteTopic] && self.deleted_status == 4);
}

//删除状态--不允许删除
- (BOOL)is_undeletable {
    return (![TTQCommonHelp deleteTopic] || self.deleted_status == 1);
}

//删除状态--已删除
- (BOOL)is_deleted {
    return [TTQCommonHelp deleteTopic] && self.deleted_status == 3;
}

// 判断是否是有效的投票卡片
- (BOOL)isValidVoteCard {
    return (nil != self.vote);
}

- (NSString *)circle_icon {
    return _circle_icon ? _circle_icon : self.forum.icon;
}

- (NSString *)circle_name {
    return _circle_name ? _circle_name : self.forum.name;
}

- (NSString *)circle_redirect_url {
    return _circle_redirect_url ? _circle_redirect_url : self.forum.redirect_url;
}

- (BOOL)isCircleRecommendCell {
    return (self.type == TTQHome5TypeCircleRecommend
            && self.attr_type == TTQCircleAttrtypeRecommend);
}

-(void)setAttitude_data:(NSArray<TTQTopicAttitudeItemModel *> *)attitude_data{
    if ([attitude_data.firstObject isKindOfClass:[TTQTopicAttitudeItemModel class]]) {
        _attitude_data = attitude_data;
        return;
    }
    _attitude_data = [attitude_data toModels:[TTQTopicAttitudeItemModel class]];
}

- (void)handleAttitudeWithId:(NSInteger)attitudeId active:(BOOL)active {
    //重复选中某个态度不作处理
    if (self.attitude_id == attitudeId && active && self.is_attitude) {
        return;
    }
    NSMutableArray *attitudes = @[].mutableCopy;
    for (TTQTopicAttitudeItemModel *attitude in self.attitude_data) {
        //对点击的态度进行重新设置
        if (attitude.attitude_id == attitudeId) {
            NSInteger attitude_count = attitude.attitude_count;
            TTQTopicAttitudeItemModel *replaceAttitude = [TTQTopicAttitudeItemModel new];
            replaceAttitude.attitude_id = attitude.attitude_id;
            replaceAttitude.attitude_name = attitude.attitude_name;
            replaceAttitude.attitude_count = active ? ++attitude_count : --attitude_count;
            replaceAttitude.is_attitude = active;
            replaceAttitude.image = attitude.image;
            replaceAttitude.gif = attitude.gif;
            [attitudes addObject:replaceAttitude];
            self.attitude_image = attitude.image;
            self.attitude_id = attitude.attitude_id;
        }else {
            BOOL is_attitude = attitude.is_attitude;
            //对之前发表的态度进行复位
            if (is_attitude) {
                NSInteger attitude_count = attitude.attitude_count;
                TTQTopicAttitudeItemModel *replaceAttitude = [TTQTopicAttitudeItemModel new];
                replaceAttitude.attitude_id = attitude.attitude_id;
                replaceAttitude.attitude_name = attitude.attitude_name;
                replaceAttitude.attitude_count = --attitude_count;
                replaceAttitude.is_attitude = NO;
                replaceAttitude.image = attitude.image;
                replaceAttitude.gif = attitude.gif;
                [attitudes addObject:replaceAttitude];
                continue;
            }
            //之前未发表的态度
            [attitudes addObject:attitude];
        }
    }
    
    if (active) {
        if (self.is_attitude == 0) {
            self.attitude_count++;
            if (!self.has_praise) {
                self.praise_num++;
            }
        }
    }else {
        self.attitude_count--;
        self.praise_num--;
    }
    
    self.attitude_id = attitudeId;
    
    self.is_attitude = active;
    
    self.attitude_data = attitudes;
    
    self.has_praise = NO;
}

- (NSDictionary *)imy_yyjsonModelWillTransformDictionary:(NSDictionary *)dict
{
    NSMutableDictionary *copyDic = [NSMutableDictionary dictionaryWithDictionary:dict];
    
    NSArray *images = dict[@"images"];
    _images = [NSArray motherFuckerImagesWithMaybeArrayOrStringBesideUnknownObject:images];
    [copyDic removeObjectForKey:@"images"];
    
    NSMutableArray *imageQualityArray = [NSMutableArray array];
    NSArray *tmpImageQualityArray = dict[@"images_list"];
    if (tmpImageQualityArray.count == _images.count)  {
        for (NSDictionary *dic in tmpImageQualityArray) {
            [imageQualityArray addObject:dic[@"is_low"]];
        }
    }
    _imageQualityArray = [imageQualityArray copy];
    return [copyDic copy];
}

@end

@implementation TTQTopicLink
@end

@implementation TTQUPlusBanner
@end

@implementation TTQWarmReview
+ (NSDictionary *)modelContainerPropertyGenericClass {
    return @{@"publisher" : [TTQPublisherModel class]};
}
@end

@implementation TTQShoppingPlugin
+ (NSDictionary *)modelContainerPropertyGenericClass {
    return @{@"info" : [TTQTopicRichParserYouPlusModel class]};
}
@end

@implementation TTQTips
@end

@implementation TTQShareBodyModel
@end

@implementation TTQTopicCurrentUserInfo
@end

@implementation TTQTopicCircleHotTopic
+ (void)initialize {
    [self bindYYJSONKey:@"id" toProperty:@"sujectID"];
}
@end

@interface TTQTopicItemModel ()
//  为何需要这两个字段 ？ 原因是有的 screen_name 存在于 item 中，有的 screen_name 存在于 item 的 publisher 中。
@property (nonatomic, copy) NSString *name;
@property (nonatomic, copy) NSString *publisherName;
@end

@implementation TTQTopicItemModel

+ (void)initialize {
    [self bindYYJSONKey:@"screen_name" toProperty:@"name"];
    [self bindYYJSONKey:@"publisher.screen_name" toProperty:@"publisherName"];
    [self bindYYJSONKey:@"id" toProperty:@"itemId"];
}



- (NSString *)screen_name {
    if (!_screen_name) {
        _screen_name = self.publisherName ?: self.name;
    }
    
    return _screen_name;
}

@end

@implementation TTQTopicActivity
@end

@implementation TTQTopicAttitudeItemModel
@end

@implementation TTQHighLightWordModel

@end

@implementation TTQTopicQuickAccess

@end
