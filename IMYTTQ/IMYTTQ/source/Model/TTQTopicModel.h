//
// Created by <PERSON> on 15/5/5.
// Copyright (c) 2015 <PERSON><PERSON><PERSON>. All rights reserved.
//

#import "TTQHotTopicInfoModel.h"
#import "TTQPublisherModel.h"
#import "TTQTopicModelTypeEnumHeader.h"
#import "TTQTopicSubjectModel.h"
#import "TTQVoteModel.h"
#import "YYJSONHelper.h"
#import <CoreGraphics/CoreGraphics.h>
#import <Foundation/Foundation.h>
#import "TTQHotCommentModel.h"
#import <IMYVideoPlayer/IMYVideoModel.h>
#import "TTQForumModel.h"
#import "TTQGoodAnswerModel.h"
#import "TTQRootBaseModel.h"
#import "TTQFocusCircleModel.h"
#import "IMYUGCAttitudeProtocol.h"
#import "IMYMixHomeFeedsModel.h"
#import "TTQTopicRichParserModel.h"

typedef NS_ENUM(NSUI<PERSON>ger, TTQTopicImageType) {
    TTQTopicImageTypePortrait = 0,
    TTQTopicImageTypeLandscape = 1,
};

typedef NS_ENUM(NSUInteger, TTQTopicModelType) {
    TTQTopicModelTypeNormal = 1,
    TTQTopicModelTypeVideo = 2,
};

/**
 帖子状态

 - TTQTopicStatusNormal: 0正常
 - TTQTopicStatusDeleted: 1已删除
 - TTQTopicStatusWaitForReview: 2待审核
 - TTQTopicStatusDeleteByUser: 3用户自主删除
 - TTQTopicStatusUnionDeleted: 4联动删除
 - TTQTopicStatusWaitToPublish: 5待发布
 - TTQTopicStatusVidoeWaitForReview: 6用户视频待审核
 */
typedef NS_ENUM(NSUInteger, TTQTopicStatus) {
    TTQTopicStatusNormal = 0,
    TTQTopicStatusDeleted = 1,
    TTQTopicStatusWaitForReview = 2,
    TTQTopicStatusDeleteByUser = 3,
    TTQTopicStatusUnionDeleted = 4,
    TTQTopicStatusWaitToPublish = 5,
    TTQTopicStatusVidoeWaitForReview = 6,
};

/**
 帖子分类类型

 - TTQTopicCategoryUGCVideo: 10代表用户ugc小视频贴
 */
typedef NS_ENUM(NSUInteger, TTQTopicCategory) {
    TTQTopicCategoryUGCVideo = 10,
    TTQTopicCategoryCaseHistory = 18,///< 病例贴894
};

/**
 话题类型

 - TTQTopicCategoryMaskElite: 精
 - TTQTopicCategoryMaskHot: 热
 - TTQTopicCategoryMaskActivity: 活
 - TTQTopicCategoryMaskVote: 投
 - TTQTopicCategoryMaskAsk: 问
 - TTQTopicCategoryMaskNew: 新
 */
typedef NS_ENUM(NSUInteger, TTQTopicCategoryMask) {
    TTQTopicCategoryMaskElite = 1 << 0,
    TTQTopicCategoryMaskHot = 1 << 1,
    TTQTopicCategoryMaskActivity = 1 << 2,
    TTQTopicCategoryMaskVote = 1 << 3,
    TTQTopicCategoryMaskAsk = 1<<4,
    TTQTopicCategoryMaskNew = 1<<5
};

@protocol TTQVideoModel;
@protocol TTQFeedbackModel;

@protocol TTQTopicItemModel <NSObject>
@end

@protocol TTQTopicModel <NSObject>
@end

@protocol TTQTopicActivity <NSObject>
@end


@interface TTQTopicQuickAccess:  TTQRootBaseModel <YYJSONHelperProtocol>
@property (nonatomic, copy) NSString *redirect_url; //跳转链接
@property (nonatomic, copy) NSString *status; //offline离线，busy 繁忙 available 可用的
@property (nonatomic, copy) NSString *type; //入口类型：doctor医生
@property (nonatomic, assign) NSInteger wait_patients; //等待患者数
@end


@interface TTQTopicAttitudeItemModel : TTQRootBaseModel <YYJSONHelperProtocol, IMYUGCAttitudeItemProtocol>

@property (nonatomic, assign) NSInteger attitude_id;
@property (nonatomic, strong) NSString *attitude_name; //态度名称
@property (nonatomic, assign) NSInteger is_attitude; // 0未选择态度1选择态度
@property (nonatomic, assign) NSInteger attitude_count; //态度数
@property (nonatomic, strong) NSString *image; //态度图片
@property (nonatomic, strong) NSString *gif; // 态度gif图 用于feeds流和底bar浮窗

@end

@interface TTQTopicActivity : TTQRootBaseModel <YYJSONHelperProtocol>
@property (nonatomic, copy) NSString *title; // - 标题
@property (nonatomic, copy) NSString *label; // - 标签
@property (nonatomic, copy) NSString *cover; // - 封面
@property (nonatomic, copy) NSString *redirect_uri; // - 跳转协议;
@end

@interface TTQTopicItemModel : TTQRootBaseModel <YYJSONHelperProtocol>
@property (nonatomic, assign) NSUInteger itemId;
@property (nonatomic, strong) NSArray<NSString *> *images;
//@property(nonatomic,strong) NSString *avatar;
@property (nonatomic, strong) NSString *screen_name;
@property (nonatomic, assign) NSUInteger recomm_type;
@property (nonatomic, strong) NSString *redirect_url;
@property (nonatomic, strong) NSString *title;
@property (nonatomic, assign) NSInteger total_review;
@property (nonatomic, assign) NSUInteger type;
@property (nonatomic, strong) NSString *published_date;
@property (nonatomic, assign) NSInteger play_count;     /**<  用于小视频卡片，播放数  */
@property (nonatomic, assign) NSInteger praise_count;   /**<  用于小视频卡片，点赞数  */
@property (nonatomic, assign) BOOL isReport;            /**<  是否上报  */
@end

@interface TTQTopicLink : TTQRootBaseModel <YYJSONHelperProtocol>
@property (nonatomic, assign) NSInteger type;
@property (nonatomic, copy) NSString *url;
@property (nonatomic, copy) NSString *uri;
@property (nonatomic, copy) NSString *text;
@property (nonatomic, copy) NSString *icon;
@property (nonatomic, copy) NSString *tips;
@property (nonatomic, copy) NSString *error; //有值的时候不跳转
/// 1不显示副标题  2显示人数  3显示副标题 
@property (nonatomic, assign) NSInteger tips_style;

@end
@interface TTQTips : TTQRootBaseModel <YYJSONHelperProtocol>
@property (nonatomic, copy) NSString *icon;
@property (nonatomic, copy) NSString *text;
@property (nonatomic, copy) NSString *app_url;

@end

@interface TTQShareBodyModel : TTQRootBaseModel

@property (nonatomic, assign) BOOL is_share;
@property (nonatomic, copy) NSString *src;
@property (nonatomic, copy) NSString *share_url;
@property (nonatomic, copy) NSString *title;
@property (nonatomic, copy) NSString *content;
/// 分享图片地址，8.0.6新接口启用
@property (nonatomic, copy) NSString *share_image;
/// 评论接口业务返回的
@property (nonatomic, copy) NSString *image;
@property (nonatomic, copy) NSString *url;

@end

@interface TTQTopicCurrentUserInfo : TTQRootBaseModel
@property (nonatomic, assign) NSInteger error;//错误提示 0 正常，1 用户匿名，2 用户已被封号, 3 禁言
@property (nonatomic, assign) NSInteger feedback_id;//反馈详情页的默认id
@end

@interface TTQTopicCircleHotTopic : TTQRootBaseModel
@property (nonatomic, assign) NSUInteger sujectID;//776新增 对应的话题id
@property (nonatomic, assign) NSUInteger recommend_status;//776新增 推荐状态 0 就是无 1 热 2 新 3荐
@property (nonatomic, assign) NSUInteger discuss_num;//776新增 多少人讨论
@property (nonatomic, copy) NSString *title;
@property (nonatomic, copy) NSArray *images;
@property (nonatomic, assign) NSUInteger topic_id;
@property (nonatomic, copy) NSString *redirect_url;
@end

@interface TTQUPlusBanner : TTQRootBaseModel
@property (nonatomic, strong) NSString *image_url;
@property (nonatomic, strong) NSString *redirect_url;
@end

@interface TTQWarmReview : TTQRootBaseModel
@property (nonatomic, assign) NSUInteger review_id;
@property (nonatomic, assign) NSUInteger warm_num;
@property (nonatomic, copy) NSString *content;
@property (nonatomic, copy) NSArray *avatars;
@property (nonatomic, strong) TTQPublisherModel *publisher;
@property (nonatomic, strong) NSArray *images;
@property (nonatomic, assign) NSInteger praise_num;
@property (nonatomic, assign) BOOL has_praise;
@property (nonatomic, strong) NSDate *lastTimePraiseDate;
//@property (nonatomic, copy) NSString *redirect_url;

@end

@interface TTQShoppingPlugin : TTQRootBaseModel <YYJSONHelperProtocol>

@property (nonatomic, copy) NSString *type;
@property (nonatomic, copy) NSString *button_icon;
@property (nonatomic, strong) TTQTopicRichParserYouPlusModel *info;

@end

@interface TTQHighLightWordModel : NSObject

@property (nonatomic, copy) NSString *word;
@property (nonatomic, copy) NSString *link_url;
@end

@interface TTQTopicModel : TTQRootBaseModel <YYJSONHelperProtocol>
//
@property (nonatomic, strong) IMYVideoModel *videoModel;
@property (nonatomic, copy) NSString *homeVideoURL;//映射video_url,仅用于home5model
@property (nonatomic, copy) NSString *circle_redirect_url;//7.3新增圈子跳转
@property (nonatomic, strong) NSArray <TTQHotCommentModel*> *hot_reviews;//7.3帖子新增的热评区
@property (nonatomic, copy) NSString *videoflowuri;//7.4.4跳转到沉浸流的uri
/** 小视频跳转uri */
@property (nonatomic, strong) NSString *video_redirect_uri;

@property (nonatomic, assign) NSInteger topic_user_id;//匿名禁言时候用
@property (nonatomic, copy) NSString  *ip_region;

@property (nonatomic, assign) NSUInteger cellHeight;
@property (nonatomic, strong) NSDate *lastTimePraiseDate;//用于标记点赞的时间7.4.8, 本地字段
@property (nonatomic, assign) NSInteger topic_id;
@property (nonatomic, strong) NSString *user_screen_name;
@property (nonatomic, assign) BOOL is_new;
@property (nonatomic, assign) BOOL is_ontop;
@property (nonatomic, assign) NSInteger user_id;
@property (nonatomic, assign) BOOL for_help;
@property (nonatomic, assign) BOOL show_icon;
@property (nonatomic, strong) NSString *title;
@property (nonatomic, strong) NSString *reviewed_date;
@property (nonatomic, assign) BOOL is_elite;
@property (nonatomic, assign) NSInteger total_view;
@property (nonatomic, assign) BOOL is_recommended;
@property (nonatomic, assign) BOOL is_anonymous; // 是否是匿名贴
@property (nonatomic, assign) NSInteger visible;   /// 1 公开可见，2 仅自己可见 （服务端匿名时也会默认下发1，优先是仅自己可见>匿名>公开）
@property (nonatomic, assign) BOOL is_modified; // 帖子是否可编辑
@property (nonatomic, strong) NSString *published_date;
@property (nonatomic, assign) NSInteger forum_id;
@property (nonatomic, strong) NSString *forum_name; //圈子名词
@property (nonatomic, assign) NSInteger b_forum_id;//圈子ID 话题才会下发
///总回复数
@property (nonatomic, assign) NSInteger total_review;
///总楼层数
@property (nonatomic, assign) NSInteger total_floor;
///主评论数
@property (nonatomic, assign) NSInteger main_total_review;
@property (nonatomic, strong) NSString *tag_name;
@property (nonatomic, assign) BOOL is_hot;
@property (nonatomic, assign) BOOL is_original; //是否原创
@property (nonatomic, assign) BOOL is_favorite;
@property (nonatomic, assign) NSInteger favorite_num;   /// 收藏数
@property (nonatomic, assign) BOOL is_activity; //是否活动
@property (nonatomic, assign) NSInteger site; //话题固定展位（还不是为了活动，要固定展位）
@property (nonatomic, copy) NSString *share_url; //分享的URL
@property (nonatomic, assign) BOOL reply_image; //是否可以回复图片
@property (nonatomic, assign) BOOL is_video; //是否是视频贴
@property (nonatomic, assign) BOOL is_live; //是否是直播贴
@property (nonatomic, copy) NSString *video_time; //播放时长
@property (nonatomic, assign) NSInteger privilege; // 1 表示url可以点击
@property (nonatomic, strong) TTQPublisherModel *publisher;

@property (nonatomic, assign) BOOL is_floor_host; //是否是楼主
@property (nonatomic, strong) NSString *content;
@property (nonatomic, strong) NSArray *images;//帖子中的图片组
/// 图片信息
@property (nonatomic, strong) NSArray *image_props;
@property (nonatomic, copy) NSArray *images_list;
@property (nonatomic, strong) NSArray *imageQualityArray;// 图片质量数组

@property (nonatomic, copy) NSString *thumb_gif; // gif动态图
@property (nonatomic, copy) NSString *video_thumb_gif; // gif动态图
@property (nonatomic, strong) TTQTopicLink *link_body;
@property (nonatomic, strong) TTQTopicLink *link;
@property (nonatomic, strong) TTQTips *tips;
@property (nonatomic, assign) NSInteger ordianl; //专题需要用到的排序字段
///是否问答帖
@property (nonatomic, assign) BOOL is_ask;
///视频
@property (nonatomic, strong) NSArray<TTQVideoModel> *videos;
///引导语
@property (nonatomic, strong) NSString *guide_info;
///是否首推
@property (nonatomic, assign) BOOL is_feeds;
//是否使用富文本view
@property (nonatomic, assign) NSInteger parseType;
//引用里面，去除链接的字符串
@property (nonatomic, copy) NSString *removeUrlString;

@property (nonatomic, strong) NSString *updated_date;
@property (nonatomic, copy) NSString *circle_name;
@property (nonatomic, copy) NSString *circle_icon;
@property (nonatomic, copy) NSString *recommed_icon;
///1:不能删帖，2:能进行删帖，3:帖子已经被删除, 4:帖子可申请删除, 5柚妈保护状态下可删帖
@property (nonatomic, assign) NSInteger deleted_status;
///追帖数
@property (nonatomic, assign) NSInteger followup_num;
///0为追帖显示按钮， 1已追贴按钮灰色, 2楼主不显示按钮
@property (nonatomic, assign) NSInteger is_followup;
///关注数
@property (nonatomic, assign) NSInteger followup_count;
/// 投票信息
@property (nonatomic, strong) TTQVoteModel *vote;
/// 投票卡片左下角的副标题信息
@property (nonatomic, copy) NSString *sub_title;
///是否有加入圈子了，本地计算得出的
@property (nonatomic, assign) BOOL isJoined;
///本地计算的
@property (nonatomic, assign) BOOL is_image;
///是否已经看过
@property (nonatomic, assign) BOOL hasShow;
///推荐算法
@property (nonatomic, copy) NSString *algorithm;
@property (nonatomic, copy) NSString *cust;
///点赞数
@property (nonatomic, assign) NSInteger praise_num;
///是否点赞过
@property (nonatomic, assign) BOOL has_praise;
/// 是否抱抱了
@property (nonatomic, assign) BOOL hugged;
/// 抱抱数
@property (nonatomic, assign) NSInteger hug_num;
///已表态 静态图、防止已表态的态度下架后，流中无法展示
@property (nonatomic, strong) NSString *attitude_image;
///是否发表过态度
@property (nonatomic, assign) BOOL is_attitude;
///发表态度的态度Id
@property (nonatomic, assign) NSInteger attitude_id;
///发表态度总和
@property (nonatomic, assign) NSInteger attitude_count;
///各个态度的数量信息
@property (nonatomic, strong) NSArray<TTQTopicAttitudeItemModel *> *attitude_data;

///是否显示广告标签
@property (nonatomic, assign) BOOL is_ad;
///是否显示投票标签
@property (nonatomic, assign) BOOL is_vote;
///是否显示问答标签
@property (nonatomic, assign) BOOL is_qa;
@property (nonatomic, copy) NSString *redirect_url;
@property (nonatomic, strong) NSArray<TTQFeedbackModel> *label;
/// 负反馈标签 8.2.4改版
@property (nonatomic, strong) NSDictionary *fb_labels;//不感兴趣：dislike数组 内容反馈：content数组 ,{id val type}
@property (nonatomic, assign) NSInteger imgs_count;
///1:在列表可以播放，0:不能在列表播放
@property (nonatomic, assign) BOOL feeds_play;
@property (nonatomic, assign) int show_icon_type;//1广告 2活动 3公告
/**
  1普通话题（type=1为话题，type=70为专题）  2推广 3推荐   4 美柚专享  5 天气信息  6资讯  7 专题 8-经期卡片（大姨妈来了） 9-经期卡片（大姨妈走了） 10-通用卡片 11-年龄卡片  12-专题卡片 13-今日密保\怀孕几率卡片
 */
@property (nonatomic, assign) NSUInteger recomm_type;
@property (nonatomic, strong) NSString *video_url;
@property (nonatomic, strong) NSString *video_url_h265;
@property (nonatomic, assign) NSInteger switch_type;
@property (nonatomic, assign) NSInteger switch_value;
@property (nonatomic, assign) NSInteger view_times;
@property (nonatomic, assign) BOOL is_fold;
@property (nonatomic, strong) NSArray *images_night;

// BI
@property (nonatomic, assign) NSInteger card_type;
@property (nonatomic, assign) NSInteger card_id;
@property (nonatomic, assign) NSInteger jump_type;
@property (nonatomic, assign) NSInteger al_source;
@property (nonatomic, assign) NSInteger entrance; // 2 她她圈首页feeds 14首页大社区流 0其他
/**
 1 --  美柚号
 */
@property (nonatomic, assign) NSInteger attr_type;
@property (nonatomic, assign) NSInteger type;
@property (nonatomic, assign) NSInteger old_type; //用于负反馈选择后记录上一次卡片类型
@property (nonatomic, strong) NSMutableArray *select_labels;//选中的负反馈标签
@property (nonatomic, copy) NSString *r_text; //子标题

@property (nonatomic, assign) NSInteger show_style; //自定义卡片 1.大图 2.单图 3.三图

/**
 资讯分享url
 */
@property (nonatomic, strong) NSString *url;

@property (nonatomic, strong) NSArray<TTQTopicItemModel> *items;

//回复的查看类型-是否展示只看图片的tab 字段
@property (nonatomic, assign) BOOL have_only_image_model;

@property (nonatomic, strong) NSArray<NSString *> *ad_monitor_url; //广告监测地址,获取要进行

/**
 所在的圈子信息 7.4.1 Added
 */
@property (nonatomic, strong) TTQForumModel *forum;

//额外的标记标志(非服务端下发，作为客户端的一些额外处理标记)
@property (nonatomic, assign) NSInteger extraTag;

//热议话题所属
@property (nonatomic, strong) TTQTopicSubjectModel *subject;
/// 话题列表，新样式
@property (nonatomic, strong) NSArray<NSDictionary *> *subjects;
/**
 图片类型， 1、横图 0、竖图
 */
@property (nonatomic, assign) TTQTopicImageType image_type;

/**
 h5小视频挑战需要的图片类型， 接口返回直接传给h5，不关注值
 */
@property (nonatomic, assign) NSUInteger imageType;
/**
 h5小视频挑战需要的图片，可能是默认图
 */
@property (nonatomic, copy) NSString *defaultImage;

/**
 数据流类型，视频类型和普通类型
 */
@property (nonatomic, assign) TTQTopicModelType model_type;

@property (nonatomic, assign) BOOL join_reply;///< 是否要加圈后才能恢复 v894
@property (nonatomic, assign) BOOL join_praise;///< 是否要加圈后才能点赞, 备注：v894,没有用

/**
 版权提示
 */
@property (nonatomic, copy) NSString *copywriter;
@property (nonatomic, copy) NSString *disclaimer;///< 美柚894 新增免责申明

/**
 视频主题
 */
@property (nonatomic, copy) NSString *theme_title;

/**
 视频主题ID
 */
@property (nonatomic, assign) NSInteger theme_id;

/**
 美柚784视频沉浸流跳转协议
 */
@property (nonatomic, copy) NSString *theme_redirect_url;

/**
 主题状态
 */
@property (nonatomic, assign) TTQTopicStatus topic_status;

/**
 帖子分类类型
 */
@property (nonatomic, assign) TTQTopicCategory category;

/**
 is_activity  是否与 topic_category  1<<2 等效     活动帖
 is_vote       是否与 topic_category   1<<3 等效   投票帖
 is_elite。   是否与 topic_category   1<<0 等效   精华帖
 is_hot        是否与 topic_category    1<<1 等效   热门帖
 */
@property (nonatomic, assign) NSUInteger topic_category;

/**
 回答者的头像
 */
@property (nonatomic, strong) NSArray *avatar;

/**
 问答帖子使用的跳转URI
 */
@property (nonatomic, copy) NSString *askuri;

/**
 圈子推荐cell相关
 */
@property (nonatomic, assign) NSInteger forum_user;//圈子用户数 7.4.8
@property (nonatomic, copy) NSString *forum_icon;//圈子推荐头像
@property (nonatomic, assign) BOOL isCircleRecommendCell;//是否是圈子推荐
@property (nonatomic, assign) NSInteger forum_review_count;//圈子讨论数
@property (nonatomic, copy) NSString *forum_guide_title;//圈子更多文案
@property (nonatomic, copy) NSArray<TTQTopicCircleHotTopic *> *list; // 圈子最热帖
@property (nonatomic, copy) NSArray<TTQFocusExpert *> *daren_list; // 7.7.6推荐达人卡片数据

//776 热议话题卡片
@property (nonatomic, copy) NSString *card_title;//热议话题标题776
@property (nonatomic, copy) NSString *card_redirect_uri;//曝光的上报协议776
@property (nonatomic, copy) NSString *click_card_redirect_uri;//点击上报协议(同时作为跳转协议)776

@property (nonatomic, strong) TTQGoodAnswerModel *answer;

// 彩蛋
@property (nonatomic, copy) NSArray  *eggList;

// 柚任务
@property (nonatomic, strong) TTQUPlusBanner *subject_banner;

// 暖评
@property (nonatomic, copy) NSArray<TTQWarmReview *> *warm_data;

@property (nonatomic, assign) BOOL is_experience;

/// 测试包显示， @“quality”
@property (nonatomic, strong) NSDictionary *debug_info;
/// 是否柚加文章
@property (nonatomic, assign) BOOL is_you_plus;

/// 帖子是否违规，需展示违规状态条
@property (nonatomic,strong) NSDictionary *tip; //帖子状态横幅

@property (nonatomic, assign) NSInteger templateID;// 使用的模板id
@property (nonatomic, assign) NSInteger templateType;// 使用的模板类型，不知道干嘛用的

/// 转帖的原贴
@property (nonatomic, strong) IMYMixHomeCaptionModel *forward_content;
/// 禁止回复
@property (nonatomic, assign) BOOL is_close_comment;
/// 关联帖子id(蹲后续功能使用)
@property (nonatomic, assign) NSInteger referenced_id;
/// 百科词条高亮 word
@property (nonatomic, strong) NSArray<TTQHighLightWordModel *> *highlight_words;
/// 问答使用，回答数
@property (nonatomic, assign) NSInteger answer_count;
/**
 考虑到使用缓存的情况，【命中问答】切换到【没有命中问答】需要使用特殊处理返回对应的URI
 **注意不能再redirect_url方法中做处理，会影响到数据的缓存**
 */
- (NSString *)real_redirect_url;

/**
 是否是问答帖
 */
- (BOOL)isAnswerTopic;

/**
 是否是专题
 */
- (BOOL)isSubject;

/**
 是否为多样式专题
 */
- (BOOL)isMutableSubject;
///是否是资讯
- (BOOL)is_news;
///是否是专题
- (BOOL)is_special;

//删除状态--允许删除：直接删除+申请删除
- (BOOL)is_deletable;
//删除状态--允许直接删除
- (BOOL)is_deletableForDir;
//删除状态--允许申请删除
- (BOOL)is_deletableForAsk;
//删除状态--不允许删除
- (BOOL)is_undeletable;
//删除状态--已删除
- (BOOL)is_deleted;
// 判断是否是有效的投票卡片
- (BOOL)isValidVoteCard;
// 判断是否可以进入全屏小视频
- (BOOL)shouldEnterFullPageVideo;

- (void)handleAttitudeWithId:(NSInteger)attitudeId active:(BOOL)active;

@end
