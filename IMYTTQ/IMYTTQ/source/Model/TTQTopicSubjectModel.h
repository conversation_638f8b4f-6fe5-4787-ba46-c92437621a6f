//
//  TTQTopicSubjectModel.h
//  IMYTTQ
//
//  Created by Li<PERSON> on 2017/11/28.
//  Copyright © 2017年 MeiYou. All rights reserved.
// 拷贝不拷贝isSelected

#import <Foundation/Foundation.h>
#import "YYJSONHelper.h"
#import "TTQRootBaseModel.h"

@interface TTQTopicSubjectModel : TTQRootBaseModel <YYJSONHelperProtocol, NSCopying>
@property (nonatomic, assign) NSInteger subjectID;
@property (nonatomic, copy) NSString *redirect_url;
@property (nonatomic, copy) NSString *name;
@property (nonatomic, assign) BOOL isSelected;//是否已选用
@property (nonatomic, assign) BOOL isTheme;
/// 详情页底部是否展示热议话题
@property (nonatomic, assign) BOOL show_publish_module;
@property (nonatomic, assign) NSInteger card_mode;
@property (nonatomic, copy) NSString *icon;
@property (nonatomic, copy) NSString *sub_title;
@property (nonatomic, assign) NSInteger talk_count;
@property (nonatomic, copy) NSString *publish_text;
@property (nonatomic, copy) NSString *source;
@property (nonatomic, assign) BOOL is_show_hot; /// 是否热议
@end
