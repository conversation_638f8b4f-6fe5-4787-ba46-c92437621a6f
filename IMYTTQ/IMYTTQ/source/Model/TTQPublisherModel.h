//
//  TTQPublisherModel.h
//  tataquan
//
//  Created by king on 15/5/16.
//  Copyright (c) 2015年 MeiYou. All rights reserved.
//

#import "TTQAvatarModel.h"
#import "TTQMedalModel.h"
#import "YYJSONHelper.h"
#import <Foundation/Foundation.h>
#import "TTQRootBaseModel.h"
#import <IMYUGC/IMYMixHomeFeedsModel.h>

@class TTQTagIconModel;

@interface TTQPublisherModel : TTQRootBaseModel <YYJSONHelperProtocol>
@property (nonatomic, assign) NSUInteger userID;
@property (nonatomic, copy) NSString *admin_icon; //管理员头像url，现在都用文字+背景，所以只用来判断是否是管理员
@property (nonatomic, copy) NSString *screen_name;
/// 头衔信息
@property (nonatomic, copy) NSString *title;
/** 皇冠标识；-1代表没有名次 ，值为1、2 以此类推 */
@property (assign, nonatomic) NSInteger rank;

//身份信息
@property (nonatomic, copy) NSString *baby_info;
@property (nonatomic, copy) NSString *expert_icon;
@property (nonatomic, copy) NSString *icon;
@property (nonatomic, copy) NSString *expert_name;       //达人名称
@property (nonatomic, copy) NSString *master_icon;       //圈主头像url，现在都用文字+背景，所以只用来判断是否是圈主
@property (nonatomic, copy) NSString *learn_master_icon; //实习圈主的文字，也是文字+背景
@property (nonatomic, assign) NSInteger circle_main_status;// 7.6.8 1正式圈主2实习圈主；没有的话就不下发该字段或者下发为0
@property (nonatomic, copy) NSString *circle_main_icon;//圈主图标
// 3.0新增，表示圈主图标 addby linyf 14-01-05
@property (nonatomic, assign) NSInteger score_level;
//达人堂积分
@property (nonatomic, assign) BOOL is_ask_follow;
//求关注
@property (nonatomic, copy) NSString *avatar;
@property (nonatomic, assign) NSInteger isvip; // 1 黄v 2蓝v
@property (nonatomic, strong) TTQAvatarModel *user_avatar;
///匿名帖用来判读能不能删除回复
@property (nonatomic, assign) BOOL is_owner;
@property (nonatomic, assign) BOOL is_qa_owner; /// 是否问答题主
//勋章 medal_list:[{"style":1, "name":"a"}]
@property (nonatomic, strong) NSArray<TTQMedalModel> *medal_list;
@property (nonatomic, assign) NSInteger error; // //错误提示 0正常，1用户匿名，2用户已被封号

@property (nonatomic, assign) NSInteger is_followed; /**<  是否为关注用户  */
@property (nonatomic, copy) NSString *redirect_url;  //跳转uri

@property (nonatomic, assign) BOOL is_daren; /**<  目前仅用于社区小视频，判断是否为达人  */
@property (nonatomic, copy) NSString *daren_icon;
/**
 达人图标和高宽信息
 */
@property (nonatomic, assign) NSInteger expertIconWidth;
@property (nonatomic, assign) NSInteger expertIconHeight;
@property (nonatomic, strong) NSString *expertIconNew;

@property (nonatomic, assign) BOOL ymVip;  // 整形 0 否 1是 代表医美用户认证标识

// 7.7.0新增字段
@property (nonatomic, copy) NSString *qa_user_icon; // 问答之星icon
@property (nonatomic, copy) NSString *mp_user_icon;  // 代表美柚号标识
@property (nonatomic, copy) NSString *mp_expert_user_icon;  // 代表专家标识
@property (nonatomic, copy) NSString *mp_vip_intro;  // 专家或美柚号简介
@property (nonatomic, copy) NSString *mp_expert_user;
@property (nonatomic, assign) BOOL is_official; // 是否官方账号，官方账号发布的帖子不折叠
/// 用户等级描述
@property (nonatomic, strong) NSArray<IMYMixHomeUserBadge *> *badges;
/// 头像挂件
@property (nonatomic, copy) NSString *pendant_url;
@property (nonatomic, assign) NSInteger pendant_id;

@property (nonatomic, strong) NSDictionary *hospital;///< 医院信息 @{@"name":医院名称, @"level":医院等级}
@property (nonatomic, strong) NSDictionary *doctor;///< 医生信息 @{@"title":主治医生, "department":内科}

- (NSArray<TTQTagIconModel *> *)tagIconModels;

/**
 访客用户ID
 */
+ (NSInteger)anonymousUserID;

/// 是有佩戴皇冠
- (BOOL)hasRankPendant;

//是否有蓝 V 徽章
- (BOOL)hasBlueVBadge;
@end
