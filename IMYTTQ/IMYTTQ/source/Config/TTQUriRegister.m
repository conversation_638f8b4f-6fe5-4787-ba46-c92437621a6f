//
//  TTQUriRegister.m
//  IMYTTQ
//
//  Created by ljh on 15/9/9.
//  Copyright (c) 2015年 MeiYou. All rights reserved.
//

#import "TTQTopicCategoryListViewController.h"
#import "IMYDragableHalfScreenContainerView.h"
#import "TTQUriRegister.h"
#import "IMYAccountCheckService.h"
//#import "IMYTTQVVideoDetailViewController.h"
#import "TTQDoorConfig.h"
#import "TTQFindViewController.h"
#import "TTQFindViewModel.h"
#import "TTQForumBroadcastInfoViewController.h"
#import "TTQHome5ViewController.h"
#import "TTQHomeController.h"
#import "TTQHotTopicViewController.h"
#import "TTQHttpHelper.h"
#import "TTQJumpType.h"
#import "TTQMessageDetailViewModel.h"
#import "TTQMyCollectViewController.h"
#import "TTQMyTopicVC.h"
#import "TTQMyTopicVM.h"
#import "TTQPraiseListViewController.h"
#import "TTQReadRecord.h"
#import "TTQTopicDetailViewController.h"
#import "TTQTopicFollowViewController.h"
#import "TTQTopicViewModel.h"
#import "TTQTopicsViewModel.h"
#import "TTQShortVideoFlowViewController.h"
#import "TTQShortVideoPhotoSetNavigationController.h"
#import "TTQShortVideoViewModel.h"
#import "TTQShortVideoFeedsModel.h"
#import "TTQShortVideoCommentView.h"
#import "TTQABTestConfig.h"
#import "TTQTopicContentPrefetcher.h"
#import "TTQDraftListTableViewController.h"
#import "TTQDraftListViewModel.h"
#import "TTQPublishTopicSuccessorViewModel.h"
#import "TTQCommonHelp.h"
#import "TTQCheckService.h"
#import "TTQTopics848ViewController.h"
#import "TTQPublish860ViewController.h"
#import "TTQPublishTopicViewModel.h"
#import "IMYAlertController.h"
#import <CocoaSecurity/Base64.h>
#import <IMYBaseKit/IMYBaseKit.h>
#import <IMYUGC/IMYUGC.h>
#import <IMYUGC/IMYPublishSheetView.h>
#import "TTQCommentDetailViewController.h"
#import "TTQHomeActivityViewController.h"
#import "TTQTopicDetailViewControllerV2.h"

NSString *const IMYCirclePlayVideo = @"circle/playVideo";

@implementation TTQUriRegister

IMY_KYLIN_FUNC_PREMAIN_ASYNC {
    [TTQUriRegister registerNavigationAction];
    [TTQUriRegister registerRestfulAPI];
}

/// 预埋 Universal-Link  支持
+ (void)registerRestfulAPI {
    [[IMYURIManager shareURIManager] addForPath:@"circle/*"
                                          level:100
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSString *forumID = [[actionObject.uri.path componentsSeparatedByString:@"/"].lastObject description];
        if ([forumID imy_isPureInt]) {
            NSMutableDictionary *params = [NSMutableDictionary dictionaryWithDictionary:actionObject.uri.params];
            params[@"groupID"] = forumID;
            [[IMYURIManager shareURIManager] runActionWithPath:@"circles/group" params:params info:actionObject.uri.info];
        } else {
            actionObject.hasStop = NO;
        }
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"circle/detail"
                                          level:99
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSString *webURL = actionObject.uri.info[@"webURL"];
        NSString *prepare = [webURL componentsSeparatedByString:@"?"].lastObject;
        prepare = [prepare componentsSeparatedByString:@"&"].firstObject;
        prepare = [prepare componentsSeparatedByString:@"="].firstObject;
        
        NSString *topicID = prepare;
        if ([topicID imy_isPureInt]) {
            NSMutableDictionary *params = [NSMutableDictionary dictionaryWithDictionary:actionObject.uri.params];
            params[@"topicID"] = topicID;
            [[IMYURIManager shareURIManager] runActionWithPath:@"circles/group/topic" params:params info:actionObject.uri.info];
        } else {
            actionObject.hasStop = NO;
        }
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"circle/detail/*"
                                          level:100
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSString *topicID = [[actionObject.uri.path componentsSeparatedByString:@"/"].lastObject description];
        if ([topicID imy_isPureInt]) {
            NSMutableDictionary *params = [NSMutableDictionary dictionaryWithDictionary:actionObject.uri.params];
            params[@"topicID"] = topicID;
            [[IMYURIManager shareURIManager] runActionWithPath:@"circles/group/topic" params:params info:actionObject.uri.info];
        } else {
            actionObject.hasStop = NO;
        }
    }];
        
    [[IMYURIManager shareURIManager] addForPath:@"stories/messageDetail/save"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSDictionary *dicValue = actionObject.uri.info;
        if ([dicValue isKindOfClass:NSDictionary.class]) {
            NSInteger type = [[dicValue objectForKey:@"type"] integerValue];
            if (type == 48) {
                //IMYMSGTypeNewBBSReview
                [TTQMessageDetailViewModel saveMessageAction:dicValue type:TTQMessageDetailTypeNewBBS];
            }
        }
    }];
    
    // 获取某个话题评论列表
      [[IMYURIManager shareURIManager] addForPath:@"circle/messageDetail/get"
                                  withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                      NSDictionary *dicValue = actionObject.uri.params;
                                      if ([dicValue isKindOfClass:NSDictionary.class]) {
                                          BKArrayBlock block = [dicValue objectForKey:@"callBackBlock"];
                                          NSUInteger idTemp = [[dicValue objectForKey:@"topic_id"] integerValue];
                                          BOOL isRead = [[dicValue objectForKey:@"isRead"] boolValue];
                                          if (idTemp > 0) {
                                              NSArray *result = [TTQMessageDetailViewModel getMessageAndConvertToDicAction:idTemp type:TTQMessageDetailTypeTopic isRead:isRead];
                                              block(result);
                                          }
                                          idTemp = [[dicValue objectForKey:@"news_id"] integerValue];
                                          if (idTemp) {
                                              NSArray *result = [TTQMessageDetailViewModel getMessageAndConvertToDicAction:idTemp type:TTQMessageDetailTypeNews isRead:isRead];
                                              block(result);
                                          }
                                      }
                                  }];

      // 设置某个话题评论已读
      [[IMYURIManager shareURIManager] addForPath:@"circle/messageDetail/setRead"
                                  withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                      NSDictionary *params = actionObject.uri.params;
                                      if ([params isKindOfClass:NSDictionary.class]) {
                                          NSArray *needReadArray = [params objectForKey:@"needReadArray"];
                                          for (NSDictionary *dicValue in needReadArray) {
                                              NSUInteger idTemp = [[dicValue objectForKey:@"topic_id"] integerValue];
                                              if (idTemp > 0) {
                                                  [TTQMessageDetailViewModel setAllMessageRead:idTemp type:TTQMessageDetailTypeTopic];
                                              }
                                              idTemp = [[dicValue objectForKey:@"news_id"] integerValue];
                                              if (idTemp) {
                                                  [TTQMessageDetailViewModel setAllMessageRead:idTemp type:TTQMessageDetailTypeNews];
                                              }
                                          }
                                      }
                                  }];
    
    // 设置所有话题、资讯评论已读
    [[IMYURIManager shareURIManager] addForPath:@"circle/messageDetail/setAllRead"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        [TTQMessageDetailViewModel setAllMessageReadWithType:TTQMessageDetailTypeNews];
        [TTQMessageDetailViewModel setAllMessageReadWithType:TTQMessageDetailTypeTopic];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"circle/messageDetail/getAllRead"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSArray *result = [TTQMessageDetailViewModel getAllMessagesWithRead:YES];
        [actionObject callbackWithObject:result];
    }];

    [[IMYURIManager shareURIManager] addForPath:@"circle/messageDetail/getAllUnread"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSArray *result = [TTQMessageDetailViewModel getAllMessagesWithRead:NO];
        [actionObject callbackWithObject:result];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"circle/messageDetail/getMsgs"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSString *startDate = actionObject.uri.params[@"startDate"];
        NSInteger pageCount = [actionObject.uri.params[@"count"] unsignedIntegerValue];
        NSArray *result = [TTQMessageDetailViewModel getMessageFromStart:startDate count:pageCount];
        [actionObject callbackWithObject:result];
    }];


    [[IMYURIManager shareURIManager] addForPath:@"circle/messageDetail/save"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSDictionary *dicValue = actionObject.uri.info;
        if ([dicValue isKindOfClass:NSDictionary.class]) {
            id idTemp = [dicValue objectForKey:@"topic_id"];
            
            if (idTemp) {
                [TTQMessageDetailViewModel saveMessageAction:dicValue];
                NSUInteger topicID = [idTemp integerValue];
                [TTQMessageDetailViewModel deleteOldMessage:topicID];
            }
            idTemp = [dicValue objectForKey:@"news_id"];
            TTQMessageDetailType detailType = TTQMessageDetailTypeNews;
            TTQMessageDetailType temp = [[actionObject.uri.params objectForKey:@"detailType"] integerValue];
            if (temp > TTQMessageDetailTypeTopic) {
                detailType = temp;
            }
            
            if (idTemp) {
                NSUInteger newsId = [idTemp unsignedIntegerValue];
                [TTQMessageDetailViewModel saveMessageAction:dicValue type:detailType];
                [TTQMessageDetailViewModel deleteOldMessage:newsId type:detailType];
            }
        }
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"circle/messageDetail/delete"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSDictionary *dicValue = actionObject.uri.params;
        id idTemp = [dicValue objectForKey:@"topic_id"];
        if ([idTemp isKindOfClass:NSNumber.class]) {
            NSUInteger topicID = [idTemp integerValue];
            if (topicID > 0) {
                [TTQMessageDetailViewModel deleteAllTopicMessage:topicID];
            }
        }
        idTemp = [dicValue objectForKey:@"news_id"];
        NSUInteger newsId = [idTemp integerValue];
        if (newsId > 0) {
            TTQMessageDetailType detailType = TTQMessageDetailTypeNews;
            TTQMessageDetailType temp = [[actionObject.uri.params objectForKey:@"detailType"] integerValue];
            if (temp > TTQMessageDetailTypeTopic) {
                detailType = temp;
            }
            [TTQMessageDetailViewModel deleteAllMessageAction:newsId type:detailType];
        }
    }];
    /// 按时间顺序获取评论数据，传第一条数据的时间 date,不传则默认按最新获取，  page:每次分页的数量
    [[IMYURIManager shareURIManager] addForPath:@"circle/messageDetail/getList"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSDictionary *dicValue = actionObject.uri.params;
        
    }];

    
}

+ (void)registerNavigationAction {
    [[IMYURIManager shareURIManager] addForPath:@"circles/home"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        //                                    [IMYGAEventHelper postWithURI:actionObject.uri];//7.5.8
        TTQHome5ViewModel *viewModel = [[TTQHome5ViewModel alloc] init];
        viewModel.home5Type = 1;
        viewModel.isFromUri = YES;
        TTQHome5ViewController *home5Ctr = [[TTQHome5ViewController alloc] initWithViewModel:viewModel];
        home5Ctr.fromURI = actionObject.uri;
        [viewModel setupSearchHelperWithController:home5Ctr];
        UIViewController *ctr = [actionObject getUsingViewController];
        if (![ctr isKindOfClass:[UINavigationController class]] && ![ctr isKindOfClass:[UITabBarController class]]) {
            if (ctr.navigationController.viewControllers.count > 0) {
                home5Ctr.isWhiteNavigationBar = YES;
            }
        }
        [ctr imy_push:home5Ctr];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"circles/group"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        // 如果由外部业务控制埋点，这里不再重复埋点
        BOOL disableGAEvent = [actionObject.uri.params[@"disableGAEvent"] boolValue];
        if (!disableGAEvent) {
            [IMYGAEventHelper postWithURI:actionObject.uri];
        }
        
        // 应用未打开时，通过push跳转话题详情，如果有她她圈首页，就需要先跳转至她她圈首页，再进入详情
        BOOL isPush = [actionObject.uri.params[@"isPush"] boolValue];
        if (isPush) {
            [self positionToCircleHomeTabAfterLaunching:NO];
        }
        
        UIViewController *pushBasedViewController = nil;
        IMYWeakObject *weakObj = actionObject.uri.params[@"pushBasedViewController"];
        if ([weakObj isKindOfClass:IMYWeakObject.class]) {
            pushBasedViewController = weakObj.obj;
        } else if([weakObj isKindOfClass:UIViewController.class]) {
            pushBasedViewController = (UIViewController *)weakObj;
        }
        
        UIViewController *destVC = [self groupDestVCWithActionObject:actionObject];
        
        // 页面跳转
        if (pushBasedViewController && [pushBasedViewController isKindOfClass:UIViewController.class]) {
            [pushBasedViewController imy_push:destVC];
        } else {
            [[actionObject getUsingViewController] imy_push:destVC];
        }
    }];
    
    /**
     其它模块获取圈子详情页
     @param groupID 圈子ID NSInteger类型
     @return forum 圈子模型 TTQForumModel类型
     */
    [[IMYURIManager shareURIManager] addForPath:@"circles/groupDetailVC"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        UIViewController *destVC = [self groupDestVCWithActionObject:actionObject];
        [actionObject callbackWithObject:destVC];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"circles/group/topic"
                                          level:90
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        [NSObject
         imy_asyncBlock:^{
            TTQReadRecord *record = [TTQReadRecord new];
            record.redirect_url = actionObject.uri.uri;
            [record saveToDB];
        }
         level:IMYQueueLevelBackground];
        
        // 应用未打开时，通过push跳转话题详情，如果有她她圈首页，就需要先跳转至她她圈首页，再进入详情
        BOOL isPush = [actionObject.uri.params[@"isPush"] boolValue];
        if (isPush) {
            [self positionToCircleHomeTabAfterLaunching:NO];
        }
            
        NSInteger topicID = [actionObject.uri.params[@"topicID"] integerValue];
        NSInteger forumID = [actionObject.uri.params[@"groupID"] integerValue];
        
        if (topicID < 1) {
            NSString *vcName = NSStringFromClass([UIViewController imy_currentViewControlloer].class);
            if (!vcName) {
                vcName = @"";
            }
            [IMYErrorTraces postWithType:IMYErrorTraceTypePageFails pageName:vcName category:IMYErrorTraceCategoryCommunity message:@"uri lost id" detail:actionObject.uri.params];
        }
        
        UIViewController *destVC = nil;
        TTQTopicViewModel *viewModel = [[TTQTopicViewModel alloc] initWithTopicID:topicID];
        viewModel.redirect_url = actionObject.uri.uri;
        if (forumID > 0) {
            viewModel.forum_id = forumID;
        }
        if (actionObject.uri.params[@"channel_id"]) {
            viewModel.channelID = [actionObject.uri.params[@"channel_id"] integerValue];
        }
        
        if (actionObject.uri.params[@"content_form"] && [self isInUGCStyle]) {
            viewModel.isUGCUIStlye = [actionObject.uri.params[@"content_form"] boolValue];
        }
        
        // 7.6.4 版本关注推荐页面使用的类型参数
        id catidValue = actionObject.uri.params[@"catid_s"];
        NSInteger cateId = 0;
        if ([catidValue respondsToSelector:@selector(integerValue)]) {
            NSInteger tmpCateId = [catidValue integerValue];
            if (tmpCateId == 73 || tmpCateId == 74) {
                cateId = tmpCateId;
            }
        }
        [viewModel imy_setPropertyWithDictionary:actionObject.uri.params filter:@"topicID", @"groupID", @"dataSource",@"topic_id", nil];
        if (viewModel.gotoID > 0) {
            viewModel.top_review_id = viewModel.gotoID;
        }
        if (viewModel.gotoID > 0 && viewModel.animationCommentIdWhenAppear < 1) {
            viewModel.animationCommentIdWhenAppear = viewModel.gotoID;
        }
        if (viewModel.sub_review_id) {
//            viewModel.animationCommentIdWhenAppear = 0;
            /// 8.90样式里，已经不在二级页了，服务端给了新字段
            viewModel.sub_comment_id = viewModel.sub_review_id;
        }
        viewModel.bi_catid = cateId;
        if ([self detailV2]) {
            viewModel.keepRowsWhenDeleteComment = YES;
            destVC = [[TTQTopicDetailViewControllerV2 alloc] initWithViewModel:viewModel];
        } else {
            destVC = [[TTQTopicDetailViewController alloc] initWithViewModel:viewModel];
        }        
        [destVC imy_setPropertyWithDictionary:actionObject.uri.params];
        if ([destVC isKindOfClass:IMYPublicBaseViewController.class]) {
            ((IMYPublicBaseViewController *)destVC).fromURI = actionObject.uri;
        }
        [[UIViewController imy_currentViewControlloer] imy_push:destVC];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"circles/group/review"
                                          level:90
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        [IMYGAEventHelper postWithURI:actionObject.uri];
        TTQTopicReferenceViewModel *viewModel = [TTQTopicReferenceViewModel new];
        [viewModel imy_setPropertyWithDictionary:actionObject.uri.params filter:@"gotoID", nil];
        viewModel.animationCommentIdWhenAppear = [actionObject.uri.params[@"gotoID"] integerValue];
        if (viewModel.animationCommentIdWhenAppear > 1) {
            viewModel.locate_to_comment = YES;
        }
        viewModel.becomeFirstResponder = [actionObject.uri.params[@"becomeFirstResponder"] boolValue];
        viewModel.redirect_url = actionObject.uri.params[@"biURI"];
        TTQCommentDetailViewController *vc = [[TTQCommentDetailViewController alloc] initWithViewModel:viewModel];
        vc.fromURI = actionObject.uri;
        [[actionObject getUsingViewController] imy_push:vc];
    }];
    
//    /circles/group/commentDetail
    [[IMYURIManager shareURIManager] addForPath:@"circles/group/commentDetail"
                                          level:90
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        [IMYGAEventHelper postWithURI:actionObject.uri];
        TTQTopicReferenceViewModel *viewModel = nil;
        NSDictionary *viewModelDataDict = actionObject.uri.params[@"viewModel"];
        if ([viewModelDataDict isKindOfClass:NSDictionary.class]) {
            viewModel = [TTQTopicReferenceViewModel yy_modelWithDictionary:viewModelDataDict];
            NSArray *dataSourceDictArr = actionObject.uri.params[@"viewModel"][@"dataSource"];
            if ([dataSourceDictArr isKindOfClass:NSArray.class] && [dataSourceDictArr.firstObject isKindOfClass:NSDictionary.class]) {
                viewModel.dataSource = [dataSourceDictArr toModels:TTQCommentModel.class];
            }
            viewModel.referenced.cellHeight = 0;
            [viewModel.dataSource bk_each:^(TTQCommentModel *obj) {
                obj.cellHeight = 0;
            }];
        } else if ([viewModelDataDict isKindOfClass:TTQTopicReferenceViewModel.class]) {
            viewModel = (TTQTopicReferenceViewModel *)viewModelDataDict;
        }
        if (!viewModel) {
            NSInteger topic_id = [actionObject.uri.params[@"topic_id"] integerValue];
            NSInteger referenced_id = [actionObject.uri.params[@"referenced_id"] integerValue];
            viewModel = [[TTQTopicReferenceViewModel alloc] initWithTopicID:topic_id referenced_id:referenced_id];
        }
        if (actionObject.uri.params[@"type"]) {
            viewModel.topicType = [actionObject.uri.params[@"type"] integerValue];
        } else {
            viewModel.topicType = TTQCommentBindTopicTypeNormal;
        }
        viewModel.isNewCommuity = [actionObject.uri.params[@"topic_type"] integerValue] == 2;
        
        [viewModel imy_setPropertyWithDictionary:actionObject.uri.params filter:@"topic_id", @"referenced_id", @"viewModel", nil];
        viewModel.redirect_url = actionObject.uri.params[@"biURI"];
        TTQCommentDetailViewController *vc = [[TTQCommentDetailViewController alloc] initWithViewModel:viewModel];
        [vc imy_setPropertyWithDictionary:[actionObject.uri.params bk_reject:^BOOL(NSString *key, id obj) {
            if ([key isKindOfClass:NSString.class] && [key isEqualToString:@"viewModel"]) {
                return YES;
            }
            return NO;
        }]];
        vc.fromURI = actionObject.uri;
        [[actionObject getUsingViewController] imy_push:vc];
    }];

    [[IMYURIManager shareURIManager] addForPath:@"circles/specialtopic"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        [[IMYURIManager shareURIManager] runActionWithPath:@"news/special" params:actionObject.uri.params info:nil];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"circles/mytopic"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        TTQMyTopicVM *viewModel = [TTQMyTopicVM new];
        [viewModel imy_setPropertyWithDictionary:actionObject.uri.params];
        IMYPublicBaseViewController *vc = [[TTQMyTopicVC alloc] initWithViewModel:viewModel];
        [vc imy_setPropertyWithDictionary:actionObject.uri.params];
        vc.fromURI = actionObject.uri;
        [[actionObject getUsingViewController] imy_push:vc];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"circles/find"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        TTQFindViewModel *viewModel = [TTQFindViewModel new];
        [viewModel imy_setPropertyWithDictionary:actionObject.uri.params];
        TTQFindViewController *vc = [[TTQFindViewController alloc] initWithViewModel:viewModel];
        [vc imy_setPropertyWithDictionary:actionObject.uri.params];
        vc.fromURI = actionObject.uri;
        [[actionObject getUsingViewController] imy_push:vc];
    }];
        
    [[IMYURIManager shareURIManager] addForPath:@"community/publish"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        //发布页进入条件
        if ([TTQABTestConfig disableInteractByType:TTQ_Interact_type_post]) {
            return;
        }
        NSString *inletSource = actionObject.uri.params[@"inletSource"];
        if (![IMYPublicAppHelper shareAppHelper].hasLogin) {
            [UIWindow imy_showTextHUD:kStatusText_unLogin];
            [[IMYURIManager shareURIManager] runActionWithString:@"login"];
            return;
        }
        if (imy_isEmptyString([IMYPublicAppHelper shareAppHelper].nickName)) {
            [UIWindow imy_showTextHUD:IMYString(@"请先设置你的昵称哦~")];
            void(^editFinishBlock)() = ^() {
                if (!imy_isEmptyString([IMYPublicAppHelper shareAppHelper].nickName)) {
                    [[IMYURIManager shareURIManager] runActionWithURI:actionObject.uri];
                }
            };
            IMYURI *uri = [IMYURI uriWithURIString:@"user/nickname"];
            [uri appendingParams:@{@"editFinishBlock":editFinishBlock}];
            [[IMYURIManager shareURIManager] runActionWithURI:uri];
            return;
        }
        if ([IMYAccountCheckService checkPhoneShouldBeBindedWithCompletion:nil]) {
            return;
        }
        if ([IMYCKFeedsHelper praiseStyleExp] && [actionObject.uri.params[@"type"] integerValue] == 0) {
            /// 实验样式下，未指定发布类型的都显示弹窗，让用户选择
            [self showPublishSheetView:actionObject.uri.params];
            return;
        }
        // 进入新版发布器聚合页
        /* 参数：
           指定进入某个发布器携带
           type ： 1 笔记 2 图文 3 问答
           forum_id: 圈子id（图文、问答、视频会归圈）
         */
        NSUInteger type = [actionObject.uri.params[@"type"] integerValue];
        NSInteger forum_id = [actionObject.uri.params[@"forum_id"] integerValue];
        NSString *forum_name = actionObject.uri.params[@"forum_name"];

        NSInteger publish_entrance = actionObject.uri.params[@"publish_entrance"]? [actionObject.uri.params[@"publish_entrance"] integerValue]: 0;
        NSInteger activity_id = [actionObject.uri.params[@"activity_id"] integerValue];
        NSInteger publishTipPopId = [actionObject.uri.params[@"publishTipPopId"] integerValue];
        if (type > 0) {
            id model = actionObject.uri.params[@"model"];
            NSUInteger enterType = [actionObject.uri.params[@"enterType"] integerValue];
            TTQPublish860ViewController *publishVC = [[TTQPublish860ViewController alloc] initWithType:type enterType:enterType model:model];
            publishVC.activity_id = activity_id;
            publishVC.forum_id = forum_id;
            publishVC.forum_name = forum_name;
            publishVC.publish_entrance = publish_entrance;
            publishVC.publishTipPopId = publishTipPopId;
            if(enterType == IMYPublishEnterTypeEdit && type == 1){
                publishVC.publish_entrance = TTQPublishEntranceEdit; // 笔记类型的二次编辑
            }
            IMYPublicBaseNavigationController *nav = [[IMYPublicBaseNavigationController alloc] initWithRootViewController:publishVC];
            nav.navigationBarHidden = YES;
            [[UIViewController imy_currentTopViewController] imy_present:nav];
        } else {
            TTQPublish860ViewController *publishVC = [[TTQPublish860ViewController alloc] init];
            publishVC.activity_id = activity_id;
            publishVC.forum_id = forum_id;
            publishVC.forum_name = forum_name;
            publishVC.publish_entrance = publish_entrance;
            publishVC.publishTipPopId = publishTipPopId;
            IMYPublicBaseNavigationController *nav = [[IMYPublicBaseNavigationController alloc] initWithRootViewController:publishVC];
            nav.navigationBarHidden = YES;
            [[UIViewController imy_currentTopViewController] imy_present:nav];
        }
    }];
        
    [[IMYURIManager shareURIManager] addForPath:@"circles/publish"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        //发布页进入条件
        NSString *inletSource = actionObject.uri.params[@"inletSource"];
        if (![IMYPublicAppHelper shareAppHelper].hasLogin) {
            [IMYEventHelper event:@"dl" addType:0 attributes:@{@"来源": [NSString stringWithFormat:@"%@发布新话题", inletSource ?: @""]}];
            [UIWindow imy_showTextHUD:kStatusText_unLogin];
            [[IMYURIManager shareURIManager] runActionWithString:@"login"];
            return;
        }
        if (imy_isEmptyString([IMYPublicAppHelper shareAppHelper].nickName)) {
            [UIWindow imy_showTextHUD:IMYString(@"请先设置你的昵称哦~")];
            void(^editFinishBlock)() = ^() {
                if (!imy_isEmptyString([IMYPublicAppHelper shareAppHelper].nickName)) {
                    [[IMYURIManager shareURIManager] runActionWithURI:actionObject.uri];
                }
            };
            IMYURI *uri = [IMYURI uriWithURIString:@"user/nickname"];
            [uri appendingParams:@{@"editFinishBlock":editFinishBlock}];
            [[IMYURIManager shareURIManager] runActionWithURI:uri];
            return;
        }
        if ([IMYAccountCheckService checkPhoneShouldBeBindedWithCompletion:nil]) {
            return;
        }
        
        BOOL (^eventBlock)(void) = actionObject.uri.params[@"eventBlock"];
        if (eventBlock) {
            if (!eventBlock()) {
                return;
            }
        }
        
        // TODO: 看下是否每次都有携带forum_id
        NSInteger forum_id = [actionObject.uri.params[@"forum_id"] integerValue];
        TTQPublishFromType fromType;
        NSInteger draftId;
        if(actionObject.uri.params[@"TTQPublishFromType"]) {
            fromType = (TTQPublishFromType)[actionObject.uri.params[@"TTQPublishFromType"] integerValue];
        } else {
            if ([[IMYUserDefaults standardUserDefaults] objectForKey:@"IMY_TTQ_DRAFT_EDITING_DRAFT_ID"]) {
                fromType = TTQPublishFromRestore;
            } else {
                fromType = TTQPublishFromNew;
            }
        }
        switch (fromType){
            case TTQPublishFromNew: {
                draftId = [TTQPublishModel availableDraftId];
            }
                break;
            case TTQPublishFromRestore: {
                draftId = [[[IMYUserDefaults standardUserDefaults] objectForKey:@"IMY_TTQ_DRAFT_EDITING_DRAFT_ID"] integerValue];
            }
                break;
            case TTQPublishFromDraftList: {
                draftId = [actionObject.uri.params[@"draftId"] integerValue];
            }
            case TTQPublishFromEdit: {
                NSInteger topicId = [actionObject.uri.params[@"topic_id"] integerValue];
                TTQPublishModel *lastLocalDraft = [TTQPublishModel searchSingleWithWhere:@{@"topicId":@(topicId)} orderBy:@"draftId desc"];
                draftId = lastLocalDraft.draftId;
            }
                break;
        }
        
        if (actionObject.uri.params[@"imageData"]) {
            NSData *decodeData = [actionObject.uri.params[@"imageData"] base64DecodedData];
            UIImage *image = [[[UIImage alloc] initWithData:decodeData] imy_fixOrientation];
            if (image) {
                [actionObject.uri appendingParams:@{@"defaultImage":image}];
            }
        }
        
        TTQPublishTopicViewModel *viewModel = [[TTQPublishTopicViewModel alloc] initWithforum_id:forum_id];
        if (forum_id > 0) {
            viewModel.forum_name = actionObject.uri.params[@"forum_name"];
        }
        viewModel.publishFromType = fromType;
        viewModel.draftId = draftId;
        NSInteger theme_id = [actionObject.uri.params[@"theme_id"] integerValue];
        if (theme_id > 0) {
            viewModel.theme_id = theme_id;
        }
        NSInteger from_type = [actionObject.uri.params[@"from_type"] integerValue];
        if (from_type == 1) {
            viewModel.from_type = 1;// h5发布图文贴
        } else if(from_type == 2) {
            viewModel.from_type = 2;
        }
        if (fromType == TTQPublishFromEdit) {
            viewModel.topic_id = [actionObject.uri.params[@"topic_id"] integerValue];
            viewModel.isEdit = YES;
        }
       
        viewModel.publishConfiguration.isExperiencePublish =  [actionObject.uri.params[@"isExperiencePublish"] boolValue];
        viewModel.currentTemplateID = [actionObject.uri.params[@"template_id"] integerValue];
        NSInteger subjectID =  [actionObject.uri.params[@"subject_id"] integerValue];
        
        NSString *subjectName = actionObject.uri.params[@"subject_name"];
        
        if (actionObject.uri.params[@"experience_id"] && !imy_isBlankString(actionObject.uri.params[@"experience_id"])) {
            viewModel.templateTypeID = [actionObject.uri.params[@"experience_id"] integerValue];
            viewModel.templateType = 1;
            
            {// 把经验当成热议话题处理
                if (subjectID == 0) {
                    subjectID = viewModel.templateTypeID;
                }
                if (imy_isBlankString(subjectName)) {
                    subjectName = actionObject.uri.params[@"experience_name"];
                }
            }
            
        } else if (viewModel.theme_id > 0) {
            viewModel.templateTypeID = viewModel.theme_id;
            if (actionObject.uri.params[@"mode"] && [actionObject.uri.params[@"mode"] integerValue] == 5) {
                viewModel.templateType = 1;
            } else {
                viewModel.templateType = 2;
            }
        }
       
        // 创建话题
        TTQTopicSubjectModel *subject = [[TTQTopicSubjectModel alloc] init];
        subject.subjectID = subjectID;
        subject.name = subjectName;
        if (theme_id > 0 && from_type == 1) {
            /// 小视频主题才有主题概念
            subject.subjectID = [actionObject.uri.params[@"theme_id"] integerValue];
            subject.name = actionObject.uri.params[@"theme_title"];
            subject.isTheme = YES;
        } else if (subjectID > 0 && imy_isEmptyString(subjectName) && actionObject.uri.params[@"theme_title"]) {
            /// h5传进来的from_type = 2,但没带subject_name.
            subject.name = actionObject.uri.params[@"theme_title"];
        }
        if ((subject.subjectID > 0) && (imy_isNotEmptyString(subject.name))) {// 存在外部传入的话题
            viewModel.subjectModel = subject;
            viewModel.subjectModel.isSelected = YES;
//            viewModel.unavailableSubject = YES;// 不允许修改热议话题
        }
        UIImage *image = actionObject.uri.info[@"image"];
        viewModel.defaultImage = image;
        [viewModel imy_setPropertyWithDictionary:actionObject.uri.params filter:@"forum_id", @"draft", nil];
        if (viewModel.promise_content && viewModel.stream_type) {
            /// 标记为进入时自带AI生文
            viewModel.aiInit = YES;
            viewModel.aiReferer = viewModel.referer;
        }
        
        //7.8.2 怀孕待产包isGoodPregnancy == YES修改背景
        viewModel.isGoodPregnancy = [actionObject.uri.params[@"isGoodPregnancy"] boolValue];
        
        // 进入指定tab的新版发布器
        // 携带viewmodel和entertype
        // fromtype 转换为新版的entertype ： TTQPublishFromRestore 处理成 new，突然退出应用不携带参数进去？
        IMYPublishEnterType enterType = fromType;
        NSInteger publish_entrance = [actionObject.uri.params[@"publish_entrance"] integerValue];
        switch (fromType){
            case TTQPublishFromNew:
                break;
            case TTQPublishFromRestore:
                enterType = IMYPublishEnterTypeNew;
                break;
            case TTQPublishFromDraftList:
                enterType = IMYPublishEnterTypeDraft;
                break;
            case TTQPublishFromEdit: {
                enterType = IMYPublishEnterTypeEdit;
                if (publish_entrance == 0) {
                    publish_entrance = TTQPublishEntranceEdit;
                }
            }
                break;
        }
        NSMutableDictionary *uriParams = [NSMutableDictionary dictionary];
        uriParams[@"type"] = @2;
        uriParams[@"enterType"] = @(enterType);
        uriParams[@"model"] = viewModel;
        uriParams[@"publish_entrance"] = @(publish_entrance);
        if (actionObject.uri.params[@"jump_url_after_publish"]) {
            uriParams[@"jump_url_after_publish"] = actionObject.uri.params[@"jump_url_after_publish"];
            viewModel.jump_url_after_publish = uriParams[@"jump_url_after_publish"];
        }
        if (actionObject.uri.params[@"forceTo860Publish"]) {
            /// 增加一个指向默认全部的跳转。
            uriParams[@"type"] = @0;
            uriParams[@"model"] = nil;
        }
        if (actionObject.uri.params[@"publishTipPopId"]) {
            uriParams[@"publishTipPopId"] = actionObject.uri.params[@"publishTipPopId"];
        }
        if (actionObject.uri.params[@"bi_index"]) {
            uriParams[@"bi_index"] = actionObject.uri.params[@"bi_index"];
        }
        if (actionObject.uri.params[@"forum_id"]) {
            uriParams[@"forum_id"] = actionObject.uri.params[@"forum_id"];
        }

        [[IMYURIManager shareURIManager] runActionWithPath:@"community/publish" params:uriParams info:nil];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"circles/draft" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        TTQDraftListViewModel *viewModel = [[TTQDraftListViewModel alloc] init];
        viewModel.params = actionObject.uri.params;
        BOOL fromPublishTool = [[actionObject.uri.params objectForKey:@"fromPublishTool"] boolValue];
        TTQDraftListTableViewController *ctrl = [[TTQDraftListTableViewController alloc] initWithViewModel:viewModel];
        ctrl.fromPublishTool = fromPublishTool;
        ctrl.fromURI = actionObject.uri;
        [[actionObject getUsingViewController] imy_present:ctrl];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"circle/topic/publishstate" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        __weak IMYVKWebView *webView = actionObject.webView;
        [[[[NSNotificationCenter defaultCenter] rac_addObserverForName:TTQTopicPublishStateChangeNotification object:nil] takeUntil:webView.rac_willDeallocSignal] subscribeNext:^(NSNotification *noti) {
            NSUInteger themeId = [[noti.object valueForKey:@"theme_id"] unsignedIntegerValue];
            NSUInteger topic_status = [[noti.object valueForKey:@"topic_status"] unsignedIntegerValue];
            NSString *themeid = [NSString stringWithFormat:@"%d",themeId];
            NSDictionary *param = @{@"themeID":themeid,@"state":@(topic_status)};
            if (topic_status == 1) {
                TTQPublishTopicSuccessorViewModel *viewModel = [noti.object valueForKey:@"model"];
                NSString *title = imy_isEmptyString(viewModel.topic.title) ? @"" : viewModel.topic.title;
                NSString *content = imy_isEmptyString(viewModel.topic.content) ? @"" : viewModel.topic.content;
                param = @{@"themeID":themeid,@"state":@(topic_status),@"title":title,@"id":@(viewModel.topic.topic_id),@"image":viewModel.topic.defaultImage,@"image_type":@(viewModel.topic.imageType),@"content":content};
            }
            
            [actionObject callbackWithObject:param error:nil eventName:@"circle/topic/publishstate"];
        }];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"circle/topic/publish_success" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        __weak IMYVKWebView *webView = actionObject.webView;
        [[[[NSNotificationCenter defaultCenter] rac_addObserverForName:TTQTopicPublishStateChangeNotification object:nil] takeUntil:webView.rac_willDeallocSignal] subscribeNext:^(NSNotification *noti) {
            NSUInteger themeId = [[noti.object valueForKey:@"theme_id"] unsignedIntegerValue];
            NSUInteger topic_status = [[noti.object valueForKey:@"topic_status"] unsignedIntegerValue];
            NSString *themeid = [NSString stringWithFormat:@"%d",themeId];
            NSDictionary *param = @{@"themeID":themeid,@"state":@(topic_status)};
            NSMutableDictionary *dic = [NSMutableDictionary new];
            if (topic_status == 1) {
                TTQPublishTopicSuccessorViewModel *viewModel = [noti.object valueForKey:@"model"];
                NSString *title = imy_isEmptyString(viewModel.topic.title) ? @"" : viewModel.topic.title;
                NSString *content = imy_isEmptyString(viewModel.topic.content) ? @"" : viewModel.topic.content;
                NSString *redirectUrl = imy_isEmptyString(viewModel.topic.redirect_url) ? @"" : viewModel.topic.redirect_url;
                param = @{@"themeID":themeid,
                          @"title":title,
                          @"id":@(viewModel.topic.topic_id),
                          @"content":content,
                          @"redirect_url":redirectUrl,
                          @"topic_category":@(0),
                          @"type":@(1),
                          @"is_video":@(0),
                          @"total_review":@(0),
                          @"praise_num":@(0),
                          @"has_praise":@(0),
                          @"image_type":@(viewModel.topic.imageType),
                };
                [dic addEntriesFromDictionary:param];
                if (viewModel.forum != nil) {
                    NSString *forum_name = imy_isEmptyString(viewModel.forum.name) ? @"" : viewModel.forum.name;
                    NSString *forum_icon = imy_isEmptyString(viewModel.forum.icon) ? @"" : viewModel.forum.icon;
                    NSString *forum_url = imy_isEmptyString(viewModel.forum.redirect_url) ? @"" : viewModel.forum.redirect_url;
                    NSDictionary *forum = @{@"b_forum_id":@(viewModel.forum.forum_id),
                                            @"circle_name":forum_name,
                                            @"circle_icon":forum_icon,
                                            @"circle_redirect_url":forum_url
                    };
                    [dic addEntriesFromDictionary:forum];
                }
                if (viewModel.topic.images != nil && viewModel.topic.images.count > 0) {
                    NSDictionary *images = @{@"images":[viewModel.topic.images yy_modelToJSONString]};
                    [dic addEntriesFromDictionary:images];
                }
                if (viewModel.topic.publisher != nil) {
                    NSString *scren_name = imy_isEmptyString(viewModel.topic.publisher.screen_name) ? @"" : viewModel.topic.publisher.screen_name;
                    NSString *avatar = imy_isEmptyString(viewModel.topic.publisher.avatar) ? @"" : viewModel.topic.publisher.avatar;
                    NSString *avatar_s = imy_isEmptyString(viewModel.topic.publisher.user_avatar.small) ? avatar : viewModel.topic.publisher.user_avatar.small;
                    NSString *experticon = imy_isEmptyString(viewModel.topic.publisher.expertIconNew) ? @"" : viewModel.topic.publisher.expertIconNew;
                    NSString *mp_vip_intro = imy_isEmptyString(viewModel.topic.publisher.mp_vip_intro) ? @"" : viewModel.topic.publisher.mp_vip_intro;
                    NSString *qa_user_icon = imy_isEmptyString(viewModel.topic.publisher.qa_user_icon) ? @"" : viewModel.topic.publisher.qa_user_icon;
                    NSString *mp_user_icon = imy_isEmptyString(viewModel.topic.publisher.mp_user_icon) ? @"" : viewModel.topic.publisher.mp_user_icon;
                    NSString *mp_expert_user_icon = imy_isEmptyString(viewModel.topic.publisher.mp_expert_user_icon) ? @"" : viewModel.topic.publisher.mp_expert_user_icon;
                    NSString *baby_info = imy_isEmptyString(viewModel.topic.publisher.baby_info) ? @"" : viewModel.topic.publisher.baby_info;
                    NSDictionary *publisher = @{
                        @"user_id":@(viewModel.topic.publisher.userID),
                        @"screen_name":scren_name,
                        @"error":@(viewModel.topic.publisher.error),
                        @"isvip":@(viewModel.topic.publisher.isvip),
                        @"user_avatar_small":avatar_s,
                        @"new_expert_icon":experticon,
                        @"qa_user_icon":qa_user_icon,
                        @"mp_user_icon":mp_user_icon,
                        @"mp_expert_user_icon":mp_expert_user_icon,
                        @"mp_vip_intro":mp_vip_intro,
                        @"baby_info":baby_info
                    };
                    
                    [dic addEntriesFromDictionary:publisher];
                }
            }
            [actionObject callbackWithObject:dic error:nil eventName:@"circle/topic/publish_success"];
        }];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"circles/exitForumWithID"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSInteger forum_id = [actionObject.uri.params[@"forum_id"] integerValue];
        NSInteger name_type = [actionObject.uri.params[@"name_type"] integerValue];
        void (^block)(BOOL) = actionObject.uri.params[@"block"];
        [[[TTQForumHelper exitForumWithID:forum_id nameType:name_type] deliverOnMainThread] subscribeNext:^(TTQForumModel *forum) {
            if (block) {
                block(!forum.is_joined);
            }
        }];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"circles/joinForumWithID"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSInteger forum_id = [actionObject.uri.params[@"forum_id"] integerValue];
        NSInteger name_type = [actionObject.uri.params[@"name_type"] integerValue];
        void (^block)(BOOL) = actionObject.uri.params[@"block"];
        [[[TTQForumHelper joinForumWithID:forum_id nameType:name_type] deliverOnMainThread] subscribeNext:^(TTQForumModel *forum) {
            if (block) {
                block(forum.is_joined);
            }
        }];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"circles/topicfollow"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        TTQTopicFollowViewModel *viewModel = [TTQTopicFollowViewModel new];
        [viewModel imy_setPropertyWithDictionary:actionObject.uri.params];
        IMYPublicBaseViewController *vc = [[TTQTopicFollowViewController alloc] initWithViewModel:viewModel];
        [vc imy_setPropertyWithDictionary:actionObject.uri.params];
        vc.fromURI = actionObject.uri;
        [[actionObject getUsingViewController] imy_push:vc];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"circles/praiselist"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        TTQPraiseListViewModel *viewModel = [TTQPraiseListViewModel new];
        [viewModel imy_setPropertyWithDictionary:actionObject.uri.params];
        IMYPublicBaseViewController *vc = [[TTQPraiseListViewController alloc] initWithViewModel:viewModel];
        [vc imy_setPropertyWithDictionary:actionObject.uri.params];
        vc.fromURI = actionObject.uri;
        [[actionObject getUsingViewController] imy_push:vc];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"circles/collect"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        if (![IMYPublicAppHelper shareAppHelper].hasLogin) {
            [[IMYURIManager shareURIManager] runActionWithString:@"login"];
            return;
        }
        IMYPublicBaseViewController *vc = [TTQMyCollectViewController new];
        [vc imy_setPropertyWithDictionary:actionObject.uri.params];
        vc.fromURI = actionObject.uri;
        [[actionObject getUsingViewController] imy_push:vc];
    }];

    [[IMYURIManager shareURIManager] addForPath:@"circles/forum/setting"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        TTQForumBroadcastInfoViewModel *viewModel = [[TTQForumBroadcastInfoViewModel alloc] init];
        [viewModel imy_setPropertyWithDictionary:actionObject.uri.params];
        TTQForumBroadcastInfoViewController *vc = [[TTQForumBroadcastInfoViewController alloc] initWithViewModel:viewModel];
        [vc imy_setPropertyWithDictionary:actionObject.uri.params];
        vc.fromURI = actionObject.uri;
        [[actionObject getUsingViewController] imy_push:vc];
    }];
    
    //订阅广播操作
    [[IMYURIManager shareURIManager] addForPath:@"circles/forum/setting/broadcast"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSArray *forum_ids = actionObject.uri.params[@"op_data"];
        BOOL isJoin = [actionObject.uri.params[@"op_join"] boolValue];
        void (^block)(NSArray *, BOOL, NSError *) = actionObject.uri.params[@"block"];
        [[[TTQForumHelper forumBroadcastWithIDs:forum_ids isJoin:isJoin] deliverOnMainThread]
         subscribeNext:^(id x) {
            if (block) {
                block(forum_ids, isJoin, nil);
            }
        }
         error:^(NSError *error) {
            if (block) {
                block(forum_ids, !isJoin, error);
            }
        }];
    }];
    
    //收藏操作
    [[IMYURIManager shareURIManager] addForPath:@"circles/topic/favorite"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSInteger topic_id = [actionObject.uri.params[@"topic_id"] integerValue];
        BOOL favorite = [actionObject.uri.params[@"favorite"] boolValue];
        void (^block)(NSInteger, BOOL, NSError *) = actionObject.uri.params[@"block"];
        NSString *path = [NSString stringWithFormat:@"users/me/favorite-topics/%ld", (long)topic_id];
        if (!favorite) {
            [[[TTQHttpHelper deletePath:path params:nil] deliverOnMainThread]
             subscribeNext:^(id x) {
                if (block) {
                    block(topic_id, NO, nil);
                }
            }
             error:^(NSError *error) {
                if (block) {
                    block(topic_id, YES, error);
                }
                [IMYErrorTraces postWithType:IMYErrorTraceTypeOthers pageName:NSStringFromClass([UIViewController imy_currentViewControlloer].class) category:IMYErrorTraceCategoryCommunity message:@"帖子取消收藏失败" detail:[IMYUGCRequest errorData:error requestParams:@{@"id":@(topic_id)}]];
            }];
        } else {
            [[[TTQHttpHelper putPath:path params:nil] deliverOnMainThread]
             subscribeNext:^(id x) {
                if (block) {
                    block(topic_id, YES, nil);
                }
            }
             error:^(NSError *error) {
                if (block) {
                    block(topic_id, NO, error);
                }
                [IMYErrorTraces postWithType:IMYErrorTraceTypeOthers pageName:NSStringFromClass([UIViewController imy_currentViewControlloer].class) category:IMYErrorTraceCategoryCommunity message:@"帖子收藏失败" detail:[IMYUGCRequest errorData:error requestParams:@{@"id":@(topic_id)}]];
            }];
        }
    }];
    //更多圈
    [[IMYURIManager shareURIManager] addForPath:@"circles/more"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSMutableDictionary *params = [NSMutableDictionary dictionaryWithDictionary:actionObject.uri.params];
        params[@"ninSearchView"] = @(YES);
        params[@"ninDisaffiliateBtn"] = @(YES);
        params[@"resultOfChangesForMine"] = @(YES);
        params[@"is_my_category"] = @(YES);
        params[@"extraType"] = @(2);
        IMYURI *uri = [IMYURI uriWithPath:@"circles/find"
                                   params:params
                                     info:nil];
        [[IMYURIManager shareURIManager] runActionWithURI:uri];
    }];
    //删帖|申请删帖的回调
    [[IMYURIManager shareURIManager] addForPath:@"circles/topic/delCallBack"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        //    topic_id:1010
        //    code:200 (成功是200， 其他的就是有错)
        //    type:1 (1删帖，2申请删帖)
        NSInteger code = [actionObject.uri.params[@"code"] integerValue];
        NSInteger type = [actionObject.uri.params[@"type"] integerValue];
        if (code == 200 && type == 1) {
            NSInteger topic_id = [actionObject.uri.params[@"topic_id"] integerValue];
            [[NSNotificationCenter defaultCenter] postNotificationName:TTQTopicDetailChangeNotifition
                                                                object:@{@"topic_id": @(topic_id),
                                                                         @"topic_status": @(1)}];
        }
    }];
    //发布热议话题
    [[IMYURIManager shareURIManager] addForPath:@"circles/publish/trendingTopics"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        [IMYGAEventHelper postWithURI:actionObject.uri];
        void (^block)(NSInteger, NSString *) = actionObject.uri.params[@"block"];
        TTQTopicCategoryListViewController *trendingTopicsVC = [[TTQTopicCategoryListViewController alloc] init];
        trendingTopicsVC.didSelectedTrendingTopicHandler = ^(NSInteger topicID, NSString * _Nonnull topicTitle) {
            if (block) {
               block(topicID, topicTitle);
            }
        };
        trendingTopicsVC.fromURI = actionObject.uri;
        [[actionObject getUsingViewController] imy_present:trendingTopicsVC];
    }];
    //写经验
    [[IMYURIManager shareURIManager] addForPath:@"circles/publish/experience"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSDictionary *params = @{@"forum_id":actionObject.uri.params[@"forum_id"]?:@"",@"isExperiencePublish":@YES};
        IMYURI *uri = [IMYURI uriWithPath:@"circles/publish" params:params info:nil];
        if (actionObject.uri.params) {
            [uri appendingParams:actionObject.uri.params];
        }
        [[IMYURIManager shareURIManager] runActionWithURI:uri];
    }];
    [[IMYURIManager shareURIManager] addForPath:@"circles/comment/trendingTopics"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        [IMYGAEventHelper postWithURI:actionObject.uri];
        void (^block)(NSInteger, NSString *) = actionObject.uri.params[@"block"];
        TTQTopicCategoryListViewController *trendingTopicsVC = [[TTQTopicCategoryListViewController alloc] init];
        trendingTopicsVC.didSelectedTrendingTopicHandler = ^(NSInteger topicID, NSString * _Nonnull topicTitle) {
            if (block) {
               block(topicID, topicTitle);
            }
        };
        trendingTopicsVC.fromURI = actionObject.uri;
        [[actionObject getUsingViewController] imy_present:trendingTopicsVC];
    }];
    
    //热议话题详情页
    [[IMYURIManager shareURIManager] addForPath:@"circles/topic/subject"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSInteger hotID = [actionObject.uri.params[@"subjectID"] integerValue];
        NSInteger redirect_type = [actionObject.uri.params[@"redirect_type"] integerValue];
        TTQHotTopicViewController *vc = [[TTQHotTopicViewController alloc] initWithHotTopicID:hotID];
        [vc imy_setPropertyWithDictionary:actionObject.uri.params];
        vc.redirect_type = redirect_type;
        vc.fromURI = actionObject.uri;
        [[actionObject getUsingViewController] imy_push:vc];
    }];

    //获取首页/圈详情的feeds流样式
    [[IMYURIManager shareURIManager] addForPath:@"circles/feedstyle"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        TTQFeedsCellStyle feedsStyle = [TTQDoorConfig feedsCellStyle];
        [actionObject callbackWithObject:@(feedsStyle)];
    }];
    
    //关注|取消关注用户
    [[IMYURIManager shareURIManager] addForPath:@"circles/follow"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        BOOL delFocus = [actionObject.uri.params[@"unfollow"] boolValue];
        if (delFocus) {
            NSInteger delUserID = [actionObject.uri.params[@"userID"] integerValue];
            [TTQForumHelper delFocusFriend:delUserID
                                  callback:^(BOOL bOK) {
                [actionObject callbackWithObject:@(bOK)];
            }];
        } else {
            NSArray *userIDS = actionObject.uri.params[@"userIDS"];
            [userIDS enumerateObjectsUsingBlock:^(id _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
                [TTQForumHelper addFocusFriend:[obj integerValue]
                                      callback:^(BOOL bOK, NSArray *models) {
                    [actionObject callbackWithObject:@(bOK)];
                }];
            }];
        }
    }];
    //点赞|取消点赞
    [[IMYURIManager shareURIManager] addForPath:@"circles/praise"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        BOOL isPraise = [actionObject.uri.params[@"isPraise"] boolValue];
        BOOL shouldPraise = [TTQCheckService checkShouldPraiseWithUserStatus:0 hasPraised:isPraise];
        if (!shouldPraise) {
            return;
        }
        NSInteger topicId = [actionObject.uri.params[@"topicId"] integerValue];
        NSInteger forumId = [actionObject.uri.params[@"forumId"] integerValue];
        NSInteger ownerId = [actionObject.uri.params[@"ownerId"] integerValue];
        BOOL isAsk = [actionObject.uri.params[@"isAsk"] integerValue];
        NSString *biURI = actionObject.uri.params[@"biURI"];
        [TTQForumHelper praiseTopicId:topicId
                              forumId:forumId
                              ownerId:ownerId
                                biURI:biURI
                             isPraise:isPraise
                                isAsk:isAsk
                             callback:^(BOOL bOK, NSError *error) {
            [actionObject callbackWithObject:@(bOK) error:error eventName:nil];
        }];
    }];
    //分享
    [[IMYURIManager shareURIManager] addForPath:@"circles/share"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSInteger topicId = [actionObject.uri.params[@"topicId"] integerValue];
        NSInteger type = [actionObject.uri.params[@"type"] integerValue];
        NSInteger channel = [actionObject.uri.params[@"channel"] integerValue];
        [TTQForumHelper shareTopicId:topicId type:type channelType:channel];
    }];
    
    //社区的分享到我的动态 jer603
    [[IMYURIManager shareURIManager] addForPath:@"circles/sharetomeeyou"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSString *title = actionObject.uri.params[@"title"];
        NSString *content = actionObject.uri.params[@"content"];
        NSArray *images = actionObject.uri.params[@"images"];
        NSString *screenName = actionObject.uri.params[@"screen_name"];
        NSInteger topicID = [actionObject.uri.params[@"topic_id"] integerValue];
        NSInteger forumID = [actionObject.uri.params[@"forum_id"] integerValue];
        TTQTopicModel *shareModelTTQ = [[TTQTopicModel alloc] init];
        shareModelTTQ.title = imy_isEmptyString(title) ? @"" : title;
        shareModelTTQ.content = imy_isEmptyString(content) ? @"" : content;
        shareModelTTQ.images = images;
        TTQPublisherModel *publish = [[TTQPublisherModel alloc] init];
        publish.screen_name = screenName;
        shareModelTTQ.publisher = publish;
        shareModelTTQ.topic_id = topicID; //topicid
        shareModelTTQ.forum_id = forumID; //圈子id
        [[IMYURIManager shareURIManager] runActionWithPath:@"share/dynamic"
                                                    params:@{@"type": @(1),
                                                             @"community_type": @(1), //jer611
                                                             @"fromType": @(1),
                                                             @"shareModel": shareModelTTQ}
                                                      info:nil];
    }];
    
    // 定位到她她圈Tab
    [[IMYURIManager shareURIManager] addForPath:@"circles/home_tab"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        [self positionToCircleHomeTab:YES];
    }];
    
    // 详情页导航到首页
    [[IMYURIManager shareURIManager] addForPath:@"circles/detailPageNavigateToHome"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        [[actionObject getUsingViewController] imy_pop:NO];
        imy_asyncMainBlock(^{
            [[IMYURIManager shareURIManager] runActionWithPath:@"circles/home_tab" params:nil info:actionObject.uri.info];
            // 刷新
            [[NSNotificationCenter defaultCenter] postNotificationName:@"REFRESH_TTQ" object:nil];
        });
    }];
    
    //跳转小视频全屏流流
    [[IMYURIManager shareURIManager] addForPath:@"community/topic/short_video" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        [TTQUriRegister handleShortVideoFlow:actionObject];
    }];
    
    //跳转小视频全屏流流 冗余注册, 防止有些页面下发的是旧的uri BI要求使用新的URI进行统计需求如下
    //【视频全屏流URL变更-iOS】 https://www.tapd.cn/22362561/prong/tasks/view/1122362561001050676
    [[IMYURIManager shareURIManager] addForPath:@"community/short_video" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        [TTQUriRegister handleShortVideoFlow:actionObject];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"newTTQ/comment/open"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        TTQShortVideoCommentView *view = [TTQShortVideoCommentView commentView];
        view.topicId = [actionObject.uri.params[@"topicId"] unsignedIntegerValue];
        if (actionObject.uri.params[@"shouldCustomVideoMaxCommentCount"]) {
            view.shouldCustomVideoMaxCommentCount = [actionObject.uri.params[@"shouldCustomVideoMaxCommentCount"] boolValue];
        }
        
        if (actionObject.uri.params[@"reviewId"]) {
            view.review_id = [actionObject.uri.params[@"reviewId"] unsignedIntegerValue];
        }
        if ([actionObject.uri.params[@"needLogin"] boolValue]) {
            view.shouldLogin = YES;
        }
        
        if (actionObject.uri.params[@"fromURI"]) {
            //                                        view.fromURI = actionObject.uri.params[@"fromURI"];
        }
        if (actionObject.uri.params[@"last"]) {
            view.last = [actionObject.uri.params[@"last"] integerValue];
        }
        [view imy_setPropertyWithDictionary:actionObject.uri.params];
        view.shouldSaveText = YES;
        @weakify(view);
        [view setWebCallBack:^(NSDictionary *resultJson) {
            @strongify(view);
            NSDictionary *dic = @{@"topicId": @(view.topicId),
                                  @"reviewId": @(view.review_id),
                                  @"result": resultJson};
            if (actionObject.uri.params[@"block"]) {
                void (^actionblock)(void) = actionObject.uri.params[@"block"];
                actionblock();
            }
            [actionObject callbackWithObject:dic];
            [[NSNotificationCenter defaultCenter] postNotificationName:@"TTQCommentCallBackNotification" object:dic];
        }];
        
        IMYPhotoBrowser *photoBrower = actionObject.uri.params[@"photoBrowser"];
        if (photoBrower && [photoBrower isKindOfClass:[IMYPhotoBrowser class]]) {
            @weakify(photoBrower);
            [view setShouldLoginCallBack:^{
                @strongify(photoBrower);
                [photoBrower dismiss];
            }];
        }
        
        UIView *showAtView = actionObject.uri.params[@"showAtView"];
        if (showAtView && [showAtView isKindOfClass:[UIView class]]) {
            [view showAtView:showAtView];
        } else {
            [view show];
        }
    }];
    
    // 添加URI给资讯首页她她圈帖子使用
    [[IMYURIManager shareURIManager] addForPath:@"circles/preloadTopic"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSInteger topicId = [actionObject.uri.params[@"topicId"] integerValue];
        [[TTQTopicContentPrefetcher shareInstance] prefetchTopic:topicId];
    }];
    
    //处理发帖页面，程序进入后台时 键盘需要保持原状
    [[IMYURIManager shareURIManager] addForPath:@"publish/resignActive"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        !actionObject.implCallbackBlock ?: actionObject.implCallbackBlock(@([TTQCommonHelp sharedCommonHelp].isShowKeyBoard), nil, nil);
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"circles/publish/edit" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSInteger topicId = [actionObject.uri.params[@"itemId"] integerValue];
        NSInteger type = [actionObject.uri.params[@"type"] integerValue];
        if (type == 0 && topicId) {
            /// 处理社区帖子类型
            [self requestPublishEdit:topicId params:actionObject.uri.params];
        }
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"circles/wikiDetail/dialog" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSInteger entryId = [actionObject.uri.params[@"entry_id"] integerValue];
        NSMutableDictionary *params = [@{@"entryId":@(entryId)} mutableCopy];
        if ([TTQABTestConfig baikeSearchEnable]) {
            
            void (^searchBlock)(NSString *keyword) = ^(NSString *keyword) {
                /// 百科弹窗带的是71
                NSString *serverWord = actionObject.uri.params[@"keyword"];
                if (imy_isNotEmptyString(serverWord)) {
                    keyword = serverWord;
                }
                NSDictionary *params = @{ @"pos_id": @(71),
                                          @"present": @(YES),
                                          @"animated":@YES,
                                          @"keyword":keyword,
                };
                [[IMYURIManager shareURIManager] runActionWithURI:[IMYURI uriWithPath:@"circles/searchresult"
                                                                               params:params
                                                                                 info:@{@"present":@YES}]];
                
                // 运营词点击埋点
                NSMutableDictionary *params2 = @{}.mutableCopy;
                params2[@"pos_id"] = @71;
                params2[@"func"] = @1;
                [IMYGAEventHelper postWithPath:@"search-static" params:params2 headers:nil completed:nil];

            };
            params[@"bottomAction"] = searchBlock;
        }
        IMYPublicBaseViewController *vc = actionObject.getUsingViewController;
        if ([vc isKindOfClass:IMYPublicBaseViewController.class] && vc.navigationBarHidden) {
            /// 不是全屏的vc，展示弹窗的时候，顶部会漏出导航栏，过滤掉
            params[@"onView"] = vc.view;
        }
        IMYURI *uri = [IMYURI uriWithPath:@"tools/wikiDetail/dialog" params:params info:nil];
        [[IMYURIManager sharedInstance] runActionWithURI:uri];
    }];
    [[IMYURIManager shareURIManager] addForPath:@"circles/activity"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        TTQHomeActivityViewController *vc = [[TTQHomeActivityViewController alloc] init];
        vc.shouldShowNavibar = YES;
        [vc imy_setPropertyWithDictionary:actionObject.uri.params];
        vc.fromURI = actionObject.uri;
        [[actionObject getUsingViewController] imy_push:vc];
    }];
}


// MARK: - Helper

+ (void)positionToCircleHomeTabAfterLaunching:(BOOL)shouldPopToRoot {
    id appDelegate = [[UIApplication sharedApplication] delegate];
    IMYSwitchModel *ttqSwitch = [[IMYDoorManager sharedManager] switchForType:@"circle_home_tab"];
    BOOL hasTTQ = (!ttqSwitch || ttqSwitch.data.value == 1 || [IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModePregnancy);
    BOOL isFromFinishLaunching = [[appDelegate valueForKey:@"isFromFinishLaunching"] boolValue];
    if (hasTTQ && isFromFinishLaunching) {
        [appDelegate setValue:@(NO) forKey:@"isFromFinishLaunching"];
        [self positionToCircleHomeTab:shouldPopToRoot];
    }
}

+ (void)positionToCircleHomeTab:(BOOL)shouldPopToRoot {
    id appDelegate = [[UIApplication sharedApplication] delegate];
    UIWindow *window = [appDelegate valueForKey:@"window"];
    UITabBarController *tabbarController = (UITabBarController *)window.rootViewController;
    UIViewController *targetVC = nil;
    UIViewController *presentedViewController = nil;
    if ([tabbarController isKindOfClass:UITabBarController.class]) {
        NSInteger circleIndex = -1;
        
        for (int i = 0; i < tabbarController.viewControllers.count; i++) {
            UIViewController *vc = tabbarController.viewControllers[i];
            UIViewController *destVC = nil;
            UINavigationController *navCtl = nil;
            if ([vc isKindOfClass:UINavigationController.class]) {
                destVC = ((UINavigationController *)vc).viewControllers.firstObject;
                navCtl = (UINavigationController *)vc;
            } else {
                destVC = vc;
            }
            if ([destVC isKindOfClass:[TTQHomeController targetClass]]) {
                circleIndex = i;
                presentedViewController = destVC.presentedViewController;
                if (navCtl != nil && navCtl.viewControllers.count >= 2) { //如果是在她她圈首页发视频就要回到她她圈，否则都返回发现首页
                    UIViewController *secondVC = navCtl.viewControllers[1];
                    if ([secondVC isKindOfClass:[TTQHome5ViewController class]]) {
                        targetVC = secondVC;
                    }
                }
                break;
            }
        }
        
        if (circleIndex >= 0) {
            
            void (^popAction)(void) = ^ {
                if (tabbarController.selectedIndex != circleIndex) {
                    // 把前位于前一个Tab的navigationController的栈清空，比如在进入的详情页或者圈子页，退到首页
                    [[UIViewController imy_currentViewControlloer].navigationController popToRootViewControllerAnimated:NO];
                }
                
                imy_asyncMainBlock(^{
                    // 定位到她她圈首页
                    tabbarController.selectedIndex = circleIndex;
                    // 返回到她她圈模块的首页（她她圈首页、发现首页或者二级她她圈首页）
                    if (shouldPopToRoot) {
                        if (targetVC != nil) {
                            if ([[UIViewController imy_currentViewControlloer].navigationController.viewControllers containsObject:targetVC]) {
                                [[UIViewController imy_currentViewControlloer].navigationController popToViewController:targetVC animated:NO];
                            }
                        } else {
                            [[UIViewController imy_currentViewControlloer].navigationController popToRootViewControllerAnimated:NO];
                        }
                    }
                });
            };
            
            if (presentedViewController) {
                [presentedViewController dismissViewControllerAnimated:NO completion:^{
                    popAction();
                }];
            } else {
                popAction();
            }
            
        } else {
            /// 没有就跳2级
            [[IMYURIManager shareURIManager] runActionWithString:@"circles/home"];
        }
    }
}

+ (UIViewController *)groupDestVCWithActionObject:(IMYURIActionBlockObject *)actionObject {
    // 获取参数
    NSInteger forumID = [actionObject.uri.params[@"groupID"] integerValue];
    TTQForumModel *forum = actionObject.uri.params[@"forum"];
    
    UIViewController *destVC = nil;
    
    TTQTopicsViewModel *viewModel = nil;
    if (forum && [forum isKindOfClass:TTQForumModel.class]) {
        viewModel = [[TTQTopicsViewModel alloc] initWithForum:forum];
    } else {
        viewModel = [[TTQTopicsViewModel alloc] initWithforum_id:forumID];
    }
    [viewModel imy_setPropertyWithDictionary:actionObject.uri.params filter:@"groupID", nil];
    
    // 新圈详情页
    TTQTopics848ViewController *tipicsVC = [[TTQTopics848ViewController alloc] init];
    tipicsVC.viewModel = viewModel;
    // 获取主题色
    [tipicsVC setupCircleColor];
    destVC = tipicsVC;
    
    // 设置VC参数
    [destVC imy_setPropertyWithDictionary:actionObject.uri.params];
    if ([destVC isKindOfClass:IMYPublicBaseViewController.class]) {
        ((IMYPublicBaseViewController *)destVC).fromURI = actionObject.uri;
    }
    
    return destVC;
}


+ (void)handleShortVideoFlow:(IMYURIActionBlockObject*)actionObject {
    NSDictionary *dict = actionObject.uri.params;
    TTQShortVideoFlowViewController *vc = [TTQShortVideoFlowViewController new];
    [vc imy_setPropertyWithDictionary:dict];
    vc.itemId = [dict[@"item_id"] integerValue];
    NSInteger source = [dict[@"source"] integerValue];
    if (source == 2) {
        vc.sort = dict[@"sort"];
    } else if (source == 3) {
        vc.source = 3;
        vc.recommendStyle = TTQVideoRecommendStyleNone;
    }
    vc.themeId = [dict[@"theme_id"] integerValue];
    vc.selectedIndex = [dict[@"selectedIndex"] integerValue];
    vc.ShortVideoDissMissBlock = dict[@"ShortVideoDissMissBlock"];
    NSArray <TTQShortVideoFeedsModel *>* videos = [dict[@"videos"] toModels:TTQShortVideoFeedsModel.class];
    vc.dataSource = videos;
    [vc imy_setPropertyWithDictionary:actionObject.uri.info];
    vc.fromURI = actionObject.uri;
    //        vc.from = [dict[@"from"] integerValue];
    
    TTQShortVideoPhotoSetNavigationController *nav = [[TTQShortVideoPhotoSetNavigationController alloc] initWithRootViewController:vc];
    nav.oriRect = [(NSValue *)(actionObject.uri.params[@"oriRect"]) CGRectValue];
    nav.oriImage = actionObject.uri.params[@"oriImage"];
    nav.oriView = actionObject.uri.params[@"cell"];
    nav.scrollPage = [actionObject.uri.params[@"selectedIndexNum"] integerValue];
    if ([actionObject.getUsingViewController isKindOfClass:[TTQShortVideoFlowViewController class]]) {
        TTQShortVideoPhotoSetNavigationController *lastNav = (TTQShortVideoPhotoSetNavigationController *)actionObject.getUsingViewController.navigationController;
        [lastNav setViewControllers:@[vc] animated:NO];
    } else {
        [actionObject.getUsingViewController presentViewController:nav animated:YES completion:nil];
    }
}

+ (void)requestPublishEdit:(NSInteger)topicId params:(NSDictionary *)actionParams {
    TTQTopicViewModel *viewModel = [[TTQTopicViewModel alloc] init];
    NSDictionary *param = [NSDictionary dictionaryWithObject:@(topicId) forKey:@"topic_id"];
    [viewModel checkEditableWithParam:param completion:^(id isModified, id resData, NSError *error) {
        if (isModified != nil) {
            BOOL modi = [isModified boolValue];
            if (modi) {
                // 可编辑跳转至帖子修改页
                // 不允许删除SD缓存
                [SDImageCache sharedImageCache].pauseClean = YES;
                
                TTQTopicModel *topic = [[TTQTopicModel alloc] init];
                topic.topic_id = topicId;
                
                [TTQPublishModel draftWithTopicModel:topic needUpdate:YES completedBlock:^(TTQPublishModel *publishModel) {
                    /// 就不考虑 weak 了，页面会强制停留
                    if (!publishModel) {
                        return;
                    }
                    publishModel.draftId = topic.topic_id;
                    [publishModel saveToDB];
                    NSMutableDictionary *mutableDictionary = [NSMutableDictionary new];
                    mutableDictionary[@"TTQPublishFromType"] = @(TTQPublishFromEdit);
                    mutableDictionary[@"topic_id"] = @(topicId);
                    if (actionParams[@"publish_entrance"]) {
                        mutableDictionary[@"publish_entrance"] = actionParams[@"publish_entrance"];
                    }
                    [[IMYURIManager shareURIManager] runActionWithPath:@"circles/publish" params:mutableDictionary info:nil];
                }];
            } else {
                // 不可编辑弹窗
                IMYAlertController *alert;
                NSDictionary *hint = (NSDictionary *)resData;
                if (hint != nil) {
                    NSString *mainTitle = [hint objectForKey:@"main_title"];
                    NSString *subTitle = [hint objectForKey:@"subtitle"];
                    NSArray *detailTitle = [hint objectForKey:@"child"];
                    for (NSString *str in detailTitle) {
                        subTitle = [subTitle stringByAppendingFormat:@"\n%@",str];
                    }
                    alert = [IMYAlertController alertControllerWithTitle:IMYString(mainTitle) message:IMYString(subTitle) messageLeftAlignment:YES];
                } else {
                    // 默认文案
                    alert = [IMYAlertController alertControllerWithTitle:IMYString(@"帖子不可编辑") message:IMYString(@"满足以下任意一项，不可编辑帖子:\n1.发布时间超过7天\n2.帖子已编辑过\n3.帖子被加精\n4.帖子已推荐给更多人") messageLeftAlignment:YES];
                }
                IMYAlertAction *confirmAction = [IMYAlertAction actionWithTitle:IMYString(@"我知道了") style:UIAlertActionStyleDestructive handler:nil];
                [alert addAction:confirmAction];
                [alert imy_show];
            }
        }
    }];
}

+ (BOOL)isInUGCStyle {
    IMYABTestExperiment *exp = [[IMYABTestManager sharedInstance] experimentForKey:@"pic2024"];
    if (exp && [exp.vars integerForKey:@"pic"] == 1) {
        return YES;
    }
    return NO;
}
/// https://www.tapd.meiyou.com/21039721/prong/stories/view/1121039721001264987
/// 发布器实验
+ (void)showPublishSheetView:(NSDictionary *)params {
    IMYPublishSheetView *publishView = [IMYPublishSheetView show];
    publishView.uriData = params;
}

+ (BOOL)detailV2 {
    return YES;
//    IMYABTestExperiment *exp = [[IMYABTestManager sharedInstance] experimentForKey:@"ios_detail_2025"];
//    if (exp && [exp.vars boolForKey:@"optimize"]) {
//        return YES;
//    }
//    return NO;

}

@end
