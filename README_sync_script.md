# MeetYouApp本地Pod库分支同步脚本

## 功能说明

这个脚本用于自动同步MeetYouApp项目中所有启用的本地Pod库，确保它们与主项目保持相同的分支并获取最新代码。

## 主要功能

1. **自动检测分支**：获取当前MeetYouApp项目所在的Git分支
2. **解析配置文件**：读取`Podfile.lib`文件，识别设置为`true`的本地库
3. **智能分支切换**：为每个启用的本地库切换到与主项目相同的分支
4. **代码同步**：拉取每个库的最新代码
5. **Pod更新**：最后执行`pod update`更新依赖

## 使用方法

### 基本使用
```bash
# 在项目根目录（包含MeetYouApp文件夹的目录）运行
./sync_local_pods.sh
```

### 或者在MeetYouApp目录内运行
```bash
cd MeetYouApp
../sync_local_pods.sh
```

## 前置条件

1. **Git**：确保系统已安装Git
2. **CocoaPods**：确保已安装CocoaPods (`gem install cocoapods`)
3. **权限**：确保脚本有执行权限 (`chmod +x sync_local_pods.sh`)
4. **工作目录**：在包含`MeetYouApp`目录或`Podfile.lib`文件的目录中运行

## 脚本行为

### 安全检查
- 检查每个库是否有未提交的更改，如有则跳过
- 检查目标分支是否存在，不存在则跳过
- 检查路径是否存在，不存在则跳过

### 错误处理
- 如果某个库的分支切换失败，会跳过该库继续处理其他库
- 如果代码拉取失败，会显示警告但不中断流程
- 最终会显示处理结果统计

### 日志输出
脚本使用彩色日志输出，包括：
- 🔵 **INFO**：一般信息
- 🟢 **SUCCESS**：成功操作
- 🟡 **WARNING**：警告信息
- 🔴 **ERROR**：错误信息

## 支持的库

脚本会自动处理`Podfile.lib`中所有设置为`true`的本地库，包括但不限于：

- ZZIMYMain
- IMYBaseKit
- IMYAccount
- IMYMe
- IMYCommonKit
- IMYMSG
- ChatAI
- IMYRecord
- IMYNews
- IMYTTQ
- IMYUGC
- IMYAnswer
- IMYTools_Swift

## 注意事项

1. **分支同步**：确保所有本地库都有与主项目相同的分支，否则会跳过该库
2. **网络连接**：需要稳定的网络连接来拉取最新代码
3. **本地更改**：如果本地库有未提交的更改，脚本会跳过该库以避免数据丢失
4. **路径结构**：脚本假设本地库位于`../库名`的相对路径下

## 示例输出

```
[INFO] 开始MeetYouApp本地Pod库分支同步
[INFO] 检查依赖工具...
[SUCCESS] 依赖检查完成
[INFO] 当前分支: feature/new-ui
[INFO] 解析Podfile.lib文件...
[INFO] 找到 3 个启用的本地库: IMYBaseKit IMYCommonKit IMYMSG
[INFO] 处理库: IMYBaseKit
[INFO] 切换到分支: feature/new-ui
[SUCCESS] 成功切换到分支: feature/new-ui
[SUCCESS] IMYBaseKit 代码更新完成
[INFO] 处理库: IMYCommonKit
[WARNING] IMYCommonKit 中不存在分支 'feature/new-ui'，保持当前分支
[INFO] 处理库: IMYMSG
[SUCCESS] IMYMSG 代码更新完成

[INFO] 处理结果统计:
[SUCCESS] 成功: 2
[WARNING] 跳过: 1
[ERROR] 错误: 0

[INFO] 开始执行pod update...
[SUCCESS] Pod update完成

[SUCCESS] 所有操作完成！
```

## 故障排除

### 常见问题

1. **权限被拒绝**
   ```bash
   chmod +x sync_local_pods.sh
   ```

2. **找不到Podfile.lib**
   确保在正确的目录中运行脚本，或者检查文件路径

3. **Git命令失败**
   检查网络连接和Git配置

4. **Pod update失败**
   尝试手动运行`pod update`查看具体错误信息

### 手动检查

如果脚本出现问题，可以手动检查：
```bash
# 检查当前分支
git branch

# 检查启用的库
grep "= true" MeetYouApp/Podfile.lib

# 手动切换某个库的分支
cd ../IMYBaseKit
git checkout your-branch-name
git pull origin your-branch-name
``` 