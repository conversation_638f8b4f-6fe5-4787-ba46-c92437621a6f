//
//  ChatAIViewModelV2.swift
//  ChatAI
//
//  Created by lxb on 2023/3/14.
//

import Foundation
import SwiftyJSON
import IMYSwift

class ChatAIViewModelV2 {
    
    // MARK: -
    typealias ErrorCallback = (_ error: Error?) -> Void
    
    /// 标记当前页面是否可见，用于控制图片上传完成后的刷新逻辑
    var isPageVisible: Bool = false
    
    // MARK: 初始化流程
    
    var config_max_retry_times = 0
    var config_assistant_title = ""
    var config_assistant_avatar = ""
    var config_assistant_introduce = ""
    var config_assistant_examples: [String] = []
    
    deinit {
        disposeMarkdownCache()
        cleanupImageSendCallbacks()
    }
    
    func disposeMarkdownCache() {
        MarkdownParserV2.removeAllMarkdownCache()
    }
    
    /// 清理图片发送相关回调
    func cleanupImageSendCallbacks() {
        for index in 0..<dataArray.count {
            if let cellType = dataArray[index] {
                let uuid = cellType.model.message.uuid
                if !uuid.isEmpty && cellType.model.message.isImageMessageModel() {
                    ChatAIImageSendManager.sharedInstance.removeCallbacks(uuid: uuid)
                }
            }
        }
    }
    
    /// 请求 init 接口
    func reqInit(isRefresh: Bool, completion: @escaping (_ model: ChatInitModel?, _ error: RequestError?) -> Void) {
        if self.needInitConvisation {
            // real request init api
            self.initConversation(refresh: isRefresh) { model, error in
                completion(model, error)
            }
        } else {
            completion(nil, nil)
        }
    }
    
    /// 请求配置中心数据
    func reqConfigCenter(completion: @escaping () -> Void) {
        let keyPath = "meetyou_app_setting.chat_assistant"
        IMYConfigsCenter.sharedInstance().fetchGroup(forKeyPath: keyPath) { [weak self] group in
            DispatchQueue.main.async {
                guard let self = self, let group = group else{
                    return
                }
                
                // max_retry_times
                self.config_max_retry_times = group.integer(forKey: "max_retry_times")
                
                // assistant_title
                self.config_assistant_title = group.string(forKey: "assistant_title") ?? ""
                
                // assistant_avatar
                self.config_assistant_avatar = group.string(forKey: "assistant_avatar") ?? ""
                
                // assistant_introduce
                self.config_assistant_introduce = group.string(forKey: "assistant_introduce") ?? ""
                
                // assistant_examples
                let array = group.array(forKey: "assistant_examples") ?? []
                var exampleList:[String] = [String]()
                for dict in array {
                    let dict = dict as! NSDictionary
                    if let example_text = dict["assistant_example_text"] as? String {
                        exampleList.append(example_text)
                    }
                }
                
                self.maxRetryTimes  = self.config_max_retry_times
                self.config_assistant_examples = exampleList
                
                completion()
            }
        }
    }
    
    /// AiReply
    func showTipIfNeed() {
        if self.findReplayTipCellModel() == nil {
            _ = self.showBottomReplyTip()
        }
    }
    
//    /// 确认引导数据
//    func showExampleOrGuideIfNeed(force: Bool = false) {
//        if !self.hadFinishExample() && self.haveExampleData() && (self.dataArray.count == 0 || force) {
//            var useIntro = ""
//            if let youJiejieIntro = self.initModel?.youJiejieIntro {
//                useIntro = youJiejieIntro
//                var qModels = self.initModel?.tutorial_questions ?? []
//                if force {
//                    qModels = []
//                }
//                self.createTutorialMessage(instro: useIntro, qModels: qModels)
//                print("[新话题][showExampleOrGuideIfNeed] createTutorialMessage")
//            }
//            return
//        }
//        
//        if self.hadFinishExample() && (self.dataArray.count == 0 || force) {
//            // 非新人空引导，也取 init 接口返回的数据
//            var qModels = self.initModel?.tutorial_questions ?? []
//            if force {
//                qModels = []
//            }
//            self.createGuideMessage(instro: self.config_assistant_introduce, qList: qModels)
//            print("[新话题][showExampleOrGuideIfNeed] createGuideMessage")
//            return
//        }
//        
//        print("[新话题][showExampleOrGuideIfNeed] create nothing")
//    }
    
    // TODO: * 插入开场白和大头像逻辑需要调整
    func insertWelcom() {
        if let firstCellModel = self.dataArray.first?.model, firstCellModel.message.exampleType != .none {
            print("不重复添加")
            return
        }
        
        // 产品：清空对话后的开场白改为取柚聊管理系统配置的内容，需客户端支持：不取config接口下发的内容，取init接口下发的intro内容
        var instro = self.initModel?.youJiejieIntro ?? ""
        var qList: [ChatAITutorialQuestionModel] = []
        // 新老用户以及示例和举例问题的判断逻辑统一由服务端处理, 移除本地判断逻辑
        qList = self.initModel?.tutorial_questions ?? []
       
        // 已经有消息则不拼接问题列表
        if self.dataArray.count > 0 {
            qList = []
        }
        
        // 兜底文案
        if instro.isEmpty {
            instro = "嗨，我是柚姐姐，你的贴心闺蜜。\n如果你有感情上的、生活上的、健康上的任何问题，都可以跟我说，我秒回~"
        } else {
            instro = instro.replacingOccurrences(of: "\\n", with: "\n")
        }
        
        // ChatMessageModel
        var welcomeMsgModel = ChatMessageModel.create(content: instro, exampleType: .intro)
        welcomeMsgModel.update(tutorial_questions: qList)
        
        welcomeMsgModel.tutorial_questions_title = self.initModel?.tutorial_questions_title ?? ""
        
        // ChatAICellModel
        var welcomeCellModel = ChatAICellModel(message: welcomeMsgModel, msgType: .recieved)
        welcomeCellModel.markdownContent = MarkdownParserV2.parseMarkdown(markdownPlainString: welcomeMsgModel.content)
        
        // ChatAICellType
        let welcomeCellType: ChatAICellType = .received(model: welcomeCellModel)
        self.dataArray.insert(contentsOf: [welcomeCellType], at: 0)
    }
    
    func showNewTopicIfNeed() {
        // 确保存在 newTopic 字段
        guard let newTopic = self.initModel?.new_topic, !newTopic.isEmpty else {
            return
        }
        
        // 8.94.0及以上版本, 示例问题和新话题服务端会做互斥处理, 不需要考虑会折叠调示例问题的情况
//        //只有引导问题的时候不能添加（否则后面 clearExample 会清除引导问题）
//        guard self.dataArray.count > 1 else {
//            return
//        }
        
        // 创建并保存新话题
        self.createNewTopicMessage(newTopics: newTopic)
    }
    
    // MARK: -
    
    fileprivate lazy var requester: SSERequester = {
        let req = SSERequester()
        req.initStuffs()
        return req
    }()
    
    // MARK: - public methods
    
    // MARK: ====== Load Datas ======
    
    var hasAllServiceDatas = false
    let limit = 30
    
    /// 加载第一页数据, 服务端返回第一页数据并告知客户端是否有历史数据
    func loadFirstPageData(completion: @escaping (_ cellTypes: [ChatAICellType], _ isFinish: Bool, _ hasHistoryData: Bool, _ error: NSError?) -> Void) {
        // 重置数据
        self.dataArray.removeAll()
        hasAllServiceDatas = false
        
        let specificLimit = -1
        self.loadMoreDatasFromService(specificLimit) {[weak self] cellTypes, isFinish, error in
            guard let self = self, let unwrappedInitModel = self.initModel else { return }
            
            // 错误
            if error != nil {
                completion([], self.hasAllServiceDatas, false, error)
                return
            }
            
            // 没有错误，则添加数据到 self.dataArray
            // 注意：在这里数组的顺序发生了变化
            for (_, cellType) in cellTypes.enumerated() {
                self.dataArray.insert(contentsOf: [cellType], at: 0)
            }
            
            var historyDataExists = unwrappedInitModel.hasHistoryMsgData()
            
            // 记录是否加载完
            self.hasAllServiceDatas = isFinish
            if historyDataExists {
                self.hasAllServiceDatas = false
            }
            
            // 人工客服场景下进入柚姐姐有历史消息折叠时不插入开场白, 直接去加载展示用户的消息
            if unwrappedInitModel.is_manual_service == true {
                if unwrappedInitModel.hasHistoryMsgData() == false {
                    self.insertWelcom()
                }
            } else {
                self.insertWelcom()
            }
            
            
            completion(cellTypes, self.hasAllServiceDatas, historyDataExists, error)
        }
        
    }
    
    func firstTimeLoadMoreHisotryPageDatas(completion: @escaping (_ cellTypes: [ChatAICellType], _ isFinish: Bool, _ error: NSError?) -> Void) {
        if hasAllServiceDatas == true {
            completion([], true, nil)
        } else {
            self.loadMoreDatasFromService(self.limit) {[weak self] cellTypes, isFinish, error in
                guard let self = self else { return }
                
                // 错误
                if error != nil {
                    completion([], self.hasAllServiceDatas, error)
                    return
                }
                
                // 插入分割线
                var historySplitLineMessage = ChatMessageModel.create(content: "")
                historySplitLineMessage.update(contentType: .historyMsgSpitline)
                let historySplitLineMessageReceiveModel = ChatAICellModel(message:historySplitLineMessage, msgType: .recieved)
                let historySplitLineMessageCellType = ChatAICellType.received(model: historySplitLineMessageReceiveModel)
                self.dataArray.insert(contentsOf: [historySplitLineMessageCellType], at: 0)
                
                // 没有错误，则添加数据到 self.dataArray
                // 注意：在这里数组的顺序发生了变化
                for (_, cellType) in cellTypes.enumerated() {
                    self.dataArray.insert(contentsOf: [cellType], at: 0)
                }
                
                // 记录是否加载完
                self.hasAllServiceDatas = isFinish
                
                // 加载完，实时拼接一条欢迎语
                if self.hasAllServiceDatas {
                    self.insertWelcom()
                }
                
                completion(cellTypes, self.hasAllServiceDatas, error)
            }
        }
    }
    
    
    /// 重置，加载更多数据
    func loadDatas(completion: @escaping (_ cellTypes: [ChatAICellType], _ isFinish: Bool, _ error: NSError?) -> Void) {
        // 重置数据
        self.dataArray.removeAll()
        hasAllServiceDatas = false
        
        // 加载更多数据
        self.loadMoreDatas { cellTypes, isFinish, error in
            completion(cellTypes, isFinish, error)
        }
    }
    
    /// 加载更多数据
    func loadMoreDatas(completion: @escaping (_ cellTypes: [ChatAICellType], _ isFinish: Bool, _ error: NSError?) -> Void) {
        if hasAllServiceDatas == true {
            completion([], true, nil)
        } else {
            self.loadMoreDatasFromService(self.limit) {[weak self] cellTypes, isFinish, error in
                guard let self = self else { return }
                
                // 错误
                if error != nil {
                    completion([], self.hasAllServiceDatas, error)
                    return
                }
                
                // 没有错误，则添加数据到 self.dataArray
                // 注意：在这里数组的顺序发生了变化
                for (_, cellType) in cellTypes.enumerated() {
                    self.dataArray.insert(contentsOf: [cellType], at: 0)
                }
                
                // 记录是否加载完
                self.hasAllServiceDatas = isFinish
                
                // 加载完，实时拼接一条欢迎语
                if self.hasAllServiceDatas {
                    self.insertWelcom()
                }
                
                completion(cellTypes, self.hasAllServiceDatas, error)
            }
        }
    }
    
    // 加载服务端的消息(只负责加载数据抛出 cellTypes)
    fileprivate func loadMoreDatasFromService(_ onePageMsgsCntLimit: Int = 30, completion: @escaping (_ cellTypes: [ChatAICellType], _ isFinish: Bool, _ error: NSError?) -> Void) {
        // get firstId
        let dataArrayForFirstId = self.dataArray.filter { cellType in
            cellType.model.status != .failed &&
            cellType.model.message.exampleType == .none &&
            (cellType.model.message.msgId.count > 0 || cellType.model.message.images.count > 0) &&
            !cellType.model.message.isNewTopic() &&
            cellType.model.message.contentType != .historyMsgSpitline &&
            cellType.model.message.isMsgIdFromServer() == true
        }
        let firstId = dataArrayForFirstId.first?.model.message.msgId ?? ""
        
        IMYChatAIHTTPService.sharedInstance().fetchChatMsgList(firstId, limit: onePageMsgsCntLimit, scenarioKey: self.scenario_key) { [weak self] responseDict in
            guard let self = self else { return }
            
            // [ChatMessageModel]
            let msgModels = JSON(responseDict)["messages"].arrayValue.map {
                var messageModel = ChatMessageModel(json: $0, showAppriase: false, showDate: false)
                
                // 特殊处理一下 contentType == 9
                if messageModel.contentType == .manualReplyTip {
                    if let service_tips_config = self.initModel?.service_tips_config as? ChatAIServiceTipsConfigModel, service_tips_config.isValid() {
                        // 获取客服提示样式配置信息
                        messageModel.update(content: service_tips_config.content)
                        messageModel.service_tips_config = service_tips_config
                    } else { 
                        // 没有内容则设置一些兜底文案和内容
                        messageModel.update(content: "为了更加准确地回答你的问题，我邀请")
                        messageModel.service_tips_config.content = "为了更加准确地回答你的问题，我邀请"
                        messageModel.service_tips_config.from = "人工客服"
                        messageModel.service_tips_config.text = "为你提供服务。"
                    }
                }
                
                return messageModel
            }
            
            // [ChatAICellModel]
            let cellModels = msgModels.map { msgModel -> ChatAICellModel in
                // msgType
                var msgType = ChatAICellModel.MessageType.recieved
                if msgModel.msgType == 1 {
                    msgType = .send
                } else {
                    msgType = .recieved
                }
                
                // cellModel
                var cellModel = ChatAICellModel(message: msgModel, msgType: msgType, status: .succeed)
                
                // markdownContent
                if msgType == .send {
                    cellModel.markdownContent = MarkdownParserV2.parseAsPlainText(markdownPlainString: msgModel.content, isSent: msgType == .send)
                } else {
                    if msgModel.contentType == .htmlText {
                        cellModel.markdownContent = NSAttributedString(string: "")
                    } else {
                        cellModel.markdownContent = MarkdownParserV2.parseMarkdown(markdownPlainString: msgModel.content)
                    }
                    
                }
                
                // return
                return cellModel
            }
            
            // [ChatAICellType]
            let cellTypes = cellModels.map { cellModel -> ChatAICellType in
                switch cellModel.msgType {
                case .send:
                    return .send(model: cellModel)
                case .recieved:
                    return .received(model: cellModel)
                }
            }
            
            // isFinish
            let isFinish = cellTypes.count < self.limit
            self.hasAllServiceDatas = isFinish
            
            completion(cellTypes, isFinish, nil)
        } onError: { [weak self] error in
            guard let _ = self else { return }
            
            completion([], false, error as NSError)
        }
    }
    
    func loadFaildDatasFromDB(cb: @escaping (_ cellTypes: [ChatAICellType]) -> Void) {
        self.cache.loadSendFaild { cellTypes in
            self.sendFaildArray.append(contentsOf: cellTypes)
            self.dataArray.append(contentsOf: self.sendFaildArray.dataArray)
            cb(cellTypes)
        }
    }
    
    func removeFaildDatas(cellModel: ChatAICellModel) {
        if let index = self.dataArray.firstIndex(where: { cellType in
            cellType.model.message.msgId == cellModel.message.msgId
        }) {
            self.dataArray.remove(at: index)
        }
        
        _ = self.cache.removeSendFaild(cellModel: cellModel)
    }
    
    /// 排除一下发送失败的数据
    func dataArrayExceptSendFail() {
        // 没有发送失败，无需处理
        if self.sendFaildArray.count == 0 {
            return
        }
        
        // 过滤调服务端以为本地已经发送成功，实际上本地标记为失败的内容
        var dataArray0 = self.dataArray.filter { [weak self] aCellType in
            guard let self = self else { return false}
            
            let idx = self.sendFaildArray.firstIndex(where: { failCellType in
                let sameUUID = failCellType.model.message.uuid == aCellType.model.message.uuid
                let succeed = aCellType.model.status == .succeed
                return sameUUID && succeed
            })
            
            if let idx = idx {
                print("[rgkf][成功移除一条失败数据]")
                return false
            }
            
            return true
        }
        
        // 重置 dataArray
        self.dataArray.removeAll()
        self.dataArray.append(contentsOf: dataArray0)
    }
    
    // MARK: ====== Load Datas ======
    
    func deleteCell(indexPath: IndexPath){
        if indexPath.row >= dataArray.count {
            return
        }
        guard let item = dataArray[indexPath.row] else {
            return
        }
        switch item{
        case .received(let model):
            cache.setDeleteMark(cellModel: model)
            dataArray.remove(at: indexPath.row)
        case .send(let model):
            cache.setDeleteMark(cellModel: model)
            dataArray.remove(at: indexPath.row)
        case .memory(let model):
            cache.setDeleteMark(cellModel: model)
            dataArray.remove(at: indexPath.row)
        case .receiving(_):break
        case .replyTip(_):break
        case .example(_):break
        }
    }
    
    func combineCell(indexs: [Int]) -> String {
        
        return indexs.map {
            guard let type = dataArray[$0] else {
                return ""
            }
            return type.model.content
        }.joined(separator: "\n")
    }
    
    func handleMultiCopy() -> String{

        var indexs:[Int] = []
        for (index, element) in dataArray.enumerated() {
            let msgId = element.model.message.msgId
            let item = selectedMsgIDs.first { $0 == msgId }
            if let _ = item{
                indexs.append(index)
            }
        }
        return combineCell(indexs: indexs)
    }
    
    
    /// 选中数据
    /// - Parameters:
    ///   - indexPath: indexPath
    ///   - reset: 是否重置,第一次选中的时候传true
    func selectedCell(indexPath: IndexPath, reset: Bool) -> Bool?{
        if(indexPath.row >= dataArray.count){
            return true
        }
        self.isEditing = true
        kChatAITextMaxWidth = Device.screenWidth - 40 - 12 - 12 - 12
        kChatAIIsSelected = true
        guard let type = dataArray[indexPath.row] else {
            return false
        }
        let message = type.model.message
        if message.contentType == .memory {
            return nil
        }
        if(reset){
            selectedMsgIDs.removeAll()
            let array = dataArray.filter { type in
                type.model.message.msgId == message.msgId
                && !type.model.message.msgId.isEmpty
                && type.model.content.count > 0
            }.map { $0.model.message.msgId }
            
            if(array.count > 0){
                selectedMsgIDs.append(contentsOf: array)
            }else{
                selectedMsgIDs.append(message.msgId)
            }
        }else if(!message.content.isEmpty){
            let msgId = selectedMsgIDs.first { $0 == message.msgId }
            if let _ = msgId{
                selectedMsgIDs.removeAll { $0 == message.msgId }
            }else{
                if(selectedMsgIDs.count < 99){
                    selectedMsgIDs.append(message.msgId)
                }else{
                    return false
                }
            }
        }
        return true
    }
    
    
    /// 当前indexPath是否选中
    /// - Parameter indexPath: indexPath
    /// - Returns: true or false
    func isSelected(indexPath: IndexPath)-> Bool{
        if(indexPath.row >= dataArray.count || selectedMsgIDs.count == 0){
            return false
        }
        guard let type = dataArray[indexPath.row] else {
            return false
        }
        let message = type.model.message
        let msgId = selectedMsgIDs.first { $0 == message.msgId }
        if let _ = msgId {
            return true
        }
        return false
    }
    
    
    /// 标记发送失败, 如果已接到过对应uuid的消息, 则认为成功
    /// - Parameters:
    ///   - uuid: uuid, 同一条发送和接收的uuid一样
    ///   - status: 失败或者成功
    /// - Returns: 返回对应下标
    @discardableResult
    func updateSendCellModel(uuid: String, status: ChatAICellModel.Status) -> Int? {
//        if hasReceivedCellModel(uuid: uuid){
//            return nil
//        }
        
        let index = dataArray.firstIndex { cellType in
            switch cellType{
            case .send(let model):
                return model.message.uuid == uuid
            default:
                return false
            }
        }
        if let index = index{
            guard let type = dataArray[index] else {
                return nil
            }
            var sendModel = type.model
            
            // 状态都一样才不更新，返回和 hasReceivedCellModel 保持一致
            if (sendModel.status == status) {
                return nil
            }
 
            sendModel.status = status
            dataArray[index] = .send(model: sendModel)
            
            _ = cache.saveCache(cellModel: sendModel, chatType: chatType)
            
            if status == .failed {
                _ = self.cache.saveSendFaild(cellModel: sendModel)
            } else if status == .succeed {
                _ = self.cache.removeSendFaild(cellModel: sendModel)
            }

        }
        return index
    }
    
    @discardableResult
    func updateSendCellModelVideos(uuid: String, videos: [ChatAIVideo]) -> Int? {
        
        let index = dataArray.firstIndex { cellType in
            switch cellType{
                case .send(let model):
                    return model.message.uuid == uuid
                default:
                    return false
            }
        }
        if let index = index {
            guard let type = dataArray[index] else {
                return nil
            }
            var sendModel = type.model
            sendModel.message.videos = videos
            
            dataArray[index] = .send(model: sendModel)
            _ = cache.saveCache(cellModel: sendModel, chatType: chatType)
            //_ = self.cache.saveSendFaild(cellModel: sendModel)
        }
        return index
    }
    
    /// 更新 Content 字段
    func updateSendCellModel(uuid: String, content: String) -> Int? {
        let index = dataArray.firstIndex { cellType in
            switch cellType{
            case .send(let model):
                return model.message.uuid == uuid
            default:
                return false
            }
        }
        
        if let index = index {
            guard let type = dataArray[index] else {
                return nil
            }
            var sendModel = type.model
            
            if sendModel.content == content {
                return nil
            }
 
            sendModel.content = content
            sendModel.markdownContent = MarkdownParserV2.parseAsPlainText(markdownPlainString: content)
            dataArray[index] = .send(model: sendModel)
            
            _ = cache.saveCache(cellModel: sendModel, chatType: chatType)
        }
        return index
    }
    
    /// 更新 images 字段
    @discardableResult
    func updateSendCellModel(uuid: String, images: [ChatAIImage]) -> Int? {
        let index = dataArray.firstIndex { cellType in
            switch cellType{
            case .send(let model):
                return model.message.uuid == uuid
            default:
                return false
            }
        }
        
        if let index = index {
            guard let type = dataArray[index] else {
                return nil
            }
            var sendModel = type.model
            
            sendModel.update(images: images)
            dataArray[index] = .send(model: sendModel)
            
            _ = cache.saveCache(cellModel: sendModel, chatType: chatType)
        }
        return index
    }
    
    @discardableResult
    func updateSendCellModel(uuid: String, videos: [ChatAIVideo]) -> Int? {
        let index = dataArray.firstIndex { cellType in
            switch cellType{
                case .send(let model):
                    return model.message.uuid == uuid
                default:
                    return false
            }
        }
        
        if let index = index {
            guard let type = dataArray[index] else {
                return nil
            }
            var sendModel = type.model
            
            sendModel.update(videos: videos)
            dataArray[index] = .send(model: sendModel)
            
            _ = cache.saveCache(cellModel: sendModel, chatType: chatType)
        }
        return index
    }
    
    func refreshSendCellModelRetryCount(uuid: String) -> Int? {
        let index = dataArray.firstIndex { cellType in
            switch cellType{
            case .send(let model):
                return model.message.uuid == uuid
            default:
                return false
            }
        }
        if let index = index {
            guard let type = dataArray[index] else {
                return nil
            }
            var sendModel = type.model
            sendModel.retryCount += 1
            dataArray[index] = .send(model: sendModel)
            
            _ = cache.saveCache(cellModel: sendModel, chatType: chatType)
        }
        return index
    }
    
    func checkSendCellModelAllowRetry(uuid: String) -> Bool{
        let sendModel = dataArray.reversed().first { cellType in
            switch cellType{
            case .send(let model):
                return model.message.uuid == uuid
            default:
                return false
            }
        }?.model
        
        if let sendModel = sendModel, sendModel.retryCount < maxRetryTimes {
            return true
        }
        return false
    }
    
    func refreshSendCellModelManualRetryCount(uuid: String) -> Int? {
        let index = dataArray.firstIndex { cellType in
            switch cellType{
            case .send(let model):
                return model.message.uuid == uuid
            default:
                return false
            }
        }
        if let index = index{
            guard let type = dataArray[index] else {
                return nil
            }
            var sendModel = type.model
            sendModel.manualRetryCount += 1
            dataArray[index] = .send(model: sendModel)
            
            _ = cache.saveCache(cellModel: sendModel, chatType: chatType)
        }
        return index
    }
    
    func checkSendCellModelAllowManualRetry(uuid: String) -> Bool{
        let sendModel = dataArray.reversed().first { cellType in
            switch cellType{
            case .send(let model):
                return model.message.uuid == uuid
            default:
                return false
            }
        }?.model
        
        if let sendModel = sendModel {
            return true
        }
        return false
    }
    
    func findSendCellModel(uuid: String) -> ChatAICellModel? {
        let index = dataArray.firstIndex { cellType in
            switch cellType{
            case .send(let model):
                return model.message.uuid == uuid
            default:
                return false
            }
        }
        
        if let index = index {
            return dataArray[index]?.model
        }
        return nil
    }
    
    /// 更新记忆卡片已读状态
    /// - Parameters:
    ///   - uuid: uuid, 同一条发送和接收的uuid一样
    ///   - memoryRead: 是否已读
    /// - Returns: 对应下标
    func updateMemoryCellModel(uuid: String, memoryRead:Bool) -> Int? {
        let index = dataArray.lastIndex { cellType in
            switch cellType{
            case .memory(let model):
                return model.message.uuid == uuid
            default:
                return false
            }
        }
        
        if let index = index {
            guard let type = dataArray[index] else {
                return nil
            }
            var memoryModel = type.model
            memoryModel.update(memoryRead: memoryRead)
            dataArray[index] = .memory(model: memoryModel)
            
            _ = cache.saveCache(cellModel: memoryModel, chatType: chatType)
        }
        
        return index
    }
    
    func findUnreadMemoryCellModel() -> ChatAICellModel? {
        let index = dataArray.lastIndex { cellType in
            switch cellType{
            case .memory(let model):
                return model.message.memoryRead == false
            default:
                return false
            }
        }
        
        if let index = index {
            return dataArray[index]?.model
        }
        return nil
    }
    
    /// 所有记忆卡片更新为已读状态
    func refreshAllMemoryCellModel() {
        
        let array = dataArray.filter {
            return $0.model.message.contentType == .memory
            && $0.model.message.memoryRead == false
        }
        
        for cellType in array {
            
            let index = dataArray.firstIndex {
                return $0.model.message.uuid == cellType.model.message.uuid
            }
            
            if let index = index {
                guard let type = dataArray[index] else {
                    return
                }
                var memoryModel = type.model
                memoryModel.update(memoryRead: true)
                dataArray[index] = .memory(model: memoryModel)
                
                _ = cache.saveCache(cellModel: memoryModel, chatType: chatType)
            }
        }
                    
    }
    
    func showBottomReplyTip() -> ChatAICellModel? {
        if let _ = findReplayTipCellModel() {
            return nil
        }
        
        if dataArray.count <= 0 {
            return nil
        }
        
        var message = ChatMessageModel.create(content: "")
        message.showDate = false
        var cellModel = ChatAICellModel(message: message, msgType: .recieved)
        cellModel.replyTipType = .bottom
        let replyTipType = ChatAICellType.replyTip(model: cellModel)
        dataArray.append(replyTipType)
        
        return cellModel
        
    }
    
    func showTopReplyTip() -> ChatAICellModel? {
        if let _ = findReplayTipCellModel() {
            return nil
        }
        
        if dataArray.count > 0 {
            return nil
        }
        
        var message = ChatMessageModel.create(content: "")
        message.showDate = false
        var cellModel = ChatAICellModel(message: message, msgType: .recieved)
        cellModel.replyTipType = .top
        let replyTipType = ChatAICellType.replyTip(model: cellModel)
        dataArray.append(replyTipType)
        
        return cellModel
        
    }
    
    func findReplayTipCellModel() -> ChatAICellModel? {
        let replyTipType = dataArray.reversed().first {
            switch $0{
            case .replyTip(_): return true
            default: return false
            }
        }
        
        return replyTipType?.model
    }
    
    
    /// 只剩一个cellType且类型为replyTip
    func isOnlyReplyTipCellModel() -> Bool {
        if dataArray.count == 1, let cellType = dataArray.first, cellType.model.replyTipType != .none{
            return true
        }
        return false
    }
    
    func updateCellModel(uuid:String, replyTipType:ChatAICellModel.ReplyTipType) -> Int? {
        
        let index = dataArray.lastIndex(where: { cellType in
            let model = cellType.model
            return model.message.uuid == uuid
        })
        
        if let index = index{
            guard let type = dataArray[index] else {
                return nil
            }
            var cellModel = type.model
            cellModel.replyTipType = replyTipType
            
            switch cellModel.msgType{
            case .send:
                dataArray[index] = .send(model: cellModel)
            case .recieved:
                dataArray[index] = .received(model: cellModel)
            }
        }
        return index
    }
    
    /// 是否有收到对应消息,
    /// - Parameter uuid: uuid
    /// - Returns: true or false
    func hasReceivedCellModel(uuid: String) -> Bool{
        let count = dataArray.filter { cellType in
            cellType.model.message.uuid == uuid
        }.count
        return count > 1
    }
    
    func cancelSelectedCell(){
        self.isEditing = false
        kChatAITextMaxWidth = Device.screenWidth - 12 - 12 - 12 - 12
        kChatAIIsSelected = false
        self.selectedMsgIDs.removeAll()
    }
    
    /// 清空缓存
    func clearConversation(){
        initModel = nil
        cache.deleteAllMsg()
        cache.removeAllSendFaild()
        dataArray.removeAll()
        sendFaildArray.removeAll()
        biRecievedArray.removeAll()
        clearFirstSendTag()
        currentSendType = nil
    }
    
    func clearFirstSendTag(){
        let key = "ChatAI.isNotFirstSend.key.cache"
        IMYSwiftUserDefaults.set(false, forKey: key)
        isFirstSend = false
    }
    
    
    /// 过滤允许更新的,并且缓存
    /// - Parameter cellModels: 未过滤
    /// - Returns: 已过滤,且更新的到数据库中
    func filterAndCache(cellModels:[ChatAICellModel]) -> [ChatAICellModel] {
        //过滤数据
        let models = cellModels.filter(cache.allowUpdateCache(cellModel:))
        //更新数据库
        for model in models{
            _ = cache.saveCache(cellModel: model, chatType: chatType)
        }
        return models
    }
    
    /// 初始化会话
    /// - Parameters:
    ///   - refresh: 是否强制刷新
    ///   - completion: 完成回调
    func initConversation(refresh: Bool = false,completion: @escaping (_ model: ChatInitModel?, _ error: RequestError?) -> Void){
        requestingInitModel = true
        if !needInitConvisation && !refresh {
            self.requestingInitModel = false
            completion(self.initModel, nil)
            return
        }
        
        requestInit(refresh: refresh) { [weak self] model, error in
            guard let self = self else {
                DispatchQueue.main.async {
                    self?.requestingInitModel = false
                    completion(model, error)
                }
                return
            }

            if let model = model {
                self.chatType = model.chatType
                self.initModel = model
            }
            DispatchQueue.main.async {
                self.requestingInitModel = false
                completion(model, error)
            }
        }
    }
    
    /// 会话
    /// - Parameters:
    ///   - content: 文本内容
    ///   - uuid: UUID ,用于标记对应消息的上下文
    ///   - autoResend: 是否为系统自动重发
    ///   - error: 错误信息
    ///   - completion: 完成回调
    func sendChat(
        msg: [String: Any],
        uuid:String,
        autoResend: Bool,
        isExample: Bool,
        type: String,
        completion: @escaping (_ model: [ChatMessageModel], _ sendingIndex: Int?, _ error: String?) -> Void
    ) {
        // msg 参数解析
        let content = msg["content"] as! String
        
        // 确认 sendCellModel，入库
        var sendCellModel: ChatAICellModel
        var uuid = uuid;
        if var cellModel = findSendCellModel(uuid: uuid) {
            cellModel.status = .succeed
            uuid = cellModel.message.uuid
            sendCellModel = cellModel
        } else {
            var message = ChatMessageModel.create(content: content, uuid: uuid, showDate: needShowDate)
            message.isExample = isExample
            message.localType = type
            sendCellModel = ChatAICellModel(message: message, msgType: .send)
            sendCellModel.markdownContent = MarkdownParserV2.parseAsPlainText(markdownPlainString: sendCellModel.content, isSent: true)
        }
        _ = cache.saveCache(cellModel: sendCellModel, chatType: chatType)
        
        // 发送消息是否展示时间要客户端自己处理，其他的以服务端下发的 is_show_datetime 为准
        let needShowDate = self.cellModelNeedShowDate(cellModel: sendCellModel)
        sendCellModel.updateShowDate(is_show_datetime: needShowDate)
        
        // 处理 firstSend
        let key = "ChatAI.isNotFirstSend.key.cache"
        let hasSend = IMYSwiftUserDefaults.bool(forKey: key)
        if !hasSend , isFirstSend {
            _ = showTopReplyTip()
            IMYSwiftUserDefaults.set(true, forKey: key)
        }
        isFirstSend = false
        
        // 删除旧的所有 loading 数据
        let _ = removeLoadingIfAny()
        
        // 发送中 1 添加 sendType
        sendCellModel.isSending = false ///< 交互确认不要发送中的转动菊花
        let sendType = ChatAICellType.send(model: sendCellModel)
        if let index =  dataArray.firstIndex(where: {$0.model.message.msgId == sendCellModel.message.msgId}) {
            dataArray[index] = sendType
        } else {
            dataArray.append(sendType)
        }

        // 发前无网
        if !NetStatus.enable {
            let sendType = ChatAICellType.send(model: sendCellModel)
            if let index =  dataArray.firstIndex(where: {$0.model.message.msgId == sendCellModel.message.msgId}) {
                dataArray[index] = sendType
                if (autoResend == false && self.checkSendCellModelAllowManualRetry(uuid: uuid)) {
                    _ = updateSendCellModel(uuid: uuid, status: .failed)
                }
            } else {
                dataArray.append(sendType)
                _ = updateSendCellModel(uuid: uuid, status: .failed)
            }
            
            // 标记 currentSendType
            self.currentSendType = sendType

            // 这里固定刷新一下
            refreshAction?()
            
            DispatchQueue.main.async {
                completion([], 0, "网络不见了")
            }

            return
        }

        // 插入一个新 loading（886 不是人工回复的才需要加入 loading）
        if !self.isManualReply {
            let message = ChatMessageModel.create(content: "", uuid: uuid, showDate: false)
            var receivingModel = ChatAICellModel(message: message, msgType: .recieved)
            receivingModel.isLoading = true
            let receivingType = ChatAICellType.receiving(model: receivingModel)
            dataArray.append(receivingType)
        }

        // 刷新以上 dataArray 的改动
        self.refreshAction?()
        
        // 标记 currentSendType
        self.currentSendType = sendType
        
        // 发送参数增加 public_info
        var newMsg = msg
        newMsg["public_info"] = self.bi_feeds_view_publicInfo(cellModel: sendCellModel, isExample: isExample, localType: type)

        requestChat(msg: newMsg, uuid: uuid) { [weak self] models, error in
            guard let self = self else {
                return
            }
            
            // 发送中 2
            sendCellModel.isSending = false
            var sendingIndex: Int? = nil
            let sendType_sending = ChatAICellType.send(model: sendCellModel)
            if let index =  dataArray.firstIndex(where: {$0.model.message.msgId == sendCellModel.message.msgId}) {
                dataArray[index] = sendType_sending
                sendingIndex = index
            } else {
                dataArray.append(sendType_sending)
            }
            
            // SSE 错误
            if let error = error {
                if (autoResend == false && self.checkSendCellModelAllowManualRetry(uuid: uuid)) {
                    // 手动重发才去更新状态
                    _ = self.updateSendCellModel(uuid: uuid, status: .failed)
                    self.refreshAction?()
                    
                    ChatAIErrorTraces.postHomeVMSend(uuid: uuid, position: "callback with error, mark .failed")
                } else {
                    ChatAIErrorTraces.postHomeVMSend(uuid: uuid, position: "callback with error")
                }

                // 有错误要重新去除 loading
                self.removeLoadingIfAny()
                self.refreshAction?()

                DispatchQueue.main.async {
                    completion([], sendingIndex, error.localizedDescription)
                }
                return
            }
            
            DispatchQueue.main.async {
                completion(models, sendingIndex, nil)
            }
        }

        return
    }
    
    // 取消requester的sendchat请求
    func cancelSendChatRequestIfAny(_ callerSource: Int = 0) {
        self.requester.cancelRequestIfAny(callerSource)
    }
    
    // 取消requester的sendchat请求不带回调
    func forceCancelRequestsWithoutCallbacks(_ callerSource: Int = 0) {
        self.requester.forceCancelRequestsWithoutCallbacks(callerSource)
    }
    
    
    /// 打分反馈
    /// - Parameters:
    ///   - score: [0..5]
    ///   - model: ChatCellMessage
    ///   - completion: 完成回调
    func feedback(appraise:ChatMessageModel.AppraiseType, model: ChatAICellModel, completion: @escaping (_ result: Bool) -> Void) {
        
        var cellModel = model
        cellModel.update(appraise: appraise)
        _ = cache.saveCache(cellModel: cellModel,chatType:chatType)//更新数据库
        
        let index = dataArray.firstIndex(where: { type in
            switch type {
                case .received(let element) where element == cellModel: return true
                default: return false
            }
        })
        if let index = index{//更新数据
            dataArray[index] = .received(model: cellModel)
        }
        
        requestFeedback(messageId: model.message.msgId, appraise: appraise.rawValue, originMsgId: model.message.msgId) { result, error in
            DispatchQueue.main.async {
                completion(result)
            }
        }
    }
    
    func update(cellModel: ChatAICellModel, qModels: [ChatAITutorialQuestionModel]) -> Void {
        var theCellModel = cellModel
        theCellModel.update(qModels: qModels)
        _ = cache.saveCache(cellModel: theCellModel, chatType: chatType) // 更新数据库
        
        // 更新数据
        let index = dataArray.firstIndex(where: { type in
            switch type {
                case .received(let element) where element == cellModel: return true
                default: return false
            }
        })
        if let index = index {
            dataArray[index] = .received(model: theCellModel)
        }
    }
    
    func report(model: ChatAICellModel, completion: @escaping (_ result: Bool) -> Void) {        
        requestReport(messageId: model.message.msgId, content: model.content, originMsgId: model.message.msgId) { result, error in
            DispatchQueue.main.async {
                completion(result)
            }
        }
    }
    
    func copyReport(cellModel: ChatAICellModel, completion: @escaping (_ result: Bool) -> Void) {
        requestCopyReport(sessionId: cellModel.message.sessionId) { result, error in
            DispatchQueue.main.async {
                completion(result)
            }
        }
    }
    
    func getFloorAt(type: ChatAICellType) -> Int?{
        var msgIds:[String] = []
        return dataArray.filter {
            return !$0.model.message.isGuide
            && type == $0
            && Calendar.current.isDateInToday($0.model.message.date)
        }.filter {
            let msgId = $0.model.message.msgId
            if msgId.isEmpty{
                return true
            }else if msgIds.contains(msgId){
                return false
            }else{
                msgIds.append(msgId)
                return true
            }
        }.firstIndex {
            if type.model.message.msgId.isEmpty{
                return $0.model == type.model
            }else{
                return $0.model.message.msgId == type.model.message.msgId
            }
        }
    }
    
    func getReceiveFloorAt(type: ChatAICellType) -> (index: Int, duration: TimeInterval)? {
        if biRecievedArray.first(where: { (type.model == $0.model) && (type.model.message.isRisk == $0.model.message.isRisk) }) != nil {
            return nil
        }
        //用一个数组存放接受中的,避免重复上报
        biRecievedArray.append(type)
        
        //引导语
        if type.model.message.isGuide {
            return (0, 0)
        }
        
        if type.model.message.contentType == .memory {
            return (0, 0)
        }
        
        //原理: 反向数组,找到第一个sendType, 并获取index, (send 和 receive 楼层相同)
        //然后再和sendType 的时间戳比较
        let sendType = dataArray.reversed().first {
            switch $0{
            case .send(_): return true
            default: return false
            }
        }
        guard let sendType = sendType, let index = getFloorAt(type: sendType) else{
            return nil
        }

        let duration = abs(type.timestamp - sendType.timestamp)
        
        return (index, duration)
    }

    func logSendExposureIfNeeded(type: ChatAICellType, row: Int) {
        let model = type.model
        
        // 882: 旧数据不重复上报
        if model.isFromDB == true {
            return
        }
        
        biPostEvent(type: .send, model: type.model)
    }

    func logReceiveExposureIfNeeded(type: ChatAICellType, row: Int) {
        let model = type.model
        
        // 882: 旧数据不重复上报
        if model.isFromDB == true {
            return
        }
        
        // 引导问题都不上报 bi_feeds_view
        if type.model.message.exampleType != .none {
            return
        }
        
        if model.content.isEmpty && model.message.images.count == 0 {
            // 发送问题后会先展示一个 loading 状态的回答，要把这个过滤掉
            return
        }
        
//        guard let (_, duration) = getReceiveFloorAt(type: .receiving(model: model)) else {
//            return
//        }
        
        biPostEvent(type: .recieved, duration: 0, model: model)
        
        // 人工客服不在线提示语消息类型bi打点
        if model.message.bi_sub_type == 1 {
            var dict: [String: Any] = [
                "event": "chat_ai_rgkfbzx",
                "action": 1,
                "info_key": scenario_key
            ]
            if model.message.sessionId.isEmpty == false {
                dict["aisession_id"] = model.message.sessionId
            }
            
            IMYChatBIEvent.postGAEvent("event", params: dict)
        }
        
    }
    
    // 计算是否显示刷新按钮
    func isShowRefresh(row: Int) -> Bool {
        let lastType = self.dataArray.last
        switch lastType {
        case .replyTip(model: _):
            // 最后一条是 tip
            if row == self.dataArray.count - 2 {
                return true
            }
        default:
            // 最后一条是其他
            if row == self.dataArray.count - 1 && (lastType?.model.message.disable_refresh == false) {
                return true
            }
        }
        return false
    }
    
    // MARK: 嵌入视图偏移量
    
    func refreshReceivedCellModel(uuid: String, offsetX: CGFloat) {
        let index = dataArray.firstIndex { cellType in
            switch cellType {
            case .received(let model):
                return model.message.uuid == uuid
            default:
                return false
            }
        }
        
        if let index = index {
            guard let type = dataArray[index] else {
                return
            }
            
            var cellModel = type.model
            cellModel.offsetX = offsetX
            
            dataArray[index] = .received(model: cellModel)
        }
    }
    
    func unfoldReceivedCellModel(uuid: String, isUnfold: Bool) {
        let index = dataArray.firstIndex { cellType in
            switch cellType {
                case .received(let model):
                    return model.message.uuid == uuid
                default:
                    return false
            }
        }
        
        if let index = index {
            guard let type = dataArray[index] else {
                return
            }
            var cellModel = type.model
            cellModel.message.update(isUnfold: isUnfold)
            
            dataArray[index] = .received(model: cellModel)
        }
    }
    
    func refreshReceivedCellModel(uuid: String, tagOffsetX: CGFloat) {
        let index = dataArray.firstIndex { cellType in
            switch cellType {
            case .received(let model):
                return model.message.uuid == uuid
            default:
                return false
            }
        }
        
        if let index = index {
            guard let type = dataArray[index] else {
                return
            }
            
            var cellModel = type.model
            cellModel.tagOffsetX = tagOffsetX
            
            dataArray[index] = .received(model: cellModel)
        }
    }
    
    //MARK: - let & var
    /**
     不同场景的KEY
     1、从消息列表进入柚姐姐：Message_List
     2、从我tab-订单进入柚姐姐：Me_Order
     3、从返现tab-订单进入柚姐姐：Cashback_Order
     4、从钱包进入柚姐姐：Wallet
     5、从会员支付转化页进入柚姐姐：VIP_Counter
     6、从帮助与反馈进入柚姐姐：Help_Feedback
     7、从问医生进入柚姐姐：Doctor
     */
     
    var scenario_key: String = ""
    
    /// 缓存数据
    private let cache = ChatAICache()
    private(set) var dataArray = ThreadSafeArray<ChatAICellType>()
    private(set) var sendFaildArray = ThreadSafeArray<ChatAICellType>()
    /// 选中的数组,消息id
    private(set) var selectedMsgIDs:[String] = []
    private var biRecievedArray: [ChatAICellType] = []
    /// 默认接口返回数据
    private(set) var chatType = ChatType.http
    private(set) var initModel:ChatInitModel?
    var requestingInitModel = true
    
    /// 是否正在请求历史消息
    var isHistoryMsgsDataRequsting: Bool = false
    
    ///是否需要初始化, 初始化过的initModel 有值
    var needInitConvisation: Bool {
        guard let _ = initModel, !dataArray.isEmpty else {
            return true
        }
        return false
    }
    
    private(set) var isEditing: Bool = false ///< 是否是编辑状态
    
    // MARK: - 创建第一条消息
    
    let exampleCacheKey = "ChatAI.init.key.cahce.example_chat" ///< 标记是否完成新人引导
    var finishExampleClourse: (()->Void)? ///< init 引导回调
    var guideMessageClourse: (() -> Void)? ///< config 引导回调
    
    
    /// 获取当前session Id
    func getCurrentSessionId() -> String {
        return self.initModel?.sessionId ?? ""
    }
    /// 更新当前session Id
    func updateCurrentSessionId(_ session_id: String) {
        self.initModel?.sessionId = session_id
    }
    
    /// 检查 init 接口的数据
    func haveExampleData() -> Bool {
        
        guard let model = initModel else { return false }
        
        if model.youJiejieIntro.isEmpty {
            return false
        }
        
        return true
    }
    
//    func createTutorialMessage(instro: String, qModels: [ChatAITutorialQuestionModel]) {
//        // 不重复创建
//        let theInstro = self.dataArray.first { t in
//            return t.model.message.exampleType == .intro
//        }
//        if let _ = theInstro {
//            return
//        }
//        
//        // instro 已经在请求成功的时候兜底了
//        var useInstro = instro
//        
//        // init message
//        var message = ChatMessageModel.create(content: useInstro, exampleType: .intro)
//        
//        // setup message.tutorial_questions
//        message.update(tutorial_questions: qModels)
//        
//        // create CellModel
//        var cellModel = ChatAICellModel(message:message, msgType: .recieved)
//        cellModel.markdownContent = MarkdownParserV2.parseMarkdown(markdownPlainString: cellModel.content)
//        let model: ChatAICellType = .received(model: cellModel)
//        self.dataArray.insert(contentsOf: [model], at: 0)
//        
//        self.guideMessageClourse?()
//    }
    
//    func createGuideMessage(instro: String, qList: [ChatAITutorialQuestionModel]) {
//        // 不重复创建
//        let theInstro = self.dataArray.first { t in
//            return t.model.message.exampleType == .intro
//        }
//        if let _ = theInstro {
//            return
//        }
//        
//        var useInstro = instro
//        if (useInstro.isEmpty) {
//            useInstro = "嗨，我是柚姐姐，你的贴心闺蜜。\n如果你有感情上的、生活上的、健康上的任何问题，都可以跟我说，我秒回~"
//        } else {
//            useInstro = useInstro.replacingOccurrences(of: "\\n", with: "\n")
//        }
//        
//        // init message
//        var message = ChatMessageModel.create(content: useInstro, exampleType: .intro)
//        
//        // setup message.tutorial_questions
//        var qModels: [ChatAITutorialQuestionModel] = []
//        for q in qList {
//            // 取 question 字段
//            var qModel = ChatAITutorialQuestionModel(json: ["question": q.question, "level": 1, "type": "chat_ai_jl"])
//            // 取 source_type 字段
//            qModel.source_type = q.source_type
//            qModels.append(qModel)
//        }
//        message.update(tutorial_questions: qModels)
//        
//        // create CellModel
//        var cellModel = ChatAICellModel(message:message, msgType: .recieved)
//        cellModel.markdownContent = MarkdownParserV2.parseMarkdown(markdownPlainString: cellModel.content)
//        let model: ChatAICellType = .received(model: cellModel)
//        self.dataArray.insert(contentsOf: [model], at: 0)
//        
//        self.guideMessageClourse?()
//    }
    
    func createNewTopicMessage(newTopics: [ChatAINewTopicModel]) {
        _ = self.clearExample()
        
        var message = ChatMessageModel.create(content: "聊聊新话题")
        message.update(newTopic: newTopics)
        
        let cellModel = ChatAICellModel(message:message, msgType: .recieved)
        let model: ChatAICellType = .received(model: cellModel)
        self.dataArray.append(model)
        
        // 这里直接保存新话题消息，不再是新用户了
        _ = cache.saveCache(cellModel: cellModel, chatType: chatType)
    }
    
    // MARK: -
    
    /// 问答曝光打点
    private func biPostEvent(type: ChatAICellModel.MessageType, duration: TimeInterval? = nil, model: ChatAICellModel) {
        var dict:JSONDictionary = ["action": 1, "position": 137]
        
        // 如果 bi_type 没值，则按当前是否人工判断
        var bi_type = model.message.bi_type
        if bi_type == 0 {
            if type == .send {
                if self.isManualReply {
                    bi_type = 91
                } else {
                    bi_type = 54
                }
            } else {
                if self.isManualReply {
                    bi_type = 92
                } else {
                    bi_type = 55
                }
            }
        }
        dict["info_type"] = bi_type

        dict["info_key"] = self.scenario_key
        
//        // duration
//        if type == .recieved, let duration = duration {
//            dict["duration"] = duration
//        }

        // is_risk
        if let isRisk = model.message.isRisk {
            dict["is_risk"] = isRisk
        }
        if let riskType = model.message.riskType {
            dict["risk_type"] = riskType
        }

        // aichat_id
        if !model.message.uuid.isEmpty {
            dict["aichat_id"] = model.message.uuid
        } else {
            dict["aichat_id"] = model.message.msgId
        }
        
        // 用户提问时区分示例问题还是主动提问，仅 info_type = 54 上报
        let public_info = self.bi_feeds_view_publicInfo(cellModel: model, isExample: model.message.isExample, localType: model.message.localType)
        if public_info.count > 0 {
            dict["public_info"] = public_info
        }
        
        if type == .recieved {
            // 882 新增 服务端上报的会话 id，仅仅 info_type = 55 上报。取服务端 chat_stream 接口下发的 session_id
            if model.message.sessionId.count > 0 {
                dict["aisession_id"] = model.message.sessionId
            }
            
            // 882 新增 服务端上报的回答 id，仅仅 info_type = 55 上报。服务端字段 origin_message_id
            if model.message.msgId.count > 0 {
                dict["aianswer_id"] = model.message.msgId
            }
        }
        
        // 886 增加 duration_period_yl
        let stage_type = IMYURIManager.share().runActionAndSyncResult(withPath: "chatAI/stageType", params: [:]) as? NSInteger
        if let stage_type = stage_type, stage_type > 0  {
            dict["duration_period_yl"] = stage_type
        }
        
        // public_type: 1、文字 2、图片
        if type == .send {
            if model.message.isImageMessageModel() {
                dict["public_type"] = 2
            } else if model.message.isVideoMessageModel() {
                dict["public_type"] = 3
            } else {
                dict["public_type"] = 1
            }
        }
        
        // release_time
        dict["release_time"] = model.message.timestamp
        
        IMYChatBIEvent.postGAEvent("bi_feeds_view", params: dict)
    }
    
    /// 获取 bi_feeds_view 埋点的 public_info 参数
    func bi_feeds_view_publicInfo(cellModel: ChatAICellModel, isExample: Bool, localType: String) -> String {
        var publicInfo = ""
        
        // public_info 有值，直接使用
        if !cellModel.message.pub_info.isEmpty {
            publicInfo = cellModel.message.pub_info
            return publicInfo
        }
        
        // public_info 没值，按旧逻辑生成
        if (cellModel.msgType == .send) {
            if (isExample) {
                if localType == "chat_ai_tj" {
                    publicInfo = "延展问题"
                } else if localType == "chat_ai_xht" {
                    publicInfo = "新话题"
                } else {
                    publicInfo = "示例问题"
                }
            } else {
                publicInfo = "用户主动提问"
            }
        }
        
        return publicInfo
    }
    
    func bi_event_chat_ai_sztc(cellModel: ChatAICellModel) {
        var dict: JSONDictionary = ["event": "chat_ai_sztc", "action": 1]
        dict["aisession_id"] = self.getCurrentSessionId()
        dict["aianswer_id"] = cellModel.message.msgId
        dict["aichat_id"] = cellModel.message.uuid
        dict["info_key"] = scenario_key
        let stage_type = IMYURIManager.share().runActionAndSyncResult(withPath: "chatAI/stageType", params: [:]) as? NSInteger
        if let stage_type = stage_type, stage_type > 0  {
            dict["duration_period_yl"] = stage_type
        }
        
        IMYChatBIEvent.postGAEvent("event", params: dict)
    }
    
    
    func bi_event_chat_ai_hdwz(cellModel: ChatAICellModel) {
        var dict: JSONDictionary = ["event": "chat_ai_hdwz", "action": 1]
        dict["aisession_id"] = self.getCurrentSessionId()
        dict["aianswer_id"] = cellModel.message.msgId
        dict["aichat_id"] = cellModel.message.uuid
        dict["info_key"] = scenario_key
        let stage_type = IMYURIManager.share().runActionAndSyncResult(withPath: "chatAI/stageType", params: [:]) as? NSInteger
        if let stage_type = stage_type, stage_type > 0  {
            dict["duration_period_yl"] = stage_type
        }
        
        IMYChatBIEvent.postGAEvent("event", params: dict)
    }

    var dispatchWorkArr = [DispatchWorkItem]()
    private func cancleAllDispatchWork() {
        for item in dispatchWorkArr {
            item.cancel()
        }
        dispatchWorkArr.removeAll()
    }

    func clearExample() -> Bool {
        var isClean = false
        
        // 新话题
        let newTopic: ThreadSafeArray = ThreadSafeArray<ChatAICellType>()
        
        /// 清空所有关联问题
        for (_, obj) in dataArray.enumerated() {
            // .intro 也做一样的处理
            if !obj.model.message.tutorial_questions.isEmpty || obj.model.message.exampleType == .intro {
                self.update(cellModel: obj.model, qModels: [])
                isClean = true
            }
            
            if obj.model.message.isNewTopic() {
                newTopic.append(obj)
            }
        }
        
        // 删除新话题
        for (_, obj) in newTopic.enumerated() {
            self.dataArray.removeAll { $0.model.message.msgId == obj.model.message.msgId }
            cache.setDeleteMark(cellModel: obj.model)
        }
        
        return isClean
    }
    
    func hadFinishExample() -> Bool {
        // 旧版本判断数据库是否有数据，改成判断 dataArray 是否有数据
        let hasCache = self.dataArray.count > 0
        
        // 是否点击过删除所有对话
        let hasDeleteKey = "ChatAI.hasDeleteConversation" + User.userId
        let hasDelete = IMYSwiftUserDefaults.bool(forKey: hasDeleteKey)
        
        return hasCache || hasDelete
    }
    
    func finishExample(delay:Double = 0.0) {
        if hadFinishExample() {
            return
        }
        
        IMYSwiftUserDefaults.set(true, forKey: exampleCacheKey)
        
        DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
            self.finishExampleClourse?()
        }
    }
    
    /// 是否显示时间
    var needShowDate: Bool{
        guard let lastMessageDate = dataArray.last?.model.message.date else {
            return true
        }
        
        var lastDate = lastMessageDate
        if (lastRecieveDate.compare(lastDate) == .orderedDescending) {
            lastDate = lastRecieveDate
        }
        
        if let sec = (Calendar.current.dateComponents([.second], from: Date(), to: lastDate).second), abs(sec) > 5 * 60 {
            return true
        }
        return false
    }
    
    /// 当前 cellModel 是否需要展示时间：cellModel 的时间距离最后一个消息的时间超过指定值的时候要展示
    func cellModelNeedShowDate(cellModel: ChatAICellModel) -> Bool {
        guard let lastCellModel = dataArray.last?.model, let lastDate = dataArray.last?.model.message.date else {
            return false
        }
        
        if lastCellModel.message.msgId == cellModel.message.msgId {
            return lastCellModel.message.is_show_datetime
        }
        
        let currentDate = cellModel.message.date
        
        if let sec = (Calendar.current.dateComponents([.second], from: currentDate, to: lastDate).second), abs(sec) > 5 * 60 {
            return true
        }
        
        return false
    }

    ///是否允许点赞
    func needShowAppraise(cellModel: ChatAICellModel) -> Bool{
        if let initModel = initModel {
            if !initModel.showAppraise {
                return false
            }
            return cellModel.appraisable
        }
        if !cellModel.message.showAppraise {
            return false
        }
        return cellModel.appraisable
    }
    
    /// 用户综合年龄,  8.92.0引入
    var userCAge: Int = 0
    
    /// 是否有缓存数据
    var hasCache: Bool {
        return cache.hasCache
    }
    
    ///允许重试次数
    var maxRetryTimes: Int = 0
    
    ///最后一次接受消息时间
    var lastRecieveDate: Date = Date.init(timeIntervalSince1970: 0)
    
    ///用于标记当前发送的, 允许多发的情况使用
    var currentSendType: ChatAICellType?
    
    ///用于标记进程内第一次发送消息
    var isFirstSend: Bool = true

    /// 数据变化后用于刷新界面
    var refreshAction: (()->Void)?
    
    
    var connectingStateForceInterruptCallback: (() -> Void)?
    
    // MARK: - 柚姐姐 + 客服
    
    // MARK: 是否人工客服
    
    /// 是否人工客服
    var isManualReply = false
    
    /// 是否人工客服，更新
    func updateIsManualReply(_ value: Bool) {
        guard value != self.isManualReply else { return }
        
        self.isManualReply = value
        
        NotificationCenter.default.post(name: NSNotification.Name(rawValue: "com.chatai.notify.isManualReplyChange"), object: nil)
    }
    
    // MARK: 获取人工回复消息
    
    var timerKey = "ChatAIViewModelV2_Timer"
    
    func startPollingManualReply(_ cb: ((_ msgModels: [ChatMessageModel]?) -> Void)?) {
        print("[rgkf][轮询人工回复][startPollingManualReply]")
        
        let timerHelper_long = IMYTimerHelper(interval: 10.0)
        let timerHelper_short = IMYTimerHelper(interval: 5.0)
        timerHelper_short?.removeTimer(forKey: timerKey)
        timerHelper_long?.removeTimer(forKey: timerKey)
        
        if self.isManualReply {
            timerHelper_short?.addTimer(for: { [weak self] in
                guard let self = self else { return }
                self.doPollingManualReply(cb)
            }, key: timerKey)
        } else {
            timerHelper_short?.addTimer(for: { [weak self] in
                guard let self = self else { return }
                self.doPollingManualReply(cb)
            }, key: timerKey)
        }
    }
    
    func doPollingManualReply(_ cb: ((_ msgModels: [ChatMessageModel]?) -> Void)?) {
#if DEBUG
        // 临时全局禁用轮询
        let forbidPollingKey = "#+ChatAI-ManualReply-ForbidPolling"
        let forbidPolling = IMYKV.default().bool(forKey: forbidPollingKey)
        if forbidPolling {
            return
        }
#endif
        print("[rgkf][轮询人工回复][doPollingManualReply]")
        self.fetchManualReply(cb)
    }
    
    func fetchManualReply(_ cb: ((_ msgModels: [ChatMessageModel]?) -> Void)?) {
        // get lastId
        let dataArrayForLastId = self.dataArray.filter { cellType in
            cellType.model.status != .failed &&
            cellType.model.message.exampleType == .none &&
            cellType.model.message.msgId.count > 0 &&
            cellType.model.msgType != .send &&
            !cellType.model.isLoading &&
            !cellType.model.isLoadingPlaceholder &&
            !cellType.model.message.isNewTopic() &&
            cellType.model.message.contentType != .historyMsgSpitline &&
            cellType.model.message.isMsgIdFromServer() == true
        }
        let lastId = dataArrayForLastId.last?.model.message.msgId ?? ""
        
        IMYChatAIHTTPService.sharedInstance().fetchManualReply(lastId, scenarioKey: self.scenario_key) { [weak self] responseDict in
            guard let _ = self else { return }
            
            let msgModels = JSON(responseDict)["messages"].arrayValue.map {
                ChatMessageModel(json: $0, showAppriase: false, showDate: false)
            }
            
            let is_manual_service = JSON(responseDict)["is_manual_service"].boolValue
            if is_manual_service != self?.isManualReply {
                print("[rgkf][切换人工客服点：人工客服回复 \(is_manual_service)]")
                self?.updateIsManualReply(is_manual_service)
            }
            
            if let cb = cb {
                cb(msgModels)
            }
        } onError: { error in
            if let cb = cb {
                cb(nil)
            }
        }
    }
    
    // MARK: 接收人工回复
    
    func handleMsgListResponse(msgModels: [ChatMessageModel]) {
        guard msgModels.count > 0 else { return }
        
        let msgModels = msgModels
        
        // 滤重
        let validMsgModels = msgModels.filter({ msgModel in
            let idx = self.dataArray.firstIndex { cellType in
                return cellType.model.message.msgId == msgModel.msgId
            }
            
            if let _ = idx {
                return false
            }
            
            return true
        })
        
        guard validMsgModels.count > 0 else { return }
        
        // 执行完这块，就会清除示例问题，开始存数据库
        _ = self.clearExample()
        
        // ChatMessageModel filter and map 2 ChatAICellModel
        let cellModels = validMsgModels.map {
            var cellModel = ChatAICellModel(message: $0, msgType: .recieved)
            cellModel.isFinish = $0.isFinish
            cellModel.markdownContent = MarkdownParserV2.parseMarkdown(markdownPlainString: cellModel.content)
            return cellModel
        }
        
        // valid
        let validCellModels = filterAndCache(cellModels: cellModels)
        
        // 通知回调给 VC
        NotificationCenter.default.post(name: NSNotification.Name(rawValue: "com.chatai.notify.viewModelRecvManualReply"), object: nil, userInfo: [
            "validCellModels": validCellModels
        ])
    }
    
    // MARK: 退出人工客服
    
    /// 退出人工客服
    func exitManualService(_ cb: ((_ isSuccess: Bool) -> Void)?) -> Void {
        let currentSessionID = getCurrentSessionId()
        IMYChatAIHTTPService.sharedInstance().exitManualService(withSessionId: currentSessionID) { _ in
            print("[rgkf][切换人工客服点：主动退出 \(false)]")
            self.updateIsManualReply(false)
            if let cb = cb {
                cb(true)
            }
        } onError: { _ in
            if let cb = cb {
                cb(false)
            }
        }

    }
}

//MARK: - receiver
extension ChatAIViewModelV2 {
        
    /// 接受消息
    /// - Parameters:
    ///   - cellModel: ChatAICellModel
    ///   - needCache: 是否需要更新缓存, 默认为`false`
    /// - Returns: 返回 下标,已存在返回下标, 不存在返回nil
    func receive(cellModel: ChatAICellModel, needCache: Bool = false) -> Int?{
        
        if needCache {//缓存数据
            _ = cache.saveCache(cellModel: cellModel, chatType: chatType)
        }
        
        let index = dataArray.firstIndex { element in
            switch element {
            case .receiving(let value) where value == cellModel: return true
            case .received(let value) where value == cellModel: return true
            case .memory(let value) where value == cellModel: return true
            default: return false
            }
        }
        if let index = index {
            // 找到了相同的 model, 说明这次是更新原有一行的 UI
            replace(at: index, cellModel: cellModel)
            return index
        } else {
            // 插入新数据前先把 loading 干掉
            _ = removeLoadingIfAny()
            // 没找到 index, 需要插入新数据
            append(cellModel: cellModel)
            return nil
        }
    }

    private func replace(at index: Int, cellModel: ChatAICellModel) {
        switch cellModel.message.contentType {
        case .system:fallthrough
        case .newbie:fallthrough
        case .biz: fallthrough
        case .manualReplyTip: fallthrough
        case .manualReply: fallthrough
        case .manualServiceExitTip: fallthrough
        case .historyMsgSpitline: fallthrough
        case .video: fallthrough
        case .text:
            if cellModel.isFinish || cellModel.isFailed {
                dataArray[index] = .received(model: cellModel)
            } else {
                switch dataArray[index] {
                case .receiving(let data):
                    if cellModel == data {
                        dataArray[index] = .receiving(model: cellModel)
                    }
                default: break /// 当前不是 receiving ,或者内容小于当前的 不处理
                }
            }
        case .memory:
            dataArray[index] = .memory(model: cellModel)
        case .example:
            dataArray[index] = .example(model: cellModel)
        case .htmlText:
            dataArray[index] = .received(model: cellModel)
        }
    }

    private func append(cellModel: ChatAICellModel) {
        switch cellModel.message.contentType {
        case .system: fallthrough
        case .newbie:fallthrough
        case .biz: fallthrough
        case .manualReplyTip: fallthrough
        case .manualReply: fallthrough
        case .manualServiceExitTip: fallthrough
        case .historyMsgSpitline: fallthrough
        case .video: fallthrough
        case .text:
            if cellModel.isFinish || cellModel.isFailed {
                dataArray.append(.received(model: cellModel))
            } else{
                dataArray.append(.receiving(model: cellModel))
            }
        case .memory:
            dataArray.append(.memory(model: cellModel))
        case .example:
            dataArray.append(.example(model: cellModel))
        case .htmlText:
            dataArray.append(.received(model: cellModel))
        }
        
        
    }

    /// 去除 loading 状态的占位数据
    @discardableResult
    func removeLoadingIfAny() -> Bool {
        let didRemove = dataArray.removeAllIfSatisfy { !($0.model.isLoadingPlaceholder) }
        return didRemove
    }
}

//MARK: - request
extension ChatAIViewModelV2 {
    
    //MARK: - request
    //是否强制刷新（清空）对话
    fileprivate func requestInit(refresh: Bool = false, completion: @escaping(_ model: ChatInitModel?, _ error: RequestError?) -> Void){
        var params : [String : Any] = ["refresh": refresh]
        
        // 服务端确认：这个字段可以直接传 true
        params["new_intro"] = true
        
        // 8.90.0 接口请求新增场景key值参数scenario_key
        params["scenario_key"] = self.scenario_key
        
        params["pregnancy_stage"] = 0
        if IMYPublicAppHelper.share().userMode == .pregnancy {
            let dayDiff = IMYPublicAppHelper.share().pregnancyStartDayDiff
            if dayDiff <= 90 {
                params["pregnancy_stage"] = 1
            } else if dayDiff <= 195 {
                params["pregnancy_stage"] = 2
            } else {
                params["pregnancy_stage"] = 3
            }
        } else if IMYPublicAppHelper.share().userMode == .lama {
            if let birthDayInfo = IMYURIManager.share().runActionAndSyncResult(withPath: "chatAI/lastBirthdayBaby/birthdayInfo", params: [:]) as? [String: NSInteger] {
                if let baby_year = birthDayInfo["baby_year"] {
                    params["baby_year"] = baby_year
                }
                if let baby_month = birthDayInfo["baby_moth"] {
                    params["baby_month"] = baby_month
                }
                if let baby_day = birthDayInfo["baby_day"] {
                    params["baby_day"] = baby_day
                }
            }
        }
        
        // stage_type
        let stage_type = IMYURIManager.share().runActionAndSyncResult(withPath: "chatAI/stageType", params: [:]) as? NSInteger
        if let stage_type = stage_type, stage_type > 0  {
            params["stage_type"] = stage_type
        }
        
        // age
        params["age"] = IMYPublicAppHelper.share().userAge
        
        let type = Router.initChat(dict: params)
        RequestManager.post(type: type) { [weak self] json in
            guard let self = self else { return }
            let model = ChatInitModel(json: json)
            completion(model, nil)
        } failure: { (error) in
            completion(nil, error)
        }
    }
    
    fileprivate func requestChat(msg: [String: Any], uuid: String, completion: @escaping(_ models: [ChatMessageModel], _ error: RequestError?) -> Void ){
        
        // msg 参数解析
        let content = msg["content"] as! String
        let answer_id = msg["answer_id"] as? String
        let next_id = msg["next_id"] as? String
        let selected = msg["selected"] as? Int
        let refresh_id = msg["refresh_id"] as? String
        let public_info = msg["public_info"] as? String
        
        
        let exampleInfo = self.getLocalExampleMsg()
        let introDict = exampleInfo.0
        let exampleString = exampleInfo.1
        
        var contentType = ChatMessageModel.ContentType.text.rawValue
        if let msg_content_type = msg["content_type"] as? String,
            msg_content_type.isEmpty == false,
            let content_type = Int(msg_content_type) {
            contentType = content_type
        }
        
        guard var params = self.initModel?.getChatParams(content: content, contentType:contentType,intro: exampleString) else {
            completion([], .unkonwn)
            return ;
        }
        params["uuid"] = uuid
        if !introDict.isEmpty {
            params["intro"] = introDict
        }
        
        if let answer_id = answer_id, !answer_id.isEmpty {
            params["answer_id"] = answer_id
        }
        if let next_id = next_id, !next_id.isEmpty {
            params["next_id"] = next_id
        }
        if let selected = selected, selected > 0 {
            params["selected"] = selected
        }
        
        if let refresh_id = refresh_id, !refresh_id.isEmpty {
            params["refresh_id"] = refresh_id
        }
        
        if let images = msg["images"] as? [Dictionary<String, Any>], images.count > 0 {
            params["images"] = images
        }
        
        if let public_info = public_info, !public_info.isEmpty {
            params["public_info"] = public_info
        }
        
        // 8.90.0 接口请求新增场景key值参数scenario_key
        params["scenario_key"] = self.scenario_key
        
        // 8.92.0引入, 传入应用启动时获取的用户综合年龄
        params["age"] = self.userCAge
        
        // service_tips_config 传给 SSERequester 内部自行修改 Content
        if let initModel = initModel {
            self.requester.userInfo = ["service_tips_config": initModel.service_tips_config]
        }
        
        self.requester.send(params, callback: {[weak self] info, err in
            guard let self = self else {
                return
            }
            
            // 网络错误、服务端错误
            // 没有消息内容, 直接报错
            // 有消息内容，不报错（网络问题导致收到半段消息停止时, 则当作执行完抛给 UI 层面刷新）
            if let err = err, info == nil {
                completion([], RequestError.failedRequest(error: err))
                return
            }
            
            // 客户端解析数据失败
            guard let model = info?["msg"] as? ChatMessageModel else {
                completion([], RequestError.invalidResponse(code: -3333, message: "ChatMessageModel消息解析未知类型错误"))
                return
            }
            
            let models = [model]
            
            // 更新session id
            if let sessionId = models.first?.sessionId {
                self.updateCurrentSessionId(sessionId)
            }
            
            // 更新时间戳
            self.lastRecieveDate = Date()
            IMYSwiftUserDefaults.set(true, forKey: "ChatAI.example.upload.key")
            completion(models, nil)
        })
        
        self.requester.connectingStateForceInterruptCallback = self.connectingStateForceInterruptCallback
    }
    
    fileprivate func getLocalExampleMsg() -> (dict:JSONDictionary,string:String) {
        let hasSendExample = IMYSwiftUserDefaults.bool(forKey: "ChatAI.example.upload.key")
        if hasSendExample {
            return ([String : Any](),"")
        }
        let msgs = ChatAICache().loadIntroMessages()
        
        var welcomeQ = ""
        var welcomeA = [String]()

        var exampleQ = ""
        var exampleA = [String]()
        
        for (_,item) in msgs.enumerated() {
            if item.message.exampleType == .intro {
                continue
            }
            if item.message.exampleType == .welcomeQ{
                welcomeQ = item.message.content
            } else if item.message.exampleType == .welcomeA {
                welcomeA.append(item.message.content)
            } else if item.message.exampleType == .exampleQ {
                exampleQ = item.message.content
            } else {
                exampleA.append(item.message.content)
            }
        }

        var intro = [String : Any]()
        var stringArr = [String]()
        if !welcomeQ.isEmpty {
            var dict = [String : Any]()
            dict["question"] = welcomeQ
            dict["answers"] = welcomeA
            intro["welcome"] = dict
            
            let answerString = welcomeA.joined(separator: "❯")
            stringArr.append(welcomeQ)
            stringArr.append(answerString)
        }
        
        if !exampleQ.isEmpty {
            var dict = [String : Any]()
            dict["question"] = exampleQ
            dict["answers"] = exampleA
            intro["candidate_chat"] = dict
            let answerString = exampleA.joined(separator: "❯")
            stringArr.append(exampleQ)
            stringArr.append(answerString)
        }
        
        return (intro,stringArr.joined(separator: ","))
    }

    
    fileprivate func requestFeedback(messageId: String, appraise: Int, originMsgId: String, completion: @escaping(_ result: Bool, _ error: RequestError?) -> Void){

        var dict:JSONDictionary = ["message_id": messageId, "appraise": appraise]
        if originMsgId.count > 0{
            dict["origin_message_id"] = originMsgId
        }
        let type = Router.appraise(dict: dict)
        RequestManager.post(type: type) { json in
            let success = json["success"].boolValue
            completion(success, nil)
        } failure: { error in
            completion(false, error)
        }
    }

    fileprivate func requestReport(messageId: String, content: String, originMsgId: String, completion: @escaping(_ result: Bool, _ error: RequestError?) -> Void){
        var dict:JSONDictionary = ["message_id": messageId, "content": content]
        if originMsgId.count > 0{
            dict["origin_message_id"] = originMsgId
        }
        let type = Router.report(dict: dict)
        RequestManager.post(type: type) { json in
            let success = json["success"].boolValue
            completion(success, nil)
        } failure: { error in
            completion(false, error)
        }


    }
    
    fileprivate func requestCopyReport(sessionId: String, completion: @escaping(_ result: Bool, _ error: RequestError?) -> Void) {
        var dict:JSONDictionary = ["session_id": sessionId]
        let type = Router.copyReport(dict: dict)
        RequestManager.post(type: type) { json in
            let success = json["success"].boolValue
            completion(success, nil)
        } failure: { error in
            completion(false, error)
        }
    }
}



// MARK: - 评价弹窗弹出过时间处理逻辑
extension ChatAIViewModelV2 {
    
    func updateRatingDialogRecentlyShownTimeStamp() {
        let date = NSDate()
        __updateRatingDialogRecentlyShownTime(date)
    }

    func checkIfRatingDialogRecentlyShownTimeOverOneDay() -> Bool {
        guard let recentlyRatingDialogShownTime = __getRatingDialogRecentlyShownTime() else {
            return true
        }
        return recentlyRatingDialogShownTime.isToday() == false
    }
    
    fileprivate func __updateRatingDialogRecentlyShownTime(_ time: NSDate) {
        let key = "ChatAI_RatingDialog_RecentlyShownTime_" + IMYPublicAppHelper.share().userid
        IMYUserDefaults.standard().setObject(time, forKey: key)
        IMYUserDefaults.standard().synchronize()
    }
    
    fileprivate func __getRatingDialogRecentlyShownTime() -> NSDate? {
        let key = "ChatAI_RatingDialog_RecentlyShownTime_" + IMYPublicAppHelper.share().userid
        guard let ratingDialogRecentlyShownTime = IMYUserDefaults.standard().object(forKey: key) as? NSDate else {
            return nil
        }
        return ratingDialogRecentlyShownTime
    }
    
}

// MARK: - 发送图片逻辑
extension ChatAIViewModelV2 {
    

    
    func reSendImage(_ cellModel: ChatAICellModel, callback: @escaping ErrorCallback) {
        guard let imageUrl = cellModel.message.images.first?.url else {
            return
        }
        
        let uuid = cellModel.message.uuid
        self.updateSendCellModel(uuid: cellModel.message.uuid, status: .processing)
        
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.removeLoadingIfAny()
            self.refreshAction?()
        }
        
        // 注册回调到管理器
        ChatAIImageSendManager.sharedInstance.registerStatusUpdateCallback(uuid: uuid) { [weak self] uuid, status in
            self?.updateSendCellModel(uuid: uuid, status: status)
            self?.refreshAction?()
        }
        
        ChatAIImageSendManager.sharedInstance.registerImageUpdateCallback(uuid: uuid) { [weak self] uuid, _ , images, _ in
            self?.updateSendCellModel(uuid: uuid, images: images)
        }
        
        // 委托给管理器处理
        ChatAIImageSendManager.sharedInstance.reSendImage(cellModel, scenarioKey: scenario_key, initModel: initModel)
        
        // 调用原回调
        callback(nil)
    }
    
    func sendImage(fileName: String, msg: [String: Any], callback: @escaping ErrorCallback) {
        let fileName = fileName
        let localFileCachePath = IMYChatAIImageUploadManager.sharedInstance().uploadFilePath(ofFile: fileName)
        let uuidPart = fileName.split(separator: ".").first ?? ""
        let uuid = String(uuidPart)
        
        // 插入图片类型发送消息
        let chatImage = ChatAIImage.create(url: localFileCachePath)
        var messageObj = ChatMessageModel.create(content: "", uuid: uuid)
        messageObj.images = [chatImage]
        if let md5 = msg["md5"] as? String, md5.isEmpty == false {
            messageObj.update(md5: md5)
        }
        
        var cellModel = ChatAICellModel(message: messageObj, msgType: .send, status: .processing)
        
        // 发送消息是否展示时间要客户端自己处理，其他的以服务端下发的 is_show_datetime 为准
        let needShowDate = self.cellModelNeedShowDate(cellModel: cellModel)
        cellModel.updateShowDate(is_show_datetime: needShowDate)
        
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.removeLoadingIfAny()
            let sendType = ChatAICellType.send(model: cellModel)
            self.dataArray.append(sendType)
            self.refreshAction?()
        }
        
        // 注册回调到管理器
        ChatAIImageSendManager.sharedInstance.registerStatusUpdateCallback(uuid: uuid) { [weak self] uuid, status in
            self?.updateSendCellModel(uuid: uuid, status: status)
            self?.refreshAction?()
        }
        
        ChatAIImageSendManager.sharedInstance.registerImageUpdateCallback(uuid: uuid) { [weak self] uuid, _ , images, _ in
            self?.updateSendCellModel(uuid: uuid, images: images)
        }
        
        // 委托给管理器处理
        ChatAIImageSendManager.sharedInstance.sendImage(fileName: fileName, msg: msg, scenarioKey: scenario_key, initModel: initModel)
        
        // 调用原回调
        callback(nil)
    }
    
}


// MARK: - 发送视频逻辑
extension ChatAIViewModelV2 {
    
    /*
     ==============================
     上传部分
     ==============================
     1.create message model
     2.upload logic
     a 判断video的url是不是asset的identifier  是说明视频还未导出, 进行视频导出,  不是判断是否是在线地址, 是则说明文件已经上传, 只需要调用接口上报地址, 否则进行文件上传
     b video的thumburl使用uuid匹配
     c 截取asset原图截图 存入 thumbnail
     d update video内容的信息
     ==============================
     
     ==============================
     上传中状态调整
     ==============================
     
     ==============================
     点击调起播放器
     ==============================
     
     */
    
    func reSendVideo(_ cellModel: ChatAICellModel, callback: @escaping ErrorCallback) {
        guard let videoUrl = cellModel.message.videos.first?.url else {
            return
        }
        
        let uuid = cellModel.message.uuid
        self.updateSendCellModel(uuid: cellModel.message.uuid, status: .processing)
        
        cache.saveSendFaild(cellModel: cellModel)
        
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.removeLoadingIfAny()
            self.refreshAction?()
        }
        
        // 注册回调到管理器
        ChatAIImageSendManager.sharedInstance.registerStatusUpdateCallback(uuid: uuid) { [weak self] uuid, status in
            self?.updateSendCellModel(uuid: uuid, status: status)
            self?.refreshAction?()
        }
        
        ChatAIImageSendManager.sharedInstance.registerImageUpdateCallback(uuid: uuid) { [weak self] uuid, _ , images, _ in
            self?.updateSendCellModel(uuid: uuid, images: images)
        }
        
        // 委托给管理器处理
        ChatAIImageSendManager.sharedInstance.reSendVideo(cellModel, scenarioKey: scenario_key, initModel: initModel)
        
        // 调用原回调
        callback(nil)
    }
    
    func sendVideo(msg: [String: Any], uuid: String, assetIdentifier: String, videos: [ChatAIVideo], callback: @escaping ErrorCallback) {
        
        let fileName = uuid + ".mp4"
        let videoFilePath = IMYChatAIImageUploadManager.sharedInstance().uploadFilePath(ofFile: fileName)
        
        // 插入视频类型发送消息
        var messageObj = ChatMessageModel.create(content: "", uuid: uuid, type: .video)
        messageObj.videos = videos
        if let md5 = msg["md5"] as? String, md5.isEmpty == false {
            messageObj.update(md5: md5)
        }
        messageObj.photoAssetIdentifer = assetIdentifier
        
        var cellModel = ChatAICellModel(message: messageObj, msgType: .send, status: .processing)
        
        // 发送消息是否展示时间要客户端自己处理，其他的以服务端下发的 is_show_datetime 为准
        let needShowDate = self.cellModelNeedShowDate(cellModel: cellModel)
        cellModel.updateShowDate(is_show_datetime: needShowDate)
        
        cache.saveSendFaild(cellModel: cellModel)
        
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.removeLoadingIfAny()
            let sendType = ChatAICellType.send(model: cellModel)
            self.dataArray.append(sendType)
            self.refreshAction?()
        }
        
        // 注册回调到管理器
        ChatAIImageSendManager.sharedInstance.registerStatusUpdateCallback(uuid: uuid) { [weak self] uuid, status in
            self?.updateSendCellModel(uuid: uuid, status: status)
            self?.refreshAction?()
        }
        
        ChatAIImageSendManager.sharedInstance.registerImageUpdateCallback(uuid: uuid) { [weak self] uuid, _, images, videos in
            self?.updateSendCellModel(uuid: uuid, videos: videos)
        }
        
        // 委托给管理器处理
        ChatAIImageSendManager.sharedInstance.sendVideo(
            fileName,
            uuid: uuid,
            msg: msg,
            videos: videos,
            scenarioKey: scenario_key,
            initModel: initModel
        )
        
        // 调用原回调
        callback(nil)
    }
    
}

extension ChatAIViewModelV2 {
    
    /// AI助手页面回答失败打点
    /// - Parameter type: 失败原因，如：网络异常，返回超时等等
    func aiFailedBiPostEvent(type: String) {
        let info_key = self.scenario_key
        let dict:JSONDictionary = [
            "action": 2,
            "event": "chat_ai_hdsb",
            "public_type": type,
            "info_key": info_key
        ]
        IMYChatBIEvent.postGAEvent("event", params: dict)
    }
    
    
    /// 上报发送失败统计埋点
    /// public_type:  失败原因，如：网络异常，返回超时等等
    /// public_info:  上报发送类型 1、文字 2、图片
    func postSendFailedEvent(_ public_type: String, _ public_info: Int = 1) {
        let info_key = self.scenario_key
        var gaParams:JSONDictionary = [
            "action": 2,
            "event": "chat_ai_fssb",
            "public_type": public_type,
            "public_info": public_info,
            "info_key": info_key
        ]
        let stage_type = IMYURIManager.share().runActionAndSyncResult(withPath: "chatAI/stageType", params: [:]) as? NSInteger
        if let stage_type = stage_type, stage_type > 0  {
            gaParams["duration_period_yl"] = stage_type
        }
        IMYChatBIEvent.postGAEvent("event", params: gaParams)
    }
    
}
