//
//  ChatAIHomeViewControllerV2.swift
//  ChatAI
//
//  Created by lxb on 2023/3/14.
//

import UIKit
import IMYSwift
import SnapKit
import RxSwift
import RxCocoa
import SwiftyJSON

@objc(IMYChatAIHomeViewControllerV2)
public class ChatAIHomeViewControllerV2: IMYPublicBaseViewController, IMYAssetPickerControllerDelegate {
    
    // MARK: - Vars
    
    var exportSession: IMYAVExportSession?
    
    //-----------------------------------------
    // 8.90.0 新增, 用于在用户点击示例问题触发绑定手机号流程, 绑定完成后, 自动触发点击过的示例问题发送
    fileprivate var needToAutoSubmitTutorialQuestions: Bool = false
    fileprivate var autoSubmitTutorialQuestionsParams: (ChatA<PERSON><PERSON><PERSON>ode<PERSON>,Int)?
    //-----------------------------------------
    
    
    // MARK: - Override
    
    public override func _initMyself() {
        super._initMyself()
        
        imy_addLanguageChangedActionBlock({ [weak self] anyObj in
            guard let self = self else {
                return
            }
            self.tabBarItem.title = "AI聊天".imy.local()
            self.tableView.reloadData()
            self.navigationView.refreshTitle()
            self.aiInputView.inputTextView.refreshView()
        }, forKey: "setTitle")
        
        tabBarItem.imy_setFinishedSelectedImageName("icon_tab_chat_selected", withFinishedUnselectedImageName: "icon_tab_chat_unselected")
    }
    
    public override func viewDidLoad() {
        super.viewDidLoad()
        
        // 883 不支持夜间模式
        self.disableThemeActionToAutoNightMask()
        
        prepareUI()
        addNotifications()
        addGestures()
        prepareData()
    }
    
    public override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        view.layoutIfNeeded()
        
        aiInputView.inputTextView.loadLocalCache()
    }
    
    public override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        
        viewModel.disposeMarkdownCache()
        // 标记页面已显示
        viewModel.isPageVisible = true
        
        if !NetStatus.enable {
            HUD.show(text: "网络不见了，请检查网络".imy.local())
        }
        
        // 8.90.0 如果用户点击示例问题跳转到绑定手机号模块, 绑定完成后返回自动发送点击的示例问题
        if self.needToAutoSubmitTutorialQuestions == false {
            self.autoSubmitTutorialQuestionsParams = nil;
        } else {
            if let autoSubmitTutorialQuestionsParams = self.autoSubmitTutorialQuestionsParams {
                self.submitTutorialQuestions(
                    cellModel: autoSubmitTutorialQuestionsParams.0,
                    index: autoSubmitTutorialQuestionsParams.1
                )
            }
            self.autoSubmitTutorialQuestionsParams = nil;
            self.needToAutoSubmitTutorialQuestions = false
        }
        
    }

    public override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        
        // 标记页面已退出
        viewModel.isPageVisible = false
        
        IMYChatAIManager.sharedInstance().refreshMsg2ListAndRedotIfNeed()
    }
    
    // MARK: - 初始化
    
    private func prepareUI() {
        
        self.view.accessibilityIdentifier = "ChatAIHomeViewControllerV2.view"
        
        // 固定背景色
        self.view.imy_setBackgroundColor(forKey: "FAF0F3")
        
        // nav
        isNavigationBarHidden = true
        
        // from home
        self.fromHome = self.navigationController?.viewControllers.count ?? 0 < 2;
        if self.fromHome == true {
            self.safeAreaBottom = 0
        } else {
            self.safeAreaBottom = Device.screenTabBarSafeBottomMargin
        }
        self.aiInputView.fromHome = self.fromHome
        
        // containerView
        let offset = Device.screenStatusBarHeight
        view.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.edges.equalTo(view).inset(UIEdgeInsets(top: -offset, left: 0, bottom: offset + safeAreaBottom, right: 0))
        }
        
        // safeBottomView
        view.addSubview(self.safeBottomView)
        self.safeBottomView.snp.makeConstraints { make in
            make.leading.trailing.equalTo(view)
            make.top.equalTo(containerView.snp.bottom)
            make.height.equalTo(Device.screenTabBarSafeBottomMargin)
        }
        if self.fromHome == true {
            self.safeBottomView.imy_setBackgroundColor(forKey: kCK_Clear_A)
        } else {
            self.safeBottomView.imy_setBackgroundColor(forKey: "FAF0F3")
        }
        
        // gradientBg
        containerView.addSubview(gradientBg)
        
        // navigationView
        containerView.addSubview(navigationView)
        navigationView.snp.makeConstraints { make in
            make.leading.trailing.equalTo(containerView)
            make.top.equalTo(containerView)
            make.height.equalTo(Device.screenStatusBarAndNavigationBarHeight)
        }
        
        // tableView
        let inputViewHeight = aiInputView.viewHeight()
        containerView.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.top.equalTo(navigationView.snp.bottom)
            make.leading.trailing.equalTo(containerView)
            make.bottom.equalTo(containerView).offset(-inputViewHeight)
        }
        
        // aiInputView
        containerView.addSubview(aiInputView)
        aiInputView.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalTo(containerView)
            make.height.equalTo(inputViewHeight)
        }
        
        view.bringSubviewToFront(navigationView)
        
        // backButton
        self.view.addSubview(self.backButton)
        backButton.snp.makeConstraints { make in
            make.centerY.equalTo(self.navigationView.snp.bottom).offset(-22)
            make.leading.equalTo(self.view.snp.leading).offset(10)
            make.size.equalTo(CGSize(width: 24, height: 24))
        }
        self.view.bringSubviewToFront(self.backButton)
        self.backButton.isHidden = self.fromHome
        
        view.bringSubviewToFront(aiInputView)
        
        // 设置输入框和 bottomView
        self.aiInputView.isHidden = true
        self.safeBottomView.backgroundColor = self.captionView.backgroundColor
    }
    
    private func prepareData() {
        
        self.readUserCAgeFromPrefs()
        
        self.checkIfShowRightItems()
        
        self.setupDatas(isRefresh: false)
        
        viewModel.refreshAction = { [weak self] in
            guard let self = self else { return }
            self.handleLoopRefreshAction()
        }
        
        receiver.updatingAction = { [weak self] cellModel in
            guard let self = self else { return }
            self.handleLoopUpdatingAction(cellModel: cellModel)
        }
        
        receiver.finishAction = { [weak self] (cellModel, result) in
            guard let self = self else { return }
            self.handleLoopFinishAction(cellModel: cellModel, result: result)
        }
        
        receiver.breakVerbatimAction = { [weak self] (cellModel) in
            guard let self = self else { return }
            self.handleLoopBreakVerbatimAction(cellModel: cellModel)
        }
        
        receiver.firstCharShowAction = {  [weak self] (cellModel) in
            guard let self = self else { return }
            self.handleLoopFirstCharShownAction(cellModel: cellModel)
        }
        
        
        IMYChatAIFoldManager.sharedInstance().heightThreshold = UIScreen.main.bounds.height * 1.2;
        IMYChatAIFoldManager.sharedInstance().foldHeight = UIScreen.main.bounds.height * 0.6
        
    }
    
    @objc public func setScenarioKey(_ scenario_key: String) {
        self.viewModel.scenario_key = scenario_key
        ChatAIImageSendManager.sharedInstance.scenario_key = scenario_key
        self.aiInputView.updateInputTextViewInfoKey(scenario_key)
    }

    func readUserCAgeFromPrefs() {
        let key = "Meetyou_ChatAI_CAge_" + (IMYPublicAppHelper.share().userid ?? "")
        let cage = IMYUserDefaults.standard().integer(forKey: key)
        self.viewModel.userCAge = cage
    }
    
    func setupDatas(isRefresh: Bool) {
        self.captionView.state = .loading
        
        // 【请求配置中心】
        self.viewModel.reqConfigCenter { [weak self] in
            guard let self = self else { return }
            
            self.assistantTitle = self.viewModel.config_assistant_title
            
            // 请求 init 前：hasInit
            let key = "ChatAI.init.key.cahce.\(User.userId)"
            let hasInit = IMYSwiftUserDefaults.bool(forKey: key)
            if !hasInit {
                IMYSwiftUserDefaults.set(true, forKey: key)
            }
            
            // 请求 init 前：refresh
            var refresh = isRefresh
            
            // 【请求 Init 接口】
            self.viewModel.reqInit(isRefresh: refresh) { [weak self] model, error in
                guard let self = self else { return }
                
                if !NetStatus.enable {
                    HUD.show(text: "网络不见了，请检查网络".imy.local())
                }
                
                // 处理错误
                if let _ = error {
                    DispatchQueue.main.async { [weak self] in
                        guard let self = self else { return }
                        self.captionView.state = .retry
                        
                        // 设置输入框和 bottomView
                        self.aiInputView.isHidden = true
                        self.safeBottomView.backgroundColor = self.captionView.backgroundColor
                    }
                    return
                }
                
                // maxInput
                if let maxInput = self.viewModel.initModel?.maxInput {
                    self.aiInputView.inputTextView.maxContentLength = maxInput
                } else {
                    let maxInput = App.isMeetYou ? 100 : 400
                    self.aiInputView.inputTextView.maxContentLength = maxInput
                }
                
                // 【获取历史消息】
                //self.viewModel.loadDatas { [weak self] cellTypes, isFinish, error in
                self.viewModel.loadFirstPageData { [weak self] cellTypes, isFinish, hasHistoryData, error in
                    guard let self = self else { return }
                    
                    if error != nil && self.viewModel.dataArray.count == 0 {
                        DispatchQueue.main.async { [weak self] in
                            guard let self = self else { return }
                            
                            self.scrollToBottom(reloadData: true, animated: false)
                            self.captionView.state = .retry
                        }
                        return
                    }
                    
                    // 1、AiReply
                    // self.viewModel.showTipIfNeed()
                    
                    // 2、按需加载引导数据
                    // self.viewModel.showExampleOrGuideIfNeed()
                    
                    // 加载新话题 
                    self.viewModel.showNewTopicIfNeed()
                    
                    // 加载发送错误的错误消息
                    self.viewModel.loadFaildDatasFromDB { cellTypes in
                        // 3、refresh UI
                        DispatchQueue.main.async { [weak self] in
                            guard let self = self else { return }
                            
                            // 排除一下发送失败的数据
                            self.viewModel.dataArrayExceptSendFail()
                            
                            // 恢复输入框和 bottomView
                            self.aiInputView.isHidden = false
                            self.safeBottomView.backgroundColor = UIColor.imy_color(forKey: "FAF0F3")
                            
                            self.navigationView.assistantTitle = self.assistantTitle
                            self.navigationView.refreshTitle()
                            self.bigHeaderView.update(url: self.viewModel.config_assistant_avatar)
                            self.navigationView.refreshAvatar(url: self.viewModel.config_assistant_avatar)
                            
                            
                            self.checkIfShowRightItems()
                            self.canPreload = !isFinish
                            
                            if hasHistoryData {
                                self.canPreload = false
                                self.tableView.tableHeaderView = self.historyMsgLoadMoreTipHeader
                                self.historyMsgLoadMoreTipHeader.showTip()
                                self.navigationView.refreshWithAlpha(alpha: 1.0)
                            } else {
                                self.tableView.tableHeaderView = self.bigHeaderView
                                self.navigationView.refreshWithAlpha(alpha: 0.0)
                            }
                            self.tableView.reloadData()
                            self.scrollToBottom(reloadData: true, animated: false)
                            
                            DispatchQueue.main.async {[weak self] in
                                guard let self = self else { return }
                                self.captionView.state = .hidden
                            }
                            
                            
                            self.addManualReplyNoti()
                            
                            if let initModel = self.viewModel.initModel {
                                print("[rgkf][切换人工客服点：初始化 \(initModel.is_manual_service)]")
                                self.viewModel.updateIsManualReply(initModel.is_manual_service)
                            }
                            
                            // 开启轮询
                            self.startPolling()
                        }
                    }
                }
            }
        }
    }
    
    private func addNotifications() {
        // 修改小时制通知
        _ = NotificationCenter.default.rx
            .notification(UIApplication.significantTimeChangeNotification)
            .takeUntil(rx.deallocated)
            .observeOn(MainScheduler.instance)
            .subscribe(onNext: { [weak self] (x) in
                guard let self = self else { return }
                self.tableView.reloadData()
            })
        
        // uid 变化
        _ = User.userIdChanged.asObservable()
            .takeUntil(rx.deallocated)
            .observeOn(MainScheduler.instance)
            .skip(1)
            .distinctUntilChanged()
            .takeUntil(rx.deallocated)
            .subscribe { [weak self] userId in
                guard let self = self else { return }
                self.setupDatas(isRefresh: false)
            }
        
        // 监听滚动
        _ = tableView.rx.observeWeakly(CGPoint.self, "contentOffset")
            .asObservable()
            .takeUntil(rx.deallocated)
            .distinctUntilChanged()
            .subscribe {[weak self] point in
                guard let self = self else { return }
                
                let offsetY = self.tableView.contentOffset.y;
                self.navigationView.bottomLine.isHidden = true
                
                // 渐变控制
                var alpha = 0.0
                if (offsetY > 44) {
                    alpha = offsetY / (44.0 + 28.0)
                }
                var isHeaderBigHeadView = isTableViewHeaderBigPicutreHeader()
                if isHeaderBigHeadView == true {
                    self.navigationView.backgroundColor = UIColor.imy_color(forKey: "FAF0F3").withAlphaComponent(alpha)
                    self.navigationView.refreshWithAlpha(alpha: alpha)
                } else {
                    self.navigationView.refreshWithAlpha(alpha: 1.0)
                }
                
                // 背景图位置
                if (offsetY > 0) {
                    self.gradientBg.imy_top = -offsetY
                } else {
                    self.gradientBg.imy_top = 0
                }
            }
        
        // 监听换肤
        self.imy_addThemeChangedBlock { weakObj in
            guard let weakObj = weakObj as? ChatAIHomeViewControllerV2 else { return }
            weakObj.tableView.reloadData()
        }
        
        // 监听清空聊天记录
        _ = NotificationCenter.default.rx
            .notification(Notification.Name("com.chatai.notify.deleteConversations"))
            .takeUntil(rx.deallocated)
            .observeOn(MainScheduler.instance)
            .subscribe(onNext: { [weak self] (x) in
                guard let self = self else {
                    return
                }
                
                self.deleteConversationsAction()
            })
        
        //
        _ = NotificationCenter.default.rx
            .notification(Notification.Name(kIMYAccountBindPhoneNumberSuccessNotification))
            .takeUntil(rx.deallocated)
            .observeOn(MainScheduler.instance)
            .subscribe(onNext: { [weak self] (x) in
                guard let self = self else {
                    return
                }
                
                self.needToAutoSubmitTutorialQuestions = true
                if self.isViewActived == true {
                    if let autoSubmitTutorialQuestionsParams = self.autoSubmitTutorialQuestionsParams {
                        self.submitTutorialQuestions(
                            cellModel: autoSubmitTutorialQuestionsParams.0,
                            index: autoSubmitTutorialQuestionsParams.1
                        )
                        self.autoSubmitTutorialQuestionsParams = nil
                    }
                    self.needToAutoSubmitTutorialQuestions = false
                }
            })
        
        
        _ = NotificationCenter.default.rx
            .notification(Notification.Name("ChatAIImageSendManagerTaskStatusChanged"))
            .takeUntil(rx.deallocated)
            .observeOn(MainScheduler.instance)
            .subscribe(onNext: { [weak self] (x) in
                guard let self = self,
                      let dict = x.userInfo as [AnyHashable : Any]?,
                      let uuid = dict["uuid"] as? String,
                      let status = dict["status"] as? Int else {
                    return
                }
                
                self.viewModel.updateSendCellModel(
                    uuid: uuid,
                    status: (ChatAICellModel.Status(rawValue: status) ?? .succeed)
                )
            })
        
        _ = NotificationCenter.default.rx
            .notification(Notification.Name("ChatAIImageSendManagerVideosContentChanged"))
            .takeUntil(rx.deallocated)
            .observeOn(MainScheduler.instance)
            .subscribe(onNext: { [weak self] (x) in
                guard let self = self,
                      let dict = x.userInfo as [AnyHashable : Any]?,
                      let uuid = dict["uuid"] as? String,
                      let videosJsonStr = dict["videosJsonStr"] as? String else {
                    return
                }
                
                let videosJson = JSON(parseJSON: videosJsonStr)
                let videos = videosJson.arrayValue.map({ json in
                    ChatAIVideo(json: JSON(rawValue: json) ?? [])
                })
                
                self.viewModel.updateSendCellModelVideos(uuid: uuid, videos: videos)
                
            })

        _ = NotificationCenter.default.rx
            .notification(Notification.Name("ChatAIImageSendManagerUploadSuccessNotification"))
            .takeUntil(rx.deallocated)
            .observeOn(MainScheduler.instance)
            .subscribe(onNext: { [weak self] (x) in
                guard let self = self,
                      let dict = x.userInfo as [AnyHashable : Any]?,
                      let models = dict["models"] as? [ChatMessageModel] else {
                    return
                }
                self.handleUploadMediaSuccessWithServerCallback(models: models)
            })
        
#if DEBUG
        _ = NotificationCenter.default.rx
            .notification(Notification.Name("com.chatai.notify.debug.fastSend"))
            .takeUntil(rx.deallocated)
            .observeOn(MainScheduler.instance)
            .subscribe(onNext: { [weak self] (x) in
                guard let self = self , let dict = x.userInfo as? JSONDictionary else {
                    return
                }
                let json = JSON(dict)
                let text = json["text"].stringValue
                self.aiInputView.submitClourse?(text, false)
            })
#endif
        
    }
    
    // MARK: - 加载更多数据
    func loadMordDatas(_ forceLoad: Bool = false) {
        if !canPreload && forceLoad == false {
            return
        }
        
        canPreload = false
        self.viewModel.loadMoreDatas { [weak self] cellTypes, isFinish, error in
            guard let self = self else { return }
            
            self.viewModel.isHistoryMsgsDataRequsting = false
            if error == nil {
                self.historyMsgLoadMoreTipHeader.hideHeader()
                // 当所有数据加载完成后, tableview头部换成柚姐姐大头像
                if isFinish {
                    self.tableView.tableHeaderView = self.bigHeaderView
                } else {
                    self.tableView.tableHeaderView = self.hiddenTableHeaderView
                }
            } else {
                self.historyMsgLoadMoreTipHeader.showTip()
            }
            
            // 排除一下发送失败的数据
            self.viewModel.dataArrayExceptSendFail()
            
            // reload data
            self.tableView.reloadData()
            self.tableView.superview?.layoutIfNeeded()
            
            self.canPreload = !isFinish
            
            // 定位到原来的位置
            let hasNewDataInsert = (cellTypes.count - 1) >= 0
            if hasNewDataInsert {
                let frame = self.tableView.rectForRow(at: IndexPath(row: cellTypes.count - 1, section: 0));
                let newInsertCellsLastOneBottomY = frame.size.height + frame.origin.y;
                let offsetY = self.tableView.contentOffset.y + newInsertCellsLastOneBottomY
                self.tableView.setContentOffset(CGPointMake(self.tableView.contentOffset.x, offsetY - (self.tableView.tableHeaderView?.imy_height ?? 0.0)),
                                                animated: false)
            }
        }
    }
    
    // 首次加载历史消息
    func firstTimeLoadMoreHisotryPageDatas(_ forceLoad: Bool = false) {
        self.viewModel.firstTimeLoadMoreHisotryPageDatas { [weak self] cellTypes, isFinish, error in
            guard let self = self else { return }
            
            self.viewModel.isHistoryMsgsDataRequsting = false
            if error == nil {
                self.historyMsgLoadMoreTipHeader.hideHeader()
                // 当所有数据加载完成后, tableview头部换成柚姐姐大头像
                if isFinish {
                    self.tableView.tableHeaderView = self.bigHeaderView
                } else {
                    self.tableView.tableHeaderView = self.hiddenTableHeaderView
                }
            } else {
                self.historyMsgLoadMoreTipHeader.showTip()
            }
            
            // 排除一下发送失败的数据
            self.viewModel.dataArrayExceptSendFail()
            
            // reload data
            self.tableView.reloadData()
            self.tableView.superview?.layoutIfNeeded()
            
            self.canPreload = !isFinish
            
            // 分割线前一条消息底部露出100pt
            var splitLineMessageIdx = -1
            for itemIdx in 0...self.viewModel.dataArray.count {
                let cellType = self.viewModel.dataArray[itemIdx]
                if cellType?.model.message.contentType == .historyMsgSpitline {
                    splitLineMessageIdx = itemIdx
                }
            }
            
            if splitLineMessageIdx > 0 {
                splitLineMessageIdx -= 1
                let frame = self.tableView.rectForRow(at: IndexPath(row: splitLineMessageIdx, section: 0))
                let newInsertCellsLastOneBottomY = frame.size.height + frame.origin.y;
                let offsetY = newInsertCellsLastOneBottomY
                self.tableView.setContentOffset(CGPointMake(self.tableView.contentOffset.x, offsetY - 120.0),
                                                                animated: false)
            }
        }
    }
        
    func fetchHistoryMsgDatas() {
        self.viewModel.isHistoryMsgsDataRequsting = true
        self.historyMsgLoadMoreTipHeader.showLoading()
        firstTimeLoadMoreHisotryPageDatas(true)
    }
    
    
    // MARK: - ReceivingLoop 回调
    
    private func handleLoopRefreshAction() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            print("[rgkf][HOME_VC handleLoopRefreshAction]")
            
            self.scrollToBottom(reloadData: true, animated: false)
        }
    }
    
    private func handleLoopUpdatingAction(cellModel: ChatAICellModel) {
        print("[rgkf][HOME_VC handleLoopUpdatingAction]")
        
        // 导航栏变更
        self.navigationView.refreshTitle(state: .normal)
        
        // 解除输入限制
        if self.aiInputView.inputTextView.sendStatus == .loading {
            self.refreshInputViewSendStatus()
        }
        
        // 更新数据
        let index = viewModel.receive(cellModel: cellModel, needCache: false)
        
        // 刷新界面
        if let index = index {
            let rows = tableView.numberOfRows(inSection: 0)
            if index >= rows {
                // cell 不存在，reload
                self.scrollToBottom(reloadData: true, animated: false)
            } else if cellModel.hitRisk {
                // cell 命中风控
                self.scrollToBottom(reloadData: true, animated: false)
            }  else {
                // cell 可见，update cell
                var receivingCell: ChatAIReceivingCell? = nil
                for cell in self.tableView.visibleCells {
                    if let cell = cell as? ChatAIReceivingCell {
                        receivingCell = cell
                    }
                }
                
                if let receivingCell = receivingCell {
                    receivingCell.update(cellModel: cellModel)
                    self.scrollToBottom(reloadData: false, animated: false)
                }
            }
        }
    }
    
    private func handleLoopFinishAction(cellModel: ChatAICellModel, result: Bool) {
        print("[rgkf][HOME_VC handleLoopFinishAction]")
        
        // 分别处理成功失败
        if (result) {
            print("[rgkf][HOME_VC bi_event_chat_ai_hdwz]")
            self.viewModel.bi_event_chat_ai_hdwz(cellModel: cellModel)
            
            // 【Receive 成功】
            navigationView.refreshTitle(state: .normal)
            
//            let needCache = viewModel.hadFinishExample()
//            _ = viewModel.receive(cellModel: cellModel, needCache: needCache)
            _ = viewModel.receive(cellModel: cellModel, needCache: true)
            
            if (viewModel.isEditing) {
                return
            }
            
//            let rows = tableView.numberOfRows(inSection: 0)
//            tableView.performBatchUpdates { [weak self] in
//                guard let self = self else { return }
//                let indexPath = IndexPath(row: self.viewModel.dataArray.count - 1, section: 0)
//                if indexPath.row >= rows {
//                    self.tableView.insertRows(at: [indexPath], with: .none)
//                } else {
//                    self.tableView.reloadRows(at: [indexPath], with: .none)
//                }
//            } completion: { [weak self] _ in
//                guard let self = self else { return }
//                self.scrollToBottom(reloadData: false, animated: true)
//            }
            
            self.scrollToBottom(reloadData: true, animated: false)
        } else {
            let uuid = cellModel.message.uuid
            if viewModel.hasReceivedCellModel(uuid: uuid) {
                // 【Receive 失败】已有接收过一条或多条对应 uuid 的消息. 则认为消息发送成功
                navigationView.refreshTitle(state: .normal)
                
                // [打印等待超时]需要刷新数据
                _ = viewModel.receive(cellModel: cellModel, needCache: true)
                self.scrollToBottom(reloadData: true, animated: false)
                
                print("loop 以失败结束")
                
                #if DEBUG
                DispatchQueue.main.async {
                    UIWindow.imy_showTextHUD(0, text: "loop 以失败结束")
                }
                #endif
                
                ChatAIErrorTraces.postHomeVCLoopFinish(uuid: cellModel.message.uuid, position: "loop 以失败结束")
            } else if viewModel.checkSendCellModelAllowRetry(uuid: uuid) {
                // 【Receive 失败】自动重发
                _ = viewModel.refreshSendCellModelRetryCount(uuid: uuid)
                if let sendCellModel = viewModel.findSendCellModel(uuid: uuid) {
                    // 1.触发重发,
                    sendChat(msg: ["content": sendCellModel.content], uuid: sendCellModel.message.uuid, autoResend: true)
                    navigationView.refreshTitle()
                    // 埋点
                    self.viewModel.aiFailedBiPostEvent(type: "返回超时")
                    
                    ChatAIErrorTraces.postHomeVCLoopFinish(uuid: cellModel.message.uuid, position: "auto resend")
                } else {
                    ChatAIErrorTraces.postHomeVCLoopFinish(uuid: cellModel.message.uuid, position: "auto resend, but not found")
                }
            } else if viewModel.checkSendCellModelAllowRetry(uuid: uuid) == false && viewModel.maxRetryTimes > 0 {
                // 【Receive 失败】超过最大次数：生成兜底文案
                if viewModel.updateSendCellModel(uuid: uuid, status: .succeed) != nil {
                    let showDate = self.viewModel.needShowDate
                    var message = ChatMessageModel.create(content: "实在抱歉，我刚才没有听清，可以重新跟我说说吗？")
                    message.showDate = showDate
                    message.update(uuid: uuid)
                    let receiveModel = ChatAICellModel(message:message, msgType: .recieved)
                    
                    navigationView.refreshTitle(state: .normal)
                    _ = viewModel.receive(cellModel: receiveModel, needCache: true)
                    if (viewModel.isEditing) {
                        return
                    }
                    let indexPath = IndexPath(row: viewModel.dataArray.count - 1, section: 0)
                    tableView.insertRows(at: [indexPath], with: .fade)
                    scrollToBottom(reloadData: false, animated: true)
                    
                    ChatAIErrorTraces.postHomeVCLoopFinish(uuid: cellModel.message.uuid, position: "placeholder")
                } else {
                    ChatAIErrorTraces.postHomeVCLoopFinish(uuid: cellModel.message.uuid, position: "placeholder, but not found")
                }
                refreshInputViewSendStatus()
            } else {
                // 【Receive 失败】标记为失败
                if let index = viewModel.updateSendCellModel(uuid: cellModel.message.uuid, status: .failed) {
                    let indexPath = IndexPath(row: index, section: 0)
                    tableView.reloadRows(at: [indexPath], with: .none)
                    scrollToBottom(reloadData: false, animated: true)
                    navigationView.refreshTitle(state: .normal)
                    
                    ChatAIErrorTraces.postHomeVCLoopFinish(uuid: cellModel.message.uuid, position: "fail")
                } else {
                    ChatAIErrorTraces.postHomeVCLoopFinish(uuid: cellModel.message.uuid, position: "fail, but not found")
                }
                refreshInputViewSendStatus()
            }
        }
        
        // 展示更多
        self.checkIfShowRightItems()
    }
    
    private func handleLoopFirstCharShownAction(cellModel: ChatAICellModel) {
        self.viewModel.bi_event_chat_ai_sztc(cellModel: cellModel)
    }
    
    private func handleLoopBreakVerbatimAction(cellModel: ChatAICellModel) {
        print("[rgkf][HOME_VC bi_event_chat_ai_hdwz 打断]")
        self.viewModel.bi_event_chat_ai_hdwz(cellModel: cellModel)
        
        _ = viewModel.receive(cellModel: cellModel, needCache: true)
        
        self.clearExample()
        
        self.tableView.reloadData()
    }
    
    // MARK: - MsgActView
    
    func handleMsgActViewRefresh(indexPath: IndexPath, cellModel: ChatAICellModel) {
        self.deleteIndexPath(indexPath: indexPath)
        
        let sendIndex = self.viewModel.dataArray.lastIndex { cellType in
            switch cellType {
            case .send(model: _):
                return true
            default:
                return false
            }
        }
        
        if let sendIndex = sendIndex {
            if sendIndex < self.viewModel.dataArray.count {
                if let sendType = self.viewModel.dataArray[sendIndex] {
                    sendChat(msg: ["content": sendType.model.content, "refresh_id": cellModel.message.msgId], uuid: sendType.model.message.uuid, autoResend: false)
                }
            }
        }
        
        // chat_ai_fk
        // 886 增加 duration_period_yl
        var params: [String : Any] = [
            "event": "chat_ai_fk",
            "action": 2,
            "public_type": "刷新",
            "aianswer_id": cellModel.message.msgId,
            "info_key": viewModel.scenario_key
        ]
        let stage_type = IMYURIManager.share().runActionAndSyncResult(withPath: "chatAI/stageType", params: [:]) as? NSInteger
        if let stage_type = stage_type, stage_type > 0  {
            params["duration_period_yl"] = stage_type
        }
        
        IMYChatBIEvent.postGAEvent("event", params: params)
    }
    
    func handleMsgActViewLike(indexPath: IndexPath, cellModel: ChatAICellModel) {
        let cellModel0 = cellModel
        
        var appraiseType = cellModel0.message.appraise
        if appraiseType == .egg {
            // 取消踩
            appraiseType = .none
        } else {
            // 踩
            appraiseType = .egg
        }
        
        self.viewModel.feedback(appraise: appraiseType, model: cellModel0) { result in
            
        }
        
        // 调用协议
        if appraiseType == .egg {
            // 延迟 0.25 秒用于展示点赞效果
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.25, execute: DispatchWorkItem(block: { [weak self] in
                guard let self = self else { return }
                
                IMYURIManager.share().runAction(withPath: "showChatAIFeedBackDialog", params: [
                    "session_id": cellModel0.message.sessionId,
                    "message_id": cellModel0.message.msgId,
                    "yl_content": cellModel0.markdownContent.string,
                    "aianswer_id": cellModel0.message.msgId,
                    "info_key": viewModel.scenario_key
                ], info: [:])
            }))
        } else {
            HUD.show(text: "已取消")
        }
        
        self.tableView.reloadData()
        
        // chat_ai_fk
        // 886 增加 duration_period_yl
        var public_type = "踩"
        if appraiseType != .egg {
            public_type = "取消踩"
        }
        
        var params: [String : Any] = [
            "event": "chat_ai_fk",
            "action": 2,
            "public_type": public_type,
            "aianswer_id": cellModel.message.msgId,
            "info_key": viewModel.scenario_key
        ]
        let stage_type = IMYURIManager.share().runActionAndSyncResult(withPath: "chatAI/stageType", params: [:]) as? NSInteger
        if let stage_type = stage_type, stage_type > 0  {
            params["duration_period_yl"] = stage_type
        }
        
        
        IMYChatBIEvent.postGAEvent("event", params: params)
    }
    
    func handleMsgActViewCopy(indexPath: IndexPath, cellModel: ChatAICellModel) {
        var text = cellModel.markdownContent.string
        // html图文混排类型复制文本
        if cellModel.message.contentType == .htmlText {
            let textMaxWidth = ChatAIReceivedCell.textMaxWidth(with: false, cellModel: cellModel)
            let maxImageEdgeValue = textMaxWidth * 0.6
            let maxImageSize = CGSize(width: maxImageEdgeValue, height: maxImageEdgeValue)

            let markdownContent = IMYChatAIHtmlView.createAttributedString(
                withHtml: cellModel.message.content,
                maxImageSize: maxImageSize
            )
            
            text = markdownContent.string
        }
        
        if text.count > 0 {
            let pasteboard = UIPasteboard.general
            pasteboard.string = text
            HUD.show(text: "复制成功")
        }
        
        // chat_ai_fk
        // 886 增加 duration_period_yl
        var params: [String : Any] = [
            "event": "chat_ai_fk",
            "action": 2,
            "public_type": "复制",
            "aianswer_id": cellModel.message.msgId,
            "info_key": viewModel.scenario_key
        ]
        
        let stage_type = IMYURIManager.share().runActionAndSyncResult(withPath: "chatAI/stageType", params: [:]) as? NSInteger
        if let stage_type = stage_type, stage_type > 0  {
            params["duration_period_yl"] = stage_type
        }
        
        IMYChatBIEvent.postGAEvent("event", params: params)
    }
    
    // MARK: - ReceivedCell Action
    func handleClickNewTopics(cellModel: ChatAICellModel, index: Int) {
        if self.viewModel.isEditing {
            UIWindow.imy_showTextHUD(0, text: "所选内容不支持复制")
            return
        }
        
        if !self.checkLogin() || !self.checkRealName() {
            return
        }
        
        // 清空问题列表
        self.clearExample()
        
        // 刷新 cell
        self.tableView.reloadData()
        
        // 发送问题
        let topic = cellModel.message.new_topic[index]
        var msg: [String: Any] = [:]
        msg["content"] = topic.content
        self.handleSubmitChat(msg: msg, isExample: true, type: "chat_ai_xht")
    }
    
    func handleClickTutorialQuestions(cellModel: ChatAICellModel, index: Int) {
        if self.viewModel.isEditing {
            UIWindow.imy_showTextHUD(0, text: "所选内容不支持复制")
            return
        }
        
        if !self.checkLogin() {
            return
        }
        
        if !self.checkRealName() {
            self.autoSubmitTutorialQuestionsParams = (cellModel, index)
            return
        }
        
        self.submitTutorialQuestions(cellModel: cellModel, index: index)
    }
    
    private func submitTutorialQuestions(cellModel: ChatAICellModel, index: Int) {
        // 清空问题列表
        self.clearExample()
        
        // 刷新 cell
        self.tableView.reloadData()
        
        // 发送问题
        let qModel = cellModel.message.tutorial_questions[index]
        var msg: [String: Any] = [:]
        msg["content"] = qModel.question
        if !qModel.answer_id.isEmpty {
            msg["answer_id"] = qModel.answer_id
        }
        if !qModel.next_id.isEmpty {
            msg["next_id"] = qModel.next_id
        }
        if qModel.index > 0 {
            msg["selected"] = qModel.index
        }
        self.handleSubmitChat(msg: msg, isExample: true, type: qModel.type)
    }
    
    // MARK: - 发送消息
    
    private func handleSubmitChat(msg:[String: Any], isExample: Bool = false, type: String = "") {
        aiInputView.inputTextView.sendStatus = .loading
        
        aiInputView.inputTextView.resignFirstResponderIfNeeded()

        allowScrollToBottom = true
        
        let uuid = UUID().uuidString
        sendChat(msg: msg, uuid: uuid, autoResend: false, isExample: isExample, type: type)
    }
    
    
    private func sendVideoMsg(msg: [String: Any], uuid: String, assetIdentifier: String, videos: [ChatAIVideo]) {
        self.allowScrollToBottom = true
        self.viewModel.sendVideo(msg: msg, uuid: uuid, assetIdentifier: assetIdentifier, videos: videos) {[weak self] error in
            
        }
        
    }
    
    private func sendImage(msg: [String: Any], uuid: String, fileName: String) {
        
        self.allowScrollToBottom = true
        self.viewModel.sendImage(fileName: fileName, msg: msg) {[weak self] error in
            
        }
    }
    
    private func reSendImage(_ model: ChatAICellModel) {
        
        self.viewModel.removeLoadingIfAny()
        
        // 重新发送也要去掉延展问题
        self.clearExample()
        
        var resendModel = model
        
        // 发送消息是否展示时间要客户端自己处理，其他的以服务端下发的 is_show_datetime 为准
        let needShowDate = self.viewModel.cellModelNeedShowDate(cellModel: resendModel)
        resendModel.updateShowDate(is_show_datetime: needShowDate)
        
        // 删除错误的数据源，滚动到底部
        self.viewModel.removeFaildDatas(cellModel: model)

        let sendType = ChatAICellType.send(model: resendModel)
        self.viewModel.dataArray.append(sendType)
        self.viewModel.refreshAction?()
        
        self.viewModel.reSendImage(resendModel) {[weak self] error in

        }
    }
    
    private func reSendVideo(_ model: ChatAICellModel) {
        self.viewModel.removeLoadingIfAny()
        
        // 重新发送也要去掉延展问题
        self.clearExample()
        
        var resendModel = model
        
        // 发送消息是否展示时间要客户端自己处理，其他的以服务端下发的 is_show_datetime 为准
        let needShowDate = self.viewModel.cellModelNeedShowDate(cellModel: resendModel)
        resendModel.updateShowDate(is_show_datetime: needShowDate)
        
        // 删除错误的数据源，滚动到底部
        self.viewModel.removeFaildDatas(cellModel: model)
        
        let sendType = ChatAICellType.send(model: resendModel)
        self.viewModel.dataArray.append(sendType)
        self.viewModel.refreshAction?()
        
        self.viewModel.reSendVideo(model) {[weak self] error in
            
        }
        
    }
    
    private func sendChat(msg: [String: Any], uuid: String, autoResend: Bool, isExample: Bool = false, type: String = "") {
        // 先打断流式请求，后续如果碰到网络问题不会发起请求
        // callerSource 含义
        // 1 用户发送请求时触发打断前一个请求
        // 2 用户清空列表操作触发打断打断前一个请求
        viewModel.cancelSendChatRequestIfAny(1)
        
        // 打断打字机动效
        receiver.breakVerbatimDisplayIfAny()
        
        // 当还处于连接中未收到内容时, 强行打断走这个回调, 不会走 sendChat 的 completion 回调, 用于处理较快连续提问时较快取消前一次请求
        viewModel.connectingStateForceInterruptCallback = { [weak self] in
            guard let self = self else { return }
            
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }
                
                // 解开输入限制
                self.refreshInputViewSendStatus()
                
                // 移除所有 loading
                self.viewModel.removeLoadingIfAny()
                self.tableView.reloadData()
                
                ChatAIErrorTraces.postHomeVCSend(uuid: uuid, position: "break connetting")
            }
            
        }
        
        viewModel.sendChat(msg: msg, uuid: uuid, autoResend: autoResend, isExample: isExample, type: type) { [weak self] models, sendingIndex, error in
            guard let self = self else {
                return
            }
            
            // 不确定是否有必要：改成全刷，避免越界问题
            if let _ = sendingIndex {
                self.tableView.reloadData()
            }
            
            // 这里收到错误表示两种情况
            // 1、服务端错误 或 网络错误: (ContentType.text && content isEmpty) || (not ContentType.text)
            // 2、客户端解析失败
            // 3、发送前无网络，不请求直接抛错误
            if let err = error {
                
                // 去除 loading
                self.viewModel.removeLoadingIfAny()
                
                // 解开输入限制
                self.refreshInputViewSendStatus()
                
                self.navigationView.refreshTitle()
                self.scrollToBottom(reloadData: true, animated: false)
                
                ChatAIErrorTraces.postHomeVCSend(uuid: uuid, position: "callback error")
                
                
                // 发送文案失败埋点
                self.viewModel.postSendFailedEvent(err)
                
                return
            }
        
            
            // 处理RAG接收中, 还未获取到具体的回答content时的场景
            if let model = models.first, model.content.isEmpty, model.isFinish == false, model.needsToShowRAGTip() {
                self.viewModel.removeLoadingIfAny()
                var receivingModel = ChatAICellModel(message: model, msgType: .recieved)
                receivingModel.isLoading = true
                let receivingType = ChatAICellType.receiving(model: receivingModel)
                self.viewModel.dataArray.append(receivingType)
                    
                self.scrollToBottom(reloadData: true, animated: false)
                
                self.refreshInputViewSendStatus()
                
                return
            }
            
            // 886 切换人工客服
            let old_is_manual_service = self.viewModel.isManualReply
            var is_manual_service = false
            if let model = models.first {
                is_manual_service = model.is_manual_service
            }
            print("[rgkf][切换人工客服点：柚姐姐回复 \(is_manual_service)]")
            self.viewModel.updateIsManualReply(is_manual_service)
            
            // 转人工曝光
            if let model = models.first,
                is_manual_service != old_is_manual_service,
                is_manual_service {
                
                let sessionId = self.viewModel.getCurrentSessionId()
                let floorValue: Int = model.manual_stats.session_index
                let isManualInvokedByAI: String = model.manual_stats.from
                
                var gaParams: [String: Any] = [
                    "event": "chat_ai_jrrgkf",
                    "action" : 1,
                    "aisession_id": sessionId,
                    "public_info": floorValue,
                    "public_type": isManualInvokedByAI,
                    "info_key": viewModel.scenario_key
                ]
                let stage_type = IMYURIManager.share().runActionAndSyncResult(withPath: "chatAI/stageType", params: [:]) as? NSInteger
                if let stage_type = stage_type, stage_type > 0  {
                    gaParams["duration_period_yl"] = stage_type
                }
                IMYChatBIEvent.postGAEvent("event", params: gaParams)
            }
            
            
            // 886 contentType == 9 直接渲染不走后续流程
            if let model = models.first, model.contentType == .manualReplyTip {
                // 去除 loading
                self.viewModel.removeLoadingIfAny()
                
                // 解除输入限制
                self.refreshInputViewSendStatus()
                
                // 旧值是非人工回复状态，加入 contentType == 9
                if !old_is_manual_service {
                    var recievedCellModel = ChatAICellModel(message: model, msgType: .recieved)
                    recievedCellModel.markdownContent = MarkdownParserV2.parseMarkdown(markdownPlainString: recievedCellModel.content)
                    let recievedCellType = ChatAICellType.received(model: recievedCellModel)
                    
                    if let index = self.viewModel.dataArray.firstIndex(where: { cellType in
                        cellType.model.message.msgId == recievedCellType.model.message.msgId
                    }) {
                        self.viewModel.dataArray.replace(at: index, with: recievedCellType)
                    } else {
                        self.viewModel.dataArray.append(recievedCellType)
                    }
                }
                
                // 刷新
                self.scrollToBottom(reloadData: true, animated: false)
                
                self.checkIfShowRightItems()
                
                return
            }
            
            
            // 892 contentType == .htmlText 直接渲染不走后续流程
            if let model = models.first, model.contentType == .htmlText {
                
                // 去除 loading
                self.viewModel.removeLoadingIfAny()
                
                // 解除输入限制
                self.refreshInputViewSendStatus()
                var recievedCellModel = ChatAICellModel(message: model, msgType: .recieved)
                recievedCellModel.markdownContent = NSAttributedString(string: "")
                
                let recievedCellType = ChatAICellType.received(model: recievedCellModel)
                
                if let index = self.viewModel.dataArray.firstIndex(where: { cellType in
                    cellType.model.message.msgId == recievedCellType.model.message.msgId
                }) {
                    self.viewModel.dataArray.replace(at: index, with: recievedCellType)
                } else {
                    self.viewModel.dataArray.append(recievedCellType)
                }
                
                // 刷新
                self.scrollToBottom(reloadData: true, animated: false)
                
                self.checkIfShowRightItems()
                
                return
            }
            
            
            // 确认接收 SSE 返回的消息
            self.handleReceiveSSE(messages: models)
        }
        
        scrollToBottom(reloadData: true, animated: false)
        
        navigationView.refreshTitle()
    }
    
    // MARK: - SSE
    
    // 上传图片或者视频成功后, 服务端如果返回转人工客服结果, 则切换进人工客服, 并拼接上服务端返回的进入人工客服的提示语
    private func handleUploadMediaSuccessWithServerCallback(models: [ChatMessageModel]) {
        let old_is_manual_service = self.viewModel.isManualReply
        var is_manual_service = false
        if let model = models.first {
            is_manual_service = model.is_manual_service
        }
        print("[rgkf][切换人工客服点：柚姐姐回复 \(is_manual_service)]")
        self.viewModel.updateIsManualReply(is_manual_service)
        
        // 转人工曝光
        if let model = models.first,
           is_manual_service != old_is_manual_service,
           is_manual_service {
            
            let sessionId = self.viewModel.getCurrentSessionId()
            let floorValue: Int = model.manual_stats.session_index
            let isManualInvokedByAI: String = model.manual_stats.from
            
            var gaParams: [String: Any] = [
                "event": "chat_ai_jrrgkf",
                "action" : 1,
                "aisession_id": sessionId,
                "public_info": floorValue,
                "public_type": isManualInvokedByAI,
                "info_key": viewModel.scenario_key
            ]
            let stage_type = IMYURIManager.share().runActionAndSyncResult(withPath: "chatAI/stageType", params: [:]) as? NSInteger
            if let stage_type = stage_type, stage_type > 0  {
                gaParams["duration_period_yl"] = stage_type
            }
            IMYChatBIEvent.postGAEvent("event", params: gaParams)
        }
        
        
        // 886 contentType == 9 直接渲染不走后续流程
        if let model = models.first, model.contentType == .manualReplyTip {
            // 去除 loading
            self.viewModel.removeLoadingIfAny()
            
            // 解除输入限制
            self.refreshInputViewSendStatus()
            
            // 旧值是非人工回复状态，加入 contentType == 9
            if !old_is_manual_service {
                var recievedCellModel = ChatAICellModel(message: model, msgType: .recieved)
                recievedCellModel.markdownContent = MarkdownParserV2.parseMarkdown(markdownPlainString: recievedCellModel.content)
                let recievedCellType = ChatAICellType.received(model: recievedCellModel)
                
                if let index = self.viewModel.dataArray.firstIndex(where: { cellType in
                    cellType.model.message.msgId == recievedCellType.model.message.msgId
                }) {
                    self.viewModel.dataArray.replace(at: index, with: recievedCellType)
                } else {
                    self.viewModel.dataArray.append(recievedCellType)
                }
            }
            
            // 刷新
            self.scrollToBottom(reloadData: true, animated: false)
            
            self.checkIfShowRightItems()
            
            return
        }
    }
    
    private func handleReceiveSSE(messages: [ChatMessageModel]) {
        // messageModel 转 cellModel
        let cellModels = messages.filter { !$0.content.isEmpty || ($0.content.isEmpty && $0.needsToShowRAGTip())}.map {
            var model = ChatAICellModel(message: $0, msgType: .recieved)
            model.isFinish = $0.isFinish
            model.markdownContent = MarkdownParserV2.parseMarkdown(markdownPlainString: model.content)
            return model
        }
        
        // 过滤、更新数据
        let validCellModels = viewModel.filterAndCache(cellModels: cellModels)
        receiver.update(cellModels: validCellModels)
        
        // 解开输入限制
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.refreshInputViewSendStatus()
        }
 
    }
    
    // MARK: - 柚姐姐 + 客服
    
    // MARK: 添加通知
    
    /// 是否添加人工回复相关的通知
    var hasAddManualReplyNoti = false
    
    /// 添加人工回复相关的通知
    func addManualReplyNoti() {
        guard hasAddManualReplyNoti == false else {
            return
        }
        
        hasAddManualReplyNoti = true
        
        // 监听 TCP chat_staff
        _ = NotificationCenter.default.rx
            .notification(Notification.Name("AICS.noti.tcp.chat_staff"))
            .takeUntil(rx.deallocated)
            .observeOn(MainScheduler.instance)
            .subscribe(onNext: { [weak self] (x) in
                guard let self = self else {
                    return
                }
                
                self.handleTcpChatStaff()
            })
        
        // 监听 viewModelRecvManualReply
        _ = NotificationCenter.default.rx
            .notification(Notification.Name("com.chatai.notify.viewModelRecvManualReply"))
            .takeUntil(rx.deallocated)
            .observeOn(MainScheduler.instance)
            .subscribe(onNext: { [weak self] (x) in
                guard let self = self else {
                    return
                }
                
                guard let dict = x.userInfo as? JSONDictionary else{
                    return
                }
                
                let validCellModels = dict["validCellModels"] as? [ChatAICellModel] ?? []
                
                self.handleViewModelRecvManualReply(validCellModels: validCellModels)
            })
        
        
        // 监听 isManualReplyChange
        _ = NotificationCenter.default.rx
            .notification(Notification.Name("com.chatai.notify.isManualReplyChange"))
            .takeUntil(rx.deallocated)
            .observeOn(MainScheduler.instance)
            .subscribe(onNext: { [weak self] (x) in
                guard let self = self else {
                    return
                }
                
                self.handleIsManualReplyChange()
            })
        
        // 给 debugView 用
        _ = NotificationCenter.default.rx
            .notification(Notification.Name("com.chatai.notify.exit_manual_service"))
            .takeUntil(rx.deallocated)
            .observeOn(MainScheduler.instance)
            .subscribe(onNext: { [weak self] (x) in
                guard let self = self else {
                    return
                }
                
                self.viewModel.exitManualService(nil)
            })
    }
    
    /// 处理 TCP chat_staff
    private func handleTcpChatStaff() {
#if DEBUG
        // 模拟收不到 TCP
        let forbidTcpKey = "#+ChatAI-ManualReply-ForbidTcp"
        let forbidTcp = IMYKV.default().bool(forKey: forbidTcpKey)
        if forbidTcp {
            print("[rgkf][handleTcpChatStaff 收到 TCP 了，不过被禁用了]")
            return
        }
#endif
        print("[rgkf][handleTcpChatStaff 收到 TCP 了]")
        self.viewModel.fetchManualReply { msgModels in
            guard let msgModels = msgModels else { return }
            self.viewModel.handleMsgListResponse(msgModels: msgModels)
        }
    }
    
    /// 处理 viewModelRecvManualReply（viewModel 负责转换成了 [ChatAICellModel]）
    private func handleViewModelRecvManualReply(validCellModels: [ChatAICellModel]) {
        if validCellModels.count > 0 {
            imy_asyncMainBlock {
                // 这里直接刷新吧，不要去 receiver 转一圈了
                self.viewModel.removeLoadingIfAny()
                
                // 转成 cellType
                let cellTypes = validCellModels.map { cellModel in
                    return ChatAICellType.received(model: cellModel)
                }
                
                // 判断当前是否打印中
                var isPrint = false
                if let cellType = self.viewModel.dataArray.last {
                    switch cellType  {
                    case .receiving(model: _):
                        isPrint = true
                    default:
                        isPrint = false
                    }
                }
                if isPrint {
                    print("[rgkf][收到人工回复 打印中 放弃]")
                    return
                }
                
                // 没有打印中，则加入数据源，回执给服务端再接收数据
                let msgIds = cellTypes.map { cellType in
                    return cellType.model.message.msgId
                }
                
                if msgIds.count > 0 {
                    print("[rgkf][收到人工回复 接收]")
                    IMYChatAIHTTPService.sharedInstance().setMessageRead(msgIds) { _ in
                        
                    } onError: { _ in
                        
                    }
                    
                    // 这里需要反转一下数组（服务端的数据总是最新的消息在数组的前面）
                    let cellTypes_reversed = cellTypes.reversed()

                    for cellType in cellTypes_reversed {
                        if let index = self.viewModel.dataArray.firstIndex(where: { cellType0 in
                            cellType.model.message.msgId == cellType0.model.message.msgId
                        }) {
                            self.viewModel.dataArray.replace(at: index, with: cellType)
                        } else {
                            self.viewModel.dataArray.append(cellType)
                        }
                    }
                    
                    // 排除一下发送失败的数据
                    self.viewModel.dataArrayExceptSendFail()
                    
                    // 刷新
                    self.scrollToBottom(reloadData: true, animated: false)
                }
                
                // 客服主动发送消息，需要及时展示右上角更多按钮
                self.checkIfShowRightItems()
            }
        }
    }
    
    /// 处理 isManualReply 变化
    private func handleIsManualReplyChange() {
        // 开启轮询
        self.startPolling()
        
        // 输入框适配
        self.aiInputView.refreshIsManualSerivce(self.viewModel.isManualReply)
    }
    
    /// 主动进入人工客服
    private func handleToManual(tipCellModel: ChatAICellModel) {
        // 1、移除 tipCell
        self.viewModel.dataArray.removeAll { cellType in
            return cellType.model.message.msgId == tipCellModel.message.msgId
        }
        self.tableView.reloadData()

        // 2、发一条人工客服
        var msg: [String: Any] = [:]
        msg["content"] = "转人工客服"
        self.handleSubmitChat(msg: msg, isExample: false)
    }
    
    // MARK: 轮询
    
    func startPolling() {
        self.viewModel.startPollingManualReply { [weak self] msgModels in
            guard let self = self, let msgModels = msgModels, msgModels.count > 0 else { return }
            self.viewModel.handleMsgListResponse(msgModels: msgModels)
        }
    }

    // MARK: - 滚动到底部

    func scrollToBottom(reloadData: Bool, animated: Bool) {
        if (reloadData) {
            tableView.reloadData()
        }

        // 出现了越界，实际滚动放到下一个主线程去操作
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            if (self.allowScrollToBottom && self.viewModel.dataArray.count > 0) {
                let indexPath = IndexPath(row: self.viewModel.dataArray.count - 1, section: 0)
                self.tableView.superview?.layoutIfNeeded()
         
                // 防一手越界
                guard indexPath.row == self.viewModel.dataArray.count - 1 else {
                    return
                }
                self.tableView.scrollToRow(at: indexPath, at: .bottom, animated: animated)
                
                NotificationCenter.default.post(name: NSNotification.Name(rawValue: "K-Noti-ChatAIActionBubbleView-PositionChange"), object: nil)
            }
        }
    }
    
//    // 用这个滚动方法，避免动画频繁调用导致滚动异常
//    private var scrollAnimator: UIViewPropertyAnimator?
//    private func scrollToRow(at indexPath: IndexPath) {
//        if let animator = scrollAnimator, animator.isRunning {
//            animator.addAnimations { [weak self] in
//                guard let self = self else { return }
//                self.tableView.scrollToRow(at: indexPath, at: .bottom, animated: false)
//            }
//        } else {
//            let duration: TimeInterval = 0.3
//            scrollAnimator = UIViewPropertyAnimator(duration: duration, curve: .easeInOut) { [weak self] in
//                guard let self = self else { return }
//                self.tableView.scrollToRow(at: indexPath, at: .bottom, animated: false)
//            }
//            scrollAnimator?.startAnimation()
//            scrollAnimator?.addCompletion { [weak self] _ in
//                guard let self = self else { return }
//                self.scrollAnimator = nil
//            }
//        }
//    }
    
    private func layoutTableView(bottomOffsetY: CGFloat) {
        tableView.snp.remakeConstraints { make in
            make.top.equalTo(navigationView.snp.bottom)
            make.leading.trailing.equalTo(containerView)
            make.bottom.equalTo(containerView).offset(-bottomOffsetY)
        }
        containerView.layoutIfNeeded()
        
        NotificationCenter.default.post(name: NSNotification.Name(rawValue: "K-Noti-ChatAIActionBubbleView-PositionChange"), object: nil)
    }
    
    // MARK: - InputView
    
    func refreshInputViewSendStatus() {
        var status = SendButtonStatus.disabled
        if aiInputView.text.count > 0 {
            status = .enabled
        }
        let sendStatus = aiInputView.inputTextView.sendStatus
        if (sendStatus != status) {
            self.aiInputView.inputTextView.sendStatus = status
        }
    }
    
    // MARK: - 实名认证、登录
    
    private func checkLogin() -> Bool {
        let hasLogin = IMYPublicAppHelper.share().hasLogin
        if hasLogin == false {
            IMYURIManager.share().runAction(withPath: "login", params: [:], info: [:])
            return false
        }
        return true
    }
    
    private func checkRealName() -> Bool {
        // 旧版实名认证流程
//        let realName = IMYURIManager.share().runActionAndSyncResult(withPath: "realName/isRealName", params: [:])
//        if let realName = realName {
//            if realName as! Int != 1 {
//                IMYURIManager.share().runAction(withPath: "realName/show", params: ["isFromBiz":true], info: [:])
//                return false
//            }
//        }
//        return true
        
        // 8.90.0 已经实名过的, 直接放过, 没有实名过的用户, 实名认证改为只绑定手机号, 减少实名认证的流程步骤
        let realName = IMYURIManager.share().runActionAndSyncResult(withPath: "realName/isRealName", params: [:])
        if let realName = realName, realName as? Int == 1 {
            return true
        } else {
            
            let hasBindChinaAuth = IMYURIManager.sharedInstance().runActionAndSyncResult(withPath:"account/checkHasBindChinaAuth", params: [:])
            if let hasBindChinaAuth = hasBindChinaAuth as? Int, hasBindChinaAuth > 0 {
                return true
            } else {
                let result = IMYURIManager.sharedInstance().runActionAndSyncResult(withPath: "account/checkHasBindMobile", params: [:])
                if let result = result as? Int, result > 0 {
                    return true
                } else {
                    let params: [String : Any] = [
                        "vc": self,
                        "info_key": viewModel.scenario_key
                    ]
                    let uri = IMYURI(path: "account/bindPhoneNumberDialog", params: params, info: [:])
                    let actionObject = IMYURIActionBlockObject.actionBlock(with: uri)
                    actionObject?.implCallbackBlock = {[weak self] result, error, eventName in
                        if eventName == "cancelCallback" {
                            self?.autoSubmitTutorialQuestionsParams = nil
                            self?.needToAutoSubmitTutorialQuestions = false
                        }
                    }
                    IMYURIManager.sharedInstance().runAction(withActionObject: actionObject, completed: nil)
                    return false
                }
            }
        }
    }
    
    // MARK: - 删除对话
    
    private func deleteConversationsAction() {
        guard let inView = UIApplication.shared.delegate?.window, let showView = inView else{
            return
        }
        
        let sheetView = IMYActionSheet(cancelTitle: "取消", otherTitles: ["删除"], summary: "确定删除所有对话吗？".imy.local(), showIn: showView) { [weak self] index in
            guard let self = self else {
                return
            }
            if index == 1  && self.viewModel.dataArray.count > 0 {
                
                // 结束客服会话中进行中的图片上传任务
                IMYChatAIImageUploadManager.sharedInstance().cancelAllUploadings()
                
                // 清除Documents/my-chatai-{uid}/images/ 下所有图片缓存
                IMYChatAIImageUploadManager.sharedInstance().removeAllAICSCacheImageFiles()
                
                self.clearAction()
                HUD.show(text: "对话已删除".imy.local())
                print("delete all conversations action")
                
                // 标记该用户删除过所有对话
                let hasDeleteKey = "ChatAI.hasDeleteConversation" + User.userId
                IMYUserDefaults.standard().setBool(true, forKey: hasDeleteKey)
            }
        }
        sheetView?.show()
    }
    
    // MARK: - Handle Actions
    func handleBackItemClicked() {
        // 评价弹窗当天弹出过后, 退出时不需要再弹
        guard viewModel.checkIfRatingDialogRecentlyShownTimeOverOneDay() == true else {
            self.imy_pop(true)
            return
        }
        
        let uid = IMYPublicAppHelper.share().userid ?? ""
        IMYChatAIHTTPService.sharedInstance().checkFeekbackAvailable(uid, isAutoInvoked: true, timeOutInterval: 2.0) {[weak self] result in
            guard let self = self else { return }
            if result == true {
                self.showRatingDialog(isFromBackClick: true)
            } else {
                self.imy_pop(true)
            }
        } onError: {[weak self] err in
            guard let self = self else { return }
            self.imy_pop(true)
        }
    }
    
    func handleRatingFeedbackButtonClicked() {
        UIView.imy_showLoadingHUD()
        let uid = IMYPublicAppHelper.share().userid ?? ""
        IMYChatAIHTTPService.sharedInstance().checkFeekbackAvailable(uid, isAutoInvoked: false, timeOutInterval: 30.0) {[weak self] result in
            UIView.imy_hideHUD()
            guard let self = self else { return }
            if result == true {
                self.showRatingDialog()
            } else {
                UIView.imy_showTextHUD(0.0, text: "无服务可评价")
            }
        } onError: { err in
            UIView.imy_hideHUD()
            if !NetStatus.enable {
                UIView.imy_showTextHUD(0.0, text: "网络不见了，请检查网络")
            } else {
                UIView.imy_showTextHUD(0.0, text: "加载失败")
            }
        }
    }
    
    func handleQuitManualServiceButtonClicked() {
        // 退出人工客服点击埋点
        let params:JSONDictionary = [
            "action": 2,
            "event": "chat_rgai_tcdj",
            "aisession_id": self.viewModel.getCurrentSessionId(),
            "info_key": self.viewModel.scenario_key
        ]
        IMYChatBIEvent.postGAEvent("event", params: params)
        
        // 退出人工客服
        self.viewModel.exitManualService(nil)
        
        ChatAIImageSendManager.sharedInstance.forcePauseAllUploadings()
    }
    
    func showRatingDialog(isFromBackClick: Bool = false) {
        
        let aisession_id = self.viewModel.getCurrentSessionId()
        let info_key: Int = self.viewModel.isManualReply ? 2 : 1
        
        let ratingParams: [String: Any] = [
            "aisession_id": aisession_id,
            "public_status": info_key,
            "isFromBackClick": isFromBackClick,
            "info_key": viewModel.scenario_key
        ]
        
        IMYURIManager.share().runAction(withPath: "chat_ai/rating/dialog", params: ratingParams, info: [:])
        viewModel.updateRatingDialogRecentlyShownTimeStamp()
    }
    
    // MARK: - Load History Message Data Stuffs
    func isTableViewHeaderHistoryLoadMoreTipHeader() -> Bool {
        guard let tableViewHeaderView = self.tableView.tableHeaderView else {
            return false
        }
        let result = tableViewHeaderView is ChatAIHistoryMessageLoadMoreTipHeader
        return result
    }
    
    func isTableViewHeaderBigPicutreHeader() -> Bool {
        guard let tableViewHeaderView = self.tableView.tableHeaderView else {
            return false
        }
        let result = tableViewHeaderView is ChatAIBigHeaderView
        return result
    }
    
    // MARK: - Property
    
    let containerView = UIView()
    private(set) lazy var tableView: UITableView = {
        let _ = bigHeaderView
        let _ = historyMsgLoadMoreTipHeader
        
        let tableView = UITableView(frame: CGRect.zero, style: .grouped)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.separatorStyle = .none
        tableView.backgroundColor = UIColor.clear
        tableView.tableHeaderView = hiddenTableHeaderView
        tableView.tableFooterView = UIView(frame: CGRect(x: 0, y: 0, width: IMYSystem.screenWidth(), height: CGFloat.leastNonzeroMagnitude))
        tableView.separatorColor = UIColor.clear
        
        tableView.imy.register(cell: ChatAIReceivedCell.self)
        tableView.imy.register(cell: ChatAIReceivingCell.self)
        tableView.imy.register(cell: ChatAISendCell.self)
        tableView.imy.register(cell: ChatAIReplyTipCell.self)
        tableView.imy.register(cell: ChatAIMemoryCell.self)
        tableView.imy.register(cell: ChatAINewTopicsCell.self)
        tableView.imy.register(cell: ChatAIReceivedTipCell.self)
        tableView.imy.register(cell: ChatAIReceivedHistoryMessageSplitLineTipCell.self)
        tableView.imy.register(cell: UITableViewCell.self)
        
        tableView.keyboardDismissMode = .onDrag
        
        // 禁用状态栏点击滚动到顶部
        tableView.scrollsToTop = false
        
        return tableView
    }()
    
    /// 列表头部（渐变背景）
    private(set) lazy var gradientBg: UIImageView = {
        let ratio = 375.0 / 280.0
        let view = UIImageView(frame: CGRectMake(0, 0, Device.screenWidth, Device.screenWidth / ratio))
        view.imy_setBackgroundColor(forKey: kCK_Clear_A)
        view.imy_setImage(forKey: "yjj_bg")
        return view
    }()
    
    /// 列表头部
    private(set) lazy var bigHeaderView: ChatAIBigHeaderView = {
        let view = ChatAIBigHeaderView(frame: CGRectMake(0, 0, Device.screenWidth, 44 + 88 + 16))
        return view
    }()
    
    private(set) lazy var historyMsgLoadMoreTipHeader: ChatAIHistoryMessageLoadMoreTipHeader = {
        let headerHeight = ChatAIHistoryMessageLoadMoreTipHeader.headerHeight()
        let view = ChatAIHistoryMessageLoadMoreTipHeader(frame: CGRectMake(0, 0, Device.screenWidth, headerHeight))
        return view
    }()
    
    private(set) lazy var hiddenTableHeaderView: UIView = {
        let view = UIView(frame: CGRect(x: 0, y: 0, width: IMYSystem.screenWidth(), height: CGFloat.leastNonzeroMagnitude))
        return view
    }()
    
    /// 导航栏
    private(set) lazy var navigationView: ChatAINavigationView = {
        let view = ChatAINavigationView()
        view.moreAction = { [weak self] in
            guard let self = self else {
                return
            }
            self.handleMoreButtonAction()
        }
        view.cancelAction = { [weak self] in
            guard let self = self else {
                return
            }
            self.exitEditMode()
        }
        
#if DEBUG
        view.bk_(whenTouches: 1, tapped: 3) {
            IMYChatAIDebugView.show()
        }
#endif
        
        return view
    }()
    
    /// 输入控件
    private(set) lazy var aiInputView: ChatAIInputView = {
        let view = ChatAIInputView()
        view.layer.zPosition = 1 // 防止被遮挡
        view.inputTextView.goToAccountClourse = { [weak self] in
            guard let self = self else { return }
        }
        
        view.inputTextView.checkLoginClosure = { [weak self] in
            guard let self = self else { return false }
            return self.checkLogin()
        }
        
        view.inputTextView.checkRealNameClosure = { [weak self] in
            guard let self = self else { return false }
            return self.checkRealName()
        }
        
        view.submitClourse = { [weak self] text, isExample in
            guard let self = self else { return }
            self.clearExample()
            
            // willHideKeyboardClourse 里获取到的 self.aiInputView.inputViewHeight 不是最及时的值
            let inputViewHeight = self.aiInputView.viewHeight()
            self.layoutTableView(bottomOffsetY: inputViewHeight)
            
            self.scrollToBottom(reloadData: true, animated: false)
            self.handleSubmitChat(msg: ["content": text], isExample: isExample)
        }
        view.inputTextView.submitView.accessibilityIdentifier = "ChatAIInputTextView.submitView"
        view.inputTextView.textView.accessibilityIdentifier = "ChatAIInputTextView.textView"
        
        
        view.imageBtnClickCallback = {[weak self] in
            guard let self = self else { return }
            self.showImagePicker()
        }
        
        view.feedbackBtnClickCallback = {[weak self] in
            guard let self = self else { return }
            self.handleRatingFeedbackButtonClicked()
        }
        
        view.quitManualServiceClickCallback = {[weak self] in
            guard let self = self else { return }
            self.handleQuitManualServiceButtonClicked()
        }
        
        view.willHideKeyboardClourse = { [weak self] in
            guard let self = self else { return }
            self.inputViewOffsetY = 0
            
            let inputViewHeight = self.aiInputView.viewHeightKeyboardHidden()
            self.layoutTableView(bottomOffsetY: inputViewHeight)
            self.scrollToBottom(reloadData: false, animated: false)
        }
        
        view.willShowKeyboardClourse = { [weak self] in
            guard let self = self else { return }
            self.allowScrollToBottom = true
        }
        
        view.inputViewAreaChangeClourse = { [weak self] height in
            guard let self = self else { return }
            
            // 除去tabbar + inputVIew 高度
            var offsetY = height - Device.screenTabBarHeight
            
            if self.fromHome == false {
                offsetY = height - self.safeAreaBottom
            }
            
            
            if self.aiInputView.isKeyboardShowing == false {
                offsetY += self.safeAreaBottom
            }
            
            self.aiInputView.snp.updateConstraints { make in
                make.height.equalTo(offsetY)
            }
            
            
            self.inputViewOffsetY = offsetY
            self.layoutTableView(bottomOffsetY: self.inputViewOffsetY)
            self.scrollToBottom(reloadData: false, animated: true)
        }
        
        view.inputTextView.resignFirstResponderWhenSend = true
        
        return view
    }()
    
    /// 占位图
    private(set) lazy var captionView: IMYCaptionView = {
        let view = IMYCaptionView.add(to: self.containerView, show: true)
        view?.retryBlock = { [weak self] in
            guard let self = self else {
                return
            }
            self.setupDatas(isRefresh: false)
        }
        return view ?? IMYCaptionView()
    }()
    
    let viewModel = ChatAIViewModelV2()
    
    let receiver = ChatAiReceivingLoopV2<ChatAICellModel>()
    
    /// 用户手动滚动的时候, 不触发自动滚到底部, 提交消息的时候,再开放
    private var allowScrollToBottom = true
    
    /// 记录输入框状态偏移高度
    var inputViewOffsetY: CGFloat = 0
    
    /// 用于页面首次展示时播放引导动效
    var needAnimateGuide: Bool = false
    
    var selectedTipView: ChatAISelectedTipView?
    
    var selectedBottomView: ChatAISelectedBottomView?
    
    /// 标题
    var assistantTitle: String = "柚姐姐".imy.local()
    
    /// 是否一级页面
    var fromHome: Bool = false
    
    var safeAreaBottom: CGFloat = 0
    
    /// 是否能够预加载
    var canPreload: Bool = false
    
    private(set) lazy var backButton: IMYTouchEXButton = {
        let button = IMYTouchEXButton()
        button.setExtendTouchAllValue(10)
        button.imy_addThemeChangedBlock { weakObj in
            guard let obj = weakObj as? IMYTouchEXButton else {
                return
            }
            obj.imy_setImage(UIImage.imy.image(forKey: "nav_btn_back_black").imy_getNightStyleTopBar())
        }
        _ = button.rx.controlEvent(.touchUpInside).takeUntil(rx.deallocated).subscribe {[weak self] sender in
            guard let self = self else {
                return
            }
            self.handleBackItemClicked()
        }
        return button
    }()
    
    private(set) lazy var safeBottomView: UIView = {
        let view0 = UIView(frame: CGRectMake(0, 0, IMYSystem.screenWidth(), Device.screenTabBarSafeBottomMargin))
        view0.imy_setBackgroundColor(forKey: "FAF0F3")
        return view0
    }()
    
    
    // MARK: - 获取图片
    func showImagePicker() {
        guard IMYImagePickerController.isAccessible() else {
            UIWindow.imy_showTextHUD(0.0, text: "相册照片无法显示啦，请在系统设置-隐私-照片中打开美柚开关")
            return
        }
        
        let vc = IMYAssetPickerController()
        vc.allowsMultipleSelection = true;
        vc.styleType = .new;
        vc.allowSelectionVideo = true
        vc.maximumNumberOfSelection = 1;
        vc.minimumNumberOfSelection = 1;
        vc.isHiddenCamera = false
        vc.maxVideoDuration = 120
        vc.maxVideoSelectCount = 1
        vc.minVideoSelectCount = 1
        vc.delegate = self;
        
        self.imy_present(vc)
    }
    
    func showImageDetail(_ isRemoteImage: Bool, _ imageURL: String) {
        let photo: IMYPhoto = IMYPhoto()
        if isRemoteImage {
            photo.fileurl = nil
            photo.url = URL(string: imageURL)!
            photo.sy_placeholder = SDImageCache.shared().imageFromDiskCache(forKey: imageURL)
        } else {
            let placeholder = UIImage(contentsOfFile: imageURL)
            photo.sy_placeholder = placeholder
            photo.fileurl = URL(string: imageURL)!
            photo.url = nil
        }
        IMYPhotoBrowser.show(with: photo)
    }
    
    
    // 显示视频
    func showVideoDetail(_ isRemoteImage: Bool, _ videoURL: String) {
        if isRemoteImage {
            showRemoteVideoDetail(videoURL)
        } else {
            showLocalVideoDetail(videoURL)
        }
    }
    
    
    func showRemoteVideoDetail(_ videoURL: String) {
        let videoModel = IMYVideoModel()
        videoModel.sdVideoURL = URL(string: videoURL)
        videoModel.hdVideoURL = URL(string: videoURL)
        var coverImageURL = videoURL + "?&x-oss-process=video/snapshot,t_0,f_jpg"
        videoModel.coverImageURL = URL(string: coverImageURL)
        
        let vc: ChatAICachePlayerController = ChatAICachePlayerController()
        vc.videoModel = videoModel
        self.present(vc, animated: true)
    }
    
    
    func showLocalVideoDetail(_ videoURL: String) {
        var fixVideoURL = IMYChatAIImageUploadManager.fixAbsoluteFilePathIssue(videoURL)
        var url = URL(fileURLWithPath: fixVideoURL)
        let vc: ChatAILocalPlayerController = ChatAILocalPlayerController(videoURL: url)
        vc.modalPresentationStyle = .fullScreen
        self.present(vc, animated: true)
        
    }
    
    // MARK: - IMYAssetPickerControllerDelegate
    public func assetPickerControllerWillCancelling(_ assetPickerController:IMYAssetPickerController) {
        // 取消
    }
    
    public func assetPickerController(_ assetPickerController:IMYAssetPickerController, didSelectAssets assets:[IMYAssetModel]) {
        // 单次发送只支持单张图片
        guard let asset = assets.first else {
            // 发送失败统计埋点
            self.viewModel.postSendFailedEvent("选择照片报错, 未知错误, 请检查手机磁盘空间是否已满.", 2)
            return
        }
        
        if asset.assetType == .video {
            self.handleVideoAssetPicked(asset)
        } else {
            self.handleImageAssetPicked(asset)
        }
    }
    
    
    // MARK: - Handle IMYAssetPickerViewController callbacks
    func handleImageAssetPicked(_ asset: IMYAssetModel) {
        let uuid = UUID().uuidString
        let fileName = uuid + ".jpg"
        
        let writeToLocalPathResult = IMYChatAIImageUploadManager.sharedInstance().storeImageToPath(forUpload: asset.originImage(), fileName: fileName)
        guard writeToLocalPathResult == true else {
            self.viewModel.postSendFailedEvent("照片缓存写入失败, 未知错误, 请检查手机磁盘空间是否已满.", 2)
            return
        }
        
        var msg: [String: Any] = [:]
        let md5 = IMYChatAIImageUploadManager.sharedInstance().calculateMD5(ofFile: fileName)
        if let md5 = md5, md5.isEmpty == false {
            msg["md5"] = md5
        }
        
        self.sendImage(msg: msg, uuid: uuid, fileName: fileName)
    }
    
    func handleVideoAssetPicked(_ asset: IMYAssetModel) {
        
        asset.requestVideoAVAsset {[weak self] avAsset, info in
            guard let self = self, let avURLAsset = avAsset as? AVURLAsset else { return }
            
            guard let videoTrack = avAsset.tracks(withMediaType: .video).first else {
                self.viewModel.postSendFailedEvent("视频导出失败, 未知错误, 请检查手机系统相册权限是否开启.", 3)
                DispatchQueue.main.async {
                    UIWindow.imy_showTextHUD(0, text: "视频导出失败, 未知错误, 请检查手机系统相册权限是否开启.")
                }
                return
            }
            
            let assetURLFilePath = avURLAsset.url.path
            var fileSize: Int64 = 0
            do {
                let fileAttributes = try FileManager.default.attributesOfItem(atPath: assetURLFilePath)
                fileSize = fileAttributes[.size] as? Int64 ?? 0
            } catch {
                print("获取文件大小失败: \(error)")
            }
            
            // 小于200M的视频用系统的API导出, 速度快, 压缩率低, 大文件用自定义工具类导出, 导出慢, 压缩率高
            if fileSize > 0 && fileSize < (200 * 1024 * 1024) {
                let uuid = UUID().uuidString
                let fileName = uuid + ".mp4"
                
                let videoOutputPath = IMYChatAIImageUploadManager.sharedInstance().uploadFilePath(ofFile: fileName)
                let videoOutputURL = URL(fileURLWithPath: videoOutputPath)
                let imageFileName = uuid + ".jpg"
                let imageFilePath = IMYChatAIImageUploadManager.sharedInstance().uploadFilePath(ofFile:imageFileName)
                
                var videoOriginalSize = videoTrack.naturalSize
                if videoOriginalSize == CGSize.zero {
                    videoOriginalSize = CGSize(width: 960, height: 960)
                }
                
                let transform = videoTrack.preferredTransform
                
                var rotationNeeded = false
                
                // 计算旋转角度（弧度转角度）
                let rotation = atan2(transform.b, transform.a) * (180 / .pi)
                NSLog("XXX: video rotation - \(rotation)")
                // 检查是否为90度或270度旋转
                if abs(rotation) == 90.0 || abs(rotation) == 270.0 {
                    rotationNeeded = true
                }
                
                if rotationNeeded {
                    var fixSize = videoOriginalSize
                    videoOriginalSize = CGSize(width: fixSize.height, height: fixSize.width)
                }
                
                
                let thumbnailImage = asset.thumbnailImage(with: videoOriginalSize)
                let writeToLocalPathResult = IMYChatAIImageUploadManager.sharedInstance().storeImageToPath(forUpload: thumbnailImage, fileName: imageFileName)
                guard writeToLocalPathResult == true else {
                    self.viewModel.postSendFailedEvent("视频导出失败, 未知错误, 请检查手机磁盘空间是否已满.", 3)
                    UIWindow.imy_showTextHUD(0, text: "视频导出失败, 未知错误, 请检查手机磁盘空间是否已满.")
                    return
                }
                
                guard let exportSession = AVAssetExportSession(asset: avAsset, presetName: AVAssetExportPreset1280x720) else {
                    DispatchQueue.main.async {
                        UIWindow.imy_showTextHUD(0, text: "视频导出失败, 未知错误, 请检查手机剩余磁盘空间.")
                    }
                    return
                }
                exportSession.outputURL = videoOutputURL
                exportSession.outputFileType = .mp4
                exportSession.shouldOptimizeForNetworkUse = true
                
            
                //let time1 = CACurrentMediaTime()
                exportSession.exportAsynchronously {[weak self] in
                    DispatchQueue.main.async {[weak self] in
                        guard let self = self else { return }
                        
                        IMYChatAIImageUploadManager.sharedInstance().hideVideoExportingHUD()
                        
                        if exportSession.status == .completed {
                            
                            //let diff = CACurrentMediaTime() - time1
                                      
                            let videoFileMD5 = IMYChatAIImageUploadManager.sharedInstance().calculateMD5(ofFile: fileName) ?? ""
                            var videoParams: [String: Any] = [
                                "uuid": uuid,
                                "size": videoOriginalSize,
                                "md5": videoFileMD5,
                                "assetIdentifier": asset.identifier
                            ]
                            let delay = 0.0
                            self.perform(#selector(self.sendVideo(_:)), with: videoParams, afterDelay: delay)
                            
                        } else {
                            print("导出失败: \(exportSession.error?.localizedDescription ?? "")")
                            let localizedDescription = exportSession.error?.localizedDescription ?? ""
                            UIWindow.imy_showTextHUD(0, text: "视频导出失败: \(localizedDescription)")
                        }
                    }
                }
                DispatchQueue.main.async {
                    IMYChatAIImageUploadManager.sharedInstance().showVideoExportingHUD()
                }
                return
            }
            
            
            
            let uuid = UUID().uuidString
            let fileName = uuid + ".mp4"
            
            let videoOutputPath = IMYChatAIImageUploadManager.sharedInstance().uploadFilePath(ofFile: fileName)
            let videoOutputURL = URL(fileURLWithPath: videoOutputPath)
            let imageFileName = uuid + ".jpg"
            let imageFilePath = IMYChatAIImageUploadManager.sharedInstance().uploadFilePath(ofFile:imageFileName)
            
            var videoTrackTrackDimensions = videoTrack.naturalSize
            if videoTrackTrackDimensions == CGSize.zero {
                videoTrackTrackDimensions = CGSize(width: 960, height: 960)
            }
            
            var maxDisplaySize: CGSize = CGSize(width: 1280, height: 1280)
            var thumbnailImageSize: CGSize = IMYChatAIVideoCGSizeAdapterSize(videoTrackTrackDimensions, maxDisplaySize)
            
            
            var rotationNeeded = false
            
            let transform = videoTrack.preferredTransform
            
            // 计算旋转角度（弧度转角度）
            let rotation = atan2(transform.b, transform.a) * (180 / .pi)
            NSLog("XXX: video rotation - \(rotation)")
            // 检查是否为90度或270度旋转
            if abs(rotation) == 90.0 || abs(rotation) == 270.0 {
                rotationNeeded = true
            }
            
            if rotationNeeded {
                var fixSize = thumbnailImageSize
                thumbnailImageSize = CGSize(width: fixSize.height, height: fixSize.width)
            }
            
            
            let thumbnailImage = asset.thumbnailImage(with: thumbnailImageSize)
            let writeToLocalPathResult = IMYChatAIImageUploadManager.sharedInstance().storeImageToPath(forUpload: thumbnailImage, fileName: imageFileName)
            guard writeToLocalPathResult == true else {
                self.viewModel.postSendFailedEvent("视频导出失败, 未知错误, 请检查手机磁盘空间是否已满.", 3)
                UIWindow.imy_showTextHUD(0, text: "视频导出失败, 未知错误, 请检查手机磁盘空间是否已满.")
                return
            }
            
            
            self.exportSession = IMYAVExportSession.init(asset: avAsset)
            self.exportSession?.exportConfiguration.outputURL = videoOutputURL
            
            self.exportSession?.videoConfiguration.videoOutputSetting = {
                // 视频码率如果低于2M则采用视频自身码率, 高于2M则压缩成2M
                let videoTrackEstimatedDataRate = videoTrack.estimatedDataRate
                let minBitRate: Float = Float(2000000);
                let bitrate = Float.minimum(minBitRate, videoTrackEstimatedDataRate)
                let frameRate = Float.minimum(30.0, videoTrack.nominalFrameRate)
                
                let compressionSettings: [String: Any] = [
                    AVVideoExpectedSourceFrameRateKey: frameRate,
                    AVVideoAverageBitRateKey: bitrate,
                    AVVideoMaxKeyFrameIntervalKey: 30,
                    AVVideoProfileLevelKey: AVVideoProfileLevelH264MainAutoLevel
                ]
                var videoSettings: [String : Any] = [
                    AVVideoWidthKey: thumbnailImageSize.width,
                    AVVideoHeightKey: thumbnailImageSize.height,
                    AVVideoCompressionPropertiesKey: compressionSettings
                ]
                
                videoSettings[AVVideoCodecKey] =  AVVideoCodecType.h264
                return videoSettings
            }()
            
            self.exportSession?.audioConfiguration.audioOutputSetting = {
                var stereoChannelLayout = AudioChannelLayout()
                memset(&stereoChannelLayout, 0, MemoryLayout<AudioChannelLayout>.size)
                stereoChannelLayout.mChannelLayoutTag = kAudioChannelLayoutTag_Stereo
                
                let channelLayoutAsData = Data(bytes: &stereoChannelLayout, count: MemoryLayout<AudioChannelLayout>.size)
                let compressionAudioSettings: [String: Any] = [
                    AVFormatIDKey: kAudioFormatMPEG4AAC,
                    AVEncoderBitRateKey: 96000,
                    AVSampleRateKey: 44100,
                    AVChannelLayoutKey: channelLayoutAsData,
                    AVNumberOfChannelsKey: 2
                ]
                return compressionAudioSettings
            }()
            
            self.exportSession?.progressHandler = { [weak self] (progress) in
                guard let strongSelf = self else { return }
                DispatchQueue.main.async {
                    NSLog("XXX Exporting --- \(Int(progress * 100))%")
                }
            }
            self.exportSession?.completionHandler = { [weak self] (err) in
                guard let strongSelf = self, let strongSelfExportSession = strongSelf.exportSession else { return }
                DispatchQueue.main.async {
                    if let err = err {
                        NSLog("XXX Exporting --- \(err.localizedDescription)")
                        IMYChatAIImageUploadManager.sharedInstance().hideVideoExportingHUD()
                        UIWindow.imy_showTextHUD(0, text: "视频导出失败: \(err.localizedDescription)")

                    } else {
                        guard let timeConsume = strongSelf.exportSession?.getExportTimeConsume(), timeConsume > 0 else {
                            IMYChatAIImageUploadManager.sharedInstance().hideVideoExportingHUD()
                            UIWindow.imy_showTextHUD(0, text: "视频导出失败: 导出耗时异常")
                            return
                        }
                        var delay: CGFloat = 0.0
                        if timeConsume < 1.0 {
                            delay = 1.0
                        }
                        
     
                        
                        let videoFileMD5 = IMYChatAIImageUploadManager.sharedInstance().calculateMD5(ofFile: fileName) ?? ""
                        
                        var videoParams: [String: Any] = [
                            "uuid": uuid,
                            "size": thumbnailImageSize,
                            "md5": videoFileMD5,
                            "assetIdentifier": asset.identifier
                        ]
                        
                        strongSelf.perform(#selector(strongSelf.sendVideo(_:)), with: videoParams, afterDelay: delay)
                        
                        NSLog("XXX Exporting --- Finished \(timeConsume)s : \(strongSelf.exportSession?.exportConfiguration.outputURL)")

                    }
                }
            }
            self.exportSession?.export()
            
            DispatchQueue.main.async {
                IMYChatAIImageUploadManager.sharedInstance().showVideoExportingHUD()
            }
        }
    }
    
    @objc func sendVideo(_ videoParams: [String: Any]) {
        IMYChatAIImageUploadManager.sharedInstance().hideVideoExportingHUD()
        
        let uuid: String = (videoParams["uuid"] as? String) ?? ""
        let videoSize: CGSize = (videoParams["size"] as? CGSize) ?? CGSize.zero
        let videoFileMD5 = (videoParams["md5"] as? String) ?? ""
        
        let assetIdentifier = (videoParams["assetIdentifier"] as? String) ?? ""
        
        let imageFileName = uuid + ".jpg"
        let fileName = uuid + ".mp4"
        let videoFilePath = IMYChatAIImageUploadManager.sharedInstance().uploadFilePath(ofFile: fileName)
        let imageFilePath = IMYChatAIImageUploadManager.sharedInstance().uploadFilePath(ofFile:imageFileName)
        
        let videoModel = ChatAIVideo.create(
            url: videoFilePath,
            width: "\(videoSize.width)",
            height: "\(videoSize.height)",
            md5: videoFileMD5,
            thumbnail: imageFilePath
        )
        
        var msg: [String: Any] = [:]
        msg["md5"] = videoFileMD5
        
        self.sendVideoMsg(msg: msg, uuid: uuid, assetIdentifier: assetIdentifier, videos: [videoModel])
        
    }
}



// MARK: - UITableViewDelegate

extension ChatAIHomeViewControllerV2: UITableViewDelegate, UIScrollViewDelegate {
    
    public func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: false)
        changeSelectedCell(indexPath: indexPath)
    }
    
    public func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return CGFLOAT_MIN
    }
    
    public func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        return CGFloat.leastNormalMagnitude
    }
    
    public func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        guard let type = viewModel.dataArray[indexPath.row] else {
            return 0
        }
        switch type {
        case .received(let model):
            if model.message.isNewTopic() {
                let hPadding = 12.0
                let maxWidth = tableView.imy_width - 2.0*hPadding
                return ChatAINewTopicsCell.cellHeight(maxWidth: maxWidth, cellModel: model)
            }
            
            if model.message.contentType == .manualServiceExitTip {
                return ChatAIReceivedTipCell.cellHeight(model: model)
            }
                
            if model.message.contentType == .historyMsgSpitline {
                return ChatAIReceivedHistoryMessageSplitLineTipCell.cellHeight(model: model)
            }
            
            if model.message.contentType == .htmlText {
                return ChatAIReceivedCell.receivedCellHeight(model: model, showAppraise: false, row: indexPath.row)
            }
                
            // 是否缓存
            var canCache = (!kChatAIIsSelected) && (!model.isFailed) && (model.message.tutorial_questions.count == 0)
            
            
            // 图文可能折叠, 不缓存高度先
            if model.message.contentType == .htmlText {
                canCache = false
            }
                
            // 使用缓存高度
            if model.cellHeight > 0 && canCache {
                return model.cellHeight
            }
            
            // 计算高度
            let height = ChatAIReceivedCell.receivedCellHeight(model: model, showAppraise: false, row: indexPath.row)
            
                
            // 缓存高度
            if canCache {
                var model0 = model
                model0.cellHeight = height
                let type0 = ChatAICellType.received(model: model0)
                self.viewModel.dataArray[indexPath.row] = type0
            }
            
            return height
        case .receiving(let model):
            return ChatAIReceivingCell.cellHeight(model: model)
        case .send(let model):
            // 是否缓存
            let canCache = (!kChatAIIsSelected) && (!model.isFailed) && (model.message.tutorial_questions.count == 0)
            
            // 使用缓存高度
            if model.cellHeight > 0 && canCache {
                return model.cellHeight
            }
            
            // 计算高度
            let height = ChatAISendCell.cellHeight(model: model)
            
            // 缓存高度
            if canCache {
                var model0 = model
                model0.cellHeight = height
                let type0 = ChatAICellType.send(model: model0)
                self.viewModel.dataArray[indexPath.row] = type0
            }
            
            return height
        case .replyTip(let model):
            return ChatAIReplyTipCell.cellHeight(model: model)
        case .memory(let model):
            return ChatAIMemoryCell.cellHeight(model: model)
        case .example(let model) :
            return CGFLOAT_MIN
        }
    }
    
    public func scrollViewWillBeginDragging(_ scrollView: UIScrollView) {
        // 键盘弹起的时候要能滚动到底部
        if self.aiInputView.keyboardHeight > 0 {
            self.allowScrollToBottom = true
        } else {
            self.allowScrollToBottom = false
        }
    }
    
    public func scrollViewDidScroll(_ scrollView: UIScrollView) {
        // 当向下拉超过20pt且未在加载中时显示加载指示器
        let pullThreshold: CGFloat = ChatAIHistoryMessageLoadMoreTipHeader.getPullThreshold()
        NSLog("[scrollViewDidScroll] - \(scrollView.contentOffset.y)")
        if self.isTableViewHeaderHistoryLoadMoreTipHeader() {
            if scrollView.contentOffset.y < pullThreshold {
                self.historyMsgLoadMoreTipHeader.showLoading()
            } else {
                if self.viewModel.isHistoryMsgsDataRequsting == false {
                    self.historyMsgLoadMoreTipHeader.showTip()
                } else {
                    self.historyMsgLoadMoreTipHeader.showLoading()
                }
            }
        }
    }
    
    public func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        checkAllowScrollToBottom(scrollView: scrollView)
    }
    
    public func scrollViewDidEndDragging(_ scrollView: UIScrollView, willDecelerate decelerate: Bool) {
        if (!decelerate) {
            checkAllowScrollToBottom(scrollView: scrollView)
        }
    }
    
    public func scrollViewWillEndDragging(_ scrollView: UIScrollView, withVelocity velocity: CGPoint, targetContentOffset: UnsafeMutablePointer<CGPoint>) {
        // 停止拖拽的瞬间的偏移量计算是否发出请求
        loadHistoryMsgDataIfNeeded(scrollView)
    }
    
    
    private func checkAllowScrollToBottom(scrollView: UIScrollView) {
        let contentOffsetY = scrollView.contentOffset.y
        let visibleHeight = scrollView.bounds.size.height
        let contentHeight = scrollView.contentSize.height
        
        let isAtEnd = contentOffsetY + visibleHeight >= contentHeight
        
        if (isAtEnd) {
            allowScrollToBottom = true
        } else {
            // 键盘弹起的时候要能滚动到底部
            if self.aiInputView.keyboardHeight > 0 {
                self.allowScrollToBottom = true
            } else {
                self.allowScrollToBottom = false
            }
        }
    }
    
    func loadHistoryMsgDataIfNeeded(_ scrollView: UIScrollView) {
        // 当向下拉超过20pt且未在加载中时显示加载指示器
        let pullThreshold: CGFloat = ChatAIHistoryMessageLoadMoreTipHeader.getPullThreshold()
        
        guard self.isTableViewHeaderHistoryLoadMoreTipHeader() == true else {
            return
        }
        if scrollView.contentOffset.y < pullThreshold {
            self.historyMsgLoadMoreTipHeader.showLoading()
            if self.viewModel.isHistoryMsgsDataRequsting == false {
                self.fetchHistoryMsgDatas()
            }
        } else {
            self.historyMsgLoadMoreTipHeader.showTip()
            self.viewModel.isHistoryMsgsDataRequsting = false
        }
    }
    
    
}

extension ChatAIHomeViewControllerV2: UITableViewDataSource {

    public func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return viewModel.dataArray.count
    }
    
    public func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let type = viewModel.dataArray[indexPath.row] else {
            return tableView.dequeueReusableCell(withIdentifier: "UITableViewCell", for: indexPath)
        }

        let cell: ChatAIBaseCell
        switch type {
        case .received(let model):
            if model.message.isNewTopic() {
                
                let newTopicCell = ChatAINewTopicsCell.cell(for: tableView, cellForRowAt: indexPath)
                newTopicCell.scenario_key = viewModel.scenario_key
                let hPadding = 12.0
                let maxWidth = tableView.imy_width - 2.0*hPadding
                newTopicCell.refresh(maxWidth: maxWidth, cellModel: model)
                newTopicCell.topicClickAction = {[weak self] (cellModel, index) in
                    guard let self = self else { return }
                    // 点击问题
                    self.handleClickNewTopics(cellModel: cellModel, index: index)
                }
                
                return newTopicCell
            }
            
            if model.message.contentType == .manualServiceExitTip {
                let cell = ChatAIReceivedTipCell.cell(for: tableView, cellForRowAt: indexPath)
                cell.update(cellModel: model)
                cell.clickTipClourse = { [weak self] cellModel in
                    guard let self = self else { return }
                    
                    self.handleToManual(tipCellModel: cellModel)
                }
                return cell
            }
                
            if model.message.contentType == .historyMsgSpitline {
                let cell = ChatAIReceivedHistoryMessageSplitLineTipCell.cell(for: tableView, cellForRowAt: indexPath)
                return cell
            }
            
            let receivedCell = ChatAIReceivedCell.cell(for: tableView, cellForRowAt: indexPath)
            receivedCell.scenario_key = self.viewModel.scenario_key
                
            // update
            let isShowRefresh = self.viewModel.isShowRefresh(row: indexPath.row)
            receivedCell.updateCellModel(cellModel: model, isShowRefresh: isShowRefresh, row: indexPath.row)
            
            receivedCell.showHtmlImageDetailClourse = {[weak self] url in
                guard let self = self else { return }
                self.showImageDetail(true, url.absoluteString)
            }
                
            // 点击 TutorialQuestions
            receivedCell.qClourse = { [weak self] index in
                guard let self = self else { return }
                self.handleClickTutorialQuestions(cellModel: model, index: index)
            }
            
            // 点击 刷新
            receivedCell.refreshClourse = { [weak self] cellModel in
                guard let self = self else { return }
                self.handleMsgActViewRefresh(indexPath: indexPath, cellModel: cellModel)
            }
            
            // 点击 踩
            receivedCell.like2Clourse = { [weak self] cellModel in
                guard let self = self else { return }
                self.handleMsgActViewLike(indexPath: indexPath, cellModel: cellModel)
            }

            // 点击 复制
            receivedCell.copyClourse = { [weak self] cellModel in
                guard let self = self else { return }
                self.handleMsgActViewCopy(indexPath: indexPath, cellModel: cellModel)
            }
            
            receivedCell.offsetXChangeClosure = { [weak self] cellModel, offsetX in
                guard let self = self else { return }
                self.viewModel.refreshReceivedCellModel(uuid: cellModel.message.uuid, offsetX: offsetX)
            }
            
            receivedCell.unfoldBlock = { [weak self] callbackCellModel in
                guard let self = self, let unwrapCellModel = callbackCellModel else { return }
                self.viewModel.unfoldReceivedCellModel(uuid: unwrapCellModel.message.uuid, isUnfold: true)
                self.tableView.reloadData()
            }
                
            receivedCell.tagOffsetXChangeClosure = { [weak self] cellModel, tagOffsetX in
                guard let self = self else { return }
                self.viewModel.refreshReceivedCellModel(uuid: cellModel.message.uuid, tagOffsetX: tagOffsetX)
            }
            
            // 埋点：流式 eventName 改用 msgId
            receivedCell.imyut_eventInfo.eventName = "ChatAIReceivedCell\(model.message.msgId)\(model.message.isRisk ?? 0)"
            receivedCell.imyut_eventInfo.exposuredBlock = { [weak self] (view, params) in
                guard let self = self else { return }
                self.viewModel.logReceiveExposureIfNeeded(type: type, row: indexPath.row)
            }
                
            receivedCell.didSelectedManulReplyMediaClosure = {[weak self] selectedIdx, items in
                guard let self = self else { return }
                let item = items[selectedIdx]
                
                if item.isVideo {
                    self.showVideoDetail(true, item.videoUrl)
                } else {
                    self.showImageDetail(true, item.url)
                }
            }
            
            cell = receivedCell
        case .receiving(let model):
            let recevingCell = ChatAIReceivingCell.cell(for: tableView, cellForRowAt: indexPath)
            recevingCell.update(cellModel: model)
            recevingCell.textHeightChangeClourse = { [weak self] height in
                guard let self = self else { return }
                self.scrollToBottom(reloadData: true, animated: false)
            }
            // 881 isLoadingPlaceholder 不设置曝光
            if !type.model.isLoadingPlaceholder {
                // eventName 跟 received cell 保持一致，以此确保切换 cell 的时候不会重复上报
                // 881 eventName 改用 msgId
                recevingCell.imyut_eventInfo.eventName = "ChatAIReceivedCell\(model.message.msgId)\(model.message.isRisk ?? 0)"
                recevingCell.imyut_eventInfo.shouldExposureDetectingBlock = { view in
                    // 过滤占位的 loading
                    if type.model.isLoadingPlaceholder {
                        return false
                    }
                    return true
                }
                recevingCell.imyut_eventInfo.exposuredBlock = { [weak self] (view, params) in
                    guard let self = self else { return }
                    self.viewModel.logReceiveExposureIfNeeded(type: type, row: indexPath.row)
                }
            }
            cell = recevingCell
        case .send(let model):
            let sendCell = ChatAISendCell.cell(for: tableView, cellForRowAt: indexPath)
            sendCell.update(cellModel: model)
            sendCell.resendClourse = { [weak self] in
                guard let self = self else { return }
                
                if !IMYNetState.networkEnable() {
                    imy_asyncMainBlock {
                        UIWindow.imy_showTextHUD(0, text: "发送失败，请检查网络后重试")
                    }
                    return;
                }
                
                IMYChatAIHTTPService.sharedInstance().deleteMessage(withUUID: model.message.uuid) { [weak self] dict in
                    guard let self = self else { return }
                    
                    guard model.message.images.isEmpty else {
                        self.reSendImage(model)
                        return
                    }
                    guard model.message.videos.isEmpty else {
                        self.reSendVideo(model)
                        return
                    }
                    
                    
                    _ = self.viewModel.refreshSendCellModelManualRetryCount(uuid: model.message.uuid)
                    
                    // 重新发送也要去掉延展问题
                    self.clearExample()
                    
                    // 删除错误的数据源，滚动到底部
                    self.viewModel.removeFaildDatas(cellModel: model)
                    self.scrollToBottom(reloadData: true, animated: false)
                    
                    // 重新发送
                    self.handleSubmitChat(msg: ["content": model.content])
                    //self.sendChat(msg: ["content": model.content], uuid: model.message.uuid, autoResend: false)
                    
                    self.viewModel.aiFailedBiPostEvent(type: "网络异常")
                } onError: { [weak self] error in
                    guard let self = self else { return }
                }

            }
                
            sendCell.imageTapAction = {[weak self] (isRemoteFile, isVideo, aFileUrl) in
                guard let self = self else { return }
                if isVideo {
                    self.showVideoDetail(isRemoteFile, aFileUrl)
                } else {
                    self.showImageDetail(isRemoteFile, aFileUrl)
                }
                
            }
            sendCell.imyut_eventInfo.eventName = "ChatAISendCell\(model.cellId)"
            sendCell.imyut_eventInfo.exposuredBlock = { [weak self] (view, params) in
                guard let self = self else { return }
                self.viewModel.logSendExposureIfNeeded(type: type, row: indexPath.row)
            }
            cell = sendCell
        case .replyTip(let model):
            let replyTipCell = ChatAIReplyTipCell.cell(for: tableView, cellForRowAt: indexPath)
            replyTipCell.update(cellModel: model, title:assistantTitle)
            cell = replyTipCell
        case .memory(let model):
            let cell = ChatAIMemoryCell.cell(for: tableView, cellForRowAt: indexPath)
            cell.update(cellModel: model, assistantTitle: assistantTitle)
            cell.tapCardClourse = { [weak self] in
                guard let self = self else { return }
                
                if self.viewModel.isEditing {
                    return
                }
                if (!NetStatus.enable) {
                    HUD.show(text: "网络不见了，请检查网络".imy.local())
                } else {
                    let params = ["json":model.memoriesModel.json,"fromView":cell.cardView] as [String : Any]
                    IMYURIManager.share().runAction(withPath: "chat_ai/memories/detail", params: params, info: nil)
                }
                if self.viewModel.getFloorAt(type: type) != nil {
                    let memoriesModel = model.memoriesModel
                }
            }
            cell.imyut_eventInfo.showRadius = 1.0
            cell.imyut_eventInfo.eventName = "ChatAIMemoryCell.\(model.cellId)"
            cell.imyut_eventInfo.exposuredBlock = { [weak self] (view, params) in
                guard let self = self else { return }
                _ = self.viewModel.updateMemoryCellModel(uuid: model.message.uuid, memoryRead: true)
                if self.viewModel.findUnreadMemoryCellModel() == nil {
                    self.hiddeMemoryBadge()
                }
            }
            return cell
        case .example(let model):
            let cell = UITableViewCell(frame: CGRectZero)
            return cell
        }

        // edit
        handleEdit(cell: cell, indexPath: indexPath, type: type.model.msgType)
        
        return cell
    }
    
    public func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        if indexPath.row <= 15 {
            if self.viewModel.dataArray.count >= self.viewModel.limit && self.tableView.contentOffset.y > 0 {
                self.loadMordDatas()
            }
        }
    }
    
}

// MARK: - 埋点

extension ChatAIHomeViewControllerV2 {
   
    override public func ga_appendParams() -> [AnyHashable : Any]? {
        var gaParams: [AnyHashable : Any] = [:]
        
        // 拼接父类已有参数
        if let superClazzGAParams = super.ga_appendParams() {
            superClazzGAParams.forEach { (key: AnyHashable, value: Any) in
                gaParams[key] = value
            }
        }
        
        
        // 890版本新增： 必带参数，上报【各入口的场景key】
        gaParams["info_key"] = self.viewModel.scenario_key
        
        return gaParams
    }
}

// MARK: - 手势

extension ChatAIHomeViewControllerV2: UIGestureRecognizerDelegate {
    
    fileprivate func addGestures() {
        let longGesture = UILongPressGestureRecognizer()
        _ = longGesture.rx.event.takeUntil(rx.deallocated).subscribe { [weak self] gesture in
            guard let self = self else {
                return
            }
            self.handleLongPress(gesture: gesture)
        }
        longGesture.delegate = self;
        view.addGestureRecognizer(longGesture)
        
        let tapGesture = UITapGestureRecognizer()
        tapGesture.delegate = self
//        tapGesture.require(toFail: longGesture)
        _ = tapGesture.rx.event.asControlEvent().takeUntil(rx.deallocated).subscribe { [weak self] gesture in
            guard let self = self else { return }
            
            // 键盘弹起状态, 点击屏幕退出编辑
            if(self.inputViewOffsetY > 0) {
                self.view.endEditing(true)
                return
            }
            
            // 隐藏选中
            if(!self.viewModel.isEditing) {
                return
            }
        }
        view.addGestureRecognizer(tapGesture)
    }
    
    private func handleLongPress(gesture: UIGestureRecognizer) {
        if (gesture.state != .began) {
            return
        }
        
        let location = gesture.location(in: view)
        let point = view.convert(location, to: tableView)
        let cell = tableView.visibleCells.first { $0.frame.contains(point) }
        
        guard let cell = cell, let indexPath = tableView.indexPath(for: cell), !viewModel.isEditing else {
            return
        }
        
        guard let baseCell = cell as? ChatAIBaseCell else {
            return
        }
        
        let chatBackgroundView = baseCell.chatBackgroundView
        let chatBackgroundViewFrame = chatBackgroundView.convert(chatBackgroundView.bounds, to: tableView)
        guard chatBackgroundViewFrame.contains(point) else {
            return
        }
        
        guard let type = viewModel.dataArray[indexPath.row] else {
            return
        }
        
        if type.model.message.exampleModels.count > 0 {
            return
        }
        
        if type.model.message.exampleType != .none {
            return
        }
        
        if type.model.message.contentType == .memory  {
            return
        }
        
        if type.model.message.isImageMessageModel() {
            return
        }
        
        if type.model.message.contentType == .manualReplyTip {
            return
        }
        
        // 目前只有复制，这里也拒绝掉
        if type.model.message.disable_refresh {
            return
        }
        
        if baseCell is ChatAIReceivingCell {
            return
        }

        if !type.model.content.isEmpty {
            self.showActionBubbleView(cell: baseCell)
        }
    }
    
    fileprivate func allowTapAction() -> Bool {

        if (self.aiInputView.isKeyboardShowing) {
            return true
        }
        
        if viewModel.isEditing {
            return false
        } else if let _ = self.selectedTipView {
            return true
        }
        return false
    }
    
    public func gestureRecognizerShouldBegin(_ gestureRecognizer: UIGestureRecognizer) -> Bool {
        
        let location = gestureRecognizer.location(in: self.view)
        let frame = self.aiInputView.frame
        let point = self.view.convert(location, to: self.aiInputView)
        
        if self.aiInputView.isKeyboardShowing {
            let locationY = location.y + self.safeAreaBottom
            let fixLocation = CGPointMake(location.x, locationY)
            if self.aiInputView.frame.contains(fixLocation) {
                return false
            }
        }
        
        if let _ = gestureRecognizer as? UITapGestureRecognizer {
            if allowTapAction() {
                return true
            }
            return false
        }

        return true
    }
    
    // MARK: - 显示长按气泡
    
    private func showActionBubbleView(cell: UITableViewCell) {
        guard let cell = cell as? ChatAIBaseCell, let cellModel = cell.cellModel else {
            return
        }
        
        let bubbleView = ChatAIActionBubbleView()
        bubbleView.clickAction = { [weak self] actionType in
            guard let self = self else { return }
            
            switch actionType {
            case .copy:
                var content = cellModel.markdownContent.string
                    
                // html图文混排类型复制文本
                if cellModel.message.contentType == .htmlText {
                    let textMaxWidth = ChatAIReceivedCell.textMaxWidth(with: false, cellModel: cellModel)
                    let maxImageEdgeValue = textMaxWidth * 0.6
                    let maxImageSize = CGSize(width: maxImageEdgeValue, height: maxImageEdgeValue)
                    
                    let markdownContent = IMYChatAIHtmlView.createAttributedString(
                        withHtml: cellModel.message.content,
                        maxImageSize: maxImageSize
                    )
                    
                    content = markdownContent.string
                }
                if content.isEmpty == false {
                    let pasteboard = UIPasteboard.general
                    pasteboard.string = content
                    HUD.show(text: "复制成功")
                    
                    var params: [String : Any] = [
                        "event": "chat_ai_fz",
                        "action": 2,
                        "info_key": self.viewModel.scenario_key
                    ]
                    IMYChatBIEvent.postGAEvent("event", params: params)
                }
            case .ref:
                print("引用")
            }
        }

        DispatchQueue.main.asyncAfter(deadline: .now()) {
            bubbleView.show(cell: cell, tableView: self.tableView)
        }
    }
    
}
