//
//  ChatAIHistoryMessageLoadMoreTipHeader.swift
//  ChatAI
//
//  Created by meetyou on 2025/7/21.
//

import UIKit
import IMYSwift

@objc(IMYChatAIHistoryMessageLoadMoreTipHeader)
class ChatAIHistoryMessageLoadMoreTipHeader: UIView {
    
    private(set) lazy var activityIndicator: UIActivityIndicatorView = {
        let activityIndicator = UIActivityIndicatorView(style: .gray)
        activityIndicator.backgroundColor = UIColor.clear
        activityIndicator.hidesWhenStopped = true
        activityIndicator.isHidden = true
        return activityIndicator
    }()
    
    private(set) lazy var tipLbl: UILabel = {
        let lbl = UILabel()
        lbl.textAlignment = .center
        lbl.font = UIFont.systemFont(ofSize: 12.0)
        lbl.backgroundColor = .clear
        lbl.text = "下拉查看历史对话".imy.local()
        lbl.textColor = UIColor.imy_color(forKey: "#999999")
        return lbl
    }()
    
    private(set) lazy var leftLineView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.imy_color(forKey: "#CCCCCC")
        return view
    }()
    
    private(set) lazy var rightLineView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.imy_color(forKey: "#CCCCCC")
        return view
    }()
    
    private(set) lazy var tipLblContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        __privateInit()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        __privateInit()
    }
    
    fileprivate func __privateInit() {
        self.addSubview(self.tipLblContainerView)
        self.addSubview(self.activityIndicator)
        
        self.tipLblContainerView.addSubview(self.tipLbl)
        self.tipLblContainerView.addSubview(self.leftLineView)
        self.tipLblContainerView.addSubview(self.rightLineView)
        
        self.activityIndicator.snp.makeConstraints { make in
            make.centerY.centerX.equalTo(self.tipLblContainerView)
            make.width.height.equalTo(20)
        }
        
        self.tipLblContainerView.snp.makeConstraints { make in
            make.leading.trailing.equalTo(self)
            make.height.equalTo(20)
            make.top.equalTo(self).offset(8)
        }
        
        self.tipLbl.snp.makeConstraints { make in
            make.centerX.centerY.equalTo(self.tipLblContainerView)
            make.top.bottom.equalTo(self.tipLblContainerView)
        }
        self.tipLbl.sizeToFit()
        
        self.leftLineView.snp.makeConstraints { make in
            make.centerY.equalTo(self.tipLblContainerView)
            make.leading.equalTo(self.tipLblContainerView).offset(32)
            make.height.equalTo(0.5)
            make.trailing.equalTo(self.tipLbl.snp.leading).offset(-4)
        }
        
        self.rightLineView.snp.makeConstraints { make in
            make.centerY.equalTo(self.tipLblContainerView)
            make.trailing.equalTo(self.tipLblContainerView).offset(-32)
            make.height.equalTo(0.5)
            make.leading.equalTo(self.tipLbl.snp.trailing).offset(4)
        }
    }
    
    func showTip() {
        self.tipLblContainerView.isHidden = false
        self.activityIndicator.isHidden = true
        self.activityIndicator.stopAnimating()
    }
    
    func showLoading() {
        self.tipLblContainerView.isHidden = true
        self.activityIndicator.startAnimating()
        self.activityIndicator.isHidden = false
    }
    
    func hideHeader() {
        self.tipLblContainerView.isHidden = true
        self.activityIndicator.stopAnimating()
        self.activityIndicator.isHidden = true
    }
    
    static func headerHeight() -> CGFloat {
        return 40.0
    }
    
    static func getPullThreshold() -> CGFloat {
        return -16.0
    }
}
