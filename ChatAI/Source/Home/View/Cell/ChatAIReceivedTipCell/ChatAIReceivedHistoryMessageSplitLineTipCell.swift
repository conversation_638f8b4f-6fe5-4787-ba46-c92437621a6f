//
//  ChatAIReceivedHistoryMessageSplitLineTipCell.swift
//  ChatAI
//
//  Created by suxinde on 2025/7/24.
//

import Foundation
import UIKit
import IMYSwift

class ChatAIReceivedHistoryMessageSplitLineTipCell: ChatAIBaseCell {
    
    override func prepareUI() {
        super.prepareUI()
        layoutChatview(type: .recieved)
        chatBackgroundView.isHidden = true
        
        __privateInit()
    }
    
    override class func cellHeight(model: ChatAICellModel, showAppraise: Bool = false) -> CGFloat {
        var cellHeight: CGFloat = 40
        return cellHeight
    }
    
    // MARK: -
    private(set) lazy var tipLbl: UILabel = {
        let lbl = UILabel()
        lbl.textAlignment = .center
        lbl.font = UIFont.systemFont(ofSize: 12.0)
        lbl.backgroundColor = .clear
        lbl.text = "以上为历史对话记录".imy.local()
        lbl.textColor = UIColor.imy_color(forKey: "#999999")
        return lbl
    }()
    
    private(set) lazy var leftLineView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.imy_color(forKey: "#CCCCCC")
        return view
    }()
    
    private(set) lazy var rightLineView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.imy_color(forKey: "#CCCCCC")
        return view
    }()
    
    private(set) lazy var tipLblContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    fileprivate func __privateInit() {
        self.contentView.addSubview(self.tipLblContainerView)
        
        self.tipLblContainerView.addSubview(self.tipLbl)
        self.tipLblContainerView.addSubview(self.leftLineView)
        self.tipLblContainerView.addSubview(self.rightLineView)
        
        self.tipLblContainerView.snp.makeConstraints { make in
            make.leading.trailing.equalTo(self.contentView)
            make.height.equalTo(20)
            make.top.equalTo(self.contentView)
        }
        
        self.tipLbl.snp.makeConstraints { make in
            make.centerX.centerY.equalTo(self.tipLblContainerView)
            make.top.bottom.equalTo(self.tipLblContainerView)
        }
        self.tipLbl.sizeToFit()
        
        self.leftLineView.snp.makeConstraints { make in
            make.centerY.equalTo(self.tipLblContainerView)
            make.leading.equalTo(self.tipLblContainerView).offset(32)
            make.height.equalTo(0.5)
            make.trailing.equalTo(self.tipLbl.snp.leading).offset(-4)
        }
        
        self.rightLineView.snp.makeConstraints { make in
            make.centerY.equalTo(self.tipLblContainerView)
            make.trailing.equalTo(self.tipLblContainerView).offset(-32)
            make.height.equalTo(0.5)
            make.leading.equalTo(self.tipLbl.snp.trailing).offset(4)
        }
    }
}
