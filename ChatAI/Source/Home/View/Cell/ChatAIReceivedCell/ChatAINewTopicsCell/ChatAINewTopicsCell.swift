//
//  ChatAINewTopicsCell.swift
//  Pods
//
//  Created by meetyou on 2024/12/18.
//

import Foundation
import UIKit
import IMYSwift

// 8.84.0新话题需求链接 
// https://www.tapd.meiyou.com/tapd_fe/21039721/story/detail/1121039721001250738?from_iteration_id=1121039721001004606

class ChatAINewTopicsView: UIView {
    
    var scenario_key: String = ""
    
    private var cellModel: ChatAICellModel?
    private var maxWidth: CGFloat = CGFLOAT_MIN
    private var headViewLblViewTag = 992391
    var topicClickAction:((_ cellModel: ChatAICellModel, _ index: Int) -> Void)?
    
    public lazy var headerView: UIView = {
        let view = UIView(frame: CGRectMake(0, 0, IMYSystem.screenWidth(), 0))
        let label = UILabel(frame: CGRectZero)
        label.imy_setTextColor(forKey: kCK_Black_B)
        label.font = UIFont.systemFont(ofSize: 13)
        label.textAlignment = .center
        label.text = "聊聊新话题"
        label.tag = headViewLblViewTag
        label.sizeToFit()
        label.imy_height = 18
        view.addSubview(label)
        label.snp.makeConstraints { make in
            make.leading.equalTo(view)
            make.centerY.equalTo(view).offset(-6)
        }
        
//        let lineColor = UIColor.imy_color(forKey: "DBDBDB")
//        let leftLineView = UIView()
//        view.addSubview(leftLineView)
//        leftLineView.snp.makeConstraints { make in
//            make.left.equalTo(view)
//            make.centerY.equalTo(label)
//            make.height.equalTo(0.5)
//            make.right.equalTo(label.snp.left).offset(-8)
//        }
//        leftLineView.imy_setBackgroundColor(lineColor)
        
//        let rightLineView = UIView()
//        view.addSubview(rightLineView)
//        rightLineView.snp.makeConstraints { make in
//            make.right.equalTo(view)
//            make.centerY.equalTo(label)
//            make.height.equalTo(0.5)
//            make.left.equalTo(label.snp.right).offset(8)
//        }
//        rightLineView.imy_setBackgroundColor(lineColor)
        
        return view
    }()
    
    public lazy var tableView: UITableView = {
        let view = UITableView(frame: .zero, style: .plain)
        view.delegate = self
        view.dataSource = self
        view.imy.register(cell: ChatAINewTopicsItemCell.self)
        view.backgroundColor = .clear
        view.separatorStyle = .none
        view.isScrollEnabled = false
        view.tableHeaderView = self.headerView
        return view
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        self.addSubview(self.tableView)
        self.tableView.snp.makeConstraints { make in
            make.top.equalTo(self.snp.top).offset(0)
            make.leading.equalTo(self.snp.leading).offset(0)
            make.trailing.equalTo(self.snp.trailing).offset(0)
            make.height.equalTo(CGFLOAT_MIN)
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: Private
    class func calHeight(topics: [ChatAINewTopicModel], textMaxWidth: CGFloat) -> CGFloat {
        var h = 0.0
        for topic in topics {
            h = h + ChatAINewTopicsItemCell.cellHeight(with: topic, textMaxWidth: textMaxWidth - 8)
        }
        return h
    }
    
    class func headerHeight() -> CGFloat {
       return (26+12)
    }
    
    func biEvent(action: NSInteger, model: ChatAINewTopicModel, cellModel: ChatAICellModel) {
      
    }
    
    // MARK: Public
    
    class func viewHeight(maxWidth: CGFloat, cellModel: ChatAICellModel) -> CGFloat {
        var h = ChatAINewTopicsView.calHeight(topics: cellModel.message.new_topic, textMaxWidth:maxWidth)
        
        if h > 0 {
            h = h + (headerHeight())
        }
        
        return h
    }
    
    func refresh(maxWidth: CGFloat, cellModel: ChatAICellModel) {
        self.maxWidth = maxWidth
        self.cellModel = cellModel
        
        // 头部
        self.headerView.frame = CGRectMake(0, 0, maxWidth, Self.headerHeight())
        if let lbl = self.headerView.viewWithTag(headViewLblViewTag) as? UILabel {
            lbl.text = "聊聊新话题"
            lbl.sizeToFit()
            lbl.imy_height = 18
        }
        
        // 话题列表
        self.tableView.isHidden = cellModel.message.new_topic.count == 0
        let height = Self.viewHeight(maxWidth: maxWidth, cellModel: cellModel)
        self.tableView.snp.updateConstraints { make in
            make.height.equalTo(height)
        }
        
        self.tableView.reloadData()
    }
    
}

extension ChatAINewTopicsView: UITableViewDelegate, UITableViewDataSource {
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return self.cellModel?.message.new_topic.count ?? 0
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        if let model = self.cellModel?.message.new_topic[indexPath.row] {
            let height = ChatAINewTopicsItemCell.cellHeight(with: model, textMaxWidth: maxWidth - 32)
            return height
        }
        return CGFLOAT_MIN
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell: ChatAINewTopicsItemCell = tableView.dequeueReusableCell(withIdentifier: "ChatAINewTopicsItemCell") as! ChatAINewTopicsItemCell
        if let model = self.cellModel?.message.new_topic[indexPath.row] {
            cell.setup(topicModel: model, textMaxWidth: maxWidth - 32)
            
            // 统计埋点
            cell.imyut_eventInfo.eventName = "ChatAINewTopicCell-\(model.content)-\(indexPath.row)-\(self.cellModel?.message.uuid ?? "")"
            cell.imyut_eventInfo.exposuredBlock = { [weak self] (view, params) in
                guard let self = self, let cellModel = self.cellModel else { return }
                
                // 只上报一次
                if !cellModel.isFromDB {
                    self.biXHTEvent(action: 1, newTopicModel: model, cellModel: cellModel)
                }
            }
        }
        return cell
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        guard let cellModel = cellModel else {
            return
        }
        let model = cellModel.message.new_topic[indexPath.row]
        self.topicClickAction?(cellModel, indexPath.row)
        
        self.biXHTEvent(action: 2, newTopicModel: model, cellModel: cellModel)
    }
    
    // MARK: - 埋点
    
    func biXHTEvent(action: NSInteger, newTopicModel: ChatAINewTopicModel, cellModel: ChatAICellModel) {
        var dict:JSONDictionary = [:]
        
        dict["action"] = action
        
        dict["event"] = "chat_ai_xht"
        
        if !newTopicModel.content.isEmpty {
            dict["public_type"] = newTopicModel.content
        }
        
        if self.scenario_key.isEmpty == false {
            dict["info_key"] = self.scenario_key
        }
        
        // 886 增加 duration_period_yl
        let stage_type = IMYURIManager.share().runActionAndSyncResult(withPath: "chatAI/stageType", params: [:]) as? NSInteger
        if let stage_type = stage_type, stage_type > 0  {
            dict["duration_period_yl"] = stage_type
        }
        
        IMYChatBIEvent.postGAEvent("event", params: dict)
    }
    
}

class ChatAINewTopicsCell: ChatAIBaseCell {
    
    var scenario_key: String = "" {
        didSet {
            topicsView.scenario_key = scenario_key
        }
    }
    
    lazy var topicsView: ChatAINewTopicsView = {
        let v = ChatAINewTopicsView()
        addSubview(v)
        return v
    }()
        
    var topicClickAction:((_ cellModel: ChatAICellModel, _ index: Int) -> Void)?
    
    class func cellHeight(maxWidth: CGFloat, cellModel: ChatAICellModel) -> CGFloat {
        return ChatAINewTopicsView.viewHeight(maxWidth: maxWidth, cellModel: cellModel)
    }
    
    func refresh(maxWidth: CGFloat, cellModel: ChatAICellModel) {
        topicsView.snp.remakeConstraints { make in
            make.centerX.top.bottom.equalTo(self)
            make.width.equalTo(maxWidth)
        }
        topicsView.refresh(maxWidth: maxWidth, cellModel: cellModel)
    }
    
    override func prepareUI() {
        super.prepareUI()
        
        topicsView.topicClickAction = {[weak self] (cellModel, index) in
            self?.topicClickAction?(cellModel, index)
        }
    }
    
}
