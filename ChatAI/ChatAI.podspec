#
#  Be sure to run `pod spec lint ChatAI.podspec' to ensure this is a
#  valid spec and to remove all comments including this before submitting the spec.
#
#  To learn more about Podspec attributes see https://guides.cocoapods.org/syntax/podspec.html
#  To see working Podspecs in the CocoaPods repo see https://github.com/CocoaPods/Specs/
#

Pod::Spec.new do |spec|

  # ―――  Spec Metadata  ―――――――――――――――――――――――――――――――――――――――――――――――――――――――――― #
  #
  #  These will help people to find your libray, and whilst it
  #  can feel like a chore to fill in it's definitely to your advantage. The
  #  summary should be tweet-length, and the description more in depth.
  #

  spec.name         = "ChatAI"
  spec.version      = "**********"
  spec.summary      = "Women's Health assiant"
  spec.description  = "女性健康助手"
  spec.homepage     = "https://gitlab.meiyou.com/iOS/ChatAI"
  spec.license      = "MIT"
  spec.author       = { "lxb" => "<EMAIL>" }
  spec.platform     = :ios, "12.0"

  spec.source = { :git => '*********************:iOS/ChatAI.git', :branch => 'release-8.94.0' }
  spec.requires_arc = true
  spec.source_files = 'Source/**/*.{h,m,swift}'
  spec.resources = 'Resource/Resource/*.{db,xib,json,png,jpg,gif,js,plist,txt,wav,pag,apng}',
		  'Resource/Bundles/*.{bundle,xcassets}'

  spec.dependency 'IMYSwift'

  spec.dependency 'IMYMarkdown'
  
end
