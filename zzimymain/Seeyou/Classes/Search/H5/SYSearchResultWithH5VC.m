//
//  SYSearchResultWithH5VC.m
//  Seeyou
//
//  Created by Liya on 2018/5/2.
//  Copyright © 2018年 linggan. All rights reserved.
//

#import "SYSearchResultWithH5VC.h"
#import "SYGlobalMacros.h"
#import "SYHomeSearchAssociateVC.h"
#import "SYH5SearchManager.h"
#import "IMYNAInputSearchBar.h"
#import "IMYNASearchHomeVC.h"
#import "IMYNASearchHistoryModel.h"

#define SearchResultH5URL @"https://news-node.seeyouyima.com/searchs/lists?mywtb_name=search-list"

static NSString * const IMYSearchResultOnAssociateChangeKey = @"IMYSearchResultOnAssociateChangeKey";

@interface SYSearchResultWithH5VC () <IMYVKWebViewControllerDelegate, UIScrollViewDelegate>

@property (nonatomic, strong) IMYNAInputSearchBar *searchBar;

@property (nonatomic, strong) IMYVKWebViewController *webVC;
@property (nonatomic, strong) SYHomeSearchAssociateVC *associateVC;

@property (nonatomic, strong) IMYCaptionView *loadingView;

@property (nonatomic, copy) NSString *keyword;
@property (nonatomic, assign) NSInteger current_tab;
@property (nonatomic, assign) NSInteger forum_id;
@property (nonatomic, assign) SYSearchFromType from;
@property (nonatomic, assign) SYSearchFromType search_from;
@property (nonatomic, assign) IMYSearchEventTrackType pos_id;
@property (nonatomic, assign) SYSearchWordType words_type;
@property (nonatomic, assign) SYSearchBISource bi_source;
@property (nonatomic, assign) NSInteger info_type;

@property (nonatomic, copy) NSString *location;
@property (nonatomic, assign) NSInteger isFromlocation; //记录最开始时是否从推荐词到的这个界面
@property (nonatomic, copy) NSString *searchWord;       //为了与线上版本统一;一个遗留问题，不确定要改

// 搜索标识key
@property (nonatomic, copy) NSString *search_key;


//  这两个值为资讯标签、相关搜索点击携带的参数
@property (nonatomic, copy) NSArray *words;
@property (nonatomic, assign) NSInteger location_index;

@property (nonatomic, copy) NSString *lastTextFiledText;
@property (nonatomic, assign) BOOL haveChangedTab;

@property (nonatomic, copy) NSString *checkRepeatKeyword; // 防止短时间内 相同词多次 搜索

// 是否使用新样式（带返回按钮、右边是搜索按钮）
@property (nonatomic, assign) BOOL usingNewSearchStyle;

// 搜索栏样式值（0-老样式，1-实验组1，2-实验组2）
@property (nonatomic, assign) NSInteger searchBarStyleValue;

// 输入框清空按钮是否曝光过
@property (nonatomic, assign) BOOL isExposuredClearButton;

@end

@implementation SYSearchResultWithH5VC

+ (void)prefetchSearchResultResources {
    imy_asyncBlock(1, ^{
        [[IMYURIManager shareURIManager] runActionWithPath:@"mywtb/prefetch" params:@{@"url": SearchResultH5URL} info:nil];
    });
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.navigationBarHidden = YES;
    
    // 是否使用新样式实验
    self.usingNewSearchStyle = [[[IMYABTestManager sharedInstance] experimentForKey:@"seach_sug_btn"].vars boolForKey:@"btn"];

    // 搜索栏样式实验配置
    // 值说明：0-老样式，1-实验组1样式（左返回+右搜索黑色），2-实验组2样式（左返回+右搜索渐变色）
    IMYABTestExperiment *searchStyleExp = [[IMYABTestManager sharedInstance] experimentForKey:@"btnuniify"];
    self.searchBarStyleValue = [searchStyleExp.vars integerForKey:@"btnuniify"];

    
    // 搜索唯一性标识
    if (!self.search_key.length) {
        self.search_key = [NSString stringWithFormat:@"%@_%.0lf", [IMYPublicAppHelper shareAppHelper].userid, IMYDateTimeIntervalSince1970() * 1000];
    }
    
    // 2秒后，重置搜索拦截
    self.checkRepeatKeyword = self.keyword;
    @weakify(self);
    imy_asyncMainBlock(2, ^{
        @strongify(self);
        self.checkRepeatKeyword = nil;
    });
    
    // 加入搜索栏
    [self.view addSubview:self.searchBar];
    
    if (self.from == SYSearchFromTypeTTQ) { //她她圈搜索结果页自动定位到综合tab实验
        // 7.9.8实验固化【https://www.tapd.cn/34674824/prong/stories/view/1134674824001085307】
        self.current_tab = SYSearchFromTypeAll;
    }
    
    self.webVC.view.frame = CGRectMake(0, self.searchBar.imy_bottom, SCREEN_WIDTH, SCREEN_HEIGHT - self.searchBar.imy_bottom - SCREEN_STATUSBAR_HEIGHT);
    self.webVC.webView.scrollView.delegate = self;
    [self.view addSubview:self.webVC.view];
    [self addChildViewController:self.webVC];
    
    self.loadingView = [IMYCaptionView addToView:self.webVC.view show:YES];
    [self.loadingView setRetryBlock:^{
        @strongify(self);
        [self searchAction:self.searchBar.textField];
        [self runningTimeoutChecker];
    }];
    [self.loadingView imy_setBackgroundColor:kIMY_BG];
    
    //超时检测
    [self runningTimeoutChecker];
    
    // 注入JS API
    [self webHandler];

    // 8.95：根据启动协议 search_home_type=1 直接写入历史记录
    [self tryAddHistoryOnEnterIfNeeded];
}

- (void)runningTimeoutChecker {
    @weakify(self);
    NSString *queueKey = [NSString stringWithFormat:@"search_h5_retry_%p", self];
    [NSObject imy_asyncBlock:^{
        @strongify(self);
        if (self.loadingView.state == IMYCaptionViewStateLoading) {
            // 12s 后还处于加载状态，直接变为重试
            self.loadingView.state = IMYCaptionViewStateRetry;
        }
    } onQueue:dispatch_get_main_queue() afterSecond:12 forKey:queueKey];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    if (imy_isNotBlankString(self.location) || self.words_type == SYSearchWordTypeRecommend) {
        self.isFromlocation = 1;
    }
    if (self.isFromlocation != 1) {
        self.associateVC.originalKeyword = self.searchWord;
    }
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    [self onAssociateTextHidden];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    // 判断是否需要修正页面堆栈
    [self refitSearchViewControllers];
    // 修正关联词UI
    self.associateVC.view.frame = self.webVC.view.frame;
    [self onAssociateTextHidden];
}

#pragma mark - getter & setter

- (SYHomeSearchAssociateVC *)associateVC {
    if (!_associateVC) {
        _associateVC = [[SYHomeSearchAssociateVC alloc] init];
        _associateVC.pos_id = self.pos_id;
        _associateVC.from = self.from;
        _associateVC.search_key = self.search_key;
        _associateVC.info_type = self.info_type;
        [self addChildViewController:_associateVC];
        
        @weakify(self);
        _associateVC.scrollBlock = ^{
            @strongify(self);
            [self onEndEditingAction];
            // 输入框无内容
            if (!self.searchBar.textField.text.length) {
                [self onAssociateTextHidden];
            }
        };
        
        _associateVC.seletedCellBlock = ^(NSString *keyword, NSUInteger index) {
            @strongify(self);
            self.searchBar.textField.text = [keyword imy_trimString];
            
            NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
            gaParams[@"func"] = @(IMYSearchEventTrackFuncTypeAssociateClick);
            gaParams[@"pos_id"] = @(self.pos_id);
            gaParams[@"key"] = keyword;
            gaParams[@"words_type"] = @(IMYSearchWordsTypeLenovo);
            gaParams[@"location_index"] = @(index + 1);
            gaParams[@"searcy_key"] = self.search_key;
            gaParams[@"info_type"] = @(self.info_type);
            gaParams[@"suggest_type"] = @1;
            [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];
            if (self.associateVC.changeKeyWordTag == 1) {
                self.associateVC.originalKeyword = self.searchWord;
            }
            [self searchAction:nil];
            // 隐藏联想词，显示内容H5
            [self onAssociateTextHidden];
        };
        [self.view addSubview:_associateVC.view];
    }
    return _associateVC;
}

- (IMYNAInputSearchBar *)searchBar {
    if (!_searchBar) {
        _searchBar = [IMYNAInputSearchBar new];

        // 根据实验值配置搜索栏样式
        if (self.searchBarStyleValue == 1 || self.searchBarStyleValue == 2) {
            // 实验组1和2：使用新的样式配置
            [_searchBar setupWithExperimentGroup:self.searchBarStyleValue];
        } else if (!self.usingNewSearchStyle) {
            // 老样式：隐藏左按钮
            [_searchBar usingHideLeftButtonStyle];
        }
        _searchBar.imy_width = SCREEN_WIDTH;
        [_searchBar.textField addTarget:self action:@selector(searchAction:) forControlEvents:UIControlEventEditingDidEndOnExit];
        [_searchBar.textField addTarget:self action:@selector(textFieldTextChangeAction:) forControlEvents:UIControlEventEditingChanged];
        [_searchBar.textField addTarget:self action:@selector(textFieldDidBeginEditngAction:) forControlEvents:UIControlEventEditingDidBegin];
        [_searchBar.textField addTarget:self action:@selector(textFieldDidEnd:) forControlEvents:UIControlEventEditingDidEnd];
        @weakify(self);
        _searchBar.onTextFieldClearBlock = ^(UITextField *textField) {
            @strongify(self);
            // 当做取消按钮，需要延迟到下个主线程处理
            if (!textField.isFirstResponder) {
                [self onClearTextAction];
            }
            // 清空按钮点击
            NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
            gaParams[@"func"] = @(33);
            gaParams[@"pos_id"] = @(self.pos_id);
            gaParams[@"info_type"] = @(self.info_type);
            [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];
            // 清空按钮曝光，一个生命周期只曝光一次
            if (!self.isExposuredClearButton) {
                self.isExposuredClearButton = YES;
                NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
                gaParams[@"func"] = @(32);
                gaParams[@"pos_id"] = @(self.pos_id);
                gaParams[@"info_type"] = @(self.info_type);
                [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];
            }
            // 重置清空按钮的曝光判断
            self.isExposuredClearButton = NO;
        };
        _searchBar.onRightButtonClick = ^{
            @strongify(self);
            if (self.searchBarStyleValue == 1 || self.searchBarStyleValue == 2) {
                // 实验组1和2：右按钮是搜索
                [self searchAction:self.searchBar.textField];
            } else if (self.usingNewSearchStyle) {
                // 其他新样式：右按钮是搜索
                [self searchAction:self.searchBar.textField];
            } else {
                // 老样式：右按钮是取消
                [self onCancelButtonAction];
            }
        };
        _searchBar.onLeftButtonClick = ^{
            @strongify(self);
            [self onCancelButtonAction];
        };
        _searchBar.textField.text = self.keyword;
    }
    return _searchBar;
}

- (IMYVKWebViewController *)webVC {
    if (!_webVC) {
        _webVC = [IMYVKWebViewController webWithURLString:[self urlString]];
        _webVC.delegate = self;
    }
    return _webVC;
}

- (SYSearchFromType)from {
    if (_from == 0 && _search_from > 0) {
        _from = _search_from;
    }
    return _from;
}

#pragma mark - webView's configue

- (NSString *)urlString {
    NSString *baseUrl = SearchResultH5URL;
    
    NSMutableDictionary *urlParams = [self.fromURI.params ?: @{} mutableCopy];
    [urlParams removeObjectForKey:@"location"];
    [urlParams removeObjectForKey:@"keyword"];
    
    // 添加H5需要的参数
    urlParams[@"search_keyword"] = self.keyword;
    urlParams[@"current_tab"] = @(self.current_tab);
    urlParams[@"pos_id"] = @(self.pos_id);
    urlParams[@"words_type"] = @(self.words_type);
    urlParams[@"search_from"] = @(self.from);
    urlParams[@"tab_have_changed"] = @(self.haveChangedTab?1:0);
    urlParams[@"search_key"] = self.search_key;
    urlParams[@"themeid"] = @(0);
    urlParams[@"info_type"] = @(self.info_type);
    if (self.forum_id > 0) {
        urlParams[@"forum_id"] = @(self.forum_id);
    }
    if (self.words.count > 0) {
        NSString *words = [self.words imy_jsonString];
        urlParams[@"words"] = words;
        urlParams[@"location_index"] = @(self.location_index);
        self.words = nil; //  这边涉及到埋点上报，然后只上报一次，所以先清空
    }
    if (self.location.length > 0) {
        urlParams[@"location"] = self.location;
    }
    NSString * const urlString = [baseUrl imy_appendingURLParams:urlParams];
    return urlString;
}

- (void)webHandler {
    @weakify(self);
    [IMYJSBinding setHandler:^(IMYJSMessage *message, id<IMYJSEvaluatorProtocol> evaluator) {
        imy_asyncMainExecuteBlock(^{
            @strongify(self);
            NSString *html = [message.data[@"keyword"] imy_URLDecode];
            id tmp_current_tab = message.data[@"current_tab"];
            if (tmp_current_tab) {
                if (self.current_tab != [tmp_current_tab integerValue]) {
                    self.haveChangedTab = YES;
                }
                self.current_tab = [tmp_current_tab integerValue];
            }
            if (html && ![self.keyword isEqualToString:html]) {
                [self onEndEditingAction];
                self.keyword = html;
                self.searchBar.textField.text = self.keyword;
            }
        });
    } forApi:@"webSearch/getKeyWord" onlyWebView:self.webVC.webView];
    
    [IMYJSBinding setHandler:^(IMYJSMessage *message, id<IMYJSEvaluatorProtocol> evaluator) {
        imy_asyncMainExecuteBlock(^{
            @strongify(self);
            // 增加 alpha 动画
            const NSUInteger state = [message.data[@"state"] integerValue];
            
            POPSpringAnimation *alphaAnim = [POPSpringAnimation animationWithPropertyNamed:kPOPViewAlpha];
            if (state == IMYCaptionViewStateHidden) {
                alphaAnim.toValue = @(0);
            } else {
                alphaAnim.toValue = @(1.0);
            }
            alphaAnim.dynamicsFriction = 45;
            alphaAnim.dynamicsTension = 850;
            alphaAnim.completionBlock = ^(POPAnimation *anim, BOOL finished) {
                @strongify(self);
                // 文档：https://apidoc.seeyouyima.com/uri-docs/#/docList/5b727946ef76403935805dca
                self.loadingView.state = (state == 5 ? IMYCaptionViewStateRetry : state);
            };
            [self.loadingView pop_addAnimation:alphaAnim forKey:@"alpha"];
        });
    } forApi:@"web/pure/changeLoadingState" onlyWebView:self.webVC.webView.realWebView];
}

#pragma mark - 8.95 历史写入（协议触发）

/// 8.95：当入口协议包含 search_home_type=1 时，将关键词加入搜索历史。
- (void)tryAddHistoryOnEnterIfNeeded {
    id value = self.fromURI.params[@"search_home_type"];
    NSInteger searchHomeType = 0;
    if ([value respondsToSelector:@selector(integerValue)]) {
        searchHomeType = [value integerValue];
    }
    if (searchHomeType != 1) {
        return;
    }

    // 关键词优先取入口参数，其次回退到当前 VC 的 keyword
    id keywordParam = self.fromURI.params[@"keyword"];
    NSString *keyword = nil;
    if ([keywordParam isKindOfClass:NSString.class]) {
        keyword = [(NSString *)keywordParam imy_trimString];
    }
    if (keyword.length == 0) {
        keyword = [self.keyword imy_trimString];
    }
    if (!imy_isNotBlankString(keyword)) {
        return;
    }

    // 8.95：保存为高亮词（来源由 search_home_type=1 触发）
    NSDictionary *highlightSource = @{@"highlight": @(1)};
    IMYNASearchHistoryModel *historyModel = [IMYNASearchHistoryModel modelWithKeyword:keyword sourceModel:highlightSource];
    // 8.95：调用首页暴露的静态方法，保持去重与置顶规则一致
    [IMYNASearchHomeVC saveHistoryModelToTop:historyModel];
}

#pragma mark - search's action

- (void)searchAction:(UITextField *)sender {
    [NSObject imy_cancelBlockForKey:IMYSearchResultOnAssociateChangeKey];
    NSString *searchText = [self.searchBar.textField.text imy_trimString];
    if (imy_isNotBlankString(searchText)) {
        // 取消键盘
        [self onEndEditingAction];
        // 过滤短时间内相同词的重复搜索
        if ([self.checkRepeatKeyword isEqualToString:searchText]) {
            [self onAssociateTextHidden];
            return;
        }
        // 2秒后，重置搜索拦截
        self.checkRepeatKeyword = searchText;
        @weakify(self);
        imy_asyncMainBlock(2, ^{
            @strongify(self);
            self.checkRepeatKeyword = nil;
        });
        
        if (self.usingNewSearchStyle && ![self.keyword isEqualToString:searchText]) {
            // 命中新实验，搜索词不同，新开一个VC
            [UIView performWithoutAnimation:^{
                [self searchActionWithTextV2:searchText isInput:(sender != nil)];
            }];
        } else {
            [self searchActionWithText:searchText isInput:(sender != nil)];
        }

        // 8.95：当来源包含 search_home_type=1，结果页搜索栏触发搜索时也写入历史（与首页规则一致）
        {
            id value = self.fromURI.params[@"search_home_type"];
            NSInteger searchHomeType = 0;
            if ([value respondsToSelector:@selector(integerValue)]) {
                searchHomeType = [value integerValue];
            }
            if (searchHomeType == 1) {
                NSDictionary *highlightSource = @{@"highlight": @(1)};
                IMYNASearchHistoryModel *historyModel = [IMYNASearchHistoryModel modelWithKeyword:searchText sourceModel:highlightSource];
                [IMYNASearchHomeVC saveHistoryModelToTop:historyModel];
            }
        }
        // 更新首页搜索词
        [[NSNotificationCenter defaultCenter] postNotificationName:IMYSearchH5KeywordChangedNotification object:searchText];
    } else {
        [UIWindow imy_showTextHUD:IMYString(@"请输入关键字")];
        self.searchBar.textField.text = @"";
    }
    
    // 重新开始结果检测，防止页面一直处于loading 状态
    [self runningTimeoutChecker];
}

- (void)searchActionWithText:(NSString * const)searchText isInput:(BOOL const)isInput {
    // 原地刷新
    if (isInput) {
        self.words_type = SYSearchWordTypeUserInput;
        self.location = @"搜索栏";
    } else {
        self.words_type = SYSearchWordTypeAssociate;
        self.location = @"联想词";
    }
    self.associateVC.changeKeyWordTag = 0;
    self.keyword = searchText;
    [self.webVC setUrlString:[self urlString]];
    [self onAssociateTextHidden];
}

- (void)searchActionWithTextV2:(NSString * const)searchText isInput:(BOOL const)isInput {
    // 一个新的搜索结果页
    SYSearchResultWithH5VC *resultVC = [[SYSearchResultWithH5VC alloc] init];
    [resultVC imy_setPropertyWithDictionary:self.fromURI.params];
    resultVC.fromURI = self.fromURI;
    resultVC.keyword = searchText;
    if (isInput) {
        resultVC.words_type = SYSearchWordTypeUserInput;
        resultVC.location = @"搜索栏";
    } else {
        resultVC.words_type = SYSearchWordTypeAssociate;
        resultVC.location = @"联想词";
    }
    resultVC.view.frame = self.view.frame;
    resultVC.searchBar.textField.text = searchText;
    [resultVC.view layoutIfNeeded];
    
    UINavigationController * const navVC = self.navigationController;
    [navVC pushViewController:resultVC animated:NO];
    
    // 隐藏关联词并还原搜索词
    self.associateVC.changeKeyWordTag = 0;
    self.searchBar.textField.text = self.keyword;
    [self onAssociateTextHidden];
}

- (void)onEndEditingAction {
    [self.searchBar.textField resignFirstResponder];
    [self.webVC.webView endEditing:YES];
}

#pragma mark - native search

- (void)onAssociateWillChangeText:(NSString *)text {
    [self onAssociateTextShowing];
    if (imy_isNotBlankString(text)) {
        [self.associateVC reqAssociateWords:text];
    } else {
        [self.associateVC clearData];
    }
}

- (void)onAssociateTextShowing {
    if (self.isViewWillDisappear) {
        // 页面非显示状态
        return;
    }
    if (self.associateVC.view.hidden) {
        [self.webVC.webView sendEvent:@"onSearchSuggestionShow"];
        self.associateVC.view.hidden = NO;
        self.webVC.view.hidden = YES;
    }
}

- (void)onAssociateTextHidden {
    if (!self.associateVC.view.hidden) {
        self.associateVC.view.hidden = YES;
        self.webVC.view.hidden = NO;
    }
}

#pragma mark - UITextField Delegate

- (void)textFieldTextChangeAction:(UITextField *)textField {
    NSString * const inputText = textField.text ?: @"";
    if (inputText.length == 0) {
        // 用户手动清理所有文本，返回上一页
        if (textField.isFirstResponder) {
            [self onClearTextAction];
        }
    } else if (![self.lastTextFiledText isEqualToString:inputText]) {
        self.lastTextFiledText = inputText;
        // 变化最小间隔，再执行搜索事件，请求联想词
        @weakify(self);
        [NSObject imy_asyncBlock:^{
            @strongify(self);
            [self onAssociateWillChangeText:inputText];
            if ([self.associateVC shouldChangeOriginalAssociate]) {
                self.associateVC.originalKeyword = inputText;
                self.searchWord = inputText;
            }
        } onQueue:dispatch_get_main_queue() afterSecond:0.1 forKey:IMYSearchResultOnAssociateChangeKey];
    }
    if (!self.isExposuredClearButton && inputText.length > 0) {
        // 清空按钮曝光，一个生命周期只曝光一次
        self.isExposuredClearButton = YES;
        NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
        gaParams[@"func"] = @(32);
        gaParams[@"pos_id"] = @(self.pos_id);
        gaParams[@"info_type"] = @(self.info_type);
        [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];
    }
    // 无内容情况下，重置清空按钮曝光
    if (!inputText.length) {
        self.isExposuredClearButton = NO;
    }
}

- (void)textFieldDidBeginEditngAction:(UITextField *)textField {
    // H5 事件，可以直接进行发送
    // 变化最小时间间隔，再执行搜索事件，请求联想词
    NSString *text = textField.text;
    @weakify(self);
    [NSObject imy_asyncBlock:^{
        @strongify(self);
        if ([self.lastTextFiledText isEqualToString:text]) {
            [self onAssociateTextShowing];
        } else {
            [self.associateVC clearData];
            self.lastTextFiledText = text;
            [self onAssociateWillChangeText:text];
        }
    } onQueue:dispatch_get_main_queue() afterSecond:0 forKey:IMYSearchResultOnAssociateChangeKey];
}

- (void)textFieldDidEnd:(UITextField *)textField {
    // 输入框无焦点，进行曝光
}

- (void)onCancelButtonAction {
    // 取消已有的操作
    [NSObject imy_cancelBlockForKey:IMYSearchResultOnAssociateChangeKey];
    if (self.searchBar.textField.isFirstResponder) {
        // 取消焦点，并恢复文本
        [self onEndEditingAction];
        [self onAssociateTextHidden];
        self.searchBar.textField.text = self.keyword;
    } else {
        // 返回上个页面
        [self onEndEditingAction];
        [self onGobackAction:NO];
    }
}

- (void)onClearTextAction {
    // 界面不可操作
    if (!self.navigationController.view.userInteractionEnabled) {
        return;
    }
    // 取消已有的操作
    [NSObject imy_cancelBlockForKey:IMYSearchResultOnAssociateChangeKey];
    if (self.usingNewSearchStyle) {
        // 走实验：需要构建搜索首页进行替换
        [self onReplaceHomeAction];
    } else {
        // 直接退出
        [self onGobackAction:YES];
    }
}

- (void)refitSearchViewControllers {
    // 拦截用户点击
    UINavigationController * const navVC = self.navigationController;
    navVC.view.userInteractionEnabled = NO;
    imy_asyncMainBlock(0.2, ^{
        // 搜索结果页只保留两个
        NSMutableArray *allVCs = [navVC.viewControllers mutableCopy];
        NSInteger resultPageCount = 0;
        for (UIViewController *vc in allVCs.reverseObjectEnumerator) {
            if ([vc isKindOfClass:SYSearchResultWithH5VC.class]) {
                resultPageCount += 1;
                if (resultPageCount > 2) {
                    [allVCs removeObject:vc];
                }
            }
        }
        NSInteger homePageCount = 0;
        for (UIViewController *vc in allVCs.copy) {
            if ([vc isKindOfClass:IMYNASearchHomeVC.class]) {
                homePageCount += 1;
                if (homePageCount > 1) {
                    [allVCs removeObject:vc];
                }
            }
        }
        if (allVCs.count != navVC.viewControllers.count) {
            [navVC setViewControllers:allVCs animated:NO];
        }
        // 放开用户点击
        imy_asyncMainBlock(^{
            navVC.view.userInteractionEnabled = YES;
        });
    });
}

- (void)onReplaceHomeAction {
    // 保持键盘不下降，减少动画波动
    NSString *keyword = self.keyword;
    IMYNAInputSearchBar *holdSearchBar = self.searchBar;
    UIView *holdParentBar = holdSearchBar.superview;
    if (holdParentBar == holdSearchBar.window) {
        // 还在动画中，不执行操作
        return;
    }
    holdSearchBar.imy_top = [IMYSystem screenStatusBarHeight];
    [holdSearchBar.window addSubview:holdSearchBar];
    
    // 生成一个新搜索首页
    IMYNASearchHomeVC *searchHomeVC = [IMYNASearchHomeVC new];
    searchHomeVC.from = self.from;
    searchHomeVC.search_key = self.search_key;
    searchHomeVC.pos_id = self.pos_id;
    searchHomeVC.forum_id = self.forum_id;
    searchHomeVC.current_tab = self.current_tab;
    searchHomeVC.bi_source = self.bi_source;
    searchHomeVC.info_type = self.info_type;
    searchHomeVC.fromURI = self.fromURI;
    
    // 动画期间暂停用户交互
    UINavigationController * const navVC = self.navigationController;
    navVC.view.userInteractionEnabled = NO;
    [UIView transitionWithView:navVC.view
                      duration:0.1f
                       options:UIViewAnimationOptionTransitionCrossDissolve
                    animations:^{
        [navVC pushViewController:searchHomeVC animated:NO];
    } completion:^(BOOL finished){
        //恢复用户交互
        navVC.view.userInteractionEnabled = YES;
        imy_asyncMainBlock(0.2, ^{
            holdSearchBar.imy_top = 0;
            holdSearchBar.textField.text = keyword;
            [holdParentBar addSubview:holdSearchBar];
        });
    }];
    
    // 隐藏关联词
    [self onAssociateTextHidden];
}

- (void)onGobackAction:(BOOL const)isInputing {
    // 清空首页搜索词
    [[NSNotificationCenter defaultCenter] postNotificationName:IMYSearchH5ResultClearNotification object:@(isInputing)];
    if (self.imy_presentIn) {
        [self dismissViewControllerAnimated:YES completion:nil];
    } else {
        // 动画期间暂停用户交互
        UINavigationController * const navVC = self.navigationController;
        navVC.view.userInteractionEnabled = NO;
        [UIView transitionWithView:navVC.view
                          duration:0.1f
                           options:UIViewAnimationOptionTransitionCrossDissolve
                        animations:^{
            [navVC popViewControllerAnimated:NO];
        } completion:^(BOOL finished){
            //恢复用户交互
            navVC.view.userInteractionEnabled = YES;
        }];
    }
}

#pragma mark - IMYVKWebViewControllerDelegate

- (BOOL)imy_webVC:(IMYVKWebViewController *)webVC shouldStartURLRequest:(NSURLRequest *)request {
    if([request imy_isMainFrameRequest]) {
        self.loadingView.state = IMYCaptionViewStateLoading;
    }
    return YES;
}

- (void)imy_webVCDidLoadFinished:(IMYVKWebViewController *)webVC {
    
}

- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView {
    [self onEndEditingAction];
}

#pragma mark - Page GA Params

- (NSDictionary *)ga_appendParams {
    NSMutableDictionary *params = [NSMutableDictionary dictionaryWithDictionary:super.ga_appendParams];
    params[@"searcy_key"] = self.search_key;
    params[@"info_type"] = @(self.info_type);
    return params;
}

@end
