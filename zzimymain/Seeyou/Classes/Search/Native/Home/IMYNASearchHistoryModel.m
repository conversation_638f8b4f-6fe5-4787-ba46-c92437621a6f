//
//  IMYNASearchHistoryModel.m
//  IMYAccount
//
//  Created by ChatAI on 2024/12/20.
//  Copyright © 2024 meiyou. All rights reserved.
//

#import "IMYNASearchHistoryModel.h"

@implementation IMYNASearchHistoryModel

+ (instancetype)modelWithKeyword:(NSString *)keyword {
    return [self modelWithKeyword:keyword sourceModel:nil];
}

+ (instancetype)modelWithKeyword:(NSString *)keyword sourceModel:(nullable NSDictionary *)sourceModel {
    IMYNASearchHistoryModel *model = [[self alloc] init];
    model.keyword = keyword;
    model.sourceModel = sourceModel;
    return model;
}

- (BOOL)isCarouselWordType {
    // 轮播词类型判断：sourceModel中有scheme_uri字段(即使值为空)
    return self.sourceModel != nil && [self.sourceModel.allKeys containsObject:@"scheme_uri"];
}

- (BOOL)isHighlightWordType {
    return [self.sourceModel[@"highlight"] boolValue];
}

- (BOOL)isEqual:(id)object {
    // sourceModel 是否相同
    BOOL isSameSourceModel = [self.sourceModel isEqualToDictionary:((IMYNASearchHistoryModel *)object).sourceModel];
    return [self.keyword isEqualToString:((IMYNASearchHistoryModel *)object).keyword] && isSameSourceModel;
}

@end 
