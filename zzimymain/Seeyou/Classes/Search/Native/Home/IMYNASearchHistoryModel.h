//
//  IMYNASearchHistoryModel.h
//  IMYAccount
//
//  Created by ChatAI on 2024/12/20.
//  Copyright © 2024 meiyou. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface IMYNASearchHistoryModel : NSObject

/// 搜索关键词
@property (nonatomic, copy) NSString *keyword;

/// 来源模型转换而来的字典
@property (nonatomic, copy, nullable) NSDictionary *sourceModel;

/// 便利构造方法
+ (instancetype)modelWithKeyword:(NSString *)keyword;
+ (instancetype)modelWithKeyword:(NSString *)keyword sourceModel:(nullable NSDictionary *)sourceModel;

/// 判断是否为轮播词类型（sourceModel中有scheme_uri字段）
- (BOOL)isCarouselWordType;

/// 判断是否为高亮词类型（sourceModel中携带 highlight 标记）
- (BOOL)isHighlightWordType;

@end

NS_ASSUME_NONNULL_END 
