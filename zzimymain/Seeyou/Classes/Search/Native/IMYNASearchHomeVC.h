//
//  IMYNASearchHomeVC.h
//  ZZIMYMain
//
//  Created by ljh on 2023/8/7.
//

#import <IMYBaseKit/IMYBaseKit.h>
#import "SYH5SearchManager.h"
#import <IMYTools/IMYSearchEventTrack.h>

@class IMYNASearchHistoryModel;

NS_ASSUME_NONNULL_BEGIN

@interface IMYNASearchHomeVC : IMYPublicBaseViewController

// 来源入口（h5获取推荐词）
@property (nonatomic, assign) SYSearchFromType from;

// 入口页
@property (nonatomic, assign) IMYSearchEventTrackType pos_id;

// 她她圈新详情页或旧圈可拼接此参数
@property (nonatomic, assign) NSInteger forum_id;

// 来源入口（定位搜索结果的tab用）
@property (nonatomic, assign) NSInteger current_tab;

// 广告标识符
@property (nonatomic, assign) NSInteger info_type;

// 不弹起键盘
@property (nonatomic, assign) BOOL no_show_keyboard;

// 搜索标识key
@property (nonatomic, copy) NSString *search_key;

// 搜索来源位置，基于window坐标，用来做动画
@property (nonatomic, copy) NSString *befromBarFrame;

// 以前原生搜索页存在的参数，搜索的时候有用到
@property (nonatomic, assign) SYSearchBISource bi_source;

// 搜索推荐词
@property (nonatomic, strong) NSDictionary *searchSuggestWords;

// 搜索框占位词（支持字典和字符串）
@property (nonatomic, strong) id searchPlaceholder;

// 搜索框默认占位词（不可直接搜索）
@property (nonatomic, copy) NSString *defaultSearchPlaceholder;

// 搜索框的词是服务端传的推荐（可直接搜索）
@property (nonatomic, assign) BOOL isRecommendWord;

// 搜索框的词是服务端传的推荐，且在未输入任何关键字时，直接拿Placeholder 搜索。默认NO
@property (nonatomic, assign) BOOL isRecommendWordAndSearch;

// 进入时，是否联动搜索词展示联想列表 (来自广告)
@property (nonatomic, assign) BOOL showAssociateWithKey;

// 8.93.0版本： 从searchPlaceholder字典中提取keyword
- (NSString *)getKeywordFromSearchPlaceholder;

// 从searchPlaceholder字典中获取icon_type
- (NSInteger)getIconTypeFromSearchPlaceholder;

/// 8.95：将一条历史记录模型置顶并保存（静态方法）
/// 说明：与首页刷新逻辑一致，新模型置顶，移除相同 keyword 的旧项并持久化。
/// 参数：historyModel 需包含有效的 keyword；为空或 keyword 为空时不处理。
/// 注意：仅进行存储层处理，不涉及 UI 刷新。
+ (void)saveHistoryModelToTop:(IMYNASearchHistoryModel *)historyModel;

@end

NS_ASSUME_NONNULL_END
