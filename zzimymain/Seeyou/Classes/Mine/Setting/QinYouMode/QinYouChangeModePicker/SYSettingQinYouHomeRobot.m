//
//  SYSettingQinYouHomeRobot.m
//  ZZIMYMain
//
//  Created by huang<PERSON><PERSON><PERSON> on 2025/8/1.
//

#import "SYSettingQinYouHomeRobot.h"
#import "SYQinYouChangeModeManager.h"
#import "IMYRelativeUserDataManager.h"
#if __has_include(<IMYMSG/IMYMSG.h>)
#import <IMYMSG/IMYMsgNotifyManager.h>
#endif

@implementation SYSettingQinYouHomeRobot

IMY_KYLIN_FUNC(IMY_KYLIN_STAGE_PREMAIN, 2, IMY_KYLIN_QUEUE_ASYNC) {
    [SYSettingQinYouHomeRobot registerAlertRobotAction];
}

+ (void)registerAlertRobotAction {
    [[IMYURIManager shareURIManager] addForPath:@"alert/robot/qinyou" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        [SYSettingQinYouHomeRobot initAlertRobot:actionObject];
    }];
}

/// 初始化操作
+ (void)initAlertRobot:(IMYURIActionBlockObject *)actionObject {
    [self registerAlertRobot:actionObject];
}

+ (void)registerAlertRobot:(IMYURIActionBlockObject *)actionObject {
    @weakify(self);
    /// 限流器， normal 的优先级大于 partner
    CGFloat delaySeconds = 0.15;
    {
        NSString *key = @"SYSettingQinYouHomeRobotNormal";
        imy_throttle_on_queue(delaySeconds, key, dispatch_get_main_queue(), ^{
            @strongify(self);
            [SYSettingQinYouHomeRobotNormal registerAlertRobot:actionObject key:key];
        });
    }
    {
        NSString *key = @"SYSettingQinYouHomeRobotPartner";
        imy_throttle_on_queue(delaySeconds, key, dispatch_get_main_queue(), ^{
            @strongify(self);
            [SYSettingQinYouHomeRobotPartner registerAlertRobot:actionObject key:key];
        });
    }
}

@end

@implementation SYSettingQinYouHomeRobotNormal

+ (void)registerAlertRobot:(IMYURIActionBlockObject *)actionObject key:(NSString *)key {
    NSInteger priority = [[actionObject.uri.params objectForKey:@"priority"] integerValue];
    NSString *keySuffix = [actionObject.uri.params objectForKey:@"keySuffix"];
    if (imy_isNotBlankString(keySuffix)) {
        key = [NSString stringWithFormat:@"%@_%@", key, keySuffix];
    }
    id pageVC = [actionObject.uri.params objectForKey:@"pageVC"];
    /// 获取弹窗自动化机器人
    IMYAlertShowAutoRobot *alertRobot = [IMYAlertShowAutoRobot createRobotWithKey:key];
    /// 已有弹窗监控
    if (alertRobot.isReady) {
        /// 移除当前的，因为可能 priority、pageVC 不一致，需要移除重新添加
        if (pageVC && pageVC != alertRobot.alertAction.inPageVC) {
            alertRobot.DismissNoCallback();
            alertRobot = [IMYAlertShowAutoRobot createRobotWithKey:key];
        } else {
            return;
        }
    }
    if (pageVC) {
        alertRobot.PageVC(pageVC);
    }
    /// 注册弹窗队列
    alertRobot.Priority(priority).TabHomeType(SYTabBarIndexTypeHome).IsBaseInvalid(^BOOL{
        return [SYSettingQinYouHomeRobotNormal isBaseInvalid];
    }).CreateRealAlertView(^UIView<IMYAlertShowViewProtocol> *{
        return [SYSettingQinYouHomeRobotNormal new];
    }).Ready();
}

/// 是否无效，无效就不弹窗，无效返回 YES
+ (BOOL)isBaseInvalid {
#if __has_include(<IMYMSG/IMYMSG.h>)
    if ([IMYMsgNotifyManager sharedManager].isMsgActionBoxShowing) {
        return YES;
    }
#endif
    SYQinYouChangeModeNewBabyModel *babyModel = [[SYQinYouChangeModeManager shareInstance] checkNeedShowAlter];
    if (babyModel && [IMYPublicAppHelper shareAppHelper].userMode != IMYVKUserModeQinYou) {
        return NO;
    }
    return YES;
}

- (void)show {
    SYQinYouChangeModeNewBabyModel *babyModel = [[SYQinYouChangeModeManager shareInstance] checkNeedShowAlter];
    [[SYQinYouChangeModeManager shareInstance] handleShowGuideAlterWithBabyModel:babyModel confirmBlock:^{

    } cancelBlock:^{
        
    }];
}

- (void)dismiss {
    
}

@end

@implementation SYSettingQinYouHomeRobotPartner

+ (void)registerAlertRobot:(IMYURIActionBlockObject *)actionObject key:(NSString *)key {
    NSInteger priority = [[actionObject.uri.params objectForKey:@"priority"] integerValue] - 1;
    NSString *keySuffix = [actionObject.uri.params objectForKey:@"keySuffix"];
    if (imy_isNotBlankString(keySuffix)) {
        key = [NSString stringWithFormat:@"%@_%@", key, keySuffix];
    }
    id pageVC = [actionObject.uri.params objectForKey:@"pageVC"];
    /// 获取弹窗自动化机器人
    IMYAlertShowAutoRobot *alertRobot = [IMYAlertShowAutoRobot createRobotWithKey:key];
    /// 已有弹窗监控
    if (alertRobot.isReady) {
        /// 移除当前的，因为可能 priority、pageVC 不一致，需要移除重新添加
        if (pageVC && pageVC != alertRobot.alertAction.inPageVC) {
            alertRobot.DismissNoCallback();
            alertRobot = [IMYAlertShowAutoRobot createRobotWithKey:key];
        } else {
            return;
        }
    }
    if (pageVC) {
        alertRobot.PageVC(pageVC);
    }
    /// 注册弹窗队列
    alertRobot.Priority(priority).TabHomeType(SYTabBarIndexTypeHome).IsBaseInvalid(^BOOL{
        return [SYSettingQinYouHomeRobotPartner isBaseInvalid];
    }).CreateRealAlertView(^UIView<IMYAlertShowViewProtocol> *{
        return [SYSettingQinYouHomeRobotPartner new];
    }).Ready();
}

/// 是否无效，无效就不弹窗，无效返回 YES
+ (BOOL)isBaseInvalid {
    if ([IMYPublicAppHelper shareAppHelper].userRelationModel.group_id <= 0) {
        return YES;
    }
    BOOL hasPop = [[IMYConfigsCenter sharedInstance] boolForKeyPath:@"womens_health2.mate_homepage_pop.has_pop"];
    if ([IMYPublicAppHelper shareAppHelper].userMode != IMYVKUserModeQinYou && [IMYPublicAppHelper shareAppHelper].userRelationModel && [[IMYRelativeUserDataManager sharedInstance] isInvitee] && ![[SYQinYouChangeModeManager shareInstance] getHasShowPartnerAlter] && hasPop) {
        return NO;
    }
    return YES;
}

- (void)show {
    [[SYQinYouChangeModeManager shareInstance] handleShowPartnerGuideAlterWithConfirmBlock:^{
    } cancelBlock:^{
    }];
}

- (void)dismiss {
    
}

@end
