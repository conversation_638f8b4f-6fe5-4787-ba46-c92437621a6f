//
//  IMYRightsRenewingManageVC.m
//  ZZIMYMain
//
//  Created by ljh on 2024/2/19.
//

#import "IMYRightsRenewingManageVC.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import <IMYBaseKit/IMYSubGuideProtoManager.h>
#import <IMYBaseKit/IMYSubGuideManager.h>
#import "IMYMRRenewHeaderCardView.h"
#import "IMYMRRenewInfoCell.h"

@interface IMYRightsRenewingManageVC () <UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UIView *navBar;
@property (nonatomic, strong) UILabel *navTitleLabel;
@property (nonatomic, strong) UIView *navBarBottomLine;

@property (nonatomic, strong) UIImageView *topBGImageView;
@property (nonatomic, strong) IMYCaptionView *captionView;

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) IMYMRRenewHeaderCardView *headerCardView;
@property (nonatomic, strong) UIView *footerLinkView;

/// 吸顶状态下头部的背景色
@property (nonatomic, strong) UIView *bigNavBarBGView;

/// 原始接口数据
@property (nonatomic, copy) NSDictionary *userInfoData;
@property (nonatomic, copy) NSDictionary *renewInfoData;
@property (nonatomic, copy) NSArray<NSDictionary *> *historyInfoData;

@property (nonatomic, strong) RACDisposable *requestDisposable;

@end

@implementation IMYRightsRenewingManageVC

- (void)viewDidLoad {
    [super viewDidLoad];
    self.navigationBarHidden = YES;
    self.enableIOS7EdgesForExtendedLayout = YES;
    self.title = @"开通记录";
    
    // 初始化页面
    [self setupSubviews];
    
    // 设置标题
    self.navTitleLabel.text = self.title;
    
    // 请求页面数据
    [self setupDatas];
}

- (void)viewDidAppear:(BOOL)animated {
    BOOL const isPageShowed = self.isViewDidAppeared;
    
    // 调用父类
    [super viewDidAppear:animated];
    
    if (isPageShowed) {
        // 二次进入，刷新接口
        [self requestData];
    }
}

#pragma mark - 请求

- (void)setupDatas {
    // 如果外部没有传递用户信息，则自己构造一个，只需要一个过期时间即可
    NSDictionary *userInfo = self.fromURI.params[@"user_info"];
    if (!userInfo.count) {
        NSString *sub_info_text = nil;
        long long expires_at = [IMYRightsSDK sharedInstance].detailModel.expires_at;
        if (expires_at > 0) {
            NSDate *date = [NSDate dateWithTimeIntervalSince1970:expires_at];
            NSDateComponents *components = [[NSDate imy_getCNCalendar] components:NSCalendarUnitYear|NSCalendarUnitMonth|NSCalendarUnitDay fromDate:date];
            sub_info_text = [NSString stringWithFormat:@"%ld-%ld-%ld到期", components.year, components.month, components.day];
        }
        userInfo = @{
            @"sub_info_text" : sub_info_text ?: @"",
        };
    }
    self.userInfoData = userInfo;
    // 刷新头部信息
    [self.headerCardView refreshWithData:self.userInfoData];
    
    // 请求当前tab的内容
    [self requestData];
}

// 只显示 历史订单列表
- (IMYMRRenewingManageType)currentType {
    return IMYMRRenewingManageTypeHistory;
}

- (void)requestData {
    // 无数据才需要显示loading
    BOOL const isNoneData = (self.currentType == IMYMRRenewingManageTypeDefault ? !self.renewInfoData.count : !self.historyInfoData.count);
    if (isNoneData) {
        // 先刷新，保证布局正确
        [self.tableView reloadData];
        // 显示loading
        [self changeLoadingViewState:IMYCaptionViewStateLoading];
    }
    
    // 取消之前的请求
    [self.requestDisposable dispose];
    
    @weakify(self);
    // 显示自动续费
    if (self.currentType == IMYMRRenewingManageTypeDefault) {
       self.requestDisposable = [[[IMYServerRequest getPath:@"v3/sub/renew_info" host:sub_seeyouyima_com params:nil headers:nil] deliverOnMainThread] subscribeNext:^(IMYHTTPResponse *x) {
           @strongify(self);
           NSDictionary *body = x.responseObject;
           if (body.count > 0) {
               self.renewInfoData = body;
           }
           if (self.renewInfoData.count > 0) {
               [self.tableView reloadData];
               [self changeLoadingViewState:IMYCaptionViewStateHidden];
           } else {
               [self changeLoadingViewState:IMYCaptionViewStateNoResult];
           }
        } error:^(NSError *error) {
            @strongify(self);
            [self changeLoadingViewState:IMYCaptionViewStateRetry];
        }];
    }
    
    // 显示开通记录，直接写死120，十年
    if (self.currentType == IMYMRRenewingManageTypeHistory) {
        self.requestDisposable = [[[IMYServerRequest getPath:@"v3/sub/order_list" host:sub_seeyouyima_com params:@{
            @"page_num" : @0,
            @"page_size" : @120,
        } headers:nil] deliverOnMainThread] subscribeNext:^(IMYHTTPResponse *x) {
            @strongify(self);
            NSDictionary *list = x.responseObject[@"list"];
            if (list.count > 0) {
                self.historyInfoData = list;
            }
            if (self.historyInfoData.count > 0) {
                [self.tableView reloadData];
                [self changeLoadingViewState:IMYCaptionViewStateHidden];
            } else {
                [self changeLoadingViewState:IMYCaptionViewStateNoResult];
            }
         } error:^(NSError *error) {
             @strongify(self);
             [self changeLoadingViewState:IMYCaptionViewStateRetry];
         }];
    }
}

- (void)changeLoadingViewState:(IMYCaptionViewState const)state {
    if (!self.captionView) {
        self.captionView = [IMYCaptionView new];
        self.captionView.autoresizingMask = UIViewAutoresizingFlexibleBottomMargin;
        self.captionView.backgroundColor = UIColor.clearColor;
        self.captionView.hiddenStatusNoShowAnimation = YES;
        self.captionView.imy_size = CGSizeMake(SCREEN_WIDTH, 360);
        @weakify(self);
        [self.captionView setRetryBlock:^{
            @strongify(self);
            [self requestData];
        }];
    }
    if (state != IMYCaptionViewStateHidden) {
        // 显示阶段都需要重新 add，保证在最顶端 + 修正位置
        [self.tableView addSubview:self.captionView];
        self.captionView.imy_top = self.headerCardView.imy_bottom;
    }
    if (state == IMYCaptionViewStateNoResult) {
        if (self.currentType == IMYMRRenewingManageTypeDefault) {
            [self.captionView setTitle:@"你当前没有自动续费项目" forState:IMYCaptionViewStateNoResult];
        } else {
            [self.captionView setTitle:@"你当前没有开通记录" forState:IMYCaptionViewStateNoResult];
        }
    }
    
    self.captionView.state = state;
    
    // 是否显示底部协议链接
    if (state == IMYCaptionViewStateHidden) {
        self.footerLinkView.hidden = NO;
    } else {
        self.footerLinkView.hidden = YES;
    }
}

- (void)viewWillLayoutSubviews {
    [super viewWillLayoutSubviews];
    // 修正一些组件位置
    self.captionView.frame = CGRectMake(0, self.headerCardView.imy_bottom, SCREEN_WIDTH, 360);
    self.tableView.imy_height = self.view.imy_height - self.tableView.imy_top;
}

#pragma mark - 头部卡片tab 跟滚动区域联动

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    // 当前滚动位置
    CGFloat offsetY = scrollView.contentOffset.y;
    
    // 吸顶状态下的背景渐变
    UIView *topTabBar = self.headerCardView.parentGetTopTabBar;
    CGFloat topTabOffsetY = self.headerCardView.parentGetTopTabBarOffsetY;
    if (offsetY > topTabOffsetY && topTabBar.imy_height > 0) {
        if (topTabBar.superview != self.view) {
            topTabBar.imy_top = self.navBar.imy_bottom;
            [self.view addSubview:topTabBar];
            self.bigNavBarBGView.imy_height = topTabBar.imy_bottom;
            [self.bigNavBarBGView addSubview:self.navBarBottomLine];
            self.navBarBottomLine.imy_bottom = self.bigNavBarBGView.imy_height;
        }
        // 不做渐变了
        self.bigNavBarBGView.alpha = 1;
        self.topBGImageView.alpha = 0;
        self.navBarBottomLine.alpha = 1;
    } else {
        self.bigNavBarBGView.alpha = 0;
        self.topBGImageView.alpha = 1;
        if (topTabBar && topTabBar.superview == self.view) {
            [self.headerCardView parentResetTopTabBar];
        }
        if (self.navBarBottomLine.superview != self.navBar) {
            [self.navBar addSubview:self.navBarBottomLine];
            self.navBarBottomLine.imy_bottom = self.navBar.imy_height;
        }
        if (offsetY > topTabOffsetY) {
            self.navBarBottomLine.alpha = 1;
        } else {
            self.navBarBottomLine.alpha = 0;
        }
    }
    
    if (offsetY > self.topBGImageView.imy_height) {
        self.topBGImageView.imy_top = -self.topBGImageView.imy_height;
    } else if (offsetY < 0) {
        self.topBGImageView.imy_top = 0;
    } else {
        self.topBGImageView.imy_top = -offsetY;
    }
}

#pragma mark - 基础实现

- (void)setupSubviews {
    // 顶部背景
    self.topBGImageView = [UIImageView new];
    self.topBGImageView.imy_width = SCREEN_WIDTH;
    self.topBGImageView.imy_height = IMYIntegerBy375Design(330);
    [self.topBGImageView imy_setImage:@"img_topbg_vipcard_v2"];
    [self.view addSubview:self.topBGImageView];
    
    // 虚拟导航栏
    [self setupNavBar];
    
    // tableView
    self.tableView = [[UITableView alloc] initWithFrame:self.view.bounds style:UITableViewStylePlain];
    self.tableView.backgroundColor = UIColor.clearColor;
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.tableView.imy_top = self.navBar.imy_bottom;
    self.tableView.imy_width = SCREEN_WIDTH;
    self.tableView.imy_height = SCREEN_HEIGHT - SCREEN_TABBAR_HEIGHT - self.tableView.imy_top;
    [self.view insertSubview:self.tableView belowSubview:self.navBar];
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    [self.tableView registerClass:IMYMRRenewInfoCell.class forCellReuseIdentifier:@"cell"];
    
    /// 吸顶下的背景色
    self.bigNavBarBGView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 100)];
    [self.bigNavBarBGView imy_setBackgroundColor:kIMY_BG];
    self.bigNavBarBGView.alpha = 0;
    [self.view insertSubview:self.bigNavBarBGView belowSubview:self.navBar];
    
    // 头部卡片
    [self setupHeaderCardView];
    
    // 底部协议
    [self setupFooterLinkView];
}

- (void)setupHeaderCardView {
    self.headerCardView = [IMYMRRenewHeaderCardView new];
    self.tableView.tableHeaderView = self.headerCardView;
    
    // 自动续费页面曝光埋点，只曝光一次
    [IMYGAEventHelper postWithPath:@"event" params:@{
        @"action" : @1,
        @"event" : @"dy_xfgl_zdxf",
        @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
        @"public_type" : @"续费管理",
        @"public_info" : (IMYSubGuideManager.isFromVIPCenterTab ? @"是" : @"否"),
    } headers:nil completed:nil];
}

- (void)setupFooterLinkView {
    self.footerLinkView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 40 + SCREEN_TABBAR_SAFEBOTTOM_MARGIN)];
    self.tableView.tableFooterView = self.footerLinkView;
    
    UIButton *actionButton = [IMYTouchEXButton new];
    actionButton.titleLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
    [actionButton imy_setTitleColor:kCK_Black_B];
    
    // 移除前后的《》符号
    NSString *autoRenewalProto = @"美柚会员服务信息说明及自动续费服务协议";
    if ([autoRenewalProto hasPrefix:@"《"] && [autoRenewalProto hasSuffix:@"》"]) {
        autoRenewalProto = [autoRenewalProto substringWithRange:NSMakeRange(1, autoRenewalProto.length - 2)];
    }
    [actionButton imy_setTitle:autoRenewalProto];
    [actionButton imy_sizeToFit];
    actionButton.imy_height = 16;
    actionButton.imy_top = 12;
    actionButton.imy_centerX = self.footerLinkView.imy_width / 2.0;
    [self.footerLinkView addSubview:actionButton];
    
    [actionButton addTarget:self action:@selector(onFooterLinkPressedAction) forControlEvents:UIControlEventTouchUpInside];
    
    // 默认不显示
    self.footerLinkView.hidden = YES;
}

- (void)setupNavBar {
    self.navBar = [UIView new];
    self.navBar.imy_width = SCREEN_WIDTH;
    self.navBar.imy_height = SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT;
    [self.view addSubview:self.navBar];
    
    // 标题
    self.navTitleLabel = [UILabel new];
    self.navTitleLabel.font = [UIFont boldSystemFontOfSize:17];
    [self.navTitleLabel imy_setTextColor:kCK_Black_AT];
    self.navTitleLabel.textAlignment = NSTextAlignmentCenter;
    self.navTitleLabel.imy_top = SCREEN_STATUSBAR_HEIGHT;
    self.navTitleLabel.imy_width = SCREEN_WIDTH;
    self.navTitleLabel.imy_height = SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT - SCREEN_STATUSBAR_HEIGHT;
    [self.navBar addSubview:self.navTitleLabel];
    
    // 返回按钮
    self.imy_topLeftButton.imy_top = SCREEN_STATUSBAR_HEIGHT;
    self.imy_topLeftButton.imy_left = 16;
    [self imy_topLeftButtonIsBack];
    [self.navBar addSubview:self.imy_topLeftButton];
    
    // 底部分割线
    self.navBarBottomLine = [UIView new];
    self.navBarBottomLine.imy_width = SCREEN_WIDTH;
    self.navBarBottomLine.imy_height = 1.0/SCREEN_SCALE;
    self.navBarBottomLine.imy_bottom = self.navBar.imy_height;
    [self.navBarBottomLine imy_setBackgroundColorForKey:kCK_Black_J];
    self.navBarBottomLine.alpha = 0;
    [self.navBar addSubview:self.navBarBottomLine];
}

#pragma mark - UITableView

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (self.currentType == IMYMRRenewingManageTypeDefault) {
        if (self.renewInfoData.count > 0) {
            return 1;
        }
    }
    if (self.currentType == IMYMRRenewingManageTypeHistory) {
        return self.historyInfoData.count;
    }
    return 0;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (self.currentType == IMYMRRenewingManageTypeDefault) {
        return [IMYMRRenewInfoCell defaultCellHeightWithData:self.renewInfoData];
    }
    
    if (self.currentType == IMYMRRenewingManageTypeHistory) {
        NSDictionary *data = self.historyInfoData[indexPath.row];
        return [IMYMRRenewInfoCell defaultCellHeightWithData:data];
    }
    
    return [IMYMRRenewInfoCell defaultCellHeightWithData:nil];
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    IMYMRRenewInfoCell *cell = [tableView dequeueReusableCellWithIdentifier:@"cell" forIndexPath:indexPath];
    
    if (self.currentType == IMYMRRenewingManageTypeDefault) {
        [cell setupWithRenewingData:self.renewInfoData];
    }
    
    if (self.currentType == IMYMRRenewingManageTypeHistory) {
        NSDictionary *data = self.historyInfoData[indexPath.row];
        [cell setupWithHistoryData:data];
        cell.onActionButtonPressed = nil;
    }
    
    return cell;
}

#pragma mark -

- (void)onFooterLinkPressedAction {
    NSString *urlString = [IMYSubGuideProtoManager autoRenewalProtoURLString];
    [IMYSubGuideProtoManager enterSafariVCWithURL:[NSURL URLWithString:urlString]];
}

@end
