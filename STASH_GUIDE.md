# 脚本贮藏功能说明

## 🔄 自动贮藏功能

脚本现在具备智能的代码贮藏功能，当遇到未提交的更改时：

### ✅ 自动处理流程
1. **检测未提交更改** - 自动发现工作区的修改
2. **智能贮藏代码** - 使用带时间戳的消息贮藏更改
3. **继续分支切换** - 不会因为未提交更改而中断
4. **尝试自动恢复** - 分支切换成功后自动恢复贮藏
5. **异常情况处理** - 切换失败时自动恢复到原始状态

### 🏷️ 贮藏消息格式
```
auto-stash-20241215-143052-branch-switch
```
- 包含时间戳，便于识别
- 明确标识为分支切换时的自动贮藏

### 🛠️ 手动恢复方法

如果自动恢复失败，可以手动操作：

```bash
# 查看所有贮藏
git stash list

# 恢复最新贮藏
git stash pop

# 恢复指定贮藏
git stash apply stash@{0}

# 删除贮藏（不恢复）
git stash drop stash@{0}
```

### 📊 脚本执行后的状态检查

脚本完成后会自动检查所有库的贮藏状态：
- ✅ 显示没有贮藏的库
- ⚠️ 警告有贮藏的库及数量
- 📋 提供手动恢复的操作指引

### 💡 最佳实践

1. **定期清理**: 及时处理贮藏的代码，避免积累
2. **检查状态**: 脚本执行后查看提示信息
3. **手动验证**: 对重要更改手动验证恢复结果
4. **备份策略**: 重要代码建议提交而不是长期依赖贮藏

### ⚠️ 注意事项

- 贮藏只保存已跟踪文件的更改
- 新建的未跟踪文件不会被贮藏
- 冲突情况下可能需要手动处理
- 建议在脚本执行前检查工作区状态 