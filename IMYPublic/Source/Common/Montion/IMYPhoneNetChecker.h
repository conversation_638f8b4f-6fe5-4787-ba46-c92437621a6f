//
//  IMYPhoneNetChecker.h
//  IMYBaseKit
//
//  Created by ljh on 2023/12/4.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface IMYPhoneNetChecker : NSObject

/**
 host：data.seeyouyima.com，无需携带http头和path路径
 如果当前无网络，会等到有网时，再进行检测，所以不能基于 completed 回调来执行别的操作
 completed 不一定会回调（会被覆盖）
 */
+ (void)checkWithHost:(NSString *)host completed:(void (^)(NSString *tracertResult))completed;

@end

NS_ASSUME_NONNULL_END
