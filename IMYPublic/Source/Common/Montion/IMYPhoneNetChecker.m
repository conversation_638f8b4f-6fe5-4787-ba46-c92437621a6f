//
//  IMYPhoneNetChecker.m
//  IMYBaseKit
//
//  Created by ljh on 2023/12/4.
//

#import "IMYPhoneNetChecker.h"
#import "IMYPublic.h"
#import "netdb.h"
#import "stdlib.h"
#import "resolv.h"
#import "arpa/inet.h"

@interface IMYPhoneNetChecker (V2Proxy)
+ (void)proxyTracerouteWithHost:(NSString * _Nonnull)host
              completionHandler:(void (^ _Nonnull)(NSString * _Nonnull))completionHandler;
@end

@implementation IMYPhoneNetChecker

+ (dispatch_queue_t)ioQueue {
    static dispatch_queue_t queue;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        queue = dispatch_queue_create("IMYPhoneNetChecker.Queue", NULL);
    });
    return queue;
}

IMY_KYLIN_FUNC_IDLE_ASYNC {
    [IMYNetState.networkChangedSignal subscribeNext:^(id x) {
        dispatch_async(IMYPhoneNetChecker.ioQueue, ^{
            if (IMYNetState.networkEnable) {
                [IMYPhoneNetChecker next_checkWithQueue];
            }
        });
    }];
}

+ (void)checkWithHost:(NSString *)host completed:(void (^)(NSString * _Nonnull))completed {
    if (!host.length || !completed) {
        return;
    }
    dispatch_async(IMYPhoneNetChecker.ioQueue, ^{
        NSCharacterSet *charset = [NSCharacterSet characterSetWithCharactersInString:@":/?"];
        if ([host rangeOfCharacterFromSet:charset].length > 0) {
            NSAssert(NO, @"不用携带http头和path");
            return;
        }
        [self main_checkWithHost:host completed:completed];
    });
}

// 待执行队列
static NSMutableDictionary<NSString *, id> *kWaitHostsMap = nil;
+ (void)next_checkWithQueue {
    if (!kWaitHostsMap) {
        kWaitHostsMap = [NSMutableDictionary dictionary];
    }
    // 无等待检测对象
    if (!kWaitHostsMap.count) {
        return;
    }
    NSString *host = kWaitHostsMap.allKeys.lastObject;
    void(^block)(NSString *) = [kWaitHostsMap objectForKey:host];
    [kWaitHostsMap removeObjectForKey:host];
    
    // 开始检测
    [self main_checkWithHost:host completed:block];
}

// 开始检测
+ (void)main_checkWithHost:(NSString *)host completed:(void (^)(NSString * _Nonnull))completed {
    if (NSThread.isMainThread) {
        NSAssert(NO, @"不能在主线程做网络检测！");
        return;
    }
    if (!kWaitHostsMap) {
        kWaitHostsMap = [NSMutableDictionary dictionary];
    }
    static BOOL kNetChecking = NO;
    // 无网络 或者 在启动阶段，则丢到等待队列中
    if (kNetChecking || !IMYNetState.networkEnable || imy_load_current() < IMYLoadAtIdleTime) {
        void(^oldCBlock)(NSString *) = [kWaitHostsMap objectForKey:host];
        [kWaitHostsMap setObject:[completed copy] forKey:host];
        
        if (oldCBlock) {
            // 当前设备的基础信息
            NSMutableString *allResult = [NSMutableString string];
            [allResult appendFormat:@"host: %@，apn: %@\n", host, IMYNetState.apn];
            [allResult appendFormat:@"name: %@，code: %@\n", UIDevice.imy_carrierName, UIDevice.imy_carrierCode];
            [allResult appendFormat:@"dns: %@\n", [[self outPutDNSServers] componentsJoinedByString:@","]];
            [allResult appendFormat:@"ipv4: %@\n", [[self getIPV4DNSWithHostName:host] componentsJoinedByString:@","]];
            [allResult appendFormat:@"ipv6: %@\n", [[self getIPV6DNSWithHostName:host] componentsJoinedByString:@","]];
            [allResult appendString:@"no traceroute!"];
            imy_asyncMainBlock(^{
                oldCBlock(allResult.copy);
            });
        }
        return;
    }
    kNetChecking = YES;
    
    NSMutableString *allResult = [NSMutableString string];
    
    // 当前设备的基础信息
    [allResult appendFormat:@"host: %@，apn: %@\n", host, IMYNetState.apn];
    [allResult appendFormat:@"name: %@，code: %@\n", UIDevice.imy_carrierName, UIDevice.imy_carrierCode];
    [allResult appendFormat:@"dns: %@\n", [[self outPutDNSServers] componentsJoinedByString:@","]];
    [allResult appendFormat:@"ipv4: %@\n", [[self getIPV4DNSWithHostName:host] componentsJoinedByString:@","]];
    [allResult appendFormat:@"ipv6: %@\n", [[self getIPV6DNSWithHostName:host] componentsJoinedByString:@","]];
    NSLog(@"net test %@", allResult);
    
    // 合并路由检测数据
    void (^completionHandler)(NSString *) = ^(NSString *result) {
        [allResult appendFormat:@"tracertRes:\n%@\n", result];
        imy_asyncMainBlock(^{
            completed(allResult.copy);
            // 检测是否有待执行host
            dispatch_async(IMYPhoneNetChecker.ioQueue, ^{
                kNetChecking = NO;
                [self next_checkWithQueue];
            });
        });
    };
    
    if ([self respondsToSelector:@selector(proxyTracerouteWithHost:completionHandler:)]) {
        [self proxyTracerouteWithHost:host completionHandler:completionHandler];
    } else {
        completionHandler(@"null");
    }
}

#pragma mark - 网络信息

/*!
 * 获取当前网络DNS服务器地址
 */
+(NSArray *)outPutDNSServers {
    res_state res = malloc(sizeof(struct __res_state));
    int result = res_ninit(res);
    NSMutableArray *servers = [[NSMutableArray alloc] init];
    if (result == 0) {
        union res_9_sockaddr_union *addr_union = malloc(res->nscount * sizeof(union res_9_sockaddr_union));
        res_getservers(res, addr_union, res->nscount);
        for (int i = 0; i < res->nscount; i++) {
            if (addr_union[i].sin.sin_family == AF_INET) {
                char ip[INET_ADDRSTRLEN];
                inet_ntop(AF_INET, &(addr_union[i].sin.sin_addr), ip, INET_ADDRSTRLEN);
                NSString *dnsIP = [NSString stringWithUTF8String:ip];
                [servers addObject:dnsIP];
            } else if (addr_union[i].sin6.sin6_family == AF_INET6) {
                char ip[INET6_ADDRSTRLEN];
                inet_ntop(AF_INET6, &(addr_union[i].sin6.sin6_addr), ip, INET6_ADDRSTRLEN);
                NSString *dnsIP = [NSString stringWithUTF8String:ip];
                [servers addObject:dnsIP];
            }
        }
    }
    res_nclose(res);
    free(res);
    return [NSArray arrayWithArray:servers];
}

+ (NSArray *)getIPV4DNSWithHostName:(NSString *)hostName {
    const char *hostN = [hostName UTF8String];
    struct hostent *phot;
    @try {
        phot = gethostbyname(hostN);
    } @catch (NSException *exception) {
        return nil;
    }
    NSMutableArray *result = [[NSMutableArray alloc] init];
    int j = 0;
    while (phot && phot->h_addr_list && phot->h_addr_list[j]) {
        struct in_addr ip_addr;
        memcpy(&ip_addr, phot->h_addr_list[j], 4);
        char ip[20] = {0};
        if (inet_ntop(AF_INET, &ip_addr, ip, sizeof(ip)) != NULL) {
            NSString *strIPAddress = [NSString stringWithUTF8String:ip];
            [result addObject:strIPAddress];
        }
        j++;
    }
    return [NSArray arrayWithArray:result];
}

+ (NSArray *)getIPV6DNSWithHostName:(NSString *)hostName {
    const char *hostN = [hostName UTF8String];
    struct hostent *phot;
    @try {
        // 只有在IPV6的网络下才会有返回值
        phot = gethostbyname2(hostN, AF_INET6);
    } @catch (NSException *exception) {
        return nil;
    }
    NSMutableArray *result = [[NSMutableArray alloc] init];
    int j = 0;
    while (phot && phot->h_addr_list && phot->h_addr_list[j]) {
        struct in6_addr ip6_addr;
        memcpy(&ip6_addr, phot->h_addr_list[j], sizeof(struct in6_addr));
        char dstStr[INET6_ADDRSTRLEN];
        char srcStr[INET6_ADDRSTRLEN];
        memcpy(srcStr, &ip6_addr, sizeof(struct in6_addr));
        if(inet_ntop(AF_INET6, srcStr, dstStr, INET6_ADDRSTRLEN) != NULL) {
            NSString *strIPAddress = [NSString stringWithUTF8String:dstStr];
            [result addObject:strIPAddress];
        }
        j++;
    }
    return [NSArray arrayWithArray:result];
}

@end
