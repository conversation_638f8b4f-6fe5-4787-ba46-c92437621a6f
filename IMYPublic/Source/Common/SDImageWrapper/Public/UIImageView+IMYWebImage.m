//
// Created by <PERSON> on 15/3/24.
//
//

#import "NSObject+IMYWebImagePrivate.h"
#import "UIImageView+IMYWebImage.h"
#import "IMYPublicColor.h"

#import <BlocksKit/BlocksKit+UIKit.h>
#import <IMYVendor/IMYTheme.h>
#import <Masonry/Masonry.h>
#import <BlocksKit/NSArray+BlocksKit.h>
#import <ObjcAssociatedObjectHelpers/ObjcAssociatedObjectHelpers.h>
#import <ReactiveCocoa/ReactiveCocoa.h>
#import <THProgressView/THProgressView.h>
#import <SDWebImage/UIImageView+WebCache.h>
#import <SDWebImage/SDWebImageDecoder.h>
#import <FLAnimatedImage/FLAnimatedImage.h>

#define IMYImageViewFixTag (100)

#define IMYWebImageQueueURLKey [NSString stringWithFormat:@"WebImageURL_%p", self]
#define IMYWebImageQueueProgressKey [NSString stringWithFormat:@"WebImageProgress_%p", self]
#define IMYWebImageQueueRetryKey [NSString stringWithFormat:@"WebImageRetry_%p", self]
#define IMYWebImageFadeAnimationKey @"IMYImageFade"

@interface IMYWebImageViewHelper : NSObject

+ (void)addRetryObserverImageView:(UIImageView *)imageView;
+ (void)removeRetryObserverImageView:(UIImageView *)imageView;

@end

@interface UIImageView (IMY_WebImage_Private)
@property (nonatomic, assign, setter=imy_setWebImageState:) IMYWebImageState imy_webImageState;
@property (nonatomic, copy, setter=imy_setImageLoadedURL:) NSString *imy_imageLoadedURL;
@property (nonatomic, assign, setter=imy_setDownloadProgress:) double imy_downloadProgress;
@property (nonatomic, assign) UIViewContentMode imy_displayContentMode;
@property (nonatomic, assign) BOOL imy_setup;
@property (nonatomic, assign) BOOL imy_originalUserInteractionEnabled;

@property (nonatomic, strong) UITapGestureRecognizer *imy_originalTap;
@property (nonatomic, strong) UITapGestureRecognizer *imy_failureTap;
@property (nonatomic, strong, readonly) UILabel *imy_failureShowLabel;

@property (nonatomic, strong) NSError *imy_downloadError;
@property (nonatomic, strong) _IMYWebImageSettingBlock *imyprivate_settingBlock;
@end

@implementation UIImageView (IMYWebImage)

static NSInteger s_qiniuImageQuality = 70;
+ (void)qiniuLowImageQuality:(NSUInteger)quality {
    if (quality > 0 && quality < 100) {
        ///最少40
        s_qiniuImageQuality = MAX(40, quality);
    }
}

static BOOL s_qiniuQualityAutoChange = YES;
+ (void)qiniuQualityAutoChange:(BOOL)authChange {
    s_qiniuQualityAutoChange = authChange;
}

#pragma mark - swizzle
- (void)imyweb_layoutSubviews {
    [self imyweb_layoutSubviews];
    if (self.imy_setup) {
        @weakify(self);
        NSString *queueKey = [NSString stringWithFormat:@"imyweb_layoutSubviews-%p", self];
        [NSObject imy_asyncBlock:^{
            @strongify(self);
            NSString *backgroundColorKey = self.backgroundColorKey;
            if (backgroundColorKey) {
                [self imy_setBackgroundColorForKey:backgroundColorKey];
            }
            [self imy_themeChanged];
        } onQueue:dispatch_get_main_queue() afterSecond:0 forKey:queueKey];
    }
}

- (void)imyweb_setContentMode:(UIViewContentMode)contentMode {
    UIViewContentMode mode = contentMode;
    if ((NSInteger)contentMode < IMYImageViewFixTag) {
        self.imy_displayContentMode = contentMode;
    } else {
        mode = contentMode - IMYImageViewFixTag;
    }
    [self imyweb_setContentMode:mode];
}

- (void)imyweb_addGestureRecognizer:(UIGestureRecognizer *)gestureRecognizer {
    if ([gestureRecognizer isKindOfClass:[UITapGestureRecognizer class]] &&
        ![gestureRecognizer isKindOfClass:[_IMYWebImageTapGestureRecognizer class]]) {
        self.imy_originalTap = (UITapGestureRecognizer *)gestureRecognizer;
    }
    [self imyweb_addGestureRecognizer:gestureRecognizer];
}

- (void)imyweb_setUserInteractionEnabled:(BOOL)enable {
    self.imy_originalUserInteractionEnabled = enable;
    [self imyweb_setUserInteractionEnabled:enable];
}

IMY_KYLIN_FUNC_METHOD_SWIZZLE {
    [UIImageView imy_swizzleMethod:@selector(layoutSubviews) withMethod:@selector(imyweb_layoutSubviews) error:nil];
    [UIImageView imy_swizzleMethod:@selector(setContentMode:) withMethod:@selector(imyweb_setContentMode:) error:nil];
    [UIImageView imy_swizzleMethod:@selector(addGestureRecognizer:) withMethod:@selector(imyweb_addGestureRecognizer:) error:nil];
    [UIImageView imy_swizzleMethod:@selector(setUserInteractionEnabled:) withMethod:@selector(imyweb_setUserInteractionEnabled:) error:nil];
}

#pragma mark - setImageURL

- (CGSize)imy_showViewSize {
    id size = objc_getAssociatedObject(self, @selector(imy_showViewSize));
    if (!size) {
        return CGSizeZero;
    }
    return [size CGSizeValue];
}

- (void)setImy_showViewSize:(CGSize)size {
    objc_setAssociatedObject(self, @selector(imy_showViewSize), [NSValue valueWithCGSize:size], OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (BOOL)imy_showWebP {
    id webp = objc_getAssociatedObject(self, @selector(imy_showWebP));
    if (!webp) {
        return YES;
    }
    return [webp boolValue];
}

- (void)setImy_showWebP:(BOOL)showWebP {
    objc_setAssociatedObject(self, @selector(imy_showWebP), @(showWebP), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (void)imy_setImageURL:(id)URL {
    CGSize viewSize = self.imy_showViewSize;
    BOOL webp = self.imy_showWebP;
    if (!webp && CGSizeEqualToSize(viewSize, CGSizeZero)) {
        [self imy_setImageURL:URL shouldAutoChangeQuality:s_qiniuQualityAutoChange];
    } else {
        CGSize cdnSize = CGSizeZero;
        if (viewSize.width > 0 && viewSize.height > 0) {
            cdnSize = CGSizeMake(ceil(viewSize.width * IMYSystem.screenScale), ceil(viewSize.height * IMYSystem.screenScale));
        }
        IMY_QiNiu_ImageType type = webp ? IMY_QiNiu_WEBP : IMY_QiNiu_None;
        NSString *URLString = [NSString qiniuURL:URL resize:cdnSize mode:0 quality:0 type:type];
        [self imy_setImageURL:URLString shouldAutoChangeQuality:s_qiniuQualityAutoChange];
    }
}

- (void)imy_originalSetImageURL:(id)URL {
    [self imy_setImageURL:URL shouldAutoChangeQuality:NO];
}

- (void)imy_setCircleImage:(UIImage *)image {
    if (!image) {
        self.image = self.imy_placeholderImage;
        return;
    }
    UIImage *showImage = image;
    if (![showImage imy_isShowInscribedCircle]) {
        CGSize size = self.imy_showViewSize;
        if (size.width == 0 || size.height == 0) {
            size = self.imy_size;
        }
        if (size.width > 10 && size.height > 10) {
            showImage = [_IMYWebImageUtils resizeImageWithImage:image toSize:size withContentMode:UIViewContentModeScaleAspectFill] ?: image;
        }
        if (showImage) {
            showImage = [_IMYWebImageUtils circleImageWithImage:showImage] ?: image;
        }
    }
    self.image = showImage;
}

- (void)imy_setImageURL:(id)URL shouldAutoChangeQuality:(BOOL)autoChange {
    NSURL *imageURL = nil;
    if ([URL isKindOfClass:[NSString class]]) {
        imageURL = [self imy_getURLWithString:URL];
    } else if ([URL isKindOfClass:[NSURL class]]) {
        imageURL = URL;
    }

    self.imy_downloadError = nil;
    if ([IMYNetState isWWAN] && autoChange && !imageURL.isFileURL) {
        NSString *downLoadImageURLString = imageURL.absoluteString;
        BOOL hasCache = [[SDImageCache sharedImageCache] diskImageExistsWithKey:downLoadImageURLString];
        if (!hasCache) {
            downLoadImageURLString = [NSString qiniuURL:downLoadImageURLString quality:s_qiniuImageQuality type:IMY_QiNiu_AutoWebP];
            imageURL = [NSURL URLWithString:downLoadImageURLString];
        }
    }

    ///取消异步的 url, progress ， retry 操作
    [NSObject imy_cancelBlockForKey:IMYWebImageQueueURLKey];
    [NSObject imy_cancelBlockForKey:IMYWebImageQueueProgressKey];
    [NSObject imy_cancelBlockForKey:IMYWebImageQueueRetryKey];

    ///减少同步宏  在宏内不能断点查问题
    if ([NSThread isMainThread]) {
        [self imyprivate_mainThreadDownloadURL:imageURL retryCount:0];
    } else {
        @weakify(self);
        [NSObject imy_asyncBlock:^{
            @strongify(self);
            [self imyprivate_mainThreadDownloadURL:imageURL retryCount:0];
        } onQueue:dispatch_get_main_queue() afterSecond:0 forKey:IMYWebImageQueueURLKey];
    }
}

- (NSURL *)imy_getURLWithString:(NSString *)urlString {
    if ([urlString hasPrefix:@"http"] || ![urlString hasPrefix:@"/"]) {
        NSURL *imageURL = [NSURL imy_URLWithString:urlString];
        return imageURL;
    } else {
        NSURL *imageURL = [NSURL fileURLWithPath:urlString];
        if (!imageURL) {
            imageURL = [NSURL fileURLWithPath:urlString.imy_URLEncode];
        }
        return imageURL;
    }
}

- (void)imyprivate_mainThreadDownloadURL:(NSURL *)downloadImageURL retryCount:(NSUInteger)retryCount {
    BOOL loadedURLEqual = [self.imy_imageLoadedURL isEqualToString:downloadImageURL.absoluteString];
    ///重试取消  已经有别的下载对象了
    if (retryCount > 0 && !loadedURLEqual) {
        return;
    }
    ///url 相同并且有图片 就不去走下载图片的流程
    if (loadedURLEqual && ((self.image && self.imy_webImageState == IMYWebImageStateDownloadSucceed) || self.imyprivate_settingBlock != nil)) {
        ///gif 图片需要重新设置
        if (self.image.images.count > 0) {
            UIImage *image = self.image;
            self.image = nil;
            self.image = image;
            [self setNeedsDisplay];
        }
        [self imyweb_callbackDownloadFinishedBlock:YES];
        return;
    }

    self.imyprivate_settingBlock.isRemoved = YES;
    self.imyprivate_settingBlock = nil;
    self.imy_imageLoadedURL = nil;
    
    if (imy_isNotBlankString(downloadImageURL.absoluteString)) {
        ///正式开始下载前的准备
        [self imyprivate_prepareDownloading];
        self.contentMode = self.imy_currentContentMode + IMYImageViewFixTag;

        ///开始下载
        [self imyprivate_startDownloadImageURL:downloadImageURL retryCount:retryCount];
    } else {
        [self sd_cancelCurrentImageLoad];
        self.image = self.imy_placeholderImage;
        self.imy_webImageState = IMYWebImageStateNone;
    }
}

///网络切换时 错误状态的imageView 重试下载一次
- (void)imyprivate_retryDownloadWebImage {
    if (IMYWebImageStateDownloadFailed != self.imy_webImageState) {
        return;
    }
    NSString *imageLoadedURL = self.imy_imageLoadedURL;
    if (imy_isBlankString(imageLoadedURL)) {
        return;
    }
    NSURL *imageURL = [NSURL URLWithString:imageLoadedURL];
    @weakify(self);
    [NSObject imy_asyncBlock:^{
        @strongify(self);
        [self imyprivate_startDownloadImageURL:imageURL retryCount:2];
    } onQueue:dispatch_get_main_queue() afterSecond:0 forKey:IMYWebImageQueueRetryKey];
}

- (void)imyprivate_startDownloadImageURL:(NSURL *)downloadImageURL retryCount:(NSUInteger)retryCount {
    @weakify(self);
    [self sd_setImageWithURL:downloadImageURL
            placeholderImage:self.imy_placeholderImage
                     options:SDWebImageRetryFailed | SDWebImageAvoidAutoSetImage | SDWebImageHighPriority
                    progress:^(NSInteger receivedSize, NSInteger expectedSize) {
                        @strongify(self);
                        [NSObject imy_asyncBlock:^{
                            @strongify(self);
                            double progress = MAX(0, MIN(1, receivedSize / (CGFloat)expectedSize));
                            if (self.imy_downloadProgress < 1 && self.imy_downloadProgress < progress) {
                                self.imy_downloadProgress = progress;
                            }
                        } onQueue:dispatch_get_main_queue() afterSecond:0 forKey:IMYWebImageQueueProgressKey];
                    } completed:^(UIImage *image, NSError *error, SDImageCacheType cacheType, NSURL *imageURL) {
                        @strongify(self);
                        /// 已经切换了图片请求URL，不进行任何操作
                        if (![self.sd_imageURL isEqual:imageURL]) {
                            return;
                        }
                        [self imyprivate_downloadCompetedWithError:error
                                                        retryCount:retryCount
                                                          imageURL:imageURL
                                                         cacheType:cacheType
                                                             image:image];
                    }];
}

- (void)imyprivate_downloadCompetedWithError:(NSError *)error
                                  retryCount:(NSUInteger)retryCount
                                    imageURL:(NSURL *)imageURL
                                   cacheType:(SDImageCacheType)cacheType
                                       image:(UIImage *)image {
    self.imy_downloadError = error;
    ///加载完成后 储存downloadImageURL
    [self imy_setImageLoadedURL:imageURL.absoluteString];

    @weakify(self);
    if (!error && image) {
        [NSObject imy_cancelBlockForKey:IMYWebImageQueueProgressKey];
        [NSObject imy_cancelBlockForKey:IMYWebImageQueueRetryKey];
        [IMYWebImageViewHelper removeRetryObserverImageView:self];

        self.imy_downloadProgress = 1;

        UIImage * (^transfromBlock)(UIImageView *, UIImage *) = self.imy_imageTransfromBlock;
        if (!transfromBlock && self.imy_isShowInscribedCircle) {
            transfromBlock = ^(UIImageView *_imageView, UIImage *_image) {
                return [_IMYWebImageUtils circleImageWithImage:_image];
            };
            self.imy_imageTransfromBlock = transfromBlock;
        }
        if (transfromBlock) {
            [NSObject imy_asyncBlock:^{
                @strongify(self);
                UIImage *showImage = transfromBlock(self, image) ?: image;
                imy_asyncMainBlock(^{
                    @strongify(self);
                    [self imyprivate_downloadSuccess:showImage cacheType:cacheType];
                });
            } onQueue:dispatch_get_global_queue(0, 0) afterSecond:0 forKey:IMYWebImageQueueRetryKey];
        } else {
            [self imyprivate_downloadSuccess:image cacheType:cacheType];
        }
    } else {
        /// 有网络就进行重试，最多重试1次 (底层会进行备用域名替换)
        if ([IMYNetState networkEnable] && retryCount < 1 && error.code < NSURLErrorBadURL) {
            // 进行自动重试
            NSLog(@"image download retry = %@", error);
            [NSObject imy_asyncBlock:^{
                @strongify(self);
                [self imyprivate_startDownloadImageURL:imageURL retryCount:(retryCount + 1)];
            } onQueue:dispatch_get_main_queue() afterSecond:0 forKey:IMYWebImageQueueRetryKey];
        } else {
#ifdef DEBUG
            if (error.code != 404 && error.code != NSURLErrorCancelled) {
                NSLog(@"image download fail error = %@", error);
            }
#endif
            // 下载出错，走错误逻辑(盖文字 or 图片)
            [IMYWebImageViewHelper addRetryObserverImageView:self];

            self.imy_webImageState = IMYWebImageStateDownloadFailed;
            [self imy_setupTap];
            [self imyweb_callbackDownloadFinishedBlock:NO];
        }
    }
}

- (void)imyprivate_downloadSuccess:(UIImage *)image cacheType:(SDImageCacheType)cacheType {
    ///取消之前的block设置
    self.imyprivate_settingBlock.isRemoved = YES;
    self.imyprivate_settingBlock = nil;

    BOOL hasMemoryCache = (cacheType == SDImageCacheTypeMemory);
    BOOL hasSmall = NO;
    if (!hasMemoryCache && [image isKindOfClass:UIImage.class] && image.images.count <= 1) {
        // 限定直接加载的图片大小
        const NSInteger maxImageSize = IMYSystem.screenWidth * IMYSystem.screenWidth;
        CGSize pixelSize = [image imy_pixelSize];
        hasSmall = (pixelSize.width * pixelSize.height) < maxImageSize;
    }

    if (hasMemoryCache || hasSmall) {
        self.imy_webImageState = IMYWebImageStateDownloadSucceed;
        UIViewContentMode showContentMode = self.imy_currentContentMode + IMYImageViewFixTag;
        BOOL hasAnimation = (!hasMemoryCache && self.animationWhenFirstTimeDownload);
        [self imy_setShowContentMode:showContentMode andImage:image hasAnimation:hasAnimation];

        [self imy_setupTap];
        [self imyweb_callbackDownloadFinishedBlock:YES];
    } else {
        _IMYWebImageSettingBlock *settingBlock = [_IMYWebImageSettingBlock new];
        settingBlock.weakView = self;
        settingBlock.isDecoded = [image isDecodedForDisplay];
        @weakify(self);
        [settingBlock setBlock:^{
            @strongify(self);
            self.imy_webImageState = IMYWebImageStateDownloadSucceed;
            UIViewContentMode showContentMode = self.imy_currentContentMode + IMYImageViewFixTag;
            BOOL hasAnimation = self.animationWhenFirstTimeDownload;
            [self imy_setShowContentMode:showContentMode andImage:image hasAnimation:hasAnimation];

            [self imy_setupTap];
            [self imyweb_callbackDownloadFinishedBlock:YES];

            self.imyprivate_settingBlock = nil;
        }];
        CGSize pixelSize = [image imy_pixelSize];
        settingBlock.imageSize = ceil(pixelSize.width * pixelSize.height);
        self.imyprivate_settingBlock = settingBlock;

        [UIImageView imyprivate_asyncQueueSettingImage:settingBlock];
    }
}

- (void)imyweb_callbackDownloadFinishedBlock:(BOOL)isSuccess {
    void (^downloadFinishedBlock)(id, BOOL) = self.imy_downloadFinishedBlock;
    if (downloadFinishedBlock) {
        downloadFinishedBlock(self, isSuccess);
    }
}

+ (void)imyprivate_asyncQueueSettingImage:(_IMYWebImageSettingBlock * const)settingBlock {
    static NSMutableArray *settingQueue;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        settingQueue = [NSMutableArray array];
    });
    [settingQueue addObject:settingBlock];
    if (settingQueue.count == 1) {
        [self imyprivate_startSettingQueue:settingQueue willExecuteBlock:settingBlock];
    } else {
        // 排序小图优先显示 (递减排序)
        [settingQueue sortUsingComparator:^NSComparisonResult(_IMYWebImageSettingBlock *obj1, _IMYWebImageSettingBlock *obj2) {
            // 显示中的在尾部
            if (obj1.weakView.window && !obj2.weakView.window) {
                return NSOrderedDescending;
            }
            if (!obj1.weakView.window && obj2.weakView.window) {
                return NSOrderedAscending;
            }
            // 未移除的在尾部
            if (obj1.isRemoved && !obj2.isRemoved) {
                return NSOrderedAscending;
            }
            if (!obj1.isRemoved && obj2.isRemoved) {
                return NSOrderedDescending;
            }
            // 体积小的在尾部
            if (obj1.imageSize < obj2.imageSize) {
                return NSOrderedDescending;
            }
            if (obj1.imageSize > obj2.imageSize) {
                return NSOrderedAscending;
            }
            return NSOrderedSame;
        }];
    }
}

+ (void)imyprivate_executeQueue:(NSMutableArray * const)settingQueue
                      delayTime:(double)delayTime
                      withBlock:(_IMYWebImageSettingBlock * const)settingBlock {
    _IMYWebImageSettingBlock *executeBlock = settingBlock;
    [settingQueue removeObject:executeBlock];

    // 判断block是否被取消（并找到一个可使用的block）
    while (true) {
        if (executeBlock.block && !executeBlock.isRemoved) {
            break;
        }
        if (settingQueue.count == 0) {
            executeBlock = nil;
            break;
        }
        executeBlock = settingQueue.lastObject;
        [settingQueue removeLastObject];
    }

    if (executeBlock) {
        void (^doBlock)(void) = executeBlock.block;
        executeBlock.isRemoved = YES;
        executeBlock.block = nil;
        if (doBlock) {
            doBlock();
        }
    }
    
    if (settingQueue.count == 0) {
        // 队列无数据
        return;
    }
    // 前后有间隔时间
    if (delayTime > 0) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delayTime * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            _IMYWebImageSettingBlock *nextExecuteBlock = settingQueue.lastObject;
            [self imyprivate_startSettingQueue:settingQueue willExecuteBlock:nextExecuteBlock];
        });
    } else {
        _IMYWebImageSettingBlock *nextExecuteBlock = settingQueue.lastObject;
        [self imyprivate_startSettingQueue:settingQueue willExecuteBlock:nextExecuteBlock];
    }
}

+ (void)imyprivate_startSettingQueue:(NSMutableArray * const)settingQueue
                    willExecuteBlock:(_IMYWebImageSettingBlock * const)settingBlock {
    static NSInteger continuedCount = 0;
    static double boundaryValue = 0;
    static double halfBoundaryValue = 0;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        boundaryValue = ceil(IMYSystem.screenWidth * IMYSystem.screenScale * IMYSystem.screenWidth * IMYSystem.screenScale);
        halfBoundaryValue = ceil(boundaryValue * 0.5);
    });
    // 延迟时间
    double delayTime = 0;
    const double imageSize = settingBlock.imageSize;
    if (imageSize > boundaryValue) {
        // 超过屏幕宽度的图片，每张间隔 0.24秒
        delayTime = 0.12;
        continuedCount = 0;
    } else if (imageSize > halfBoundaryValue) {
        // 超过一半屏幕宽度的图片，每张间隔 0.12秒
        delayTime = 0.06;
        continuedCount = 0;
    } else {
        continuedCount += 1;
        if (continuedCount > 3) {
            // 连续设置3张，则增加1帧间隔时间 (16.7ms 向上取整 20ms)
            delayTime = 0.02;
            continuedCount = 0;
        }
    }
    
    if (delayTime > 0) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delayTime * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self imyprivate_executeQueue:settingQueue
                                delayTime:delayTime
                                withBlock:settingBlock];
        });
    } else {
        [self imyprivate_executeQueue:settingQueue
                            delayTime:0
                            withBlock:settingBlock];
    }
}

///保证在主线程运行 就可以不用加锁了
- (void)imyprivate_prepareDownloading {
    if (!self.imy_setup) {
        self.imy_setup = YES;

        @weakify(self);
        self.imy_failureTap = [[_IMYWebImageTapGestureRecognizer alloc] bk_initWithHandler:^(UIGestureRecognizer *sender, UIGestureRecognizerState state, CGPoint location) {
            @strongify(self);
            if (self.imy_webImageState == IMYWebImageStateDownloadFailed) {
                [self imy_setImageURL:self.sd_imageURL];
            }
        }];

        self.imy_displayContentMode = self.contentMode;
        [self addToThemeChangeObserver];
        self.imy_originalTap = [self.gestureRecognizers bk_match:^BOOL(UIGestureRecognizer *gestureRecognizer) {
            return [gestureRecognizer isKindOfClass:[UITapGestureRecognizer class]];
        }];
        ///原来用RAC监听 是否显示进度条的放到了上面
    }
    
    [self sd_cancelCurrentImageLoad];
    self.imy_webImageState = IMYWebImageStateDownloading;
    self.imy_downloadProgress = 0;
    [self imy_placeholderImage];
    [self imy_failureImage];

    [self imy_setupTap];
}

- (void)imy_setShowContentMode:(UIViewContentMode)showContentMode andImage:(UIImage *)image {
    if (self.contentMode != showContentMode) {
        self.image = nil;
        self.contentMode = showContentMode;
        self.image = image;
        [self setNeedsDisplay];
    } else if (self.image != image) {
        self.image = nil;
        self.image = image;
        [self setNeedsDisplay];
    }
}

- (void)imy_setShowContentMode:(UIViewContentMode)showContentMode andImage:(UIImage *)image hasAnimation:(BOOL)hasAnimation {
    if (hasAnimation && self.window) {
        [UIView transitionWithView:self duration:0.22 options:UIViewAnimationOptionTransitionCrossDissolve animations:^{
            [self imy_setShowContentMode:showContentMode andImage:image];
        } completion:nil];
    } else {
        [self imy_setShowContentMode:showContentMode andImage:image];
    }
}

- (UIViewContentMode)imy_currentContentMode {
    switch (self.imy_webImageState) {
        case IMYWebImageStateDownloading: {
            return self.downloadContentMode;
        }
        case IMYWebImageStateDownloadFailed: {
            return self.failureContentMode;
        }
        default: {
            return self.imy_displayContentMode;
        }
    }
}

- (void)imy_setupTap {
    if (self.imy_failureTap) {
        [self removeGestureRecognizer:self.imy_failureTap];
    }
    if (self.imy_originalTap) {
        [self removeGestureRecognizer:self.imy_originalTap];
    }
    if (!self.imy_disableFailureTap && self.imy_webImageState == IMYWebImageStateDownloadFailed) {
        BOOL is404 = self.imy_downloadError.code == 404;
        BOOL isNetworkEnable = [IMYNetState networkEnable];
        BOOL is0px = [self.imy_downloadError.localizedDescription isEqualToString:@"Downloaded image has 0 pixels"];
        if (!is404 && !is0px) {
            [self _imySetUserInteractionEnabled:YES];
            [self addGestureRecognizer:self.imy_failureTap];
            return;
        }
    }
    ///默认都是用原始手势
    [self _imySetUserInteractionEnabled:self.imy_originalUserInteractionEnabled];
    if (self.imy_originalTap) {
        [self addGestureRecognizer:self.imy_originalTap];
    }
}

- (void)_imySetUserInteractionEnabled:(BOOL)enable {
    [super setUserInteractionEnabled:enable];
}

#pragma mark - IMYWebImageAutoCompressProtocol

- (void)imy_setImageURL:(id)URL resize:(CGSize)size {
    [self imy_setImageURL:URL resize:size quality:0 type:IMY_QiNiu_None];
}

- (void)imy_setImageURL:(id)URL resize:(CGSize)size quality:(NSInteger)quality {
    [self imy_setImageURL:URL resize:size quality:quality type:IMY_QiNiu_None];
}

- (void)imy_setImageURL:(id)URL type:(IMY_QiNiu_ImageType)type {
    [self imy_setImageURL:URL resize:CGSizeZero quality:0 type:type];
}

- (void)imy_setImageURL:(id)URL resize:(CGSize)size quality:(NSInteger)quality type:(IMY_QiNiu_ImageType)type {
    IMYQiNiuContentMode mode = 0;
    switch (self.contentMode) {
        case UIViewContentModeScaleAspectFit: {
            mode = IMYQiNiuContentModeAspectFit;
        } break;
        case UIViewContentModeScaleAspectFill: {
            mode = IMYQiNiuContentModeAspectFill;
        } break;
        default: {
            mode = IMYQiNiuContentModeToFill;
        } break;
    }
    [self imy_setImageURL:URL resize:size mode:mode quality:quality type:type];
}

- (void)imy_setImageURL:(id)URL resize:(CGSize)size mode:(IMYQiNiuContentMode)mode quality:(NSInteger)quality type:(IMY_QiNiu_ImageType)type {
    BOOL widthAutoSize = NO;
    BOOL heightAutoSize = NO;

    CGSize viewSize = self.imy_showViewSize;
    if (CGSizeEqualToSize(viewSize, CGSizeZero)) {
        viewSize = self.imy_size;
        widthAutoSize = (size.width < 0);
        heightAutoSize = (size.height < 0);
    } else {
        widthAutoSize = (size.width <= 0);
        heightAutoSize = (size.height <= 0);
    }
    if (widthAutoSize) {
        size.width = ceil(viewSize.width * IMYSystem.screenScale);
    }
    if (heightAutoSize) {
        size.height = ceil(viewSize.height * IMYSystem.screenScale);
    }

    NSString *URLString = [NSString qiniuURL:URL resize:size mode:mode quality:quality type:type];
    [self imy_setImageURL:URLString shouldAutoChangeQuality:NO];
}

- (void)imy_setImageURL:(id)URL quality:(NSInteger)quality __deprecated_msg("已废弃") {
    [self imy_setImageURL:URL resize:CGSizeZero quality:quality type:IMY_QiNiu_None];
}

- (void)imy_setImageURL:(id)URL quality:(NSInteger)quality type:(IMY_QiNiu_ImageType)type __deprecated_msg("已废弃") {
    [self imy_setImageURL:URL resize:CGSizeZero quality:quality type:type];
}

- (void)imy_setImageURL:(id)URL resise:(CGSize)size __deprecated_msg("已废弃") {
    return [self imy_setImageURL:URL resize:size];
}

- (void)imy_setImageURL:(id)URL resise:(CGSize)size quality:(NSInteger)quality __deprecated_msg("已废弃") {
    return [self imy_setImageURL:URL resize:size quality:quality];
}

- (void)imy_setImageURL:(id)URL resise:(CGSize)size quality:(NSInteger)quality type:(IMY_QiNiu_ImageType)type __deprecated_msg("已废弃") {
    return [self imy_setImageURL:URL resize:size quality:quality type:type];
}

- (void)imy_setImageURL:(id)URL resise:(CGSize)size mode:(IMYQiNiuContentMode)mode quality:(NSInteger)quality type:(IMY_QiNiu_ImageType)type __deprecated_msg("已废弃") {
    return [self imy_setImageURL:URL resize:size mode:mode quality:quality type:type];
}

@end

@implementation UIImageView (IMYWebImageProperty)

SYNTHESIZE_ASC_PRIMITIVE(imy_displayContentMode, setImy_displayContentMode, UIViewContentMode)

SYNTHESIZE_ASC_PRIMITIVE(imy_originalUserInteractionEnabled, setImy_originalUserInteractionEnabled, BOOL)

SYNTHESIZE_ASC_OBJ(imyprivate_settingBlock, setImyprivate_settingBlock)

SYNTHESIZE_ASC_OBJ(imy_failureShowText, setImy_failureShowText)

SYNTHESIZE_ASC_OBJ(imy_downloadError, setImy_downloadError)

SYNTHESIZE_ASC_PRIMITIVE(imy_setup, setImy_setup, BOOL)

SYNTHESIZE_ASC_OBJ(imy_originalTap, setImy_originalTap)

SYNTHESIZE_ASC_OBJ(imy_failureTap, setImy_failureTap)

@end

#pragma mark -UI
@implementation UIImageView (IMYWebImageUI)

SYNTHESIZE_ASC_OBJ(backgroundColorKey, setBackgroundColorKey)
SYNTHESIZE_ASC_OBJ(placeholderImageName, setPlaceholderImageName)
SYNTHESIZE_ASC_OBJ(failureImageName, setFailureImageName)
SYNTHESIZE_ASC_PRIMITIVE(animationWhenFirstTimeDownload, setAnimationWhenFirstTimeDownload, BOOL)

+ (NSMutableArray *)imy_defaultPlaceholderImageNames {
    static NSMutableArray *array;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        array = [NSMutableArray array];
    });
    return array;
}

+ (NSMutableArray *)imy_defaultFailureImageNames {
    static NSMutableArray *array;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        array = [NSMutableArray array];
    });
    return array;
}

+ (void)imy_addPlaceholderImageName:(NSString *)imageName {
    [self imy_addPlaceholderImageName:imageName fillMode:NO];
}

+ (void)imy_addPlaceholderImageName:(NSString *)imageName fillMode:(BOOL)fillMode {
    [self imy_array:[self imy_defaultPlaceholderImageNames] addImageName:imageName fillMode:fillMode];
}

+ (void)imy_addFailureImageName:(NSString *)imageName {
    [self imy_addFailureImageName:imageName fillMode:NO];
}

+ (void)imy_addFailureImageName:(NSString *)imageName fillMode:(BOOL)fillMode {
    [self imy_array:[self imy_defaultFailureImageNames] addImageName:imageName fillMode:fillMode];
}

+ (void)imy_array:(NSMutableArray *)array addImageName:(NSString *)imageName fillMode:(BOOL)fillMode {
    UIImage *image = [UIImage imy_imageForKey:imageName];
    if (image) {
        _IMYWebImageData *imageData = [[_IMYWebImageData alloc] init];
        imageData.isFillMode = fillMode;
        imageData.size = CGSizeMake(ceil(image.size.width), ceil(image.size.height));
        imageData.pixLenght = imageData.size.width * imageData.size.height;
        imageData.imageName = imageName;
        [array addObject:imageData];

        [array sortUsingComparator:^NSComparisonResult(_IMYWebImageData *obj1, _IMYWebImageData *obj2) {
            if (obj1.pixLenght < obj2.pixLenght) {
                return NSOrderedDescending;
            }
            return NSOrderedAscending;
        }];
    }
}

static const void *IMYWebImageDownloadContentModeKey = &IMYWebImageDownloadContentModeKey;
- (UIViewContentMode)downloadContentMode {
    id contentMode = objc_getAssociatedObject(self, IMYWebImageDownloadContentModeKey);
    if (contentMode) {
        return [contentMode integerValue];
    }
    return UIViewContentModeScaleAspectFill;
}

- (void)setDownloadContentMode:(UIViewContentMode)downloadContentMode {
    objc_setAssociatedObject(self, IMYWebImageDownloadContentModeKey, @(downloadContentMode), OBJC_ASSOCIATION_RETAIN);
}

static const void *IMYWebImageFailureContentModeKey = &IMYWebImageFailureContentModeKey;
- (UIViewContentMode)failureContentMode {
    id contentMode = objc_getAssociatedObject(self, IMYWebImageFailureContentModeKey);
    if (contentMode) {
        return [contentMode integerValue];
    }
    return UIViewContentModeScaleAspectFill;
}

- (void)setFailureContentMode:(UIViewContentMode)failureContentMode {
    objc_setAssociatedObject(self, IMYWebImageFailureContentModeKey, @(failureContentMode), OBJC_ASSOCIATION_RETAIN);
}

static const void *IMYWebImageProgressViewKey = &IMYWebImageProgressViewKey;
- (UIView *)imy_progressView {
    return objc_getAssociatedObject(self, IMYWebImageProgressViewKey);
}

- (void)imy_setProgressView:(UIView *)progressView {
    UIImageView *imageView = self;
    if (progressView) {
        [imageView addSubview:progressView];
        if ([progressView isKindOfClass:[THProgressView class]]) {
            [progressView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.centerY.equalTo(imageView);
                make.height.mas_equalTo(20);
                make.left.mas_equalTo(5);
                make.right.mas_equalTo(-5);
            }];
        } else {
            CGSize size = progressView.bounds.size;
            [progressView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.centerY.equalTo(imageView);
                make.centerX.equalTo(imageView);
                make.size.equalTo([NSValue valueWithCGSize:size]);
            }];
        }
    }
    objc_setAssociatedObject(self, IMYWebImageProgressViewKey, progressView, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

static const void *IMYWebImageShowInscribedCircleKey = &IMYWebImageShowInscribedCircleKey;
- (BOOL)imy_isShowInscribedCircle {
    NSNumber *number = objc_getAssociatedObject(self, IMYWebImageShowInscribedCircleKey);
    return [number boolValue];
}
- (void)imy_setShowInscribedCircle:(BOOL)showInscribedCircle {
    NSNumber *number = [NSNumber numberWithBool:showInscribedCircle];
    objc_setAssociatedObject(self, IMYWebImageShowInscribedCircleKey, number, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    if (showInscribedCircle && self.image) {
        [self imy_setCircleImage:self.image];
    }
}

static const void *IMYWebImageShowProgressKey = &IMYWebImageShowProgressKey;
- (BOOL)imy_showProgress {
    NSNumber *number = objc_getAssociatedObject(self, IMYWebImageShowProgressKey);
    return [number boolValue];
}
- (void)imy_setShowProgress:(BOOL)showProgress {
    NSNumber *number = [NSNumber numberWithBool:showProgress];
    objc_setAssociatedObject(self, IMYWebImageShowProgressKey, number, OBJC_ASSOCIATION_RETAIN_NONATOMIC);

    imy_syncMainExecuteBlock(^{
        if (showProgress && self.imy_webImageState != IMYWebImageStateDownloadSucceed) {
            [self imy_setupProgressView];
        } else {
            [self.imy_progressView removeFromSuperview];
            self.imy_progressView = nil; ///释放progressView
        }
    });
}

static const void *IMYWebImagePlaceholderImageKey = &IMYWebImagePlaceholderImageKey;
- (void)imy_setPlaceholderImage:(UIImage *)imy_placeholderImage {
    objc_setAssociatedObject(self, IMYWebImagePlaceholderImageKey, imy_placeholderImage, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}
- (UIImage *)imy_placeholderImage {
    if (self.showProgress) {
        return nil;
    }
    UIImage *image = objc_getAssociatedObject(self, IMYWebImagePlaceholderImageKey);
    if (!image) {
        NSString *imageName = self.placeholderImageName;
        if (imageName) {
            image = [UIImage imy_imageForKey:imageName];
        }
    }
    if (!image) {
        _IMYWebImageData *imageData = [self imy_getImageDataForArray:[UIImageView imy_defaultPlaceholderImageNames]];
        image = [UIImage imy_imageForKey:imageData.imageName];
        if (imageData.isFillMode) {
            image = [image imy_resizableImageCenter];
            self.downloadContentMode = UIViewContentModeScaleToFill;
        } else if (image.size.width > self.bounds.size.width || image.size.height > self.bounds.size.width) {
            self.downloadContentMode = UIViewContentModeScaleAspectFit;
        }
    }
    if (self.imy_isShowInscribedCircle && image) {
        image = [_IMYWebImageUtils circleImageWithImage:image];
        [self imy_setPlaceholderImage:image];
    }
    return image;
}

static const void *IMYWebImageFailureImageKey = &IMYWebImageFailureImageKey;
- (void)imy_setFailureImage:(UIImage *)imy_failureImage {
    objc_setAssociatedObject(self, IMYWebImageFailureImageKey, imy_failureImage, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}
- (UIImage *)imy_failureImage {
    UIImage *image = objc_getAssociatedObject(self, IMYWebImageFailureImageKey);
    if (!image) {
        NSString *imageName = self.failureImageName;
        if (imageName) {
            image = [UIImage imy_imageForKey:imageName];
        }
    }

    if (!image) {
        _IMYWebImageData *imageData = [self imy_getImageDataForArray:[UIImageView imy_defaultFailureImageNames]];
        image = [UIImage imy_imageForKey:imageData.imageName];
        if (imageData.isFillMode) {
            image = [image imy_resizableImageCenter];
            self.failureContentMode = UIViewContentModeScaleToFill;
        } else if (image.size.width > self.bounds.size.width || image.size.height > self.bounds.size.width) {
            self.failureContentMode = UIViewContentModeScaleAspectFit;
        }
    }
    if (self.imy_isShowInscribedCircle && image) {
        image = [_IMYWebImageUtils circleImageWithImage:image];
        [self imy_setFailureImage:image];
    }
    return image;
}

static const void *IMYWebImageDownloadProgressKey = &IMYWebImageDownloadProgressKey;
- (double)imy_downloadProgress {
    NSNumber *number = objc_getAssociatedObject(self, IMYWebImageDownloadProgressKey);
    return [number doubleValue];
}
- (void)imy_setDownloadProgress:(double)downloadProgress {
    NSNumber *number = [NSNumber numberWithDouble:downloadProgress];
    objc_setAssociatedObject(self, IMYWebImageDownloadProgressKey, number, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    [self observer_downloadProgress];
}

- (_IMYWebImageData *)imy_getImageDataForArray:(NSArray *)array {
    NSInteger origWidth = ceil(self.bounds.size.width);
    NSInteger origHeight = ceil(self.bounds.size.height);

    _IMYWebImageData *webImageData = nil;
    for (_IMYWebImageData *imageData in array) {
        if (imageData.size.width <= origWidth && imageData.size.height <= origHeight) {
            webImageData = imageData;
            break;
        } else if (imageData.isFillMode) {
            webImageData = imageData;
        }
    }
    if (!webImageData) {
        webImageData = array.firstObject;
    }
    return webImageData;
}

- (UILabel *)imy_failureShowLabel {
    UILabel *lable = objc_getAssociatedObject(self, @selector(imy_failureShowLabel));
    if (!lable) {
        lable = [[UILabel alloc] initWithFrame:self.bounds];
        lable.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
        lable.font = [UIFont systemFontOfSize:14];
        lable.textAlignment = NSTextAlignmentCenter;
        lable.numberOfLines = 0;
        [lable imy_setTextColorForKey:kCK_Black_C];
        [lable imy_setBackgroundColorForKey:kCK_Black_F];
        
        // 增加失败点击重试手势
        lable.userInteractionEnabled = YES;
        @weakify(self);
        _IMYWebImageTapGestureRecognizer *tap = [[_IMYWebImageTapGestureRecognizer alloc] bk_initWithHandler:^(UIGestureRecognizer *sender, UIGestureRecognizerState state, CGPoint location) {
            @strongify(self);
            [self imy_setImageURL:self.sd_imageURL];
        }];
        [lable addGestureRecognizer:tap];
        
        // 绑定到ImageView中，不重复创建
        objc_setAssociatedObject(self, @selector(imy_failureShowLabel), lable, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    }
    return lable;
}

#pragma mark - Theme

- (void)imy_themeChanged {
    UIViewContentMode showContentMode = self.imy_currentContentMode + IMYImageViewFixTag;
    IMYWebImageState imageState = self.imy_webImageState;
    if (imageState == IMYWebImageStateDownloading && !self.showProgress) {
        [self imy_setShowContentMode:showContentMode andImage:self.imy_placeholderImage];
    } else if (imageState == IMYWebImageStateDownloadFailed) {
        if (self.imy_failureShowText.length > 0) {
            // 清空占位图
            [self imy_setShowContentMode:showContentMode andImage:nil];
            // 设置文本
            [self addSubview:self.imy_failureShowLabel];
            self.imy_failureShowLabel.text = IMYString(self.imy_failureShowText);
        } else {
            [self imy_setShowContentMode:showContentMode andImage:self.imy_failureImage];
        }
    }
}
- (void)observer_downloadProgress {
    UIView *progressView = self.imy_progressView;

    ///当没有进度条时 取消下面的代码判断
    if (!progressView) {
        return;
    }

    CGFloat progress = self.imy_downloadProgress;

    CGSize psize = progressView.frame.size;
    if (psize.width == 0 || psize.height == 0) {
        return;
    }
    BOOL needAnimation = YES;
    if (progress == 0) {
        needAnimation = NO;
    }
    if (needAnimation && [progressView respondsToSelector:@selector(progress)]) {
        CGFloat showProgress = [(THProgressView *)progressView progress];
        ///如果小于当前显示的 进度 则取消赋值
        if (progress <= showProgress) {
            return;
        }
    }

    if ([progressView respondsToSelector:@selector(setProgress:animated:)]) {
        [(THProgressView *)progressView setProgress:progress animated:needAnimation];
    } else if ([progressView respondsToSelector:@selector(setProgress:)]) {
        [(THProgressView *)progressView setProgress:progress];
    }
}

- (id)imy_setupProgressView {
    if (!self.imy_progressView) {
        THProgressView *progressView = [[THProgressView alloc] initWithFrame:CGRectMake(0, 0, 20, 20)];
        progressView.borderTintColor = [UIColor whiteColor];
        progressView.progressTintColor = [UIColor whiteColor];
        self.imy_progressView = (id)progressView;
    }
    return self.imy_progressView;
}

@end

@implementation UIImageView (IMYWebImageState)

static const void *IMYWebImageDownloadFinishedBlockKey = &IMYWebImageDownloadFinishedBlockKey;
- (void (^)(UIImageView *, BOOL))imy_downloadFinishedBlock {
    return objc_getAssociatedObject(self, IMYWebImageDownloadFinishedBlockKey);
}
- (void)imy_setDownloadFinishedBlock:(void (^)(UIImageView *, BOOL))downloadFinishedBlock {
    objc_setAssociatedObject(self, IMYWebImageDownloadFinishedBlockKey, downloadFinishedBlock, OBJC_ASSOCIATION_COPY_NONATOMIC);
}

static const void *IMYWebImageImageTransfromBlockKey = &IMYWebImageImageTransfromBlockKey;
- (UIImage * (^)(UIImageView *, UIImage *))imy_imageTransfromBlock {
    return objc_getAssociatedObject(self, IMYWebImageImageTransfromBlockKey);
}
- (void)imy_setImageTransfromBlock:(UIImage * (^)(UIImageView *, UIImage *))imageTransfromBlock {
    objc_setAssociatedObject(self, IMYWebImageImageTransfromBlockKey, imageTransfromBlock, OBJC_ASSOCIATION_COPY_NONATOMIC);
}

static const void *IMYWebImageLoadedFinishedURLKey = &IMYWebImageLoadedFinishedURLKey;
- (NSString *)imy_imageLoadedURL {
    return objc_getAssociatedObject(self, IMYWebImageLoadedFinishedURLKey);
}
- (void)imy_setImageLoadedURL:(NSString *)imageURL {
    objc_setAssociatedObject(self, IMYWebImageLoadedFinishedURLKey, imageURL, OBJC_ASSOCIATION_COPY);
}

static const void *IMYWebImageStatePropertyKey = &IMYWebImageStatePropertyKey;
- (IMYWebImageState)imy_webImageState {
    NSNumber *number = objc_getAssociatedObject(self, IMYWebImageStatePropertyKey);
    return [number integerValue];
}
- (void)imy_setWebImageState:(IMYWebImageState)webImageState {
    [self willChangeValueForKey:@"imy_webImageState"];

    NSNumber *number = [NSNumber numberWithInteger:webImageState];
    objc_setAssociatedObject(self, IMYWebImageStatePropertyKey, number, OBJC_ASSOCIATION_RETAIN_NONATOMIC);

    // 移除进度条
    UIView *progressView = self.imy_progressView;
    if (IMYWebImageStateDownloading != webImageState) {
        [progressView.layer removeAllAnimations];
        [progressView.layer.presentationLayer removeAllAnimations];
        progressView.hidden = YES;
    } else {
        progressView.hidden = NO;
    }
    
    // 移除重试文本
    if (IMYWebImageStateDownloadFailed != webImageState) {
        [self.imy_failureShowLabel removeFromSuperview];
    }
    
    [self imy_themeChanged];

    [self didChangeValueForKey:@"imy_webImageState"];
}

static const void *IMYWebImageDisableFailureTapKey = &IMYWebImageDisableFailureTapKey;
- (BOOL)imy_disableFailureTap {
    return [objc_getAssociatedObject(self, IMYWebImageDisableFailureTapKey) boolValue];
}
- (void)imy_setDisableFailureTap:(BOOL)disableFailureTap {
    objc_setAssociatedObject(self, IMYWebImageDisableFailureTapKey, @(disableFailureTap), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    imy_asyncMainExecuteBlock(^{
        [self imy_setupTap];
    });
}
@end

@interface IMYWebImageViewHelper ()
@property (nonatomic, strong) NSHashTable *hashTable;
@end

@implementation IMYWebImageViewHelper

+ (NSString *)netStateChangedNotification {
    return [[self netStateClass] netStateChangedNotification];
}

+ (IMYWebImageViewHelper *)shareInstance {
    static id instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        self.hashTable = [NSHashTable weakObjectsHashTable];
        NSString *notifyName = [IMYWebImageViewHelper netStateChangedNotification];
        if (imy_isNotBlankString(notifyName)) {
            [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(networkDidChanged:) name:notifyName object:nil];
        }
    }
    return self;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)networkDidChanged:(id)notify {
    ///对错误状态的图片进行重试
    imy_asyncMainExecuteBlock(^{
        NSHashTable *hashTable = self.hashTable;
        [hashTable.allObjects enumerateObjectsUsingBlock:^(UIImageView *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
            [obj imyprivate_retryDownloadWebImage];
        }];
    });
}

+ (void)addRetryObserverImageView:(UIImageView *)imageView {
    if (!imageView) {
        return;
    }
    imy_asyncMainExecuteBlock(^{
        NSHashTable *hashTable = [self shareInstance].hashTable;
        if ([hashTable containsObject:imageView]) {
            return;
        }
        [hashTable addObject:imageView];
    });
}

+ (void)removeRetryObserverImageView:(UIImageView *)imageView {
    if (!imageView) {
        return;
    }
    imy_asyncMainExecuteBlock(^{
        NSHashTable *hashTable = [self shareInstance].hashTable;
        [hashTable removeObject:imageView];
    });
}

+ (BOOL)isWiFi {
    return [[self netStateClass] isWiFi];
}

+ (BOOL)isWWAN {
    return [[self netStateClass] isWWAN];
}

+ (BOOL)networkEnable {
    return [[self netStateClass] networkEnable];
}

+ (Class)netStateClass {
    static Class IMYNetStateClass = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        IMYNetStateClass = NSClassFromString(@"IMYNetState");
    });
    return IMYNetStateClass;
}

@end
