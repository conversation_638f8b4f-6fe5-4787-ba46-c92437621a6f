//
//  IMYReportDoctorModel.m
//  AFNetworking
//
//  Created by meetyou on 2024/11/27.
//

#import "IMYReportOperateModel.h"
#import <IMYBaseKit/IMYPublic.h>

#if __has_include(<IMYRecord/IMYRecordBabyManager.h>)
#import <IMYRecord/IMYRecordBabyManager.h>
#endif
FOUNDATION_EXPORT NSString *const MODULE_BABY_WEEK_CHANGE = @"baby_week_change";
FOUNDATION_EXPORT NSString *const MODULE_TODAY_SUGGEST = @"today_suggest";
FOUNDATION_EXPORT NSString *const MODULE_TODAY_REPORT = @"today_report";
FOUNDATION_EXPORT NSString *const MODULE_GESTATION_GROWTH_WEEK = @"gestation_growth_week";
FOUNDATION_EXPORT NSString *const MODULE_HEALTH_ENCYCLOPEDIA = @"health_encyclopedia";
FOUNDATION_EXPORT NSString *const MODULE_BABY_HOME_ALBUM = @"baby_home_album";
FOUNDATION_EXPORT NSString *const MODULE_FOLLOWER_HOME_ALBUM = @"follower_home_album";
FOUNDATION_EXPORT NSString *const MODULE_FETAL_MOVEMENT = @"fetal_movement";
FOUNDATION_EXPORT NSString *const MODULE_CONTRACTION = @"contraction";
FOUNDATION_EXPORT NSString *const PREGNANCY_HOME = @"pregnancy_home";
FOUNDATION_EXPORT NSString *const PREPARE_PREGNANCY_HOME = @"prepare_pregnancy_home";
FOUNDATION_EXPORT NSString *const EXPLAIN_REPORT = @"explain_report";
FOUNDATION_EXPORT NSString *const PAY_PRE_CONCEPTION_ANALYSIS = @"pay_pre_conception_analysis";
@implementation IMYReportOperateModel

+ (void)initialize {
    [self bindYYJSONKey:@"exposure_days" toProperty:@"exposureDays"];
    [self bindYYJSONKey:@"btn_content" toProperty:@"btnContent"];
    [self bindYYJSONKey:@"sub_content" toProperty:@"subContent"];
    [self bindYYJSONKey:@"module_key" toProperty:@"moduleKey"];
    [self bindYYJSONKey:@"week_range" toProperty:@"weekRange"];
    [self bindYYJSONKey:@"day_range" toProperty:@"dayRange"];
    [self bindYYJSONKey:@"id" toProperty:@"imyId"];
    [self bindYYJSONKey:@"pregnancy_week_range" toProperty:@"pregnancyWeekRange"];
    [self bindYYJSONKey:@"encyclopedia_entry" toProperty:@"encyclopediaEntry"];
    [self bindYYJSONKey:@"encyclopedia_category" toProperty:@"encyclopediaEntryCategoryId"];
    [self bindYYJSONKey:@"disappear_days" toProperty:@"disappearDays"];
    [self bindYYJSONKey:@"has_overlay" toProperty:@"hasOverlay"];
    [self bindYYJSONKey:@"overlay_content" toProperty:@"overlayContent"];
    [self bindYYJSONKey:@"pregnancy_days_range" toProperty:@"pregnancyDaysRange"];
    [self bindYYJSONKey:@"badge_content" toProperty:@"badgeContent"];
}

@end
