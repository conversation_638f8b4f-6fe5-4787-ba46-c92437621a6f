//
//  IMYReportDoctorModel.h
//  AFNetworking
//  运营列表数据模型
//  Created by meetyou on 2024/11/27.
//

#import <Foundation/Foundation.h>
#import "IMYCommonPeriodDefines.h"
#import "IMYCKOperateBannerModel.h"

NS_ASSUME_NONNULL_BEGIN

/// 引导类型: 1-文案 2-图片 3-图文 4-底部模块 5-互医贴边
typedef NS_ENUM(NSUInteger, IMYReportOperateType) {
    IMYReportOperateTypeText = 1,           // (文案：主文案，按键文案（非必填）)
    IMYReportOperateTypeImage = 2,              // (纯图片)
    IMYReportOperateTypeimageText = 3,          // (图文：主文案，副文案，图片)
    IMYReportOperateTypeBottom = 4,             // (底部模块 - 互医：主文案，副文案，图片，按键文案)
    IMYReportOperateTypeDoctorEdge = 5,         // (互医贴边：图片)
    IMYReportOperateTypeButton = 6,             // 独立按钮
    IMYReportOperateTypeIconText = 7,           // 图标+文案
    IMYReportOperateTypeIconTextButton = 8,     // 互医引导条 (图片、主文案、按钮文案)
    IMYReportOperateTypeBadge = 9,              // 带角标按钮 (按钮文案、角标文案)
    IMYReportOperateTypeBottom2 = 10,           // 会员免费医生解读 (与原版医生解读略有区别, 图片、主文案、副文案、按钮文案)
};

typedef NS_ENUM(NSUInteger, IMYReportOperateFrom) {
    IMYReportOperateFromBabyChange = 1,           // 宝宝周变化
    IMYReportOperateFromGestationGrowthWeek = 2,   // 孕周变化
    IMYReportOperateFromTodaySuggest = 3,          // 今日密报
    IMYReportOperateFromForPregnancyHome = 4          // 伴侣备孕首页
};
/** 模块key
 宝宝周变化 baby_week_change
 今日密报 today_suggest
 好孕日报 today_report
 孕周变化 gestation_growth_week
 健康百科 health_encyclopedia
 889版本:
 新增页面:
 宝宝首页-相册二级页    baby_home_album
 亲友宝宝首页-相册二级页  follower_home_album
 怀孕首页  pregnancy_home
 备孕首页  prepare_pregnancy_home
 894:
 产检报告解读 explain_report
 黄金受孕期 pay_pre_conception_analysis
 */
/** 宝宝周变化  */
FOUNDATION_EXPORT NSString *const MODULE_BABY_WEEK_CHANGE;
/** 今日密报  */
FOUNDATION_EXPORT NSString *const MODULE_TODAY_SUGGEST;
/** 好孕日报  */
FOUNDATION_EXPORT NSString *const MODULE_TODAY_REPORT;
/** 孕周变化  */
FOUNDATION_EXPORT NSString *const MODULE_GESTATION_GROWTH_WEEK;
/** 健康百科  */
FOUNDATION_EXPORT NSString *const MODULE_HEALTH_ENCYCLOPEDIA;
/** 宝宝首页-相册二级页  */
FOUNDATION_EXPORT NSString *const MODULE_BABY_HOME_ALBUM;
/** 亲友宝宝首页-相册二级页  */
FOUNDATION_EXPORT NSString *const MODULE_FOLLOWER_HOME_ALBUM;
/** 数胎动  */
FOUNDATION_EXPORT NSString *const MODULE_FETAL_MOVEMENT;
/** 数宫缩  */
FOUNDATION_EXPORT NSString *const MODULE_CONTRACTION;
/**  怀孕首页   */
FOUNDATION_EXPORT NSString *const PREGNANCY_HOME;
/** 备孕首页  */
FOUNDATION_EXPORT NSString *const PREPARE_PREGNANCY_HOME;
/** 产检报告解读*/
FOUNDATION_EXPORT NSString *const EXPLAIN_REPORT;
/** 黄金受孕期*/
FOUNDATION_EXPORT NSString *const PAY_PRE_CONCEPTION_ANALYSIS;

@interface IMYReportOperateModel : NSObject

/// id == imyId
@property (nonatomic, assign) NSInteger imyId;

/// 点击后跳转的uri
@property (nonatomic, copy) NSString *uri;

/// 曝光天数
@property (nonatomic, assign) NSInteger exposureDays;

/// 排序
@property (nonatomic, assign) NSInteger sort;

/// 引导类型: 1-文案 2-图片 3-图文 4-医生解读卡片 5-互医贴边 6-独立按钮 7-图标+文案
@property (nonatomic, assign) IMYReportOperateType type;

/// 图片
@property (nonatomic, copy) NSString *image;

/// 标题
@property (nonatomic, copy) NSString *title;

/// 按键文本
@property (nonatomic, copy) NSString *btnContent;

/// 副文案
@property (nonatomic, copy) NSString *subContent;

/// 主文案
@property (nonatomic, copy) NSString *content;

/// 角标文案
@property (nonatomic, copy) NSString *badgeContent;

/** 模块key
 宝宝周变化模块位置：
 顶部引导条, 宝宝本周概况, 生长发育周周看, 养育要点, 本周亲子游戏推荐, 本周可以为宝宝做什么
 
 辣妈今日密报模块位置:
 顶部引导条, 周期模块, 今日身体变化, 今日心理变化, 今日恶露变化, 月经知识, 推迟期知识, 两性建议, 运动建议, 饮食建议, 护肤建议, 睡眠建议, 产康提示, 症状自查, 心理建议, 身体修复, 私处护理
 
 互医模块:
 医生解读, 贴边
 
 孕期周变化模块
 顶部引导条, 胎宝宝本周概况, 准妈妈本周概况, 心理变化, 关爱提醒, 饮食建议, 产前检查, 常见问题, 准爸爸能做什么
 */
@property (nonatomic, copy) NSString *moduleKey;

/// 孕周数
@property (nonatomic, copy) NSString *pregnancyWeekRange;

/// 宝宝周数
@property (nonatomic, copy) NSString *weekRange;

/// 宝宝天数
@property (nonatomic, copy) NSString *dayRange;

/**
 育儿阶段
 产后修复-产褥期:1-1,
 产后修复-黄金期:1-2,
 产后修复-理想期:1-3,
 产后1年内来月经-产后月经期:2-1,
 产后一年内来月经-产后卵泡期:2-2,
 产后排卵日:2-3,
 产后黄体期:2-4,
 产后预测经期开始日:2-5,
 产后月经推迟期:2-6,
 超过产后1年-月经期:3-1,
 超过产后1年-卵泡期:3-2,
 超过产后1年-排卵日:3-3,
 超过产后1年-黄体期:3-4,
 超过产后1年-预测经期开始日:3-5,
 超过产后1年-月经推迟:3-6
 */
@property (nonatomic, copy) NSString *phase;

/** 经期阶段
 1-月经期 2-卵泡期 3-排卵日 4-黄体期 5-预测经期开始日 6-推迟期
 备孕时期: 13-月经期 14-孕几率上升期 15-易孕期 16-孕几率下降期 17-月经推迟期 18-预测经期开始日
 
 */
@property (nonatomic, assign) IMYRecordDRPeriodType period;

/**
 健康百科词条id,逗号分隔
 */
@property (nonatomic, copy) NSString* encyclopediaEntry;


/**
 健康百科分类id,逗号分隔
 */
@property (nonatomic, copy) NSString* encyclopediaEntryCategoryId;
/// 点击后不展示天数
@property (nonatomic, assign) NSInteger disappearDays;
/// 蒙层引导开关
@property (nonatomic, assign) NSInteger hasOverlay;
/// 蒙层引导文案
@property (nonatomic, copy) NSString *overlayContent;
/// 怀孕天数范围
@property (nonatomic, copy) NSString *pregnancyDaysRange;


//埋点需要

///  banner 图文上报事件名
@property (nonatomic, copy) NSString *eventName;

@property (nonatomic, assign) IMYReportOperateFrom public_type;

///文案类型内边不需要更改
///lama 今日密报不需要处理(涉及 经期今日密报),其他宝宝周变化/孕周期 需要
@property (nonatomic, assign) BOOL isNotChangeTextStyleMargin;
//标签 0-无 1-会员 2-邀请亲友 3-邀请伴侣
@property (nonatomic, assign) NSInteger label;

@end

NS_ASSUME_NONNULL_END
