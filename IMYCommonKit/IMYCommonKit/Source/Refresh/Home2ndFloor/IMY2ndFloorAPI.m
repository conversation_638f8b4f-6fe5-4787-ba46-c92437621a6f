//
//  IMY2ndFloorAPI.m
//  IMYCommonKit
//
//  Created by HBQ on 2025/6/13.
//

#import "IMY2ndFloorAPI.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import "IOC-Protocols/IOCMemberRights2ndFloorAdapter.h"

@implementation IMY2ndFloorAPI

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static IMY2ndFloorAPI *instance = nil;
    dispatch_once(&onceToken, ^{
        instance = [[super allocWithZone:NULL] init];
    });
    return instance;
}

+ (id)allocWithZone:(struct _NSZone *)zone {
    return [self sharedInstance];
}

// MARK: - 接口

/// GET 用户使用过汇总 https://apidoc.seeyouyima.com/doc/684a90f75d15134f533189e9
- (void)udimAllOnSuccess:(void (^)(NSDictionary *dict))onSuccess
                 onError:(void (^)(NSError *error))onError {
    NSMutableDictionary *parameter = [NSMutableDictionary dictionary];
    
    NSNumber *stageType = [[IMYURIManager sharedInstance] runActionAndSyncResultWithPath:@"chatAI/stageType" params:nil];
    [parameter imy_setNonNilObject:stageType forKey:@"stage_type"];
    NSNumber *babyMonth = [[IMYURIManager sharedInstance] runActionAndSyncResultWithPath:@"general/babyMonth" params:nil];
    [parameter imy_setNonNilObject:babyMonth forKey:@"baby_month"];
    NSNumber *pregnancyWeek = [[IMYURIManager sharedInstance] runActionAndSyncResultWithPath:@"general/pregnancyWeek" params:nil];
    [parameter imy_setNonNilObject:pregnancyWeek forKey:@"pregnancy_week"];
    
    NSDictionary *mainViewRequestParams = [IMYHIVE_BINDER(IOCMemberRights2ndFloorAdapter) mainViewRequestParams];
    [parameter imy_setNonNilObject:mainViewRequestParams forKey:@"vip_privileges_req"];
    
    RACSignal *signal = [IMYServerRequest postPath:@"udim/all" host:msp_seeyouyima_com params:parameter headers:nil];
    
    @weakify(self);
    RACSignal *resSignal = [signal flattenMap:^__kindof RACStream * _Nullable(id  _Nullable value) {
        @strongify(self);
        return [self.class responseFlattenMap:value];
    }];
    
    [[resSignal deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
        if (onSuccess) {
            onSuccess(x);
        }
    } error:^(NSError * _Nullable error) {
        if (onError) {
            onError(error);
        }
    }];
}

/// GET 用户访问过全部服务 https://apidoc.seeyouyima.com/doc/684a96b15d15134f53318a21
- (void)udimAllServiceOnSuccess:(void (^)(NSDictionary *dict))onSuccess
                        onError:(void (^)(NSError *error))onError {
    NSMutableDictionary *parameter = [NSMutableDictionary dictionary];
    
    RACSignal *signal = [IMYServerRequest getPath:@"udim/all_service" host:msp_seeyouyima_com params:parameter headers:nil];
    
    @weakify(self);
    RACSignal *resSignal = [signal flattenMap:^__kindof RACStream * _Nullable(id  _Nullable value) {
        @strongify(self);
        return [self.class responseFlattenMap:value];
    }];
    
    [[resSignal deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
        if (onSuccess) {
            onSuccess(x);
        }
    } error:^(NSError * _Nullable error) {
        if (onError) {
            onError(error);
        }
    }];
}

/// GET 全部服务 https://apidoc.seeyouyima.com/doc/685226e0ef511d4f6be02af6
- (void)serviceAllOnSuccess:(void (^)(NSDictionary *dict))onSuccess
                    onError:(void (^)(NSError *error))onError {
    NSMutableDictionary *parameter = [NSMutableDictionary dictionary];
    
    RACSignal *signal = [IMYServerRequest getPath:@"services/all" host:msp_seeyouyima_com params:parameter headers:nil];
    
    @weakify(self);
    RACSignal *resSignal = [signal flattenMap:^__kindof RACStream * _Nullable(id  _Nullable value) {
        @strongify(self);
        return [self.class responseFlattenMap:value];
    }];
    
    [[resSignal deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
        if (onSuccess) {
            onSuccess(x);
        }
    } error:^(NSError * _Nullable error) {
        if (onError) {
            onError(error);
        }
    }];
}

/// GET 用户下单过的全部商城 https://apidoc.seeyouyima.com/doc/684a97a5ef511d4f6be0105d
- (void)udimAllMallOnSuccess:(void (^)(NSDictionary *dict))onSuccess
                     onError:(void (^)(NSError *error))onError {
    NSMutableDictionary *parameter = [NSMutableDictionary dictionary];
    
    RACSignal *signal = [IMYServerRequest getPath:@"udim/all_mall" host:msp_seeyouyima_com params:parameter headers:nil];
    
    @weakify(self);
    RACSignal *resSignal = [signal flattenMap:^__kindof RACStream * _Nullable(id  _Nullable value) {
        @strongify(self);
        return [self.class responseFlattenMap:value];
    }];
    
    [[resSignal deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
        if (onSuccess) {
            onSuccess(x);
        }
    } error:^(NSError * _Nullable error) {
        if (onError) {
            onError(error);
        }
    }];
}

/// POST 用户删除使用过服务 https://apidoc.seeyouyima.com/doc/684a985140b4504f59bd525e
- (void)udimDelServiceWithServiceCode:(NSString *)serviceCode
                            onSuccess:(void (^)(NSDictionary *dict))onSuccess
                              onError:(void (^)(NSError *error))onError {
    NSMutableDictionary *parameter = [NSMutableDictionary dictionary];
    
    [parameter imy_setNonNilObject:serviceCode forKey:@"service_code"];
    
    RACSignal *signal = [IMYServerRequest postPath:@"udim/del_service" host:msp_seeyouyima_com params:parameter headers:nil];
    
    @weakify(self);
    RACSignal *resSignal = [signal flattenMap:^__kindof RACStream * _Nullable(id  _Nullable value) {
        @strongify(self);
        return [self.class responseFlattenMap:value];
    }];
    
    [[resSignal deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
        if (onSuccess) {
            onSuccess(x);
        }
    } error:^(NSError * _Nullable error) {
        if (onError) {
            onError(error);
        }
    }];
}

/// POST 删除下单过的商家 https://apidoc.seeyouyima.com/doc/68521a46adbe7a4f718ad785
- (void)udimDelMallWithMallCode:(NSString *)mallCode
                         source:(NSString *)source
                      onSuccess:(void (^)(NSDictionary *dict))onSuccess
                        onError:(void (^)(NSError *error))onError; {
    NSMutableDictionary *parameter = [NSMutableDictionary dictionary];
    
    [parameter imy_setNonNilObject:mallCode forKey:@"mall_code"];
    [parameter imy_setNonNilObject:source forKey:@"source"];
    
    RACSignal *signal = [IMYServerRequest postPath:@"udim/del_mall" host:msp_seeyouyima_com params:parameter headers:nil];
    
    @weakify(self);
    RACSignal *resSignal = [signal flattenMap:^__kindof RACStream * _Nullable(id  _Nullable value) {
        @strongify(self);
        return [self.class responseFlattenMap:value];
    }];
    
    [[resSignal deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
        if (onSuccess) {
            onSuccess(x);
        }
    } error:^(NSError * _Nullable error) {
        if (onError) {
            onError(error);
        }
    }];
}

// MARK: - 转换

/// IMYHTTPResponse 转 NSDictionary 或 error
+ (RACSignal *)responseFlattenMap:(IMYHTTPResponse *)x {
    if ([x.responseObject isKindOfClass:[NSDictionary class]]) {
        return [RACSignal return:x.responseObject];
    } else {
        NSMutableDictionary *userInfo = [NSMutableDictionary dictionaryWithDictionary:x.userInfo];
        userInfo[AFNetworkingOperationFailingURLResponseDataErrorKey] = x.responseData;
        userInfo[AFNetworkingOperationFailingURLResponseErrorKey] = x.response;
        userInfo[NSLocalizedDescriptionKey] = @"网络缓慢，请稍后再试";
        return [RACSignal error:[NSError errorWithDomain:@"ChatAI" code:-9 userInfo:userInfo]];
    }
}

// MARK: - 埋点

/// bi_feeds_view 埋点 https://metadata-dp.meiyou.com/scene/info?id=1104
- (void)bi_feeds_view:(NSInteger)action
             position:(NSInteger)position
       subscribe_type:(NSInteger)subscribe_type
                floor:(NSInteger)floor
             info_tag:(NSString *)info_tag
                index:(NSInteger)index
              info_id:(NSString *)info_id
          public_info:(NSString *)public_info {
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    
    [dict imy_setNonNilObject:@(action) forKey:@"action"];
    [dict imy_setNonNilObject:@(position) forKey:@"position"];
    // [dict imy_setNonNilObject:@(subscribe_type) forKey:@"subscribe_type"];
    [dict imy_setNonNilObject:@(floor) forKey:@"floor"];
    [dict imy_setNonNilObject:info_tag forKey:@"info_tag"];
    [dict imy_setNonNilObject:@(index) forKey:@"index"];
    [dict imy_setNonNilObject:info_id forKey:@"info_id"];
    [dict imy_setNonNilObject:public_info forKey:@"public_info"];
    
    [IMYGAEventHelper postWithPath:@"bi_feeds_view" params:dict headers:nil completed:nil];
}

/// ppt_sy_elyddx https://metadata-dp.meiyou.com/scene/info?id=1112
- (void)postEvent_ppt_sy_elyddx:(NSInteger)action {
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    [dict imy_setNonNilObject:@"pt_sy_elyddx" forKey:@"event"];
    [dict imy_setNonNilObject:@(action) forKey:@"action"];
    [IMYGAEventHelper postWithPath:@"event" params:dict headers:nil completed:nil];
}

@end
