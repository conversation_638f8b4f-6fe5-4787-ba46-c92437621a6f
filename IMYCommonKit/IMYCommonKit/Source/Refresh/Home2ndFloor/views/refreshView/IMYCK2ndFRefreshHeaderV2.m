//
//  IMYCK2ndFRefreshHeaderV2.m
//  IMYCommonKit
//
//  Created by HBQ on 2025/7/23.
//

#import "IMYCK2ndFRefreshHeaderV2.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import "IMYCKUserModeAnimationView.h"

#import "IMYTwoFloorFeedsAdExtendsInjector.h"
#import "IMY2ndFloorManager.h"
#import "IMY2ndFloorRootVC.h"
#import "IMY2ndFloorView.h"
#import "IMYCKABTestManager.h"
#import "IMY2ndFloorDebuger.h"

#import <IOCAppMainTabVC.h>

@interface IMYCK2ndFRefreshHeaderV2 ()

// MARK: - 视图层级

@property (nonatomic, strong) IMYCKUserModeAnimationView *userModeAnimationView;

@property (nonatomic, strong) IMY2ndFloorView *floorView;

// MARK: - 刷新控件数据

@property (nonatomic, assign) IMYCKRefreshState state;

@property (nonatomic, assign) CGFloat insetTDelta;

@property (nonatomic, assign) BOOL actionToTop;

@property (nonatomic, assign) BOOL superClipsToBounds;

// MARK: - 二楼数据

@property (nonatomic, weak) IMY2ndFloorRootVC *floorVC;

@property (nonatomic, weak) UIView *navBar;

@property (nonatomic, weak) UIView *navBackView;

@property (nonatomic, assign) NSInteger goOnPullLen;

@property (nonatomic, assign) CGFloat show2ndFloor_pullY;

@property (nonatomic, strong) UIView *show2ndFloor_snapView;

@property (nonatomic, strong) UIView *show2ndFloor_tabarView;

@property (nonatomic, assign) BOOL has2ndFloorData;

/// 广告注入器
@property (nonatomic, strong) id<IMYTwoFloorFeedsAdExtendsInjector> adInjector;

/// 广告数据
@property (nonatomic, copy) NSArray<NSDictionary *> *adDatas;

/// 广告数据（banner）
@property (nonatomic, copy) NSArray<NSDictionary *> *bannerAdDatas;

/// 总数据
@property (nonatomic, copy) NSArray<IMY2ndFloorGridModel *> *gridModels;


// MARK: - Debug

@property (nonatomic, strong) UILabel *debugInfoLabel;

@property (nonatomic, strong) UIView *debugRedDot;

// MARK: -

@end

@implementation IMYCK2ndFRefreshHeaderV2

@dynamic state;

+ (instancetype)headerWithRefreshingBlock:(MJRefreshComponentRefreshingBlock)refreshingBlock {
    IMYCK2ndFRefreshHeaderV2 *header = [[self alloc] init];
    header.refreshingBlock = refreshingBlock;
    return header;
}

- (void)didMoveToWindow {
    [super didMoveToWindow];
    
    if ([self useV2]) {
        // v2 不用开启全局渲染缓存
        return;
    }
    
    if (self.window) {
        IMYRM80AttributedLabel.enableGlobalSnapshotCaching = YES;
    } else {
        IMYRM80AttributedLabel.enableGlobalSnapshotCaching = NO;
    }
}

// MARK: - Override

- (void)prepare {
    [super prepare];
    
    self.clipsToBounds = NO;
    
    // 【数据】
    self.goOnPullLen = MJRefreshHeaderHeight;
    self.adDatas = [NSArray array];
    self.bannerAdDatas = [NSArray array];
    self.gridModels = [NSArray array];
    
    // 【视图层级】
    self.mj_w = SCREEN_WIDTH;
    self.mj_h = MJRefreshHeaderHeight;
    self.mj_y = -self.mj_h;
    [self addSubview:self.floorView];
    self.floorView.imy_bottom = self.imy_height;
    [self addSubview:self.userModeAnimationView];
    self.userModeAnimationView.imy_bottom = self.mj_h;
    [self delayGetView:0];
    
    // 【通知】
    @weakify(self);
    [[[[[[NSNotificationCenter defaultCenter] rac_addObserverForName:@"imy2ndfloor_rebuilddata" object:nil] deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] throttle:0.1] subscribeNext:^(NSNotification * _Nullable x) {
        @strongify(self);
        [self doReq2ndFloorData:YES];
    }];
    [[[[[NSNotificationCenter defaultCenter] rac_addObserverForName:@"secondFloor/reloadUdimAll" object:nil] takeUntil:self.rac_willDeallocSignal] deliverOnMainThread] subscribeNext:^(NSNotification * _Nullable x) {
        [IMYThrottle throttle:0.1 block:^{
            @strongify(self);
            [self doReq2ndFloorData:NO];
        }];
    }];
    
    // 【DEBUG】
#ifdef DEBUG
    BOOL isDebug = [[IMYKV defaultKV] boolForKey:@"#+DEBUG2NDFLOORREFRESHHEADER"];
    if (isDebug) {
        self.backgroundColor = UIColor.systemPinkColor;
        self.userModeAnimationView.backgroundColor = UIColor.systemBlueColor;
    }
    [[IMY2ndFloorDebuger sharedInstance] toggleDebug:isDebug];
#endif
    /// 创建一个广告实例，adInjector需要绑定一个身份
    [self adInjector];
}

- (void)placeSubviews {
    [super placeSubviews];
}

- (void)scrollViewPanStateDidChange:(NSDictionary *)change {
    [super scrollViewPanStateDidChange:change];
    
    if (self.scrollView.mj_offsetY < 0) {
        [self home2ndMarkPullY];
        [self home2ndSnapshot];
    }
}

- (void)scrollViewContentOffsetDidChange:(NSDictionary *)change {
    [super scrollViewContentOffsetDidChange:change];
    
    // 表示控件的原始 contentOffset.y
    CGFloat offsetY  = self.scrollView.mj_offsetY;
    // 表示真实下拉了多少（和 offsetY 一样下拉是负值）
    CGFloat pullY = offsetY + self.scrollViewOriginalInset.top;
    // 表示刷新控件透出百分比（只有正值是合法的，负值无意义）
    CGFloat pullPercent = 0;
    if (self.mj_h != 0) {
        pullPercent = MIN(1.0, -pullY / self.mj_h);
    }
    if (pullPercent < 0) {
        pullPercent = 0;
    }
    [[IMY2ndFloorDebuger sharedInstance] logOffsetY:offsetY pullY:pullY pullPercent:pullPercent];
    
    // 【无需处理】
    if (pullY >= 0) {
        // 这里的处理要确保多次调用不影响外部
        if (self.alpha != 0.0) {
            self.alpha = 0.0;
        }
        
        if (self.state != IMYCKRefreshStateRefreshing) {
            [self.userModeAnimationView stopAnimation];
        }
        
        [self setupSuperClipsToBounds:YES navAlpha:1.0];
        self.floorView.hidden = YES;
        [self.floorView setupStage:1];
        return;
    }
    
    // 【外部导航栏和裁剪】二楼实验 pullY < 0 导航栏渐隐（去掉裁剪），其他导航栏常显（恢复裁剪）
    if ([IMYCKABTestManager canShowHome2ndFloor] && pullY < 0) {
        [self setupSuperClipsToBounds:NO navAlpha:1.0];
    } else {
        [self setupSuperClipsToBounds:YES navAlpha:1.0];
    }
    
    // 【刷新控件】层级、透明度
    [self.scrollView bringSubviewToFront:self];
    if (pullY >= -self.mj_h) {
        // 渐显
        self.alpha = pullPercent;
        self.userModeAnimationView.alpha = pullPercent;
        
        // 这里再处理一下隐藏的情况（state 变化只会触发一次，隐藏动作会变渐显操作覆盖）
        if ([self reachStateGoOn:pullY] && self.state != IMYCKRefreshStateRefreshing) {
            self.userModeAnimationView.alpha = 0;
        }
    } else {
        // 常显
        self.alpha = 1.0;
    }
    
    // 【floorView】透明度
    if ([IMYCKABTestManager canShowHome2ndFloor] && [self reachStateWillWelcome:pullY]) {
        self.floorView.hidden = NO;
        [self.floorView setupStage:2];
        [self setupSuperClipsToBounds:NO navAlpha:0.0];
    } else if ([IMYCKABTestManager canShowHome2ndFloor]) {
        self.floorView.hidden = NO;
        [self.floorView setupStage:1];
    } else {
        self.floorView.hidden = YES;
    }
    
    // 【身份动画】位置
    [self adjustUserModeAnimationViewPosition:pullY];
    
    // 【透出刷新控件】
    if (self.state == IMYCKRefreshStateRefreshing) {
        if (self.window == nil) return;
        
        // 设置 mj_insetT 透出刷新控件
        CGFloat new_mj_insetT = self.scrollViewOriginalInset.top + self.mj_h;
        self.scrollView.mj_insetT = new_mj_insetT;
        
        // 记录 insetTDelta，用于闲置状态的恢复
        self.insetTDelta = self.scrollViewOriginalInset.top - new_mj_insetT;
        
        [[IMY2ndFloorDebuger sharedInstance] logInsetTDelta:self.insetTDelta];
        
        return;
    }
    
    // 【scrollViewOriginalInset】
    _scrollViewOriginalInset = self.scrollView.mj_inset;
    
    // TODO: debug actionToTop
    // 【actionToTop】正在回退禁止状态切换，回退完会切换
    if (self.actionToTop) {
        return;
    }
    
    // 【状态切换】放在最后处理，正好衔接 setState 方法
    if (self.scrollView.tracking) {
        if ([self reachStateWillWelcome:pullY]) {
            // 欢迎进入美柚二楼
            self.state = IMYCKRefreshStateWillWelcome;
        } else if ([self reachStateGoOn:pullY]) {
            // 继续下拉得惊喜
            self.state = IMYCKRefreshStateGoOn;
        } else if (pullY <= -self.mj_h) {
            // 松开就可以进行刷新的状态
            self.state = IMYCKRefreshStatePulling;
        } else if (pullY > -self.mj_h) {
            // 普通闲置状态
            self.state = IMYCKRefreshStateIdle;
        }
    } else {
        if (self.state == IMYCKRefreshStatePulling || self.state == IMYCKRefreshStateGoOn) {
            // 正在刷新中的状态(内部隐含了状态切换)
            [self beginRefreshing];
        } else if (self.state == IMYCKRefreshStateWillWelcome) {
            // 进入美柚二楼
            self.state = IMYCKRefreshStateWelcome;
        } else {
            // 普通闲置状态
            self.state = IMYCKRefreshStateIdle;
        }
    }
}

- (void)setState:(MJRefreshState)state {
    // 过滤重复、调用 super（记录 state，下一个主线程触发 setNeedsLayout）
    MJRefreshCheckState;
    
    [[IMY2ndFloorDebuger sharedInstance] logRefreshState:state];
    
    @weakify(self);
    // 表示控件的原始 contentOffset.y
    CGFloat offsetY  = self.scrollView.mj_offsetY;
    // 表示真实下拉了多少（和 offsetY 一样下拉是负值）
    CGFloat pullY = offsetY + self.scrollViewOriginalInset.top;
    // 表示刷新控件透出百分比（只有正值是合法的，负值无意义）
    CGFloat pullPercent = 0;
    if (self.mj_h != 0) {
        pullPercent = MIN(1.0, -pullY / self.mj_h);
    }
    if (pullPercent < 0) {
        pullPercent = 0;
    }
    self.actionToTop = NO;
    
    // 【处理不同状态】二楼
    if ([IMYCKABTestManager canShowHome2ndFloor]) {
        [self.floorView updateWithRefreshState:state];
    }
    
    // 【处理不同状态】刷新控件本身
    if (state == IMYCKRefreshStateIdle) {
        // [松手]普通闲置状态
        
        // 后续只处理这几种旧状态的变化
        if (oldState != IMYCKRefreshStateRefreshing &&
            oldState != IMYCKRefreshStateWillWelcome &&
            oldState != IMYCKRefreshStateWelcome &&
            oldState != IMYCKRefreshStateGif) {
            self.insetTDelta = 0;
            return;
        }
        
        // 恢复闲置位置
        CGFloat oldInsetTDelta = self.insetTDelta;
        self.insetTDelta = 0;
        [[IMY2ndFloorDebuger sharedInstance] logInsetTDelta:self.insetTDelta];
        if (pullY > 0) {
            self.scrollView.mj_insetT = self.scrollView.mj_insetT + oldInsetTDelta;
            if (self.endRefreshingCompletionBlock) {
                self.endRefreshingCompletionBlock();
            }
        } else {
            self.actionToTop = YES;
            [UIView animateWithDuration:MJRefreshSlowAnimationDuration animations:^{
                @strongify(self);
                self.scrollView.mj_insetT = self.scrollView.mj_insetT + oldInsetTDelta;
                
                CGPoint offset = self.scrollView.contentOffset;
                offset.y = - self.scrollView.mj_insetT;
                self.scrollView.contentOffset = offset;
                
                [self adjustUserModeAnimationViewPosition:offset.y + self.scrollView.mj_insetT];
            } completion:^(BOOL finished) {
                @strongify(self);
                self.actionToTop = NO;
                if (self.endRefreshingCompletionBlock) {
                    self.endRefreshingCompletionBlock();
                }
            }];
        }
    } else if (state == IMYCKRefreshStatePulling) {
        // [下拉]松开就可以进行刷新的状态。
        [self handleImpact];
    } else if (state == IMYCKRefreshStateRefreshing) {
        MJRefreshDispatchAsyncOnMainQueue({
            @strongify(self);
            // 1、开始播放身份动画
            self.userModeAnimationView.alpha = 1.0;
            [self.userModeAnimationView startAnimation];
            
            // 2、动画到刚好透出刷新控件
            [UIView animateWithDuration:MJRefreshFastAnimationDuration animations:^{
                @strongify(self);
                if (self.scrollView.panGestureRecognizer.state != UIGestureRecognizerStateCancelled) {
                    CGFloat top = self.scrollViewOriginalInset.top + self.mj_h;
                    // 增加滚动区域top
                    self.scrollView.mj_insetT = top;
                    // 设置滚动位置
                    CGPoint offset = self.scrollView.contentOffset;
                    offset.y = -top;
                    [self.scrollView setContentOffset:offset animated:NO];
                }
            } completion:^(BOOL finished) {
                @strongify(self);
                [self executeRefreshingCallback];
            }];
        })
    } else if (state == IMYCKRefreshStateWillRefresh) {
        // [下拉]即将刷新的状态。
        [self handleImpact];
    } else if (state == IMYCKRefreshStateNoMoreData) {
        // 所有数据加载完毕，没有更多的数据了。
    } else if (state == IMYCKRefreshStateGoOn || state == IMYCKRefreshStateWillWelcome) {
        // [下拉]继续下拉得惊喜、[下拉]欢迎进入美柚二楼。
        self.userModeAnimationView.alpha = 0.0;
        [self.userModeAnimationView stopAnimation];
        [self handleImpact];
    } else if (state == IMYCKRefreshStateWelcome) {
        // [松手]进入美柚二楼。
        [self goto2ndFloor];
    } else if (state == IMYCKRefreshStateGif) {
        // 二楼广告动效(无了，无此状态)
    }
}

- (void)beginRefreshing {
    [super beginRefreshing];
}

- (void)endRefreshing {
    [super endRefreshing];
    [self.userModeAnimationView stopAnimation];
    
    CGFloat offsetY  = self.scrollView.mj_offsetY;
    CGFloat pullY = offsetY + self.scrollViewOriginalInset.top;
    [self setupSuperClipsToBounds:YES navAlpha:1.0];
}

- (void)executeRefreshingCallback {
    [super executeRefreshingCallback];
    [[IMY2ndFloorDebuger sharedInstance] logExecuteRefreshingCallback];
}

// MARK: - Data

- (void)req2ndFloorDataIfNeed {
    BOOL needReq = !self.has2ndFloorData && [IMYCKABTestManager canShowHome2ndFloor];
    if (needReq) {
        [self doReq2ndFloorData:NO];
        self.has2ndFloorData = YES;
    }
    
    if ([IMYCKABTestManager canShowHome2ndFloor]) {
        [self doReqBannerAd];
    }
}

- (void)doReq2ndFloorData:(BOOL)useCache {
    @weakify(self);
    [[IMY2ndFloorManager sharedInstance] req2ndFloorData:^(NSArray<IMY2ndFloorGridModel *> * _Nonnull gridModels, NSError * _Nonnull error) {
        @strongify(self);
        
        self.gridModels = gridModels;
        
        [self mergeAllDatas];
        
        [self.floorVC refreshWithGridModels:self.gridModels scene:IMY2ndFloorViewScene_Home injector:self.adInjector];
    } useCache:useCache];
}

- (void)doReqAdDatas {
    [self.adInjector refresh];
}

- (void)doReqBannerAd {
    [self.adInjector refreshBanner];
}

- (void)didRefreshAd:(NSArray *)adDatas {
    self.adDatas = adDatas;
    [self mergeAllDatas];
    [self.floorVC refreshWithGridModels:self.gridModels scene:IMY2ndFloorViewScene_Home injector:self.adInjector];
}

- (void)didRefreshAdBanner:(NSArray *)adDatas {
    self.bannerAdDatas = adDatas;
    [self mergeAllDatas];
    [self.floorVC refreshWithGridModels:self.gridModels scene:IMY2ndFloorViewScene_Home injector:self.adInjector];
}

- (void)didDeleteAdBanner {
    @weakify(self);
    [self.floorView deleBannerAd:nil];
    [self.floorVC deleBannerAd:^(BOOL hasDeleted) {
        @strongify(self);
        if (hasDeleted) {
            self.gridModels = [self.gridModels bk_select:^BOOL(IMY2ndFloorGridModel *obj) {
                return obj.gridType != IMY2ndFloorGridType_banner_ad;
            }];
            self.bannerAdDatas = @[];
            [self mergeAllDatas];
        }
    }];
}

/// 融合数据
- (void)mergeAllDatas {
    NSMutableArray *newGridModels = [NSMutableArray array];
    
    // 0、顶部广告专区或者会员专区
    if (self.bannerAdDatas && self.bannerAdDatas.count > 0 && [IMYRightsSDK sharedInstance].currentRightsType == IMYRightsTypeNone) {
        IMY2ndFloorGridModel *bannerAdGridModel = [[IMY2ndFloorGridModel alloc] init];
        bannerAdGridModel.gridType = IMY2ndFloorGridType_banner_ad;
        bannerAdGridModel.bannerAdDatas = self.bannerAdDatas;
        [newGridModels insertObject:bannerAdGridModel atIndex:0];
    } else if ([IMYRightsSDK sharedInstance].currentRightsType != IMYRightsTypeNone) {
        for (IMY2ndFloorGridModel *gridModel in self.gridModels) {
            if (gridModel.gridType == IMY2ndFloorGridType_vip) {
                [newGridModels addObject:gridModel];
            }
        }
    }
    
    // 1、添加兜底
    NSInteger latestServiceCount = 0;
    NSInteger latestMallCount = 0;
    for (IMY2ndFloorGridModel *gridModel in self.gridModels) {
        if (gridModel.gridType == IMY2ndFloorGridType_latest_services) {
            latestServiceCount = gridModel.gridCellModels.count;
        } else if (gridModel.gridType == IMY2ndFloorGridType_latest_mall) {
            latestMallCount = gridModel.gridCellModels.count;
        }
    }
    if (latestServiceCount <= 0 && latestMallCount <= 0) {
        IMY2ndFloorGridModel *gridModel = [[IMY2ndFloorGridModel alloc] init];
        gridModel.isPlaceholder = YES;
        [newGridModels addObject:gridModel];
    }
        
    // 2、添加普通楼层
    for (IMY2ndFloorGridModel *gridModel in self.gridModels) {
        if ((gridModel.gridType == IMY2ndFloorGridType_latest_services ||
             gridModel.gridType == IMY2ndFloorGridType_latest_mall ||
             gridModel.gridType == IMY2ndFloorGridType_hot_service) && gridModel.gridCellModels.count > 0) {
            [newGridModels addObject:gridModel];
        }
    }

    // 3、添加网格广告
    for (IMY2ndFloorGridModel *gridModel in self.gridModels) {
        gridModel.gridCellModels = [gridModel.gridCellModels bk_select:^BOOL(IMY2ndFloorGridCellModel *obj) {
            return !obj.isAd;
        }];
    }
    for (IMY2ndFloorGridModel *gridModel in self.gridModels) {
        if (gridModel.gridType == IMY2ndFloorGridType_hot_service) {
            NSMutableArray *cellModels = [gridModel.gridCellModels mutableCopy];
            
            if (!cellModels) {
                cellModels = [NSMutableArray array];
            }
            
            /* 添加广告规则
             1.广告先按升序排列，每个广告插入位置 <= （热门服务数 + 当前已插入数）+ 1，可以插入广告，否则不能插入广告过滤掉
             2.广告插入后，热门服务的icon，往后挪
             3.ordinal = 1，插入在第一位，从 1 开始
             */
            for (NSDictionary *adData in self.adDatas) {
                IMY2ndFloorGridCellModel *cellModel = [[IMY2ndFloorGridCellModel alloc] init];
                cellModel.isAd = YES;
                cellModel.adData = adData;
                
                NSInteger ordinal = [adData[@"ordinal"] integerValue];
                NSInteger ordinal_index = ordinal - 1;
                if (ordinal_index < 0 || ordinal_index > cellModels.count) {
                    continue;
                } else if (ordinal_index == cellModels.count) {
                    [cellModels addObject:cellModel];
                } else {
                    [cellModels insertObject:cellModel atIndex:ordinal_index];
                }
            }
            
            gridModel.gridCellModels = cellModels;
        }
    }
    
    self.gridModels = newGridModels.copy;
}

// MARK: - Helper: 身份动画位置调整

- (void)adjustUserModeAnimationViewPosition:(CGFloat)y {
    if ([IMYCKABTestManager canShowHome2ndFloor]) {
        // 下拉前段居中，后段贴底
        if (y > -self.mj_h) {
            self.imy_centerY = y / 2.0;
        } else {
            self.mj_y = -self.mj_h;
        }
    } else {
        // 下拉居中
        self.imy_centerY = y / 2.0;
    }
}

// MARK: - Helper: Nav and clipsToBounds

/// 获取 navBar 和 navBackView（3秒内多次查询获取）
- (void)delayGetView:(CGFloat)delayTime {
    if (delayTime > 3) {
        return;
    }
    @weakify(self);
    imy_asyncMainBlock(0.3, ^{
        @strongify(self);
        [self forcedGetNavBar];
        [self forcedGetNavBackView];
        [self delayGetView:delayTime + 0.3];
    });
}

- (void)forcedGetNavBar {
    if (self.navBar) {
        return;
    }
    
    Class cls = NSClassFromString(@"IMYCKDynamicNavBar");
    if (cls == NULL) {
        NSCAssert(self, @"没有找到IMYCKDynamicNavBar，已经被删除");
    }
    
    UIView *navBar = nil;
    UIView *superView = self.superview;
    while (superView) {
        navBar = [superView imy_findSubviewWithClass:cls];
        if (navBar) {
            break;
        }
        superView = superView.superview;
        if ([superView isKindOfClass:[UIWindow class]]) {
            break;
        }
    }
    
    if (navBar) {
        [navBar.superview bringSubviewToFront:navBar];
        self.navBar = navBar;
    }
}

- (void)forcedGetNavBackView {
    if (self.navBackView) {
        return;
    }
    
    UIView *navBackView = nil;
    UIView *superView = self.superview;
    while (superView) {
        navBackView = [superView imy_findViewWithTag:91102];
        if (navBackView) {
            break;
        }
        superView = superView.superview;
        if ([superView isKindOfClass:[UIWindow class]]) {
            break;
        }
    }
    
    if (navBackView) {
        [navBackView.superview bringSubviewToFront:navBackView];
        self.navBackView = navBackView;
    }
}

- (void)setupSuperClipsToBounds:(BOOL)superClipsToBounds
                       navAlpha:(CGFloat)alpha {
    if (self.navBar != nil && self.navBar.alpha != alpha) {
        self.navBar.alpha = alpha;
    }
    
    if (self.navBackView != nil && self.navBackView.alpha != alpha) {
        self.navBackView.alpha = alpha;
    }
    
    if (self.superClipsToBounds != superClipsToBounds) {
        self.superClipsToBounds = superClipsToBounds;
        UIView *view = self;
        UIView *rootView = [UIViewController imy_currentTopViewController].view;
        while (view && view != rootView && view != self.navBar.superview) {
            if ([view isKindOfClass:[UIWindow class]]) {
                break;
            }
            
            if (view.clipsToBounds != superClipsToBounds && view != self) {
                view.clipsToBounds = superClipsToBounds;
            }
            
            view = view.superview;
        }
    }
}

// MARK: - Helper: ImpactFeedback

- (void)handleImpact {
    UIImpactFeedbackGenerator *g = [[UIImpactFeedbackGenerator alloc] initWithStyle:UIImpactFeedbackStyleLight];
    [g prepare];
    [g impactOccurred];
}

// MARK: - Helper: 2ndFloor util

- (BOOL)reachStateGoOn:(CGFloat)pullY {
    if (![IMYCKABTestManager canShowHome2ndFloor] || pullY >= 0) {
        return NO;
    }
    
    return fabs(pullY) >= self.goOnPullLen;
}

- (BOOL)reachStateWillWelcome:(CGFloat)pullY {
    if (![IMYCKABTestManager canShowHome2ndFloor] || pullY >= 0) {
        return NO;
    }
    
    return fabs(pullY) >= (self.goOnPullLen + 36);
}

- (void)home2ndMarkPullY {
    self.show2ndFloor_pullY = [self home2ndCurentPullY];
}

- (void)home2ndSnapshot {
    [self doSnapshot];
}

- (CGFloat)home2ndCurentPullY {
    return -(self.scrollView.mj_offsetY + self.scrollViewOriginalInset.top);
}

- (void)goto2ndFloor {
    BOOL readyGoto2ndFloor = NO;
    
    // 要求在首页
    BOOL isHome = YES;
    id<IOCAppMainTabVC> rootVC = IMYHIVE_BINDER(IOCAppMainTabVC);
    SYTabBarIndexType const tabIndexType = rootVC.selectedTabIndexType;
    if (tabIndexType != SYTabBarIndexTypeHome) {
        isHome = NO;
    }
    UINavigationController *navVC = [rootVC getRootVCWithTabIndexType:tabIndexType];
    if (navVC.viewControllers.count > 1 || navVC.presentedViewController) {
        isHome = NO;
    }
    
    readyGoto2ndFloor = isHome;
    
    if (!readyGoto2ndFloor) {
        self.userModeAnimationView.alpha = 0.0;
        [self.userModeAnimationView stopAnimation];
        [self endRefreshing];
        
#ifdef DEBUG
        NSString *toast = [NSString stringWithFormat:@"[DEBUG]不能进二楼！isHome = %@", isHome ? @"是" : @"否"];
        [UIWindow imy_showTextHUD:toast];
#endif
        
        return;
    }
    
    self.userModeAnimationView.alpha = 0.0;
    [self.userModeAnimationView stopAnimation];
    
    if ([IMYCKABTestManager canShowHome2ndFloor]) {
        @weakify(self);
        if (!self.floorVC) {
            self.floorVC = [[IMY2ndFloorRootVC alloc] init];
        }
        
        [self.floorVC showWithPullY:self.show2ndFloor_pullY
                           snapView:self.show2ndFloor_snapView
                         tabBarView:self.show2ndFloor_tabarView];
            
        [self doReq2ndFloorData:NO];
        [self doReqAdDatas];
    }
    
    [self endRefreshing];
}

// MARK: - Snapshot

- (void)doSnapshot {
    [self.show2ndFloor_snapView imy_removeAllSubviews];
    
    UIView *scrollView0 = [self snapScrollView:self.scrollView];
    [self.show2ndFloor_snapView addSubview:scrollView0];
    
    UIView *bottomView = [self snapBottomView];
    [self.show2ndFloor_snapView addSubview:bottomView];
    self.show2ndFloor_tabarView = bottomView;
    CGFloat pullY = [self home2ndCurentPullY];
    CGFloat fromY = pullY + SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT;
    bottomView.imy_bottom = SCREEN_HEIGHT - fromY;
}

- (UIView *)snapScrollView:(UIScrollView *)scrollView {
    // 获取要被截图的视图
    UIViewController *vc = [UIViewController imy_currentViewControlloer];
    UIView *view = vc.navigationController.view;
    
    // 创建快照视图
    CGFloat pullY = [self home2ndCurentPullY];
    CGFloat fromY = pullY + SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT;
    UIView *snapshotView = [view resizableSnapshotViewFromRect:CGRectMake(0, fromY, scrollView.mj_w, scrollView.mj_h)
                                            afterScreenUpdates:YES
                                                 withCapInsets:UIEdgeInsetsZero];
    
    // 确保快照视图的 frame 正确
    snapshotView.frame = CGRectMake(0, 0, scrollView.bounds.size.width, scrollView.bounds.size.height);
    
    return snapshotView;
}

- (UIView *)snapBottomView {
    UITabBarController *tabBarController = (UITabBarController *)[UIApplication sharedApplication].delegate.window.rootViewController;
    UIView *snap = [tabBarController.tabBar snapshotViewAfterScreenUpdates:YES];
    snap.frame = CGRectMake(0,
                            SCREEN_HEIGHT - SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT - SCREEN_TABBAR_HEIGHT,
                            SCREEN_WIDTH,
                            SCREEN_TABBAR_HEIGHT);
    return snap;
}

// MARK: - Get

- (IMYCKUserModeAnimationView*)userModeAnimationView {
    if (!_userModeAnimationView) {
        CGRect initFrame = CGRectMake(0, 0, SCREEN_WIDTH, self.mj_h);
        _userModeAnimationView = [[IMYCKUserModeAnimationView alloc] initWithFrame:initFrame];
        _userModeAnimationView.hidden = NO;
    }
    return _userModeAnimationView;
}

- (BOOL)useV2 {
    return YES;
}

- (IMY2ndFloorView *)floorView {
    if (!_floorView) {
        _floorView = [[IMY2ndFloorView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
        [_floorView setupBackViewAlpha:0.0];
        [_floorView setupCoverViewAlpha:1.0];
        [_floorView setupTableViewAlpha:0.1];
    }
    return _floorView;
}

- (UIView *)show2ndFloor_snapView {
    if (!_show2ndFloor_snapView) {
        _show2ndFloor_snapView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
        _show2ndFloor_snapView.clipsToBounds = YES;
    }
    return _show2ndFloor_snapView;
}

- (id<IMYTwoFloorFeedsAdExtendsInjector>)adInjector {
    if (!_adInjector) {
        _adInjector = [[IMYHiveMind sharedInstance] getBinder:@protocol(IMYTwoFloorFeedsAdExtendsInjector)];
        
        @weakify(self);
        [_adInjector setRefreshWithBlock:^(NSArray<NSDictionary *> *adDatas) {
            @strongify(self);
            [self didRefreshAd:adDatas];
        }];
        [_adInjector setRefreshBannerWithBlock:^(NSArray<NSDictionary *> *adDatas) {
            @strongify(self);
            [self didRefreshAdBanner:adDatas];
        }];
        [_adInjector setBannerDeleteBlock:^{
            @strongify(self);
            imy_asyncMainBlock(^{
                @strongify(self);
                [self didDeleteAdBanner];
            });
        }];
    }
    return _adInjector;
}

@end
