//
//  IMY2ndFloorView.m
//  IMYCommonKit
//
//  Created by HBQ on 2025/6/10.
//

#import "IMY2ndFloorView.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import <IMY2ndFloorCell.h>
#import "IMY2ndFloorVIPCell.h"
#import "IMY2ndFloorUdimAllModel.h"
#import "IMY2ndFloorManager.h"
#import "IMY2ndFloorAPI.h"

@interface IMY2ndFloorView () <IMYPAGViewDelegate, UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) IMYSafeArray *gridModels;
@property (nonatomic, assign) IMY2ndFloorViewScene scene;

@property (nonatomic, strong) UIView *coverView;

/// 大容器
@property (nonatomic, strong) UIView *contentView;

@property (nonatomic, strong) UIView *fakeNav;
@property (nonatomic, strong) UILabel *fakeTitleLabel;
@property (nonatomic, strong) IMYTouchEXButton *fakeBackBtn;
@property (nonatomic, strong) IMYTouchEXButton *searchBtn;

@property (nonatomic, strong) UITableView *tableView;

@property (nonatomic, strong) UIView *backView;
@property (nonatomic, strong) UILabel *backTitleLabel;

@property (nonatomic, strong) UIView *deleteView;
@property (nonatomic, strong) IMYButton *deleteBtn;

@property (nonatomic, strong) UILabel *stateLabel;
@property (nonatomic, strong) IMYPAGView *statePagView;

@property (nonatomic, assign) CGRect longPressIconFromFrame;
@property (nonatomic, assign) CGPoint longPressOffset;
@property (nonatomic, strong) UIImageView *longPressIcon;
@property (nonatomic, assign) BOOL isCanDelete;

@property (nonatomic, strong) id<IMYTwoFloorFeedsAdExtendsInjector> injector;
@property (nonatomic, copy) NSString *defaultTitle;

@end

@implementation IMY2ndFloorView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupSubviews];
    }
    return self;
}

// MARK: - Subviews

- (void)setupSubviews {
    [self imy_setBackgroundColorForKey:kCK_Clear_A];
    
    // contentView
    [self addSubview:self.contentView];
    self.contentView.frame = CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);
    
    // nav
    [self.contentView addSubview:self.fakeNav];
    [self.fakeNav addSubview:self.fakeTitleLabel];
    [self.fakeNav addSubview:self.fakeBackBtn];
    [self.fakeNav addSubview:self.searchBtn];
    [self.fakeNav mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.contentView.mas_top).offset(SCREEN_STATUSBAR_HEIGHT);
        make.leading.mas_equalTo(self.contentView.mas_leading).offset(0);
        make.trailing.mas_equalTo(self.contentView.mas_trailing).offset(0);
        make.height.mas_equalTo(SCREEN_NAVIGATIONBAR_HEIGHT);
    }];
    [self.fakeTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(self.fakeNav);
        make.centerY.mas_equalTo(self.fakeNav);
    }];
    [self.fakeBackBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.fakeNav.mas_leading).offset(12);
        make.width.mas_equalTo(24);
        make.height.mas_equalTo(24);
        make.centerY.mas_equalTo(self.fakeNav);
    }];
    [self.searchBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.mas_equalTo(self.fakeNav.mas_trailing).offset(-12);
        make.width.mas_equalTo(62);
        make.height.mas_equalTo(24);
        make.centerY.mas_equalTo(self.fakeNav);
    }];

    // tableView
    [self.contentView addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.fakeNav.mas_bottom).offset(0);
        make.leading.mas_equalTo(self.contentView.mas_leading).offset(0);
        make.trailing.mas_equalTo(self.contentView.mas_trailing).offset(0);
        make.height.mas_equalTo(SCREEN_HEIGHT - SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT);
    }];
    
    // 返回首页按钮
    [self addSubview:self.backView];
    [self.backView addSubview:self.backTitleLabel];
    [self.backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.mas_leading).offset(0);
        make.trailing.mas_equalTo(self.mas_trailing).offset(0);
        make.bottom.mas_equalTo(self.mas_bottom).offset(0);
        make.height.mas_equalTo(75);
    }];
    [self.backTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.backView).offset(16);
        make.centerX.mas_equalTo(self.backView);
    }];
    self.backTitleLabel.text = @"回到首页";
    
    // 删除按钮
    [self addSubview:self.deleteView];
    [self.deleteView addSubview:self.deleteBtn];
    [self.deleteView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.mas_leading).offset(0);
        make.trailing.mas_equalTo(self.mas_trailing).offset(0);
        make.bottom.mas_equalTo(self.mas_bottom).offset(0);
        make.height.mas_equalTo(75);
    }];
    [self.deleteBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.deleteView).offset(16);
        make.centerX.mas_equalTo(self.deleteView);
    }];
    
    // cover
    [self addSubview:self.coverView];
    
    // state
    [self addSubview:self.stateLabel];
    [self.stateLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.mas_bottom).offset(0);
        make.height.mas_equalTo(54);
        make.centerX.mas_equalTo(self);
    }];
    [self addSubview:self.statePagView];
    [self.statePagView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.stateLabel.mas_trailing);
        make.width.mas_equalTo(16);
        make.height.mas_equalTo(16);
        make.centerY.mas_equalTo(self.stateLabel);
    }];
    
    [self addSubview:self.longPressIcon];
    
    [self refreshTitle:@"最近"];
}

// MARK: - Public

/// 刷新数据
- (void)refreshWithGridModels:(NSArray<IMY2ndFloorGridModel *> *)gridModels
                        scene:(IMY2ndFloorViewScene)scene {
    self.gridModels = [IMYSafeArray arrayWithArray:gridModels];
    self.scene = scene;
    @weakify(self);
    imy_asyncMainBlock(^{
        @strongify(self);
        // 标题
        if (scene == IMY2ndFloorViewScene_Home) {
            [self refreshTitle:@"最近"];
            [self refreshBackBtnHide:YES];
        } else {
            IMY2ndFloorGridModel *gridModel = [gridModels firstObject];
            
            // 如果没有标题就使用 默认标题
            if (imy_isNotEmptyString(gridModel.title)) {
                [self refreshTitle:gridModel.title];
            } else {
                [self refreshTitle:self.defaultTitle];
            }
            
            [self refreshBackBtnHide:NO];
        }
        
        // 列表
        [self.tableView reloadData];
    });
}


/// 刷新数据（广告注入器）
- (void)refreshWithGridModels:(NSArray<IMY2ndFloorGridModel *> *)gridModels
                        scene:(IMY2ndFloorViewScene)scene
                     injector:(id<IMYTwoFloorFeedsAdExtendsInjector>)injector {
    self.injector = injector;
    [self refreshWithGridModels:gridModels scene:scene];
    [self.injector registerCellWithTableView:self.tableView];
}

/// 刷新数据（广告注入器、默认标题）
- (void)refreshWithGridModels:(NSArray<IMY2ndFloorGridModel *> *)gridModels
                        scene:(IMY2ndFloorViewScene)scene
                     injector:(id<IMYTwoFloorFeedsAdExtendsInjector>)injector
                 defaultTitle:(NSString *)defaultTitle {
    self.injector = injector;
    self.defaultTitle = defaultTitle;
    [self refreshWithGridModels:gridModels scene:scene];
}

/// 外部刷新导航栏标题
- (void)refreshTitle:(NSString *)title {
    self.fakeTitleLabel.text = title;
}

/// 隐藏返回
- (void)refreshBackBtnHide:(BOOL)isHide {
    self.fakeBackBtn.hidden = isHide;
}

/// 隐藏搜索
- (void)refreshSearchBtnHide:(BOOL)isHide {
    self.searchBtn.hidden = isHide;
}

/// 修改底部高度
- (void)refreshFooterWithHeight:(CGFloat)height {
    UIView *footer = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, height)];
    [footer imy_setBackgroundColorForKey:kCK_Clear_A];
    self.tableView.tableFooterView = footer;
}

/// 感知：刷新控件下拉了多少
- (void)updateWithRefreshPullY:(CGFloat)pullY {
//    NSLog(@"[2nd floor][感知：刷新控件下拉了多少 %@]", @(pullY));
//    
//    CGFloat refreshHeight = 54.0;
//    
//    CGFloat visibleSpace = pullY + SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT;
//    self.contentView.imy_top = self.imy_height - visibleSpace;
//    self.contentView.imy_height = visibleSpace;
//    
//    CGFloat pullPercent = pullY / (SCREEN_HEIGHT - SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT);
//    pullPercent = ABS(pullPercent);
//    if (pullPercent > 1.0) {
//        pullPercent = 1.0;
//    }
//    
//    if (pullPercent < 0.85) {
//        pullPercent = 0.85;
//    }
//    
//    self.tableView = CGAffineTransformMakeScale(pullPercent, pullPercent);
}

/// 感知：刷新控件状态变化
- (void)updateWithRefreshState:(IMYCKRefreshState)state {
    NSLog(@"[2nd floor][感知：刷新控件状态变化 %@]", @(state));
    
    NSString *startTitle = @"";
    NSString *firstTitle = @"继续下拉进入最近使用的服务";
    NSString *secondTitle = @"我最近使用的都在这里";
    
    NSString *title = startTitle;
    
    switch (state) {
        case IMYCKRefreshStateGoOn: {
            title = firstTitle;
            // [self showArrow:NO];
        }
            break;
        case IMYCKRefreshStateWillWelcome: {
            title = secondTitle;
            // [self showArrow:YES];
        }
            break;
        case IMYCKRefreshStateWelcome: {
            title = secondTitle;
            // [self showArrow:YES];
        }
            break;
        default: {
            title = startTitle;
            // [self showArrow:NO];
        }
            break;
    }
    
    self.stateLabel.text = title;
}

/// 设置：封面透明度
- (void)setupCoverViewAlpha:(CGFloat)alpha {
    self.coverView.alpha = alpha;
}

/// 设置：回到首页按钮透明度
- (void)setupBackViewAlpha:(CGFloat)alpha {
    self.backView.alpha = alpha;
}

/// 设置：列表透明度
- (void)setupTableViewAlpha:(CGFloat)alpha {
    self.tableView.alpha = alpha;
}

/// 设置：阶段（1阶段透明。2阶段全显示封面 0.3 透明度）
- (void)setupStage:(CGFloat)stage {
    if (stage == 1) {
        self.contentView.alpha = 0.0;
        self.coverView.alpha = 0.0;
    } else if (stage == 2) {
        self.contentView.alpha = 1.0;
        self.coverView.alpha = 0.3;
    } else {
        self.contentView.alpha = 1.0;
        self.coverView.alpha = 1.0;
    }
}

/// 删除广告 banner
- (void)deleBannerAd:(void (^)(BOOL hasDeleted))cb {
    NSMutableArray *deleteIdxs = [NSMutableArray array];
    NSInteger row = 0;
    for (IMY2ndFloorGridModel *gridModel in self.gridModels) {
        if (gridModel.gridType == IMY2ndFloorGridType_banner_ad) {
            [deleteIdxs addObject:[NSIndexPath indexPathForRow:row inSection:0]];
        }
        row = row + 1;
    }
    
    if (deleteIdxs.count > 0) {
        @weakify(self);
        
        [self.tableView beginUpdates];
        NSArray *array = [self.gridModels bk_select:^BOOL(IMY2ndFloorGridModel *obj) {
            return obj.gridType != IMY2ndFloorGridType_banner_ad;
        }];
        self.gridModels = [IMYSafeArray arrayWithArray:array];
        [self.tableView deleteRowsAtIndexPaths:deleteIdxs withRowAnimation:UITableViewRowAnimationFade];
        [self.tableView endUpdates];
        
        if (cb) {
            cb(YES);
        }
    } else {
        if (cb) {
            cb(NO);
        }
    }
}

// MARK: - Adjust UI

- (void)adjustUIWithPullY:(CGFloat)pullY {
    
}

- (void)showArrow:(BOOL)isShow {
    if (isShow) {
        // 显示箭头
        self.statePagView.hidden = NO;
        [self.stateLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.centerX.mas_equalTo(self).offset(-16);
        }];
    } else {
        // 隐藏箭头
        self.statePagView.hidden = YES;
        [self.stateLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.centerX.mas_equalTo(self).offset(0);
        }];
    }
}

// MARK: - Actions

- (void)handleNavBackEvent {
    if (self.clickNavBackBlock) {
        self.clickNavBackBlock();
    }
}

- (void)handleClickSearchBtnEvent {
    if (self.clickSearchBtn) {
        self.clickSearchBtn();
    }
}

- (void)handleBackBtnClickEvent {
    if (self.clickBackBlock) {
        self.clickBackBlock();
    }
}

- (void)handlelongPressWithFloorCell:(IMY2ndFloorCell *)floorCell
                       longPressCell:(IMY2ndFloorGridCell *)longPressCell
                               atIdx:(NSIndexPath *)idx
                                  gr:(UILongPressGestureRecognizer *)gr {
    // 回调给 VC 预处理手势
    if (self.longPressGridBlock) {
        self.longPressGridBlock(longPressCell, idx, gr);
    }
    
    UIGestureRecognizerState state = gr.state;
    switch (state) {
        case UIGestureRecognizerStateBegan: {
            // get icon
            UIImageView *icon = [longPressCell getIcon];
            
            // get icon from frame
            CGRect iconFromFrame = [icon.superview convertRect:icon.frame toView:self];
            self.longPressIconFromFrame = iconFromFrame;
            
            // cal location
            CGPoint grLocation = [gr locationInView:self];
            CGFloat grOffsetX = grLocation.x - CGRectGetMidX(iconFromFrame);
            CGFloat grOffsetY = grLocation.y - CGRectGetMidY(iconFromFrame);
            self.longPressOffset = CGPointMake(grOffsetX, grOffsetY);
            
            // setup longPressIcon
            self.longPressIcon.image = icon.image;
            self.longPressIcon.center = [gr locationInView:self];
            
            // animation
            longPressCell.alpha = 1.0;
            longPressCell.hidden = NO;
            
            self.longPressIcon.alpha = 0.0;
            self.longPressIcon.hidden = YES;
            
            self.deleteView.alpha = 0.0;
            self.deleteView.hidden = YES;
            
            self.longPressIcon.frame = iconFromFrame;
            
            [UIView animateWithDuration:0.3 animations:^{
                longPressCell.alpha = 0.0;
                longPressCell.hidden = YES;
                
                self.longPressIcon.alpha = 0.6;
                self.longPressIcon.hidden = NO;
                
                self.deleteView.alpha = 1.0;
                self.deleteView.hidden = NO;
                
                self.longPressIcon.imy_size = CGSizeMake(66, 66);
                self.longPressIcon.center = CGPointMake(grLocation.x - self.longPressOffset.x, grLocation.y - self.longPressOffset.y);
            } completion:^(BOOL finished) {
                
            }];
        }
            break;
        case UIGestureRecognizerStateChanged: {
            CGPoint grLocation = [gr locationInView:self];
            
            self.longPressIcon.imy_size = CGSizeMake(66, 66);
            self.longPressIcon.center = CGPointMake(grLocation.x - self.longPressOffset.x, grLocation.y - self.longPressOffset.y);
            
            if (self.longPressIcon.imy_bottom >= self.imy_height - 75) {
                [self.deleteBtn setTitle:IMYString(@"松开即可删除") forState:UIControlStateNormal];
                
                if (!self.isCanDelete) {
                    if (@available(ios 10.0, *)) {
                        UIImpactFeedbackGenerator *generator = [[UIImpactFeedbackGenerator alloc] initWithStyle:UIImpactFeedbackStyleLight];
                        [generator prepare];
                        [generator impactOccurred];
                    }
                }
                
                self.isCanDelete = YES;
            } else {
                [self.deleteBtn setTitle:IMYString(@"删除") forState:UIControlStateNormal];
                self.isCanDelete = NO;
            }
        }
            break;
        case UIGestureRecognizerStateCancelled:
        case UIGestureRecognizerStateFailed:
        case UIGestureRecognizerStateEnded: {
            // animation
            if (!self.isCanDelete) {
                longPressCell.alpha = 0.0;
                longPressCell.hidden = YES;
                
                self.longPressIcon.alpha = 0.6;
                self.longPressIcon.hidden = NO;
                
                self.deleteView.alpha = 1.0;
                self.deleteView.hidden = NO;
                
                [UIView animateWithDuration:0.3 animations:^{
                    self.longPressIcon.frame = self.longPressIconFromFrame;
                } completion:^(BOOL finished) {
                    longPressCell.alpha = 1.0;
                    longPressCell.hidden = NO;
                    
                    self.longPressIcon.alpha = 0.0;
                    self.longPressIcon.hidden = YES;
                    
                    self.deleteView.alpha = 0.0;
                    self.deleteView.hidden = YES;
                }];
            } else {
                [self handleDeleteGrid:floorCell gridCell:longPressCell idx:idx];
            }
        }
            break;
        default:
            break;
    }
}

- (void)handleDeleteGrid:(IMY2ndFloorCell *)floorCell
                gridCell:(IMY2ndFloorGridCell *)gridCell
                     idx:(NSIndexPath *)idx {
    IMY2ndFloorGridModel *gridModel0 = floorCell.gridModel;
    IMY2ndFloorGridCellModel *cellModel0 = gridCell.gridCellModel;
    
    // 页面内把外部传进来的数据，先删一次（这个数据可能是副本，所以回调到 rootVC 的时候会有一个统一删除的机会）
    for (IMY2ndFloorGridModel *gridModel in self.gridModels) {
        if (gridModel.gridType == gridModel0.gridType) {
            gridModel.gridCellModels = [gridModel.gridCellModels bk_select:^BOOL(IMY2ndFloorGridCellModel *cellModel) {
                return ![cellModel.code isEqualToString:cellModel0.code];
            }];
        }
    }
    
    // 只删除 cell 内数据
    @weakify(self, gridModel0, cellModel0);
    [floorCell deleteWithIndex:idx cb:^{
        imy_asyncMainBlock(^{
            @strongify(self, gridModel0, cellModel0);
            if (self.onDelBlock) {
                self.onDelBlock(gridModel0, cellModel0);
            }
            
            [self.tableView reloadData];
        });
    }];
    
    self.longPressIcon.alpha = 0.0;
    self.longPressIcon.hidden = YES;
    
    self.deleteView.alpha = 0.0;
    self.deleteView.hidden = YES;
}

// MARK: - UITableViewDelegate

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.gridModels.count;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    IMY2ndFloorGridModel *gridModel = self.gridModels[indexPath.row];
    
    if (gridModel.gridType == IMY2ndFloorGridType_banner_ad) {
        return [self.injector tableView:tableView heightForRowAtIndexPath:indexPath adData:gridModel.bannerAdDatas];
    }
    
    if (gridModel.gridType == IMY2ndFloorGridType_vip) {
        return [IMY2ndFloorVIPCell cellHeightWithWidth:SCREEN_WIDTH gridModel:gridModel];
    }
    
    return [IMY2ndFloorCell cellHeightWithWidth:SCREEN_WIDTH gridModel:gridModel];
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    IMY2ndFloorGridModel *gridModel = self.gridModels[indexPath.row];
    gridModel.bi_floor = indexPath.row + 1;
    
    if (gridModel.gridType == IMY2ndFloorGridType_banner_ad) {
        return [self.injector tableView:tableView cellForRowAtIndexPath:indexPath adData:gridModel.bannerAdDatas];
    }
    
    if (gridModel.gridType == IMY2ndFloorGridType_vip) {
        static NSString *identifier = @"IMY2ndFloorVIPCell";
        IMY2ndFloorVIPCell *cell = [tableView dequeueReusableCellWithIdentifier:identifier];
        if (!cell) {
            cell = [[IMY2ndFloorVIPCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:identifier];
        }
        [cell setGridModel:gridModel];
        return cell;
    }
    
    static NSString *identifier = @"IMY2ndFloorCell";
    IMY2ndFloorCell *cell = [tableView dequeueReusableCellWithIdentifier:identifier];
    if (!cell) {
        cell = [[IMY2ndFloorCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:identifier];
    }
    [cell setGridModel:gridModel injector:self.injector];
    
    @weakify(self, cell);
    
    [cell setClickMoreBlock:^(IMY2ndFloorGridModel * _Nonnull gridModel) {
        @strongify(self);
        if (self.clickMoreBlock) {
            self.clickMoreBlock(indexPath, gridModel);
        }
    }];
    
    [cell setClickGridBlock:^(IMY2ndFloorGridModel * _Nonnull gridModel, NSIndexPath * _Nonnull collectionIdx) {
        @strongify(self);
        if (self.clickGridBlock) {
            self.clickGridBlock(indexPath, gridModel, collectionIdx);
        }
    }];
    
    [cell setLongPressGridBlock:^(IMY2ndFloorGridCell * _Nonnull longPressCell, NSIndexPath * _Nonnull idx, UILongPressGestureRecognizer * _Nonnull gr) {
        @strongify(self, cell);
        [self handlelongPressWithFloorCell:cell longPressCell:longPressCell atIdx:idx gr:gr];
    }];
    
    return cell;
}

- (void)tableView:(UITableView *)tableView willDisplayCell:(UITableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath {
    IMY2ndFloorGridModel *gridModel = self.gridModels[indexPath.row];
    gridModel.bi_floor = indexPath.row + 1;
    
    if (gridModel.gridType == IMY2ndFloorGridType_banner_ad) {
        return [self.injector tableView:tableView willDisplayCell:cell forRowAtIndexPath:indexPath adData:gridModel.bannerAdDatas];
    }
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    
}

// MARK: - Get

- (UIView *)contentView {
    if (!_contentView) {
        _contentView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
        [_contentView imy_setBackgroundColorForKey:@"212229"];
        _contentView.clipsToBounds = YES;
    }
    return _contentView;
}

- (UIView *)coverView {
    if (!_coverView) {
        _coverView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
        [_coverView imy_setBackgroundColorForKey:kIMY_BG];
    }
    return _coverView;
}

- (UIView *)fakeNav {
    if (!_fakeNav) {
        _fakeNav = [[UIView alloc] initWithFrame:CGRectMake(0, SCREEN_STATUSBAR_HEIGHT, SCREEN_WIDTH, SCREEN_NAVIGATIONBAR_HEIGHT)];
        [_fakeNav imy_setBackgroundColorForKey:kCK_Clear_A];
    }
    return _fakeNav;
}

- (UILabel *)fakeTitleLabel {
    if (!_fakeTitleLabel) {
        _fakeTitleLabel = [[UILabel alloc] init];
        _fakeTitleLabel.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
        [_fakeTitleLabel imy_setTextColorForKey:kCK_White_A];
    }
    return _fakeTitleLabel;
}

- (IMYTouchEXButton *)fakeBackBtn {
    if (!_fakeBackBtn) {
        _fakeBackBtn = [[IMYTouchEXButton alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
        [_fakeBackBtn imy_setImageForKey:@"imy2ndfl_back" andState:UIControlStateNormal];
        _fakeBackBtn.hidden = YES;
        
        @weakify(self);
        [_fakeBackBtn bk_whenTapped:^{
            @strongify(self);
            [self handleNavBackEvent];
        }];
    }
    return _fakeBackBtn;
}

- (IMYTouchEXButton *)searchBtn {
    if (!_searchBtn) {
        _searchBtn = [[IMYTouchEXButton alloc] initWithFrame:CGRectMake(0, 0, 62, 24)];
        [_searchBtn imy_setImageForKey:@"imy2ndfl_searchBtn" andState:UIControlStateNormal];
        _searchBtn.hidden = NO;
        _searchBtn.imyut_eventInfo.eventName = [NSString stringWithFormat:@"second_floor_search"];
        @weakify(self);
        [_searchBtn bk_whenTapped:^{
            @strongify(self);
            [self handleClickSearchBtnEvent];
            [self postSecondFloorSearchBarClickEvent];
        }];
        _searchBtn.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
            @strongify(self);
            [self postSecondFloorSearchBarExposureEvent];
        };
    }
    return _searchBtn;
}

- (UITableView *)tableView {
    if (!_tableView) {
        UITableView *tableView = [[UITableView alloc] initWithFrame:CGRectZero];
        tableView.dataSource = self;
        tableView.delegate = self;
        tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        [tableView imy_setBackgroundColorForKey:kCK_Clear_A];
        
        UIView *footer = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 75)];
        [footer imy_setBackgroundColorForKey:kCK_Clear_A];
        tableView.tableFooterView = footer;
        
        _tableView = tableView;
        [_tableView registerClass:[IMY2ndFloorCell class] forCellReuseIdentifier:@"IMY2ndFloorCell"];
        [_tableView registerClass:[IMY2ndFloorVIPCell class] forCellReuseIdentifier:@"IMY2ndFloorVIPCell"];
    }
    return _tableView;
}

- (UIView *)backView {
    if (!_backView) {
        _backView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 75)];
        
        // 创建高斯模糊效果（20% 模糊强度对应 UIBlurEffectStyleLight）
        UIBlurEffect *blurEffect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleLight];
        UIVisualEffectView *blurView = [[UIVisualEffectView alloc] initWithEffect:blurEffect];
        blurView.frame = _backView.bounds;
        blurView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
        [_backView addSubview:blurView];
        
        _backView.backgroundColor = [UIColor clearColor];
        
        [_backView imy_drawTopCornerRadius:8.0];
        
        @weakify(self);
        [_backView bk_whenTapped:^{
            @strongify(self);
            [self handleBackBtnClickEvent];
        }];
    }
    return _backView;
}

- (UILabel *)backTitleLabel {
    if (!_backTitleLabel) {
        _backTitleLabel = [[UILabel alloc] init];
        _backTitleLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightRegular];
        [_backTitleLabel imy_setTextColorForKey:kCK_White_A];
    }
    return _backTitleLabel;
}

- (UIView *)deleteView {
    if (!_deleteView) {
        _deleteView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 75)];
        
        _deleteView.backgroundColor = [UIColor imy_colorForKey:kCK_Red_E];
        
        [_deleteView imy_drawTopCornerRadius:8.0];
        
        [_deleteView setAlpha:0.0];
    }
    return _deleteView;
}

- (IMYButton *)deleteBtn {
    if (!_deleteBtn) {
        _deleteBtn = [[IMYButton alloc] initWithFrame:CGRectMake(0, 0, 0, 0)];
        _deleteBtn.titleLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightRegular];
        [_deleteBtn setTitleColor:[IMYColor colorWithNormal:kCK_White_A]];
        [_deleteBtn imy_setBackgroundColorForKey:kIMYClearColorKey];
        [_deleteBtn setTitle:IMYString(@"删除") forState:UIControlStateNormal];
        UIImage *image = [[UIImage imy_imageForKey:@"imy2ndfl_delete"] imy_scaledImageWithSize:CGSizeMake(20, 20)];
        [_deleteBtn setImage:image forState:UIControlStateNormal];
        [_deleteBtn setImageAtDirection:IMYDirectionLeft];
        [_deleteBtn setOffset:4];
    }
    return _deleteBtn;
}

- (UILabel *)stateLabel {
    if (!_stateLabel) {
        UILabel *lb = [[UILabel alloc] init];
        lb.font = [UIFont systemFontOfSize:15 weight:UIFontWeightRegular];
        [lb imy_setTextColorForKey:kCK_Black_J];
        _stateLabel = lb;
    }
    return _stateLabel;
}

- (IMYPAGView *)statePagView {
    if (!_statePagView) {
        _statePagView = [[IMYPAGView alloc] initWithFrame:CGRectMake(0, 0, 16, 16)];
        _statePagView.delegate = self;
        
        NSURL *pagURL = [[NSBundle mainBundle] URLForResource:@"home2ndFloor_arrow" withExtension:@"pag"];
        
        [_statePagView loadWithURL:pagURL placeholder:nil completed:nil];
        
        _statePagView.hidden = YES;
    }
    return _statePagView;
}

- (UIImageView *)longPressIcon {
    if (!_longPressIcon) {
        _longPressIcon = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, 66, 66)];
        _longPressIcon.hidden = YES;
        _longPressIcon.userInteractionEnabled = YES;
    }
    return _longPressIcon;
}

/// Mark:  GA

// 二楼搜索框点击埋点
- (void)postSecondFloorSearchBarClickEvent {
    NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
    gaParams[@"pos_id"] = @(79);
    gaParams[@"func"] = @(1);
    gaParams[@"search_icon_type"] = @(1); // 放大镜
    [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];
}

// 二楼搜索框曝光埋点
- (void)postSecondFloorSearchBarExposureEvent {
    NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
    gaParams[@"pos_id"] = @(79);//79
    gaParams[@"func"] = @(2);//2
    gaParams[@"search_icon_type"] = @(1); // 放大镜
    [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];
}

@end
