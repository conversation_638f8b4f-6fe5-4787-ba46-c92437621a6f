//
//  IMY2ndFloorVIPCell.m
//  IMYCommonKit
//
//  Created by HBQ on 2025/8/11.
//

#import "IMY2ndFloorVIPCell.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import <IOC-Protocols/IOCMemberRights2ndFloorAdapter.h>

@interface IMY2ndFloorVIPCell ()

@property (nonatomic, strong) UIView<IOCMemberRights2ndFloorMainView> *mrMainView;

@end

@implementation IMY2ndFloorVIPCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        [self setSelectionStyle:UITableViewCellSelectionStyleNone];
        [self setupUI];
    }
    return self;
}

- (void)awakeFromNib {
    [super awakeFromNib];
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];
}

// MARK: - setup

- (void)setupUI {
    [self imy_setBackgroundColorForKey:kCK_Clear_A];
    [self.contentView imy_setBackgroundColorForKey:kCK_Clear_A];
    
    [self.contentView addSubview:self.mrMainView];
    self.mrMainView.imy_top = 8;
}

// MARK: - public

- (void)setGridModel:(IMY2ndFloorGridModel *)gridModel {
    _gridModel = gridModel;
    [self.mrMainView setupWithData:gridModel.vip_privileges_resp];
}

+ (CGFloat)cellHeightWithWidth:(CGFloat)width
                     gridModel:(IMY2ndFloorGridModel *)gridModel {
    if (IMYHIVE_BINDER(IOCMemberRights2ndFloorAdapter)) {
        return 8 + [IMYHIVE_BINDER(IOCMemberRights2ndFloorAdapter) mainViewSize].height + 16;
    }
    return CGFLOAT_MIN;
}

// MARK: - get

- (UIView<IOCMemberRights2ndFloorMainView> *)mrMainView {
    if (!_mrMainView) {
        _mrMainView = [IMYHIVE_BINDER(IOCMemberRights2ndFloorAdapter) mainView];
    }
    return _mrMainView;
}

@end
