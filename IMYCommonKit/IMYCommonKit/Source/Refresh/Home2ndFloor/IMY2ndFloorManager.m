//
//  IMY2ndFloorManager.m
//  IMYCommonKit
//
//  Created by HBQ on 2025/6/12.
//

#import "IMY2ndFloorManager.h"
#import "IMY2ndFloorAPI.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import "IMY2ndFloorUdimAllModel.h"
#import "IMYPublic.h"
#import <IOC-Protocols/IOCAppMainTabVC.h>
#import <IOC-Protocols/IOCMemberRights2ndFloorAdapter.h>
#import "IMY2ndFloorGuideView.h"

#if __has_include(<IMYAdvertisement/IMYAdSplashManager.h>)
#import <IMYAdvertisement/IMYAdSplashManager.h>
#endif


@interface IMY2ndFloorManager () <IMYPAGViewDelegate>

@property (nonatomic, strong) IMY2ndFloorUdimAllModel *udimAllModel;
@property (nonatomic, copy) NSArray<IMY2ndFloorGridModel *> *gridModels;
@property (nonatomic, assign) BOOL hasReq2ndFloorData;

@property (nonatomic, strong) IMY2ndFloorGuideView *secondFloorGuidPagView;

/// 插屏广告信号量
@property (nonatomic, strong) RACDisposable *popupSplashSignal;

/// 开屏广告信号量
@property (nonatomic, strong) RACDisposable *adSplashTypeSignal;

@property (nonatomic, assign) BOOL isChecking;

@end

@implementation IMY2ndFloorManager

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static IMY2ndFloorManager *instance = nil;
    dispatch_once(&onceToken, ^{
        instance = [[super allocWithZone:NULL] init];
        instance.udimAllModel = nil;
        instance.gridModels = [NSArray array];
        [instance addNotification];
    });
    return instance;
}

+ (id)allocWithZone:(struct _NSZone *)zone {
    return [self sharedInstance];
}

// MARK: - Notification

- (void)addNotification {
    @weakify(self);
    NSString *notifyName = IMYPublicBaseViewController.IMYViewControllerDidActiveChangedNotification;
    [[[[[NSNotificationCenter defaultCenter] rac_addObserverForName:notifyName object:nil] throttle:0.1] deliverOnMainThread] subscribeNext:^(NSNotification * _Nullable x) {
        @strongify(self);
        if (self.secondFloorGuidPagView) {
            BOOL isHomeTab = NO;
            BOOL isSecondVC = NO;
            id<IOCAppMainTabVC> rootVC = IMYHIVE_BINDER(IOCAppMainTabVC);
            SYTabBarIndexType const tabIndexType = rootVC.selectedTabIndexType;
            if (tabIndexType == SYTabBarIndexTypeHome) {
                isHomeTab = YES;
            }
            UINavigationController *navVC = [rootVC getRootVCWithTabIndexType:tabIndexType];
            if (navVC.viewControllers.count > 1 || navVC.presentedViewController) {
                isSecondVC = YES;
            }
            
            if (!isHomeTab || isSecondVC) {
                NSString *log = @"[2nd][引导动画] DidActiveChanged 不在首页 隐藏";
                [IMY2ndFloorManager logHelper:log];
                [self dismissGuidePag];
            }
        } else {
            NSLog(@"[2nd][引导动画] DidActiveChanged 无引导动画");
        }
    }];
}

// MARK: - Data

/// 请求二楼数据
- (void)req2ndFloorData:(IMYGridModlesResBlock)cb
               useCache:(BOOL)useCache {
    // 优先使用缓存的数据
    if (useCache) {
        NSArray *gridModels = [self createGridModels];
        if (gridModels && gridModels.count > 0) {
            if (cb) {
                cb(gridModels, nil);
            }
            return;
        }
    }
    
    if (useCache) {
        NSString *log = @"[2nd][数据] req2ndFloorData 要使用缓存 但 没有缓存";
        [IMY2ndFloorManager logHelper:log];
    }
    
    NSString *log = @"[2nd][数据] req2ndFloorData 请求接口";
    [IMY2ndFloorManager logHelper:log];
    
    // 请求最新的数据
    @weakify(self);
    [[IMY2ndFloorAPI sharedInstance] udimAllOnSuccess:^(NSDictionary * _Nonnull dict) {
        @strongify(self);
        // 解析数据，只要非空数据
        IMY2ndFloorUdimAllModel *allModel = [dict toModel:[IMY2ndFloorUdimAllModel class]];
        self.udimAllModel = allModel;
        
        // 请求接口的全部都重建数据
        self.gridModels = @[];
        NSArray<IMY2ndFloorGridModel *> *gridModels = [self createGridModels];
        
        // 缓存数据
        self.gridModels = gridModels;
        
        // 回调
        if (cb) {
            cb(gridModels, nil);
        }
        
        // 首次请求完接口，去检查是否需要展示引导动画
        if (!self.hasReq2ndFloorData) {
            [self startShowGuidePag];
        }
        self.hasReq2ndFloorData = YES;
    } onError:^(NSError * _Nonnull error) {
        @strongify(self);
        if (cb) {
            cb(self.gridModels, error);
        }
    }];
}

/// 请求 "最近使用过的服务"
- (void)reqLatestServiceData:(IMYGridModlesResBlock)cb {
    @weakify(self);
    [[IMY2ndFloorAPI sharedInstance] udimAllServiceOnSuccess:^(NSDictionary * _Nonnull dict) {
        @strongify(self);
        NSArray *list = dict[@"list"];
        NSArray<IMY2ndFloorServiceModel *> *modelList = [list toModels:[IMY2ndFloorServiceModel class]];
        
        NSMutableArray *gridModels = [NSMutableArray array];
        IMY2ndFloorGridModel *gridModel = [[IMY2ndFloorGridModel alloc] init];
        gridModel.gridType = IMY2ndFloorGridType_latest_services;
        gridModel.title = @"最近使用过的服务";
        gridModel.moreTitle = @"更多";
        gridModel.gridCellModels = [modelList bk_map:^id(IMY2ndFloorServiceModel *obj) {
            IMY2ndFloorGridCellModel *cellModel = [[IMY2ndFloorGridCellModel alloc] init];
            cellModel.code = obj.service_code;
            cellModel.title = obj.name;
            cellModel.icon = obj.icon;
            cellModel.uri = obj.uri;
            return cellModel;
        }];
        gridModel.maxRow = 0;
        gridModel.isShowTitleView = false;
        gridModel.isShowReddot = NO;
        gridModel.canDelete = YES;
        [gridModels addObject:gridModel];
        
        if (cb) {
            cb(gridModels, nil);
        }
    } onError:^(NSError * _Nonnull error) {
        if (cb) {
            cb(nil, error);
        }
    }];
}

/// 请求 "最近下单过的商家"
- (void)reqLatestMallData:(IMYGridModlesResBlock)cb {
    @weakify(self);
    [[IMY2ndFloorAPI sharedInstance] udimAllMallOnSuccess:^(NSDictionary * _Nonnull dict) {
        @strongify(self);
        NSArray *list = dict[@"list"];
        NSArray<IMY2ndFloorMallModel *> *modelList = [list toModels:[IMY2ndFloorMallModel class]];
        
        NSMutableArray *gridModels = [NSMutableArray array];
        IMY2ndFloorGridModel *gridModel = [[IMY2ndFloorGridModel alloc] init];
        gridModel.gridType = IMY2ndFloorGridType_latest_mall;
        gridModel.title = @"最近下单过的商家";
        gridModel.moreTitle = @"更多";
        gridModel.gridCellModels = [modelList bk_map:^id(IMY2ndFloorMallModel *obj) {
            IMY2ndFloorGridCellModel *cellModel = [[IMY2ndFloorGridCellModel alloc] init];
            cellModel.code = obj.mall_code;
            cellModel.mall_source = obj.source;
            cellModel.title = obj.name;
            cellModel.icon = obj.icon;
            cellModel.uri = obj.uri;
            cellModel.isMall = YES;
            return cellModel;
        }];
        gridModel.maxRow = 0;
        gridModel.isShowTitleView = false;
        gridModel.isShowReddot = NO;
        gridModel.canDelete = YES;
        [gridModels addObject:gridModel];
        
        if (cb) {
            cb(gridModels, nil);
        }
    } onError:^(NSError * _Nonnull error) {
        if (cb) {
            cb(nil, error);
        }
    }];
}

/// 请求 "热门服务"
- (void)reqHotServiceData:(IMYGridModlesResBlock)cb {
    @weakify(self);
    [[IMY2ndFloorAPI sharedInstance] serviceAllOnSuccess:^(NSDictionary * _Nonnull dict) {
        NSArray *list = dict[@"list"];
        NSArray<IMY2ndFloorServiceModel *> *modelList = [list toModels:[IMY2ndFloorServiceModel class]];
        
        NSMutableArray *gridModels = [NSMutableArray array];
        IMY2ndFloorGridModel *gridModel = [[IMY2ndFloorGridModel alloc] init];
        gridModel.gridType = IMY2ndFloorGridType_hot_service;
        gridModel.title = @"热门服务";
        gridModel.moreTitle = @"更多";
        gridModel.gridCellModels = [modelList bk_map:^id(IMY2ndFloorServiceModel *obj) {
            IMY2ndFloorGridCellModel *cellModel = [[IMY2ndFloorGridCellModel alloc] init];
            cellModel.code = obj.service_code;
            cellModel.title = obj.name;
            cellModel.icon = obj.icon;
            cellModel.uri = obj.uri;
            return cellModel;
        }];
        gridModel.maxRow = 0;
        gridModel.isShowTitleView = false;
        gridModel.isShowReddot = NO;
        gridModel.canDelete = NO;
        [gridModels addObject:gridModel];
        
        if (cb) {
            cb(gridModels, nil);
        }
    } onError:^(NSError * _Nonnull error) {
        if (cb) {
            cb(nil, error);
        }
    }];
}

- (NSArray<IMY2ndFloorGridModel *> *)createGridModels {
    if (self.gridModels && self.gridModels.count > 0) {
        return self.gridModels;
    }
    
    NSMutableArray *gridModels = [NSMutableArray array];
    
    if (self.udimAllModel && [self.udimAllModel isKindOfClass:[IMY2ndFloorUdimAllModel class]]) {
        // 会员权益接口响应
        NSDictionary *vip_privileges_resp = self.udimAllModel.vip_privileges_resp;
        BOOL isValid = YES;
        id<IOCMemberRights2ndFloorAdapter> adpt = IMYHIVE_BINDER(IOCMemberRights2ndFloorAdapter);
        if ([adpt respondsToSelector:@selector(validWithData:)]) {
            isValid = [adpt validWithData:vip_privileges_resp];
        } else {
            isValid = vip_privileges_resp != nil;
        }
        if (isValid) {
            IMY2ndFloorGridModel *gridModel = [[IMY2ndFloorGridModel alloc] init];
            gridModel.gridType = IMY2ndFloorGridType_vip;
            gridModel.vip_privileges_resp = vip_privileges_resp;
            [gridModels addObject:gridModel];
        }
        
        // latest_services
        NSArray<IMY2ndFloorServiceModel *> *latest_services = self.udimAllModel.latest_services;
        if (latest_services.count > 0) {
            IMY2ndFloorGridModel *gridModel = [[IMY2ndFloorGridModel alloc] init];
            gridModel.gridType = IMY2ndFloorGridType_latest_services;
            gridModel.title = @"最近使用过的服务";
            gridModel.moreTitle = @"更多";
            gridModel.gridCellModels = [latest_services bk_map:^id(IMY2ndFloorServiceModel *obj) {
                IMY2ndFloorGridCellModel *cellModel = [[IMY2ndFloorGridCellModel alloc] init];
                cellModel.code = obj.service_code;
                cellModel.title = obj.name;
                cellModel.icon = obj.icon;
                cellModel.uri = obj.uri;
                return cellModel;
            }];
            gridModel.maxRow = 2;
            gridModel.isShowTitleView = YES;
            gridModel.canDelete = YES;
            [gridModels addObject:gridModel];
        }
        
        // latest_mall
        NSArray<IMY2ndFloorMallModel *> *latest_mall = self.udimAllModel.latest_mall;
        if (latest_mall.count > 0) {
            IMY2ndFloorGridModel *gridModel = [[IMY2ndFloorGridModel alloc] init];
            gridModel.gridType = IMY2ndFloorGridType_latest_mall;
            gridModel.title = @"最近下单过的商家";
            gridModel.moreTitle = @"更多";
            gridModel.gridCellModels = [latest_mall bk_map:^id(IMY2ndFloorMallModel *obj) {
                IMY2ndFloorGridCellModel *cellModel = [[IMY2ndFloorGridCellModel alloc] init];
                cellModel.code = obj.mall_code;
                cellModel.mall_source = obj.source;
                cellModel.title = obj.name;
                cellModel.icon = obj.icon;
                cellModel.uri = obj.uri;
                cellModel.isMall = YES;
                return cellModel;
            }];
            gridModel.maxRow = 1;
            gridModel.isShowTitleView = YES;
            gridModel.canDelete = YES;
            [gridModels addObject:gridModel];
        }
        
        // hot_services
        NSArray<IMY2ndFloorServiceModel *> *hot_services = self.udimAllModel.hot_services;
        if (hot_services.count > 0) {
            IMY2ndFloorGridModel *gridModel = [[IMY2ndFloorGridModel alloc] init];
            gridModel.gridType = IMY2ndFloorGridType_hot_service;
            gridModel.title = @"热门服务";
            gridModel.moreTitle = @"更多";
            gridModel.gridCellModels = [hot_services bk_map:^id(IMY2ndFloorServiceModel *obj) {
                IMY2ndFloorGridCellModel *cellModel = [[IMY2ndFloorGridCellModel alloc] init];
                cellModel.code = obj.service_code;
                cellModel.title = obj.name;
                cellModel.icon = obj.icon;
                cellModel.uri = obj.uri;
                return cellModel;
            }];
            gridModel.maxRow = 2;
            gridModel.isShowTitleView = YES;
            gridModel.isShowReddot = NO;
            gridModel.canDelete = NO;
            [gridModels addObject:gridModel];
        }
    }
    
    return gridModels.copy;
}

/// 二楼相关页面，重新构建数据
+ (void)postRebuildData {
    [[NSNotificationCenter defaultCenter] postNotificationName:@"imy2ndfloor_rebuilddata" object:nil];
}

/// 删除服务或商城
- (void)deleteGridModel:(IMY2ndFloorGridModel *)gridModel
              cellModel:(IMY2ndFloorGridCellModel *)cellModel {
    // 删除已存在数据
    for (IMY2ndFloorGridModel *gridModel0 in self.gridModels) {
        if (gridModel0.gridType == gridModel.gridType) {
            gridModel0.gridCellModels = [gridModel0.gridCellModels bk_select:^BOOL(IMY2ndFloorGridCellModel *cellModel0) {
                return ![cellModel0.code isEqualToString:cellModel.code];
            }];
        }
    }
    
    // 通知重建数据
    [IMY2ndFloorManager postRebuildData];
    
    // 上报服务端
    if (gridModel.gridType == IMY2ndFloorGridType_latest_services) {
        [[IMY2ndFloorAPI sharedInstance] udimDelServiceWithServiceCode:cellModel.code
                                                             onSuccess:^(NSDictionary * _Nonnull dict) {
            [UIWindow imy_showTextHUD:@"已删除"];
        } onError:^(NSError * _Nonnull error) {
            [UIWindow imy_showTextHUD:@"已删除"];
        }];
    } else if (gridModel.gridType == IMY2ndFloorGridType_latest_mall) {
        [[IMY2ndFloorAPI sharedInstance] udimDelMallWithMallCode:cellModel.code
                                                          source:cellModel.mall_source
                                                       onSuccess:^(NSDictionary * _Nonnull dict) {
            [UIWindow imy_showTextHUD:@"已删除"];
        } onError:^(NSError * _Nonnull error) {
            [UIWindow imy_showTextHUD:@"已删除"];
        }];
    }
}

// MARK: - Guide pag

- (void)startShowGuidePag {
#if __has_include(<IMYAdvertisement/IMYAdSplashManager.h>)
    [self.popupSplashSignal dispose];
    self.popupSplashSignal = nil;
    [self.adSplashTypeSignal dispose];
    self.adSplashTypeSignal = nil;
    
    BOOL waitSignal = NO;
    
    // 首页插屏
    IMYADSplashType homePopupSplashType = [IMYAdSplashManager shareInstance].homePopupSplashType;
    if (homePopupSplashType == IMYADSplashTypeNone ||
        homePopupSplashType == IMYADSplashTypeLoading ||
        homePopupSplashType == IMYADSplashTypeShowing) {
        NSString *log = @"[2nd][引导动画] 广告信号 监听信号 homePopupSplashType）";
        [IMY2ndFloorManager logHelper:log];
        
        waitSignal = YES;
        
        @weakify(self);
        RACSignal *signal = [RACObserve([IMYAdSplashManager shareInstance], homePopupSplashType) takeUntil:self.rac_willDeallocSignal];
        self.popupSplashSignal = [[[signal throttle:0.1] deliverOnMainThread] subscribeNext:^(id x) {
            @strongify(self);
            [self checkIfShowGuidePag];
        }];
    }
    
    // 开屏
    IMYADSplashType showADSplashType = [IMYAdSplashManager shareInstance].showADSplashType;
    if (showADSplashType == IMYADSplashTypeNone ||
        showADSplashType == IMYADSplashTypeLoading ||
        showADSplashType == IMYADSplashTypeShowing) {
        NSString *log = @"[2nd][引导动画] 广告信号 监听信号 showADSplashType）";
        [IMY2ndFloorManager logHelper:log];
        
        waitSignal = YES;
        
        @weakify(self);
        RACSignal *signal = [RACObserve([IMYAdSplashManager shareInstance], showADSplashType) takeUntil:self.rac_willDeallocSignal];
        self.adSplashTypeSignal = [[[signal throttle:0.1] deliverOnMainThread] subscribeNext:^(id x) {
            @strongify(self);
            [self checkIfShowGuidePag];
        }];
    }
    
    if (!waitSignal) {
        NSString *log = @"[2nd][引导动画] 广告信号 通过（不用监听信号）";
        [IMY2ndFloorManager logHelper:log];
        
        [self checkIfShowGuidePag];
    }
#endif
}

- (void)checkIfShowGuidePag {
    @weakify(self);
    
#if __has_include(<IMYAdvertisement/IMYAdSplashManager.h>)
    if (self.isChecking) {
        return;
    }
    
    self.isChecking = YES;
    
    // [check] 广告信号
    IMYADSplashType homePopupSplashType = [IMYAdSplashManager shareInstance].homePopupSplashType;
    IMYADSplashType showADSplashType = [IMYAdSplashManager shareInstance].showADSplashType;
    
    NSString *log = [NSString stringWithFormat:@"[2nd][引导动画] 广告信号 %@ %@", @(homePopupSplashType), @(showADSplashType)];
    [IMY2ndFloorManager logHelper:log];
    
    if (homePopupSplashType == IMYADSplashTypeNone ||
        homePopupSplashType == IMYADSplashTypeLoading ||
        homePopupSplashType == IMYADSplashTypeShowing) {
        
        NSString *log = @"[2nd][引导动画] 广告信号 拦截 首页插屏";
        [IMY2ndFloorManager logHelper:log];
        
        self.isChecking = NO;
        
        return;
    }
    if (showADSplashType == IMYADSplashTypeNone ||
        showADSplashType == IMYADSplashTypeLoading ||
        showADSplashType == IMYADSplashTypeShowing) {
        
        NSString *log = @"[2nd][引导动画] 广告信号 拦截 闪屏广告";
        [IMY2ndFloorManager logHelper:log];
        
        self.isChecking = NO;
        
        return;
    }
    
    [self.popupSplashSignal dispose];
    self.popupSplashSignal = nil;
    [self.adSplashTypeSignal dispose];
    self.adSplashTypeSignal = nil;
    
    [IMY2ndFloorManager logHelper:@"[2nd][引导动画] 广告信号 通过"];
    
    // [check] 其他弹窗
    BOOL hasOtherAlert = IMYPublicAppHelper.shareAppHelper.isCloseTwoFloorAdGif;
#ifdef DEBUG
    if ([[IMYKV defaultKV] boolForKey:@"#+imy-home2ndfloor-guide-forceShow"]) {
        hasOtherAlert = NO;
    }
#endif
    if (hasOtherAlert) {
        NSString *log = @"[2nd][引导动画] 其他弹窗 拦截";
        [IMY2ndFloorManager logHelper:log];
        
        self.isChecking = NO;
        
        return;
    }
    
#endif
    
    BOOL canShowWithTopPush = [IMYAlertShowManager canShowWithTopPushStatus];
#ifdef DEBUG
    if ([[IMYKV defaultKV] boolForKey:@"#+imy-home2ndfloor-guide-forceShow"]) {
        canShowWithTopPush = YES;
    }
#endif
    if (!canShowWithTopPush) {
        NSString *log = @"[2nd][引导动画] 存在顶部 push 拦截";
        [IMY2ndFloorManager logHelper:log];
        
        self.isChecking = NO;
        
        return;
    }
    
    // [check] 是否在首页
    BOOL isHomeTab = NO;
    BOOL isSecondVC = NO;
    id<IOCAppMainTabVC> rootVC = IMYHIVE_BINDER(IOCAppMainTabVC);
    SYTabBarIndexType const tabIndexType = rootVC.selectedTabIndexType;
    if (tabIndexType == SYTabBarIndexTypeHome) {
        isHomeTab = YES;
    }
    UINavigationController *navVC = [rootVC getRootVCWithTabIndexType:tabIndexType];
    if (navVC.viewControllers.count > 1 || navVC.presentedViewController) {
        isSecondVC = YES;
    }
    
    if (!isHomeTab || isSecondVC) {
        NSString *log = @"[2nd][引导动画] 不在首页 拦截";
        [IMY2ndFloorManager logHelper:log];
        
        self.isChecking = NO;
        
        return;
    }
    
    // [check] 是否展示过
    NSString *key = [NSString stringWithFormat:@"imy-home2ndfloor-guide-lastShowTime-%@", IMYPublicAppHelper.shareAppHelper.userid];
    NSString *lastShowTime = [[IMYKV defaultKV] stringForKey:key];
    BOOL hasShowed = imy_isNotEmptyString(lastShowTime);
#ifdef DEBUG
    if ([[IMYKV defaultKV] boolForKey:@"#+imy-home2ndfloor-guide-forceShow"]) {
        hasShowed = NO;
    }
#endif
    if (hasShowed) {
        NSString *log = @"[2nd][引导动画] 展示过 拦截";
        [IMY2ndFloorManager logHelper:log];
        
        self.isChecking = NO;
        
        return;
    }
    
    imy_asyncMainBlock(^{
        @strongify(self);
        [self doShowGuidePag];
    });
    
    self.isChecking = NO;
}

- (void)doShowGuidePag {
    NSLog(@"[2nd][引导动画][显示隐藏][显示]");
    kCurrentShowing = YES;
    if (!self.secondFloorGuidPagView) {
        CGFloat ratio = 1206.0 / 402.0;
        CGFloat height = SCREEN_WIDTH / ratio;
        CGFloat y = height - (SCREEN_STATUSBAR_HEIGHT + SCREEN_NAVIGATIONBAR_HEIGHT);
        CGRect frame = CGRectMake(0, -y, SCREEN_WIDTH, height);
        self.secondFloorGuidPagView = [[IMY2ndFloorGuideView alloc] initWithFrame:frame];
        @weakify(self);
        [self.secondFloorGuidPagView setOnPlayEnd:^{
            @strongify(self);
            [self dismissGuidePag];
        }];
        
        [[IMY2ndFloorAPI sharedInstance] postEvent_ppt_sy_elyddx:1];
    }
    
    [self.secondFloorGuidPagView show];
}

- (void)dismissGuidePag {
    NSLog(@"[2nd][引导动画][显示隐藏][隐藏]");
    kCurrentShowing = NO;
    if (self.secondFloorGuidPagView) {
        [self.secondFloorGuidPagView dismiss];
        self.secondFloorGuidPagView = nil;
    }
}

// MARK: - Helper

/// 工具：是否需要显示提示文案 “最近使用过的服务、下单过的商家将出现在这里”
+ (BOOL)isNeedShowPlaceholder:(NSArray<IMY2ndFloorGridModel *> *)gridModels {
    BOOL latest_mall_valid = NO;
    BOOL latest_services_valid = NO;
    
    for (IMY2ndFloorGridModel *gridModel in gridModels) {
        if (gridModel.gridType == IMY2ndFloorGridType_latest_mall && gridModel.gridCellModels.count > 0) {
            latest_mall_valid = YES;
        }
        
        if (gridModel.gridType == IMY2ndFloorGridType_latest_services && gridModel.gridCellModels.count > 0) {
            latest_services_valid = YES;
        }
    }
    
    return !latest_mall_valid && !latest_services_valid;
}

static BOOL kCurrentShowing = NO;
+ (BOOL)isShowTopPushBar {
    return kCurrentShowing;
}

// MARK: - Log

+ (void)logHelper:(NSString *)log {
    if (log) {
        NSLog(log);
    }
    
#ifdef DEBUG
    NSArray *key = @"#+imykv-2ndfloor-log";
    NSArray *logs = [[IMYKV defaultKV] arrayForKey:key];
    if (logs == nil) {
        logs = @[];
    }
    
    if (imy_isNotEmptyString(log)) {
        NSMutableArray *logs_m = [logs mutableCopy];
        [logs_m addObject:[NSString stringWithFormat:@"%@", log]];
        [[IMYKV defaultKV] setArray:logs_m forKey:key];
    } else {
        [[IMYKV defaultKV] removeForKey:key];
    }
#endif
}

+ (NSArray *)getLogs {
    NSArray *key = @"#+imykv-2ndfloor-log";
    
    NSArray *logs = [[IMYKV defaultKV] arrayForKey:key];
    if (logs == nil) {
        logs = @[];
    }
    
    return logs;
}

@end
