//
//  IMY2ndFloorGridModel.h
//  IMYCommonKit
//
//  Created by HBQ on 2025/6/12.
//

#import <Foundation/Foundation.h>
#import "IMY2ndFloorGridCellModel.h"

typedef NS_ENUM(NSUInteger, IMY2ndFloorGridType) {
    IMY2ndFloorGridType_latest_services = 0, ///< 最近使用过的服务
    IMY2ndFloorGridType_latest_mall, ///< 最近下单过的商家
    IMY2ndFloorGridType_hot_service, ///< 热门服务
    IMY2ndFloorGridType_vip, ///< 会员专区
    IMY2ndFloorGridType_banner_ad ///< banner 广告
};

NS_ASSUME_NONNULL_BEGIN

@interface IMY2ndFloorGridModel : NSObject <NSCopying>

@property (nonatomic, assign) IMY2ndFloorGridType gridType;

@property (nonatomic, copy) NSString *title;

@property (nonatomic, copy) NSString *moreTitle;

@property (nonatomic, copy) NSArray<IMY2ndFloorGridCellModel *> *gridCellModels;

/// 最多显示行数：0 表示不限制
@property (nonatomic, assign) NSInteger maxRow;

/// 设置为 false 则会隐藏整个标题区域
@property (nonatomic, assign) BOOL isShowTitleView;

/// 如果这个字段为 true 则 UI 上只展示一个提示文案 “最近使用过的服务、下单过的商家将出现在这里”
@property (nonatomic, assign) BOOL isPlaceholder;

/// 标题栏是否显示红点
@property (nonatomic, assign) BOOL isShowReddot;

/// 元素是否能删除
@property (nonatomic, assign) BOOL canDelete;

/// 埋点用：楼层
@property (nonatomic, assign) NSInteger bi_floor;

/// banner 广告原始数据
@property (nonatomic, copy) NSArray *bannerAdDatas;

- (CGFloat)rowCount;

// MARK: - 会员

@property (nonatomic, copy) NSDictionary *vip_privileges_resp;

@end

NS_ASSUME_NONNULL_END
