//
//  IMY2ndFloorUdimAllModel.h
//  IMYCommonKit
//
//  Created by HBQ on 2025/6/17.
//

#import <Foundation/Foundation.h>
#import "IMY2ndFloorServiceModel.h"
#import "IMY2ndFloorMallModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface IMY2ndFloorUdimAllModel : NSObject

@property (nonatomic, copy) NSArray<IMY2ndFloorServiceModel *> *latest_services;

@property (nonatomic, copy) NSArray<IMY2ndFloorMallModel *> *latest_mall;

@property (nonatomic, copy) NSArray<IMY2ndFloorServiceModel *> *hot_services;

@property (nonatomic, copy) NSDictionary *vip_privileges_resp;

- (BOOL)isEmptyData;

@end

NS_ASSUME_NONNULL_END
